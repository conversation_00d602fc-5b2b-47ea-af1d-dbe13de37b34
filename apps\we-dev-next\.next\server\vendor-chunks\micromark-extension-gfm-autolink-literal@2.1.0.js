"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-extension-gfm-autolink-literal@2.1.0";
exports.ids = ["vendor-chunks/micromark-extension-gfm-autolink-literal@2.1.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/micromark-extension-gfm-autolink-literal@2.1.0/node_modules/micromark-extension-gfm-autolink-literal/dev/lib/html.js":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-extension-gfm-autolink-literal@2.1.0/node_modules/micromark-extension-gfm-autolink-literal/dev/lib/html.js ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmAutolinkLiteralHtml: () => (/* binding */ gfmAutolinkLiteralHtml)\n/* harmony export */ });\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/./node_modules/.pnpm/micromark-util-sanitize-uri@2.0.1/node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/**\n * @import {CompileContext, Handle, HtmlExtension, Token} from 'micromark-util-types'\n */\n\n\n\n/**\n * Create an HTML extension for `micromark` to support GitHub autolink literal\n * when serializing to HTML.\n *\n * @returns {HtmlExtension}\n *   Extension for `micromark` that can be passed in `htmlExtensions` to\n *   support GitHub autolink literal when serializing to HTML.\n */\nfunction gfmAutolinkLiteralHtml() {\n  return {\n    exit: {literalAutolinkEmail, literalAutolinkHttp, literalAutolinkWww}\n  }\n}\n\n/**\n * @this {CompileContext}\n * @type {Handle}\n */\nfunction literalAutolinkWww(token) {\n  anchorFromToken.call(this, token, 'http://')\n}\n\n/**\n * @this {CompileContext}\n * @type {Handle}\n */\nfunction literalAutolinkEmail(token) {\n  anchorFromToken.call(this, token, 'mailto:')\n}\n\n/**\n * @this {CompileContext}\n * @type {Handle}\n */\nfunction literalAutolinkHttp(token) {\n  anchorFromToken.call(this, token)\n}\n\n/**\n * @this CompileContext\n * @param {Token} token\n * @param {string | null | undefined} [protocol]\n * @returns {undefined}\n */\nfunction anchorFromToken(token, protocol) {\n  const url = this.sliceSerialize(token)\n  this.tag('<a href=\"' + (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__.sanitizeUri)((protocol || '') + url) + '\">')\n  this.raw(this.encode(url))\n  this.tag('</a>')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-extension-gfm-autolink-literal@2.1.0/node_modules/micromark-extension-gfm-autolink-literal/dev/lib/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/micromark-extension-gfm-autolink-literal@2.1.0/node_modules/micromark-extension-gfm-autolink-literal/dev/lib/syntax.js":
/*!***************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-extension-gfm-autolink-literal@2.1.0/node_modules/micromark-extension-gfm-autolink-literal/dev/lib/syntax.js ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmAutolinkLiteral: () => (/* binding */ gfmAutolinkLiteral)\n/* harmony export */ });\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/.pnpm/micromark-util-character@2.1.1/node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/codes.js\");\n/**\n * @import {Code, ConstructRecord, Event, Extension, Previous, State, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */\n\n\n\n\nconst wwwPrefix = {tokenize: tokenizeWwwPrefix, partial: true}\nconst domain = {tokenize: tokenizeDomain, partial: true}\nconst path = {tokenize: tokenizePath, partial: true}\nconst trail = {tokenize: tokenizeTrail, partial: true}\nconst emailDomainDotTrail = {\n  tokenize: tokenizeEmailDomainDotTrail,\n  partial: true\n}\n\nconst wwwAutolink = {\n  name: 'wwwAutolink',\n  tokenize: tokenizeWwwAutolink,\n  previous: previousWww\n}\n\nconst protocolAutolink = {\n  name: 'protocolAutolink',\n  tokenize: tokenizeProtocolAutolink,\n  previous: previousProtocol\n}\n\nconst emailAutolink = {\n  name: 'emailAutolink',\n  tokenize: tokenizeEmailAutolink,\n  previous: previousEmail\n}\n\n/** @type {ConstructRecord} */\nconst text = {}\n\n/**\n * Create an extension for `micromark` to support GitHub autolink literal\n * syntax.\n *\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions` to enable GFM\n *   autolink literal syntax.\n */\nfunction gfmAutolinkLiteral() {\n  return {text}\n}\n\n/** @type {Code} */\nlet code = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit0\n\n// Add alphanumerics.\nwhile (code < micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftCurlyBrace) {\n  text[code] = emailAutolink\n  code++\n  if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.colon) code = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseA\n  else if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket) code = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseA\n}\n\ntext[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.plusSign] = emailAutolink\ntext[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dash] = emailAutolink\ntext[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dot] = emailAutolink\ntext[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore] = emailAutolink\ntext[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseH] = [emailAutolink, protocolAutolink]\ntext[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseH] = [emailAutolink, protocolAutolink]\ntext[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseW] = [emailAutolink, wwwAutolink]\ntext[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseW] = [emailAutolink, wwwAutolink]\n\n// To do: perform email autolink literals on events, afterwards.\n// That’s where `markdown-rs` and `cmark-gfm` perform it.\n// It should look for `@`, then for atext backwards, and then for a label\n// forwards.\n// To do: `mailto:`, `xmpp:` protocol as prefix.\n\n/**\n * Email autolink literal.\n *\n * ```markdown\n * > | a <EMAIL> b\n *       ^^^^^^^^^^^^^^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeEmailAutolink(effects, ok, nok) {\n  const self = this\n  /** @type {boolean | undefined} */\n  let dot\n  /** @type {boolean} */\n  let data\n\n  return start\n\n  /**\n   * Start of email autolink literal.\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (\n      !gfmAtext(code) ||\n      !previousEmail.call(self, self.previous) ||\n      previousUnbalanced(self.events)\n    ) {\n      return nok(code)\n    }\n\n    effects.enter('literalAutolink')\n    effects.enter('literalAutolinkEmail')\n    return atext(code)\n  }\n\n  /**\n   * In email atext.\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atext(code) {\n    if (gfmAtext(code)) {\n      effects.consume(code)\n      return atext\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.atSign) {\n      effects.consume(code)\n      return emailDomain\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In email domain.\n   *\n   * The reference code is a bit overly complex as it handles the `@`, of which\n   * there may be just one.\n   * Source: <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L318>\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailDomain(code) {\n    // Dot followed by alphanumerical (not `-` or `_`).\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dot) {\n      return effects.check(\n        emailDomainDotTrail,\n        emailDomainAfter,\n        emailDomainDot\n      )(code)\n    }\n\n    // Alphanumerical, `-`, and `_`.\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dash ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlphanumeric)(code)\n    ) {\n      data = true\n      effects.consume(code)\n      return emailDomain\n    }\n\n    // To do: `/` if xmpp.\n\n    // Note: normally we’d truncate trailing punctuation from the link.\n    // However, email autolink literals cannot contain any of those markers,\n    // except for `.`, but that can only occur if it isn’t trailing.\n    // So we can ignore truncating!\n    return emailDomainAfter(code)\n  }\n\n  /**\n   * In email domain, on dot that is not a trail.\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *                      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailDomainDot(code) {\n    effects.consume(code)\n    dot = true\n    return emailDomain\n  }\n\n  /**\n   * After email domain.\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *                          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailDomainAfter(code) {\n    // Domain must not be empty, must include a dot, and must end in alphabetical.\n    // Source: <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L332>.\n    if (data && dot && (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlpha)(self.previous)) {\n      effects.exit('literalAutolinkEmail')\n      effects.exit('literalAutolink')\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n}\n\n/**\n * `www` autolink literal.\n *\n * ```markdown\n * > | a www.example.org b\n *       ^^^^^^^^^^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeWwwAutolink(effects, ok, nok) {\n  const self = this\n\n  return wwwStart\n\n  /**\n   * Start of www autolink literal.\n   *\n   * ```markdown\n   * > | www.example.com/a?b#c\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function wwwStart(code) {\n    if (\n      (code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseW && code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseW) ||\n      !previousWww.call(self, self.previous) ||\n      previousUnbalanced(self.events)\n    ) {\n      return nok(code)\n    }\n\n    effects.enter('literalAutolink')\n    effects.enter('literalAutolinkWww')\n    // Note: we *check*, so we can discard the `www.` we parsed.\n    // If it worked, we consider it as a part of the domain.\n    return effects.check(\n      wwwPrefix,\n      effects.attempt(domain, effects.attempt(path, wwwAfter), nok),\n      nok\n    )(code)\n  }\n\n  /**\n   * After a www autolink literal.\n   *\n   * ```markdown\n   * > | www.example.com/a?b#c\n   *                          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function wwwAfter(code) {\n    effects.exit('literalAutolinkWww')\n    effects.exit('literalAutolink')\n    return ok(code)\n  }\n}\n\n/**\n * Protocol autolink literal.\n *\n * ```markdown\n * > | a https://example.org b\n *       ^^^^^^^^^^^^^^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeProtocolAutolink(effects, ok, nok) {\n  const self = this\n  let buffer = ''\n  let seen = false\n\n  return protocolStart\n\n  /**\n   * Start of protocol autolink literal.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function protocolStart(code) {\n    if (\n      (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseH || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseH) &&\n      previousProtocol.call(self, self.previous) &&\n      !previousUnbalanced(self.events)\n    ) {\n      effects.enter('literalAutolink')\n      effects.enter('literalAutolinkHttp')\n      buffer += String.fromCodePoint(code)\n      effects.consume(code)\n      return protocolPrefixInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In protocol.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *     ^^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function protocolPrefixInside(code) {\n    // `5` is size of `https`\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlpha)(code) && buffer.length < 5) {\n      // @ts-expect-error: definitely number.\n      buffer += String.fromCodePoint(code)\n      effects.consume(code)\n      return protocolPrefixInside\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.colon) {\n      const protocol = buffer.toLowerCase()\n\n      if (protocol === 'http' || protocol === 'https') {\n        effects.consume(code)\n        return protocolSlashesInside\n      }\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In slashes.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *           ^^\n   * ```\n   *\n   * @type {State}\n   */\n  function protocolSlashesInside(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.slash) {\n      effects.consume(code)\n\n      if (seen) {\n        return afterProtocol\n      }\n\n      seen = true\n      return protocolSlashesInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After protocol, before domain.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function afterProtocol(code) {\n    // To do: this is different from `markdown-rs`:\n    // https://github.com/wooorm/markdown-rs/blob/b3a921c761309ae00a51fe348d8a43adbc54b518/src/construct/gfm_autolink_literal.rs#L172-L182\n    return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiControl)(code) ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEndingOrSpace)(code) ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodeWhitespace)(code) ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodePunctuation)(code)\n      ? nok(code)\n      : effects.attempt(domain, effects.attempt(path, protocolAfter), nok)(code)\n  }\n\n  /**\n   * After a protocol autolink literal.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *                              ^\n   * ```\n   *\n   * @type {State}\n   */\n  function protocolAfter(code) {\n    effects.exit('literalAutolinkHttp')\n    effects.exit('literalAutolink')\n    return ok(code)\n  }\n}\n\n/**\n * `www` prefix.\n *\n * ```markdown\n * > | a www.example.org b\n *       ^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeWwwPrefix(effects, ok, nok) {\n  let size = 0\n\n  return wwwPrefixInside\n\n  /**\n   * In www prefix.\n   *\n   * ```markdown\n   * > | www.example.com\n   *     ^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function wwwPrefixInside(code) {\n    if ((code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseW || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseW) && size < 3) {\n      size++\n      effects.consume(code)\n      return wwwPrefixInside\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dot && size === 3) {\n      effects.consume(code)\n      return wwwPrefixAfter\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After www prefix.\n   *\n   * ```markdown\n   * > | www.example.com\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function wwwPrefixAfter(code) {\n    // If there is *anything*, we can link.\n    return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof ? nok(code) : ok(code)\n  }\n}\n\n/**\n * Domain.\n *\n * ```markdown\n * > | a https://example.org b\n *               ^^^^^^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeDomain(effects, ok, nok) {\n  /** @type {boolean | undefined} */\n  let underscoreInLastSegment\n  /** @type {boolean | undefined} */\n  let underscoreInLastLastSegment\n  /** @type {boolean | undefined} */\n  let seen\n\n  return domainInside\n\n  /**\n   * In domain.\n   *\n   * ```markdown\n   * > | https://example.com/a\n   *             ^^^^^^^^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function domainInside(code) {\n    // Check whether this marker, which is a trailing punctuation\n    // marker, optionally followed by more trailing markers, and then\n    // followed by an end.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dot || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore) {\n      return effects.check(trail, domainAfter, domainAtPunctuation)(code)\n    }\n\n    // GH documents that only alphanumerics (other than `-`, `.`, and `_`) can\n    // occur, which sounds like ASCII only, but they also support `www.點看.com`,\n    // so that’s Unicode.\n    // Instead of some new production for Unicode alphanumerics, markdown\n    // already has that for Unicode punctuation and whitespace, so use those.\n    // Source: <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L12>.\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEndingOrSpace)(code) ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodeWhitespace)(code) ||\n      (code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dash && (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodePunctuation)(code))\n    ) {\n      return domainAfter(code)\n    }\n\n    seen = true\n    effects.consume(code)\n    return domainInside\n  }\n\n  /**\n   * In domain, at potential trailing punctuation, that was not trailing.\n   *\n   * ```markdown\n   * > | https://example.com\n   *                    ^\n   * ```\n   *\n   * @type {State}\n   */\n  function domainAtPunctuation(code) {\n    // There is an underscore in the last segment of the domain\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore) {\n      underscoreInLastSegment = true\n    }\n    // Otherwise, it’s a `.`: save the last segment underscore in the\n    // penultimate segment slot.\n    else {\n      underscoreInLastLastSegment = underscoreInLastSegment\n      underscoreInLastSegment = undefined\n    }\n\n    effects.consume(code)\n    return domainInside\n  }\n\n  /**\n   * After domain.\n   *\n   * ```markdown\n   * > | https://example.com/a\n   *                        ^\n   * ```\n   *\n   * @type {State} */\n  function domainAfter(code) {\n    // Note: that’s GH says a dot is needed, but it’s not true:\n    // <https://github.com/github/cmark-gfm/issues/279>\n    if (underscoreInLastLastSegment || underscoreInLastSegment || !seen) {\n      return nok(code)\n    }\n\n    return ok(code)\n  }\n}\n\n/**\n * Path.\n *\n * ```markdown\n * > | a https://example.org/stuff b\n *                          ^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizePath(effects, ok) {\n  let sizeOpen = 0\n  let sizeClose = 0\n\n  return pathInside\n\n  /**\n   * In path.\n   *\n   * ```markdown\n   * > | https://example.com/a\n   *                        ^^\n   * ```\n   *\n   * @type {State}\n   */\n  function pathInside(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftParenthesis) {\n      sizeOpen++\n      effects.consume(code)\n      return pathInside\n    }\n\n    // To do: `markdown-rs` also needs this.\n    // If this is a paren, and there are less closings than openings,\n    // we don’t check for a trail.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightParenthesis && sizeClose < sizeOpen) {\n      return pathAtPunctuation(code)\n    }\n\n    // Check whether this trailing punctuation marker is optionally\n    // followed by more trailing markers, and then followed\n    // by an end.\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.exclamationMark ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.quotationMark ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.ampersand ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.apostrophe ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightParenthesis ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.comma ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dot ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.colon ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.semicolon ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lessThan ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.questionMark ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.tilde\n    ) {\n      return effects.check(trail, ok, pathAtPunctuation)(code)\n    }\n\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEndingOrSpace)(code) ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodeWhitespace)(code)\n    ) {\n      return ok(code)\n    }\n\n    effects.consume(code)\n    return pathInside\n  }\n\n  /**\n   * In path, at potential trailing punctuation, that was not trailing.\n   *\n   * ```markdown\n   * > | https://example.com/a\"b\n   *                          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function pathAtPunctuation(code) {\n    // Count closing parens.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightParenthesis) {\n      sizeClose++\n    }\n\n    effects.consume(code)\n    return pathInside\n  }\n}\n\n/**\n * Trail.\n *\n * This calls `ok` if this *is* the trail, followed by an end, which means\n * the entire trail is not part of the link.\n * It calls `nok` if this *is* part of the link.\n *\n * ```markdown\n * > | https://example.com\").\n *                        ^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeTrail(effects, ok, nok) {\n  return trail\n\n  /**\n   * In trail of domain or path.\n   *\n   * ```markdown\n   * > | https://example.com\").\n   *                        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function trail(code) {\n    // Regular trailing punctuation.\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.exclamationMark ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.quotationMark ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.apostrophe ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightParenthesis ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.comma ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dot ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.colon ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.semicolon ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.questionMark ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.tilde\n    ) {\n      effects.consume(code)\n      return trail\n    }\n\n    // `&` followed by one or more alphabeticals and then a `;`, is\n    // as a whole considered as trailing punctuation.\n    // In all other cases, it is considered as continuation of the URL.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.ampersand) {\n      effects.consume(code)\n      return trailCharacterReferenceStart\n    }\n\n    // Needed because we allow literals after `[`, as we fix:\n    // <https://github.com/github/cmark-gfm/issues/278>.\n    // Check that it is not followed by `(` or `[`.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket) {\n      effects.consume(code)\n      return trailBracketAfter\n    }\n\n    if (\n      // `<` is an end.\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lessThan ||\n      // So is whitespace.\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEndingOrSpace)(code) ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodeWhitespace)(code)\n    ) {\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In trail, after `]`.\n   *\n   * > 👉 **Note**: this deviates from `cmark-gfm` to fix a bug.\n   * > See end of <https://github.com/github/cmark-gfm/issues/278> for more.\n   *\n   * ```markdown\n   * > | https://example.com](\n   *                         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function trailBracketAfter(code) {\n    // Whitespace or something that could start a resource or reference is the end.\n    // Switch back to trail otherwise.\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftParenthesis ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEndingOrSpace)(code) ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodeWhitespace)(code)\n    ) {\n      return ok(code)\n    }\n\n    return trail(code)\n  }\n\n  /**\n   * In character-reference like trail, after `&`.\n   *\n   * ```markdown\n   * > | https://example.com&amp;).\n   *                         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function trailCharacterReferenceStart(code) {\n    // When non-alpha, it’s not a trail.\n    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlpha)(code) ? trailCharacterReferenceInside(code) : nok(code)\n  }\n\n  /**\n   * In character-reference like trail.\n   *\n   * ```markdown\n   * > | https://example.com&amp;).\n   *                         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function trailCharacterReferenceInside(code) {\n    // Switch back to trail if this is well-formed.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.semicolon) {\n      effects.consume(code)\n      return trail\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlpha)(code)) {\n      effects.consume(code)\n      return trailCharacterReferenceInside\n    }\n\n    // It’s not a trail.\n    return nok(code)\n  }\n}\n\n/**\n * Dot in email domain trail.\n *\n * This calls `ok` if this *is* the trail, followed by an end, which means\n * the trail is not part of the link.\n * It calls `nok` if this *is* part of the link.\n *\n * ```markdown\n * > | <EMAIL>.\n *                        ^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeEmailDomainDotTrail(effects, ok, nok) {\n  return start\n\n  /**\n   * Dot.\n   *\n   * ```markdown\n   * > | <EMAIL>.\n   *                    ^   ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // Must be dot.\n    effects.consume(code)\n    return after\n  }\n\n  /**\n   * After dot.\n   *\n   * ```markdown\n   * > | <EMAIL>.\n   *                     ^   ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    // Not a trail if alphanumeric.\n    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlphanumeric)(code) ? nok(code) : ok(code)\n  }\n}\n\n/**\n * See:\n * <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L156>.\n *\n * @type {Previous}\n */\nfunction previousWww(code) {\n  return (\n    code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof ||\n    code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftParenthesis ||\n    code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk ||\n    code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore ||\n    code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket ||\n    code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket ||\n    code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.tilde ||\n    (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEndingOrSpace)(code)\n  )\n}\n\n/**\n * See:\n * <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L214>.\n *\n * @type {Previous}\n */\nfunction previousProtocol(code) {\n  return !(0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlpha)(code)\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Previous}\n */\nfunction previousEmail(code) {\n  // Do not allow a slash “inside” atext.\n  // The reference code is a bit weird, but that’s what it results in.\n  // Source: <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L307>.\n  // Other than slash, every preceding character is allowed.\n  return !(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.slash || gfmAtext(code))\n}\n\n/**\n * @param {Code} code\n * @returns {boolean}\n */\nfunction gfmAtext(code) {\n  return (\n    code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.plusSign ||\n    code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dash ||\n    code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dot ||\n    code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore ||\n    (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlphanumeric)(code)\n  )\n}\n\n/**\n * @param {Array<Event>} events\n * @returns {boolean}\n */\nfunction previousUnbalanced(events) {\n  let index = events.length\n  let result = false\n\n  while (index--) {\n    const token = events[index][1]\n\n    if (\n      (token.type === 'labelLink' || token.type === 'labelImage') &&\n      !token._balanced\n    ) {\n      result = true\n      break\n    }\n\n    // If we’ve seen this token, and it was marked as not having any unbalanced\n    // bracket before it, we can exit.\n    if (token._gfmAutolinkLiteralWalkedInto) {\n      result = false\n      break\n    }\n  }\n\n  if (events.length > 0 && !result) {\n    // Mark the last token as “walked into” w/o finding\n    // anything.\n    events[events.length - 1][1]._gfmAutolinkLiteralWalkedInto = true\n  }\n\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-extension-gfm-autolink-literal@2.1.0/node_modules/micromark-extension-gfm-autolink-literal/dev/lib/syntax.js\n");

/***/ })

};
;