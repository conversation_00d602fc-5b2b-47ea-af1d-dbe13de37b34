{"name": "@we-dev/next", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/openai": "^1.0.11", "@ai-sdk/deepseek": "0.1.10", "@babel/parser": "^7.26.5", "@babel/traverse": "^7.26.5", "@codemirror/autocomplete": "^6.18.4", "@codemirror/commands": "^6.8.0", "@codemirror/lang-css": "^6.3.1", "@codemirror/lang-html": "^6.4.9", "@codemirror/lang-javascript": "^6.2.2", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-markdown": "^6.3.2", "@codemirror/lang-python": "^6.1.6", "@codemirror/language": "^6.10.8", "@codemirror/search": "^6.5.8", "@codemirror/state": "^6.5.1", "@codemirror/view": "^6.36.2", "@imgcook/dsl-helper": "^0.0.1", "@lezer/highlight": "^1.2.1", "@modelcontextprotocol/sdk": "^1.7.0", "@mozilla/readability": "^0.5.0", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.7", "@sketch-hq/sketch-file-format-ts": "^6.5.0", "@stripe/stripe-js": "^6.1.0", "@tanstack/react-query": "^5.66.7", "@types/mdx": "^2.0.13", "@webcontainer/api": "1.5.1-internal.9", "accept-language": "^3.0.20", "adm-zip": "^0.5.16", "ag-psd": "^22.0.2", "ai": "^4.1.46", "antd": "^5.23.0", "antd-style": "^3.7.1", "axios": "^1.7.9", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "css": "^3.0.0", "framer-motion": "^11.18.0", "fumadocs-core": "^14.7.4", "fumadocs-mdx": "^11.3.1", "fumadocs-ui": "^14.7.4", "gsap": "^3.12.5", "highlight.js": "^11.11.1", "i18next": "^24.2.2", "ignore": "^7.0.3", "jose": "^5.9.6", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "lodash": "^4.17.21", "lucide-react": "^0.475.0", "minio": "^8.0.4", "mongoose": "^8.9.3", "next": "14.2.23", "next-intl": "^3.26.3", "npx-scope-finder": "^1.3.0", "posthog-js": "^1.223.3", "prettier": "^3.4.2", "react": "^18.3.1", "react-custom-scroll": "^7.0.0", "react-diff-viewer": "^3.1.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "react-i18next": "^15.4.0", "react-icons": "^5.5.0", "react-markdown": "^9.0.1", "react-resizable-panels": "^2.1.7", "react-toastify": "^11.0.2", "remark-gfm": "4.0.1", "stripe": "^17.7.0", "styled-components": "^6.1.16", "tailwind-merge": "^2.6.0", "tar": "^7.4.3", "undici": "^7.5.0", "uuid": "^11.0.3", "uuidv4": "^6.2.13", "zod": "^3.24.1", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@iconify/json": "^2.2.300", "@iconify/tailwind": "^1.2.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.7", "@types/lodash": "^4.17.13", "@types/node": "^22.10.2", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/stripe": "^8.0.417", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "15.1.2", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5.5.2"}}