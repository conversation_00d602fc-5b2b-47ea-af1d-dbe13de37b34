"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/utils/messageParserNew.ts":
/*!***************************************!*\
  !*** ./src/utils/messageParserNew.ts ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StreamingMessageParser: function() { return /* binding */ StreamingMessageParser; },\n/* harmony export */   debugFileCreation: function() { return /* binding */ debugFileCreation; },\n/* harmony export */   parseMessages: function() { return /* binding */ parseMessages; },\n/* harmony export */   testDirectFileCreation: function() { return /* binding */ testDirectFileCreation; },\n/* harmony export */   testWorkingFormat: function() { return /* binding */ testWorkingFormat; }\n/* harmony export */ });\n/* harmony import */ var _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/stores/fileStore */ \"(app-pages-browser)/./src/stores/fileStore.ts\");\n/* __next_internal_client_entry_do_not_use__ StreamingMessageParser,parseMessages,testWorkingFormat,debugFileCreation,testDirectFileCreation auto */ \nclass StreamingMessageParser {\n    parse(messageId, input) {\n        console.log(\"\\uD83D\\uDD0D Parsing message \".concat(messageId, \" with content length: \").concat(input.length));\n        console.log(\"\\uD83D\\uDCC4 Content preview:\", input.substring(0, 200) + \"...\");\n        let state = this.messages.get(messageId);\n        if (!state) {\n            state = {\n                position: 0,\n                insideAction: false,\n                insideArtifact: false,\n                currentAction: {\n                    content: \"\"\n                },\n                actionId: 0,\n                hasInstallExecuted: false\n            };\n            this.messages.set(messageId, state);\n        }\n        let output = \"\";\n        const regex = {\n            artifactOpen: /<boltArtifact[^>]*>/g,\n            artifactClose: /<\\/boltArtifact>/g,\n            actionOpen: /<boltAction[^>]*>/g,\n            actionClose: /<\\/boltAction>/g\n        };\n        const allActionData = {};\n        while(state.position < input.length){\n            if (state.insideArtifact) {\n                if (state.insideAction) {\n                    // 查找动作结束标签\n                    regex.actionClose.lastIndex = state.position;\n                    const actionCloseMatch = regex.actionClose.exec(input);\n                    if (actionCloseMatch) {\n                        const content = input.slice(state.position, actionCloseMatch.index);\n                        // 处理 file 和 shell 类型的 action\n                        if (\"type\" in state.currentAction) {\n                            const actionData = {\n                                artifactId: state.currentArtifact.id,\n                                messageId,\n                                actionId: String(state.actionId - 1),\n                                action: {\n                                    ...state.currentAction,\n                                    content\n                                }\n                            };\n                            console.log(\"\\uD83D\\uDCE6 Found complete action:\", actionData);\n                            // 根据 action 类型调用不同的回调\n                            if (state.currentAction.type === \"file\") {\n                                var // Call onActionStream for file creation\n                                _this_options_callbacks_onActionStream, _this_options_callbacks;\n                                allActionData[state.currentAction.filePath] = actionData;\n                                (_this_options_callbacks = this.options.callbacks) === null || _this_options_callbacks === void 0 ? void 0 : (_this_options_callbacks_onActionStream = _this_options_callbacks.onActionStream) === null || _this_options_callbacks_onActionStream === void 0 ? void 0 : _this_options_callbacks_onActionStream.call(_this_options_callbacks, actionData);\n                            } else if (state.currentAction.type === \"shell\" || state.currentAction.type === \"start\") {\n                                var // shell 类型只在关闭时处理\n                                _this_options_callbacks_onActionClose, _this_options_callbacks1;\n                                (_this_options_callbacks1 = this.options.callbacks) === null || _this_options_callbacks1 === void 0 ? void 0 : (_this_options_callbacks_onActionClose = _this_options_callbacks1.onActionClose) === null || _this_options_callbacks_onActionClose === void 0 ? void 0 : _this_options_callbacks_onActionClose.call(_this_options_callbacks1, actionData);\n                            }\n                        }\n                        state.position = actionCloseMatch.index + actionCloseMatch[0].length;\n                        state.insideAction = false;\n                    } else {\n                        // 只对 file 类型进行流式处理\n                        const remainingContent = input.slice(state.position);\n                        if (\"type\" in state.currentAction && state.currentAction.type === \"file\" && !allActionData[state.currentAction.filePath]) {\n                            var // Call onActionStream for streaming file content\n                            _this_options_callbacks_onActionStream1, _this_options_callbacks2;\n                            allActionData[state.currentAction.filePath] = {\n                                artifactId: state.currentArtifact.id,\n                                messageId,\n                                actionId: String(state.actionId - 1),\n                                action: {\n                                    ...state.currentAction,\n                                    content: remainingContent,\n                                    filePath: state.currentAction.filePath\n                                }\n                            };\n                            console.log(\"\\uD83D\\uDCE6 Found streaming action:\", allActionData[state.currentAction.filePath]);\n                            (_this_options_callbacks2 = this.options.callbacks) === null || _this_options_callbacks2 === void 0 ? void 0 : (_this_options_callbacks_onActionStream1 = _this_options_callbacks2.onActionStream) === null || _this_options_callbacks_onActionStream1 === void 0 ? void 0 : _this_options_callbacks_onActionStream1.call(_this_options_callbacks2, allActionData[state.currentAction.filePath]);\n                        }\n                        break;\n                    }\n                } else {\n                    // 查找下一个动作开始标签或者 artifact 结束标签\n                    const nextActionMatch = regex.actionOpen.exec(input.slice(state.position));\n                    const artifactCloseMatch = regex.artifactClose.exec(input.slice(state.position));\n                    if (nextActionMatch && (!artifactCloseMatch || nextActionMatch.index < artifactCloseMatch.index)) {\n                        var _this_options_callbacks_onActionOpen, _this_options_callbacks3;\n                        const actionTag = nextActionMatch[0];\n                        state.currentAction = this.parseActionTag(actionTag);\n                        state.insideAction = true;\n                        state.position += nextActionMatch.index + nextActionMatch[0].length;\n                        console.log(\"\\uD83D\\uDE80 Found action tag:\", actionTag, state.currentAction);\n                        (_this_options_callbacks3 = this.options.callbacks) === null || _this_options_callbacks3 === void 0 ? void 0 : (_this_options_callbacks_onActionOpen = _this_options_callbacks3.onActionOpen) === null || _this_options_callbacks_onActionOpen === void 0 ? void 0 : _this_options_callbacks_onActionOpen.call(_this_options_callbacks3, {\n                            artifactId: state.currentArtifact.id,\n                            messageId,\n                            actionId: String(state.actionId++),\n                            action: state.currentAction\n                        });\n                    } else if (artifactCloseMatch) {\n                        var _this_options_callbacks_onArtifactClose, _this_options_callbacks4;\n                        state.position += artifactCloseMatch.index + artifactCloseMatch[0].length;\n                        state.insideArtifact = false;\n                        (_this_options_callbacks4 = this.options.callbacks) === null || _this_options_callbacks4 === void 0 ? void 0 : (_this_options_callbacks_onArtifactClose = _this_options_callbacks4.onArtifactClose) === null || _this_options_callbacks_onArtifactClose === void 0 ? void 0 : _this_options_callbacks_onArtifactClose.call(_this_options_callbacks4, {\n                            messageId,\n                            ...state.currentArtifact\n                        });\n                    } else {\n                        break;\n                    }\n                }\n            } else {\n                // 查找 artifact 开始标签\n                const artifactMatch = regex.artifactOpen.exec(input.slice(state.position));\n                if (artifactMatch) {\n                    output += input.slice(state.position, state.position + artifactMatch.index);\n                    const artifactTag = artifactMatch[0];\n                    const artifactTitle = this.extractAttribute(artifactTag, \"title\");\n                    const artifactId = this.extractAttribute(artifactTag, \"id\");\n                    state.currentArtifact = {\n                        id: artifactId || \"default\",\n                        title: artifactTitle || \"Untitled\"\n                    };\n                    console.log(\"\\uD83C\\uDFAF Found artifact:\", state.currentArtifact);\n                    state.insideArtifact = true;\n                    state.position += artifactMatch.index + artifactMatch[0].length;\n                } else {\n                    output += input.slice(state.position);\n                    break;\n                }\n            }\n        }\n        return output;\n    }\n    parseActionTag(tag) {\n        const typeMatch = tag.match(/type=\"([^\"]+)\"/);\n        const filePathMatch = tag.match(/filePath=\"([^\"]+)\"/);\n        return {\n            type: typeMatch === null || typeMatch === void 0 ? void 0 : typeMatch[1],\n            filePath: filePathMatch === null || filePathMatch === void 0 ? void 0 : filePathMatch[1],\n            content: \"\"\n        };\n    }\n    extractAttribute(tag, attribute) {\n        const match = tag.match(new RegExp(\"\".concat(attribute, '=\"([^\"]+)\"')));\n        return match ? match[1] : null;\n    }\n    constructor(options = {}){\n        this.options = options;\n        this.messages = new Map();\n        this.isUseStartCommand = false;\n    }\n}\n// Create file with content function (equivalent to we-dev-client's createFileWithContent)\nconst createFileWithContent = async (filePath, content, syncFileClose)=>{\n    const { addFile } = _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__.useFileStore.getState();\n    console.log(\"\\uD83D\\uDCC1 Creating file: \".concat(filePath));\n    console.log(\"\\uD83D\\uDCC4 Content preview: \".concat(content.substring(0, 100), \"...\"));\n    await addFile(filePath, content, syncFileClose);\n    console.log(\"✅ File created successfully: \".concat(filePath));\n    return filePath;\n};\n// Create a global message parser instance with the working callback\nconst messageParser = new StreamingMessageParser({\n    callbacks: {\n        onActionStream: async (data)=>{\n            console.log(\"\\uD83D\\uDD25 onActionStream called with:\", data);\n            const action = data.action;\n            if (action.type === \"file\" && action.filePath && action.content) {\n                await createFileWithContent(action.filePath, action.content, true);\n                console.log(\"✅ File created via onActionStream: \".concat(action.filePath));\n            }\n        }\n    }\n});\nconst parseMessages = async (messages)=>{\n    console.log(\"\\uD83D\\uDE80 parseMessages called with \".concat(messages.length, \" messages\"));\n    for (const message of messages){\n        if (message.role === \"assistant\") {\n            console.log(\"\\uD83E\\uDD16 Parsing assistant message: \".concat(message.id));\n            console.log(\"\\uD83D\\uDCDD Message content:\", message.content);\n            messageParser.parse(message.id, message.content);\n        }\n    }\n};\n// Test functions\nconst testWorkingFormat = ()=>{\n    const testContent = 'I\\'ll create a simple HTML file for you:\\n\\n<boltArtifact id=\"simple-html\" title=\"Simple HTML File\">\\n<boltAction type=\"file\" filePath=\"index.html\"><!DOCTYPE html>\\n<html lang=\"en\">\\n<head>\\n    <meta charset=\"UTF-8\">\\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\\n    <title>Test Page</title>\\n</head>\\n<body>\\n    <h1>Hello from AI!</h1>\\n    <p>This file was created by the AI response parser.</p>\\n</body>\\n</html></boltAction>\\n</boltArtifact>\\n\\nThe HTML file has been created successfully.';\n    console.log(\"\\uD83E\\uDDEA Testing working format...\");\n    messageParser.parse(\"test-working\", testContent);\n};\nconst debugFileCreation = ()=>{\n    console.log(\"\\uD83D\\uDD27 Testing file creation directly...\");\n    const { addFile } = _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__.useFileStore.getState();\n    addFile(\"debug-test.html\", \"<h1>Debug Test</h1>\").then(()=>{\n        console.log(\"✅ Direct file creation successful\");\n        const files = _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__.useFileStore.getState().files;\n        console.log(\"\\uD83D\\uDCC1 Current files:\", Object.keys(files));\n    }).catch((error)=>{\n        console.error(\"❌ Direct file creation failed:\", error);\n    });\n};\n// Test function to create a file directly via file store\nconst testDirectFileCreation = async ()=>{\n    console.log(\"\\uD83E\\uDDEA Testing direct file creation...\");\n    const { addFile } = _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__.useFileStore.getState();\n    try {\n        await addFile(\"test-direct.html\", \"<!DOCTYPE html>\\n<html>\\n<head><title>Direct Test</title></head>\\n<body><h1>This file was created directly!</h1></body>\\n</html>\");\n        console.log(\"✅ Direct file creation successful\");\n        const files = _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__.useFileStore.getState().files;\n        console.log(\"\\uD83D\\uDCC1 Current files:\", Object.keys(files));\n        console.log(\"\\uD83D\\uDCC4 File content:\", files[\"test-direct.html\"]);\n    } catch (error) {\n        console.error(\"❌ Direct file creation failed:\", error);\n    }\n};\n// Make test functions available globally for debugging\nif (true) {\n    window.testWorkingFormat = testWorkingFormat;\n    window.debugFileCreation = debugFileCreation;\n    window.testDirectFileCreation = testDirectFileCreation;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/messageParserNew.ts\n"));

/***/ })

});