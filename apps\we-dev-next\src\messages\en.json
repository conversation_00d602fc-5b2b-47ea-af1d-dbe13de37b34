{"header": {"pricing": "Pricing", "aboutUs": "About Us", "webVersion": "Web Version", "account": "Account", "logout": "Logout", "login": "<PERSON><PERSON>"}, "hero": {"language": "en", "title": "Generate any code at once and design", "slogan": "Create AI-powered applications including Vue, React, Next.js, Python, Java, and WeChat Mini Programs.", "download": {"macIntel": "Download for Mac (Intel)", "macArm": "Download for Mac (Apple Silicon)", "windows": "Download for Windows"}}, "inputSection": {"title": "Start with a simple prompt", "placeholder": "Start using we0 to create a program", "button": "Generate", "importImage": "Import Image", "sketchFile": "Sketch or Figma File", "generateDesign": "Generate Design", "send": "Send", "maxImagesAlert": "You can only upload up to 5 images", "uploadSuccess": "Upload successful:", "uploadFailed": "Upload failed:", "uploadError": "Upload error:", "inputRequired": "Please enter text or upload an image", "uploadedImage": "Uploaded image", "removeImage": "Remove image", "dropImageHere": "Drop image here", "uploading": "Uploading..."}, "projectsSection": {"title": "Showcase Projects", "subtitle": "See what others have built with We0", "viewMore": "View More", "saas": {"description": "Backend Visualization Page"}, "business": {"description": "Backend Homepage"}, "corporate": {"description": "<PERSON> <PERSON><PERSON>"}, "product": {"description": "Mobile Login Page"}}, "pricingPlans": {"paymentSuccess": "Payment Success!", "upgradePlan": "Upgrade Plan", "title": "Choose Your Plan", "subtitle": "We offer flexible pricing options for different development needs", "loginRequired": "Please login first", "currentPlan": "Current Plan", "downgradeToFree": "Downgrade to Free", "upgradeToPro": "Upgrade to Pro", "upgradeToProMax": "Upgrade to Pro Max", "upgradeToEnterprise": "Upgrade to Enterprise", "getStarted": "Get Started", "free": {"name": "Free", "price": "Free", "description": "Perfect for individual developers and small projects", "features": ["AI Code Generation", "Basic Project Templates", "Community Support", "Basic Development Tools", "15 Requests per Month"], "button": "Get Started"}, "pro": {"name": "Pro", "price": "$ 13.7", "period": "/month", "description": "Ideal for advanced developers and teams", "features": ["All Free Features", "Priority AI Response", "Backend API Generation", "Sketch to Code Conversion", "200 Requests per Month"], "button": "Get Started", "popular": "Most Popular"}, "enterprise": {"name": "Enterprise", "price": "$ 27.5", "period": "/month", "description": "For professional development needs", "features": ["All Pro Features", "500 Requests per Month", "Dedicated Customer Support", "Custom Model Training", "Advanced API Integration"], "button": "Get Started"}}, "userPage": {"title": "Settings", "subtitle": "You can manage your account and billing here", "upgradeToPro": "Upgrade to Pro", "upgradeToProMax": "Upgrade to Pro Max", "getMoreToken": "Get More Token", "basicInfo": {"title": "Basic Information", "username": "Username", "email": "Email", "setEmail": "Click here to set your email"}, "emailDialog": {"title": "<PERSON> Email", "description": "You need to set up your email address before you can use paid services", "confirm": "Confirm", "invalidEmail": "Please enter a valid email address", "sendSuccess": "<PERSON>ail sent successfully", "emailUsed": "This email is already used by another user"}, "account": {"title": "Account", "advanced": "Advanced"}, "usage": {"title": "Usage", "requestsLeft": "Requests Left", "unlimited": "Unlimited", "tier": "Current Tier", "free": "Free", "pro": "Pro", "enterprise": "Enterprise", "nextResetDate": "Next reset date", "totalQuota": "Total Quota", "quotaDescription": "You've used {used} requests out of your {total} total requests quota.", "refillQuota": "Refill Quota", "refillQuotaDescription": "You have a remaining refill quota of {remaining} available."}}, "login": {"signIn": "Sign in", "signingIn": "Signing in...", "or": "or", "createAccount": "Create an account", "emailAddress": "Email Address", "emailPlaceholder": "Your email", "password": "Password", "passwordPlaceholder": "Password", "signInWithGithub": "Sign in with GitHub", "signInWithWechat": "Sign in with WeChat", "loginFailed": "<PERSON><PERSON> failed", "wechatLoginFailed": "We<PERSON><PERSON> login failed", "githubLoginFailed": "GitHub login failed", "passwordError": "Password is incorrect"}, "payment": {"updateToPro": "Upgrade to Pro", "updateToProMax": "Upgrade to Pro Max", "getMoreToken": "Get More Tokens", "paymentSuccess": "Payment Successful!", "upgradePlan": "Upgrade Plan", "selectPaymentMethod": "Select Payment Method", "confirmPayment": "Confirm Payment", "paymentCompleted": "Payment Completed", "orderError": "Order error occurred, please contact support", "orderCancelled": "Order cancelled", "loginRequired": "Please login first", "loginFirst": "Please login before making a purchase", "goToLogin": "Go to Login", "stripePayment": "Stripe Payment", "stripePaymentDesc": "Redirecting to Stripe payment page", "processingPayment": "Processing payment...", "paymentSuccessDesc": "Payment successful, updating your account information", "paymentFailed": "Payment Failed", "paymentFailedDesc": "An error occurred during payment, please try again", "cancelPayment": "Cancel Payment", "stripeError": "Stripe payment error", "paymentError": "Payment process error", "features": {"title": "{name} - {price}/month", "pro": "Pro", "proMax": "Pro Max"}, "wechat": {"title": "WeChat Pay", "subtitle": "Please scan the QR code with WeChat to complete payment", "amount": "Payment Amount", "validityPeriod": "QR Code Valid For: {time}", "expired": "QR Code Expired", "getNew": "Get New Code", "cancel": "Cancel Payment", "refresh": "Refresh QR Code", "tips": {"title": "Tips:", "scanWithWechat": "Please scan with WeChat Pay", "refreshIfNeeded": "Click refresh if you encounter any issues", "autoRedirect": "Will redirect automatically after payment"}, "orderError": "Failed to get order information, please contact customer service"}}, "downloadButton": {"comingSoon": "Coming soon, please wait... you can use web version now"}, "register": {"createAccount": "Create Account", "alreadyHaveAccount": "Already have an account?", "signInNow": "Sign in now", "emailAddress": "Email Address", "password": "Password", "confirmPassword": "Confirm Password", "register": "Register", "registering": "Registering...", "or": "or", "registerWithGithub": "Register with GitHub", "registerWithWechat": "Register with WeChat", "passwordsNotMatch": "Passwords do not match", "registrationFailed": "Registration failed", "wechatRegisterFailed": "WeChat registration failed", "githubRegisterFailed": "GitHub registration failed", "emailAlreadyExists": "User already exists"}, "forgotPassword": {"title": "Reset Password", "email": "Email Address", "emailPlaceholder": "Enter your email", "oldPassword": "Old Password", "oldPasswordPlaceholder": "Enter your old password", "newPassword": "New Password", "newPasswordPlaceholder": "Enter your new password", "submit": "Update Password", "submitting": "Updating...", "backToLogin": "Back to Login", "success": "Password updated successfully", "error": {"missingFields": "Please fill in all fields", "userNotFound": "User not found", "invalidOldPassword": "Invalid old password", "updateFailed": "Failed to update password"}}, "featureCards": {"title": "We0's Code generation capabilities", "screenshotToCode": {"title": "Screenshot to Code", "description": "Upload any design screenshot and get pixel-perfect code."}, "urlToCode": {"title": "URL to Code", "description": "Clone any website by simply pasting its URL."}, "figmaToCode": {"title": "Figma to Code", "description": "Direct integration with Figma/Sketch - convert your designs with one click."}, "smartComponentDetection": {"title": "Smart Component Detection", "description": "AI automatically identifies and maps UI components."}, "frameworkChoice": {"title": "Framework Choice", "description": "Generate code for Vue, React, Next.js, Python, Java, and WeChat Mini Programs"}, "responsiveByDefault": {"title": "Responsive by <PERSON><PERSON><PERSON>", "description": "All generated code is mobile-friendly and responsive."}}}