// Model configuration file
// Configure models based on actual scenarios

interface ModelConfig {
    modelName: string;
    modelKey: string;
    useImage: boolean;
    description?: string;
    iconUrl?: string;
    provider?: string; // Model provider
    apiKey?: string;
    apiUrl?: string;
    functionCall: boolean;
}

export const modelConfig: ModelConfig[] = [
    {
        modelName: 'Claude 3.5 Sonnet',
        modelKey: 'anthropic/claude-3.5-sonnet',
        useImage: true,
        provider: 'anthropic',
        description: 'Claude 3.5 Sonnet model via OpenRouter',
        functionCall: true,
    },
    {
        modelName: 'GPT-4o Mini',
        modelKey: 'openai/gpt-4o-mini',
        useImage: true,
        provider: 'openai',
        description: 'GPT-4 Optimized Mini model via OpenRouter',
        functionCall: true,
    },
    {
        modelName: 'DeepSeek R1',
        modelKey: 'deepseek/deepseek-r1',
        useImage: false,
        provider: 'deepseek',
        description: 'DeepSeek R1 model with reasoning capabilities via OpenRouter',
        functionCall: false,
    },
    {
        modelName: 'DeepSeek V3',
        modelKey: 'deepseek/deepseek-chat',
        useImage: false,
        provider: 'deepseek',
        description: 'DeepSeek V3 model via OpenRouter',
        functionCall: true,
    }
]
