'use client';

import { create } from 'zustand';

interface EditorState {
  isDirty: Record<string, boolean>;
  setDirty: (path: string, dirty: boolean) => void;
  clearDirty: (path: string) => void;
  clearAllDirty: () => void;
}

export const useEditorStore = create<EditorState>((set) => ({
  isDirty: {},

  setDirty: (path, dirty) =>
    set((state) => ({
      isDirty: { ...state.isDirty, [path]: dirty },
    })),

  clearDirty: (path) =>
    set((state) => {
      const newDirty = { ...state.isDirty };
      delete newDirty[path];
      return { isDirty: newDirty };
    }),

  clearAllDirty: () => set({ isDirty: {} }),
}));
