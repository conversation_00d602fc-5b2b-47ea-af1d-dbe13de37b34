"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jszip@3.10.1";
exports.ids = ["vendor-chunks/jszip@3.10.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/base64.js":
/*!**************************************************************************!*\
  !*** ./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/base64.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\nvar support = __webpack_require__(/*! ./support */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/support.js\");\n// private property\nvar _keyStr = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\";\n\n\n// public method for encoding\nexports.encode = function(input) {\n    var output = [];\n    var chr1, chr2, chr3, enc1, enc2, enc3, enc4;\n    var i = 0, len = input.length, remainingBytes = len;\n\n    var isArray = utils.getTypeOf(input) !== \"string\";\n    while (i < input.length) {\n        remainingBytes = len - i;\n\n        if (!isArray) {\n            chr1 = input.charCodeAt(i++);\n            chr2 = i < len ? input.charCodeAt(i++) : 0;\n            chr3 = i < len ? input.charCodeAt(i++) : 0;\n        } else {\n            chr1 = input[i++];\n            chr2 = i < len ? input[i++] : 0;\n            chr3 = i < len ? input[i++] : 0;\n        }\n\n        enc1 = chr1 >> 2;\n        enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);\n        enc3 = remainingBytes > 1 ? (((chr2 & 15) << 2) | (chr3 >> 6)) : 64;\n        enc4 = remainingBytes > 2 ? (chr3 & 63) : 64;\n\n        output.push(_keyStr.charAt(enc1) + _keyStr.charAt(enc2) + _keyStr.charAt(enc3) + _keyStr.charAt(enc4));\n\n    }\n\n    return output.join(\"\");\n};\n\n// public method for decoding\nexports.decode = function(input) {\n    var chr1, chr2, chr3;\n    var enc1, enc2, enc3, enc4;\n    var i = 0, resultIndex = 0;\n\n    var dataUrlPrefix = \"data:\";\n\n    if (input.substr(0, dataUrlPrefix.length) === dataUrlPrefix) {\n        // This is a common error: people give a data url\n        // (data:image/png;base64,iVBOR...) with a {base64: true} and\n        // wonders why things don't work.\n        // We can detect that the string input looks like a data url but we\n        // *can't* be sure it is one: removing everything up to the comma would\n        // be too dangerous.\n        throw new Error(\"Invalid base64 input, it looks like a data url.\");\n    }\n\n    input = input.replace(/[^A-Za-z0-9+/=]/g, \"\");\n\n    var totalLength = input.length * 3 / 4;\n    if(input.charAt(input.length - 1) === _keyStr.charAt(64)) {\n        totalLength--;\n    }\n    if(input.charAt(input.length - 2) === _keyStr.charAt(64)) {\n        totalLength--;\n    }\n    if (totalLength % 1 !== 0) {\n        // totalLength is not an integer, the length does not match a valid\n        // base64 content. That can happen if:\n        // - the input is not a base64 content\n        // - the input is *almost* a base64 content, with a extra chars at the\n        //   beginning or at the end\n        // - the input uses a base64 variant (base64url for example)\n        throw new Error(\"Invalid base64 input, bad content length.\");\n    }\n    var output;\n    if (support.uint8array) {\n        output = new Uint8Array(totalLength|0);\n    } else {\n        output = new Array(totalLength|0);\n    }\n\n    while (i < input.length) {\n\n        enc1 = _keyStr.indexOf(input.charAt(i++));\n        enc2 = _keyStr.indexOf(input.charAt(i++));\n        enc3 = _keyStr.indexOf(input.charAt(i++));\n        enc4 = _keyStr.indexOf(input.charAt(i++));\n\n        chr1 = (enc1 << 2) | (enc2 >> 4);\n        chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);\n        chr3 = ((enc3 & 3) << 6) | enc4;\n\n        output[resultIndex++] = chr1;\n\n        if (enc3 !== 64) {\n            output[resultIndex++] = chr2;\n        }\n        if (enc4 !== 64) {\n            output[resultIndex++] = chr3;\n        }\n\n    }\n\n    return output;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vanN6aXBAMy4xMC4xL25vZGVfbW9kdWxlcy9qc3ppcC9saWIvYmFzZTY0LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsWUFBWSxtQkFBTyxDQUFDLHdGQUFTO0FBQzdCLGNBQWMsbUJBQU8sQ0FBQyw0RkFBVztBQUNqQztBQUNBOzs7QUFHQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQSwyQkFBMkIseUJBQXlCLGNBQWM7QUFDbEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHdlLWRldi9uZXh0Ly4vbm9kZV9tb2R1bGVzLy5wbnBtL2pzemlwQDMuMTAuMS9ub2RlX21vZHVsZXMvanN6aXAvbGliL2Jhc2U2NC5qcz83N2QzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xudmFyIHV0aWxzID0gcmVxdWlyZShcIi4vdXRpbHNcIik7XG52YXIgc3VwcG9ydCA9IHJlcXVpcmUoXCIuL3N1cHBvcnRcIik7XG4vLyBwcml2YXRlIHByb3BlcnR5XG52YXIgX2tleVN0ciA9IFwiQUJDREVGR0hJSktMTU5PUFFSU1RVVldYWVphYmNkZWZnaGlqa2xtbm9wcXJzdHV2d3h5ejAxMjM0NTY3ODkrLz1cIjtcblxuXG4vLyBwdWJsaWMgbWV0aG9kIGZvciBlbmNvZGluZ1xuZXhwb3J0cy5lbmNvZGUgPSBmdW5jdGlvbihpbnB1dCkge1xuICAgIHZhciBvdXRwdXQgPSBbXTtcbiAgICB2YXIgY2hyMSwgY2hyMiwgY2hyMywgZW5jMSwgZW5jMiwgZW5jMywgZW5jNDtcbiAgICB2YXIgaSA9IDAsIGxlbiA9IGlucHV0Lmxlbmd0aCwgcmVtYWluaW5nQnl0ZXMgPSBsZW47XG5cbiAgICB2YXIgaXNBcnJheSA9IHV0aWxzLmdldFR5cGVPZihpbnB1dCkgIT09IFwic3RyaW5nXCI7XG4gICAgd2hpbGUgKGkgPCBpbnB1dC5sZW5ndGgpIHtcbiAgICAgICAgcmVtYWluaW5nQnl0ZXMgPSBsZW4gLSBpO1xuXG4gICAgICAgIGlmICghaXNBcnJheSkge1xuICAgICAgICAgICAgY2hyMSA9IGlucHV0LmNoYXJDb2RlQXQoaSsrKTtcbiAgICAgICAgICAgIGNocjIgPSBpIDwgbGVuID8gaW5wdXQuY2hhckNvZGVBdChpKyspIDogMDtcbiAgICAgICAgICAgIGNocjMgPSBpIDwgbGVuID8gaW5wdXQuY2hhckNvZGVBdChpKyspIDogMDtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGNocjEgPSBpbnB1dFtpKytdO1xuICAgICAgICAgICAgY2hyMiA9IGkgPCBsZW4gPyBpbnB1dFtpKytdIDogMDtcbiAgICAgICAgICAgIGNocjMgPSBpIDwgbGVuID8gaW5wdXRbaSsrXSA6IDA7XG4gICAgICAgIH1cblxuICAgICAgICBlbmMxID0gY2hyMSA+PiAyO1xuICAgICAgICBlbmMyID0gKChjaHIxICYgMykgPDwgNCkgfCAoY2hyMiA+PiA0KTtcbiAgICAgICAgZW5jMyA9IHJlbWFpbmluZ0J5dGVzID4gMSA/ICgoKGNocjIgJiAxNSkgPDwgMikgfCAoY2hyMyA+PiA2KSkgOiA2NDtcbiAgICAgICAgZW5jNCA9IHJlbWFpbmluZ0J5dGVzID4gMiA/IChjaHIzICYgNjMpIDogNjQ7XG5cbiAgICAgICAgb3V0cHV0LnB1c2goX2tleVN0ci5jaGFyQXQoZW5jMSkgKyBfa2V5U3RyLmNoYXJBdChlbmMyKSArIF9rZXlTdHIuY2hhckF0KGVuYzMpICsgX2tleVN0ci5jaGFyQXQoZW5jNCkpO1xuXG4gICAgfVxuXG4gICAgcmV0dXJuIG91dHB1dC5qb2luKFwiXCIpO1xufTtcblxuLy8gcHVibGljIG1ldGhvZCBmb3IgZGVjb2RpbmdcbmV4cG9ydHMuZGVjb2RlID0gZnVuY3Rpb24oaW5wdXQpIHtcbiAgICB2YXIgY2hyMSwgY2hyMiwgY2hyMztcbiAgICB2YXIgZW5jMSwgZW5jMiwgZW5jMywgZW5jNDtcbiAgICB2YXIgaSA9IDAsIHJlc3VsdEluZGV4ID0gMDtcblxuICAgIHZhciBkYXRhVXJsUHJlZml4ID0gXCJkYXRhOlwiO1xuXG4gICAgaWYgKGlucHV0LnN1YnN0cigwLCBkYXRhVXJsUHJlZml4Lmxlbmd0aCkgPT09IGRhdGFVcmxQcmVmaXgpIHtcbiAgICAgICAgLy8gVGhpcyBpcyBhIGNvbW1vbiBlcnJvcjogcGVvcGxlIGdpdmUgYSBkYXRhIHVybFxuICAgICAgICAvLyAoZGF0YTppbWFnZS9wbmc7YmFzZTY0LGlWQk9SLi4uKSB3aXRoIGEge2Jhc2U2NDogdHJ1ZX0gYW5kXG4gICAgICAgIC8vIHdvbmRlcnMgd2h5IHRoaW5ncyBkb24ndCB3b3JrLlxuICAgICAgICAvLyBXZSBjYW4gZGV0ZWN0IHRoYXQgdGhlIHN0cmluZyBpbnB1dCBsb29rcyBsaWtlIGEgZGF0YSB1cmwgYnV0IHdlXG4gICAgICAgIC8vICpjYW4ndCogYmUgc3VyZSBpdCBpcyBvbmU6IHJlbW92aW5nIGV2ZXJ5dGhpbmcgdXAgdG8gdGhlIGNvbW1hIHdvdWxkXG4gICAgICAgIC8vIGJlIHRvbyBkYW5nZXJvdXMuXG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIkludmFsaWQgYmFzZTY0IGlucHV0LCBpdCBsb29rcyBsaWtlIGEgZGF0YSB1cmwuXCIpO1xuICAgIH1cblxuICAgIGlucHV0ID0gaW5wdXQucmVwbGFjZSgvW15BLVphLXowLTkrLz1dL2csIFwiXCIpO1xuXG4gICAgdmFyIHRvdGFsTGVuZ3RoID0gaW5wdXQubGVuZ3RoICogMyAvIDQ7XG4gICAgaWYoaW5wdXQuY2hhckF0KGlucHV0Lmxlbmd0aCAtIDEpID09PSBfa2V5U3RyLmNoYXJBdCg2NCkpIHtcbiAgICAgICAgdG90YWxMZW5ndGgtLTtcbiAgICB9XG4gICAgaWYoaW5wdXQuY2hhckF0KGlucHV0Lmxlbmd0aCAtIDIpID09PSBfa2V5U3RyLmNoYXJBdCg2NCkpIHtcbiAgICAgICAgdG90YWxMZW5ndGgtLTtcbiAgICB9XG4gICAgaWYgKHRvdGFsTGVuZ3RoICUgMSAhPT0gMCkge1xuICAgICAgICAvLyB0b3RhbExlbmd0aCBpcyBub3QgYW4gaW50ZWdlciwgdGhlIGxlbmd0aCBkb2VzIG5vdCBtYXRjaCBhIHZhbGlkXG4gICAgICAgIC8vIGJhc2U2NCBjb250ZW50LiBUaGF0IGNhbiBoYXBwZW4gaWY6XG4gICAgICAgIC8vIC0gdGhlIGlucHV0IGlzIG5vdCBhIGJhc2U2NCBjb250ZW50XG4gICAgICAgIC8vIC0gdGhlIGlucHV0IGlzICphbG1vc3QqIGEgYmFzZTY0IGNvbnRlbnQsIHdpdGggYSBleHRyYSBjaGFycyBhdCB0aGVcbiAgICAgICAgLy8gICBiZWdpbm5pbmcgb3IgYXQgdGhlIGVuZFxuICAgICAgICAvLyAtIHRoZSBpbnB1dCB1c2VzIGEgYmFzZTY0IHZhcmlhbnQgKGJhc2U2NHVybCBmb3IgZXhhbXBsZSlcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiSW52YWxpZCBiYXNlNjQgaW5wdXQsIGJhZCBjb250ZW50IGxlbmd0aC5cIik7XG4gICAgfVxuICAgIHZhciBvdXRwdXQ7XG4gICAgaWYgKHN1cHBvcnQudWludDhhcnJheSkge1xuICAgICAgICBvdXRwdXQgPSBuZXcgVWludDhBcnJheSh0b3RhbExlbmd0aHwwKTtcbiAgICB9IGVsc2Uge1xuICAgICAgICBvdXRwdXQgPSBuZXcgQXJyYXkodG90YWxMZW5ndGh8MCk7XG4gICAgfVxuXG4gICAgd2hpbGUgKGkgPCBpbnB1dC5sZW5ndGgpIHtcblxuICAgICAgICBlbmMxID0gX2tleVN0ci5pbmRleE9mKGlucHV0LmNoYXJBdChpKyspKTtcbiAgICAgICAgZW5jMiA9IF9rZXlTdHIuaW5kZXhPZihpbnB1dC5jaGFyQXQoaSsrKSk7XG4gICAgICAgIGVuYzMgPSBfa2V5U3RyLmluZGV4T2YoaW5wdXQuY2hhckF0KGkrKykpO1xuICAgICAgICBlbmM0ID0gX2tleVN0ci5pbmRleE9mKGlucHV0LmNoYXJBdChpKyspKTtcblxuICAgICAgICBjaHIxID0gKGVuYzEgPDwgMikgfCAoZW5jMiA+PiA0KTtcbiAgICAgICAgY2hyMiA9ICgoZW5jMiAmIDE1KSA8PCA0KSB8IChlbmMzID4+IDIpO1xuICAgICAgICBjaHIzID0gKChlbmMzICYgMykgPDwgNikgfCBlbmM0O1xuXG4gICAgICAgIG91dHB1dFtyZXN1bHRJbmRleCsrXSA9IGNocjE7XG5cbiAgICAgICAgaWYgKGVuYzMgIT09IDY0KSB7XG4gICAgICAgICAgICBvdXRwdXRbcmVzdWx0SW5kZXgrK10gPSBjaHIyO1xuICAgICAgICB9XG4gICAgICAgIGlmIChlbmM0ICE9PSA2NCkge1xuICAgICAgICAgICAgb3V0cHV0W3Jlc3VsdEluZGV4KytdID0gY2hyMztcbiAgICAgICAgfVxuXG4gICAgfVxuXG4gICAgcmV0dXJuIG91dHB1dDtcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/base64.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/compressedObject.js":
/*!************************************************************************************!*\
  !*** ./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/compressedObject.js ***!
  \************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar external = __webpack_require__(/*! ./external */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/external.js\");\nvar DataWorker = __webpack_require__(/*! ./stream/DataWorker */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/DataWorker.js\");\nvar Crc32Probe = __webpack_require__(/*! ./stream/Crc32Probe */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/Crc32Probe.js\");\nvar DataLengthProbe = __webpack_require__(/*! ./stream/DataLengthProbe */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/DataLengthProbe.js\");\n\n/**\n * Represent a compressed object, with everything needed to decompress it.\n * @constructor\n * @param {number} compressedSize the size of the data compressed.\n * @param {number} uncompressedSize the size of the data after decompression.\n * @param {number} crc32 the crc32 of the decompressed file.\n * @param {object} compression the type of compression, see lib/compressions.js.\n * @param {String|ArrayBuffer|Uint8Array|Buffer} data the compressed data.\n */\nfunction CompressedObject(compressedSize, uncompressedSize, crc32, compression, data) {\n    this.compressedSize = compressedSize;\n    this.uncompressedSize = uncompressedSize;\n    this.crc32 = crc32;\n    this.compression = compression;\n    this.compressedContent = data;\n}\n\nCompressedObject.prototype = {\n    /**\n     * Create a worker to get the uncompressed content.\n     * @return {GenericWorker} the worker.\n     */\n    getContentWorker: function () {\n        var worker = new DataWorker(external.Promise.resolve(this.compressedContent))\n            .pipe(this.compression.uncompressWorker())\n            .pipe(new DataLengthProbe(\"data_length\"));\n\n        var that = this;\n        worker.on(\"end\", function () {\n            if (this.streamInfo[\"data_length\"] !== that.uncompressedSize) {\n                throw new Error(\"Bug : uncompressed data size mismatch\");\n            }\n        });\n        return worker;\n    },\n    /**\n     * Create a worker to get the compressed content.\n     * @return {GenericWorker} the worker.\n     */\n    getCompressedWorker: function () {\n        return new DataWorker(external.Promise.resolve(this.compressedContent))\n            .withStreamInfo(\"compressedSize\", this.compressedSize)\n            .withStreamInfo(\"uncompressedSize\", this.uncompressedSize)\n            .withStreamInfo(\"crc32\", this.crc32)\n            .withStreamInfo(\"compression\", this.compression)\n        ;\n    }\n};\n\n/**\n * Chain the given worker with other workers to compress the content with the\n * given compression.\n * @param {GenericWorker} uncompressedWorker the worker to pipe.\n * @param {Object} compression the compression object.\n * @param {Object} compressionOptions the options to use when compressing.\n * @return {GenericWorker} the new worker compressing the content.\n */\nCompressedObject.createWorkerFrom = function (uncompressedWorker, compression, compressionOptions) {\n    return uncompressedWorker\n        .pipe(new Crc32Probe())\n        .pipe(new DataLengthProbe(\"uncompressedSize\"))\n        .pipe(compression.compressWorker(compressionOptions))\n        .pipe(new DataLengthProbe(\"compressedSize\"))\n        .withStreamInfo(\"compression\", compression);\n};\n\nmodule.exports = CompressedObject;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/compressedObject.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/compressions.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/compressions.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar GenericWorker = __webpack_require__(/*! ./stream/GenericWorker */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/GenericWorker.js\");\n\nexports.STORE = {\n    magic: \"\\x00\\x00\",\n    compressWorker : function () {\n        return new GenericWorker(\"STORE compression\");\n    },\n    uncompressWorker : function () {\n        return new GenericWorker(\"STORE decompression\");\n    }\n};\nexports.DEFLATE = __webpack_require__(/*! ./flate */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/flate.js\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vanN6aXBAMy4xMC4xL25vZGVfbW9kdWxlcy9qc3ppcC9saWIvY29tcHJlc3Npb25zLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLG9CQUFvQixtQkFBTyxDQUFDLHNIQUF3Qjs7QUFFcEQsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtIQUFvQyIsInNvdXJjZXMiOlsid2VicGFjazovL0B3ZS1kZXYvbmV4dC8uL25vZGVfbW9kdWxlcy8ucG5wbS9qc3ppcEAzLjEwLjEvbm9kZV9tb2R1bGVzL2pzemlwL2xpYi9jb21wcmVzc2lvbnMuanM/OGNmOSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxudmFyIEdlbmVyaWNXb3JrZXIgPSByZXF1aXJlKFwiLi9zdHJlYW0vR2VuZXJpY1dvcmtlclwiKTtcblxuZXhwb3J0cy5TVE9SRSA9IHtcbiAgICBtYWdpYzogXCJcXHgwMFxceDAwXCIsXG4gICAgY29tcHJlc3NXb3JrZXIgOiBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiBuZXcgR2VuZXJpY1dvcmtlcihcIlNUT1JFIGNvbXByZXNzaW9uXCIpO1xuICAgIH0sXG4gICAgdW5jb21wcmVzc1dvcmtlciA6IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIG5ldyBHZW5lcmljV29ya2VyKFwiU1RPUkUgZGVjb21wcmVzc2lvblwiKTtcbiAgICB9XG59O1xuZXhwb3J0cy5ERUZMQVRFID0gcmVxdWlyZShcIi4vZmxhdGVcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/compressions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/crc32.js":
/*!*************************************************************************!*\
  !*** ./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/crc32.js ***!
  \*************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\n\n/**\n * The following functions come from pako, from pako/lib/zlib/crc32.js\n * released under the MIT license, see pako https://github.com/nodeca/pako/\n */\n\n// Use ordinary array, since untyped makes no boost here\nfunction makeTable() {\n    var c, table = [];\n\n    for(var n =0; n < 256; n++){\n        c = n;\n        for(var k =0; k < 8; k++){\n            c = ((c&1) ? (0xEDB88320 ^ (c >>> 1)) : (c >>> 1));\n        }\n        table[n] = c;\n    }\n\n    return table;\n}\n\n// Create table on load. Just 255 signed longs. Not a problem.\nvar crcTable = makeTable();\n\n\nfunction crc32(crc, buf, len, pos) {\n    var t = crcTable, end = pos + len;\n\n    crc = crc ^ (-1);\n\n    for (var i = pos; i < end; i++ ) {\n        crc = (crc >>> 8) ^ t[(crc ^ buf[i]) & 0xFF];\n    }\n\n    return (crc ^ (-1)); // >>> 0;\n}\n\n// That's all for the pako functions.\n\n/**\n * Compute the crc32 of a string.\n * This is almost the same as the function crc32, but for strings. Using the\n * same function for the two use cases leads to horrible performances.\n * @param {Number} crc the starting value of the crc.\n * @param {String} str the string to use.\n * @param {Number} len the length of the string.\n * @param {Number} pos the starting position for the crc32 computation.\n * @return {Number} the computed crc32.\n */\nfunction crc32str(crc, str, len, pos) {\n    var t = crcTable, end = pos + len;\n\n    crc = crc ^ (-1);\n\n    for (var i = pos; i < end; i++ ) {\n        crc = (crc >>> 8) ^ t[(crc ^ str.charCodeAt(i)) & 0xFF];\n    }\n\n    return (crc ^ (-1)); // >>> 0;\n}\n\nmodule.exports = function crc32wrapper(input, crc) {\n    if (typeof input === \"undefined\" || !input.length) {\n        return 0;\n    }\n\n    var isArray = utils.getTypeOf(input) !== \"string\";\n\n    if(isArray) {\n        return crc32(crc|0, input, input.length, 0);\n    } else {\n        return crc32str(crc|0, input, input.length, 0);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/crc32.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/defaults.js":
/*!****************************************************************************!*\
  !*** ./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/defaults.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nexports.base64 = false;\nexports.binary = false;\nexports.dir = false;\nexports.createFolders = true;\nexports.date = null;\nexports.compression = null;\nexports.compressionOptions = null;\nexports.comment = null;\nexports.unixPermissions = null;\nexports.dosPermissions = null;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vanN6aXBAMy4xMC4xL25vZGVfbW9kdWxlcy9qc3ppcC9saWIvZGVmYXVsdHMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYixjQUFjO0FBQ2QsY0FBYztBQUNkLFdBQVc7QUFDWCxxQkFBcUI7QUFDckIsWUFBWTtBQUNaLG1CQUFtQjtBQUNuQiwwQkFBMEI7QUFDMUIsZUFBZTtBQUNmLHVCQUF1QjtBQUN2QixzQkFBc0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ad2UtZGV2L25leHQvLi9ub2RlX21vZHVsZXMvLnBucG0vanN6aXBAMy4xMC4xL25vZGVfbW9kdWxlcy9qc3ppcC9saWIvZGVmYXVsdHMuanM/MDVlNiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbmV4cG9ydHMuYmFzZTY0ID0gZmFsc2U7XG5leHBvcnRzLmJpbmFyeSA9IGZhbHNlO1xuZXhwb3J0cy5kaXIgPSBmYWxzZTtcbmV4cG9ydHMuY3JlYXRlRm9sZGVycyA9IHRydWU7XG5leHBvcnRzLmRhdGUgPSBudWxsO1xuZXhwb3J0cy5jb21wcmVzc2lvbiA9IG51bGw7XG5leHBvcnRzLmNvbXByZXNzaW9uT3B0aW9ucyA9IG51bGw7XG5leHBvcnRzLmNvbW1lbnQgPSBudWxsO1xuZXhwb3J0cy51bml4UGVybWlzc2lvbnMgPSBudWxsO1xuZXhwb3J0cy5kb3NQZXJtaXNzaW9ucyA9IG51bGw7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/defaults.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/external.js":
/*!****************************************************************************!*\
  !*** ./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/external.js ***!
  \****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n// load the global object first:\n// - it should be better integrated in the system (unhandledRejection in node)\n// - the environment may have a custom Promise implementation (see zone.js)\nvar ES6Promise = null;\nif (typeof Promise !== \"undefined\") {\n    ES6Promise = Promise;\n} else {\n    ES6Promise = __webpack_require__(/*! lie */ \"(ssr)/./node_modules/.pnpm/lie@3.3.0/node_modules/lie/lib/index.js\");\n}\n\n/**\n * Let the user use/change some implementations.\n */\nmodule.exports = {\n    Promise: ES6Promise\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vanN6aXBAMy4xMC4xL25vZGVfbW9kdWxlcy9qc3ppcC9saWIvZXh0ZXJuYWwuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsRUFBRTtBQUNGLGlCQUFpQixtQkFBTyxDQUFDLCtFQUFLO0FBQzlCOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL0B3ZS1kZXYvbmV4dC8uL25vZGVfbW9kdWxlcy8ucG5wbS9qc3ppcEAzLjEwLjEvbm9kZV9tb2R1bGVzL2pzemlwL2xpYi9leHRlcm5hbC5qcz81YTRmIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG4vLyBsb2FkIHRoZSBnbG9iYWwgb2JqZWN0IGZpcnN0OlxuLy8gLSBpdCBzaG91bGQgYmUgYmV0dGVyIGludGVncmF0ZWQgaW4gdGhlIHN5c3RlbSAodW5oYW5kbGVkUmVqZWN0aW9uIGluIG5vZGUpXG4vLyAtIHRoZSBlbnZpcm9ubWVudCBtYXkgaGF2ZSBhIGN1c3RvbSBQcm9taXNlIGltcGxlbWVudGF0aW9uIChzZWUgem9uZS5qcylcbnZhciBFUzZQcm9taXNlID0gbnVsbDtcbmlmICh0eXBlb2YgUHJvbWlzZSAhPT0gXCJ1bmRlZmluZWRcIikge1xuICAgIEVTNlByb21pc2UgPSBQcm9taXNlO1xufSBlbHNlIHtcbiAgICBFUzZQcm9taXNlID0gcmVxdWlyZShcImxpZVwiKTtcbn1cblxuLyoqXG4gKiBMZXQgdGhlIHVzZXIgdXNlL2NoYW5nZSBzb21lIGltcGxlbWVudGF0aW9ucy5cbiAqL1xubW9kdWxlLmV4cG9ydHMgPSB7XG4gICAgUHJvbWlzZTogRVM2UHJvbWlzZVxufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/external.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/flate.js":
/*!*************************************************************************!*\
  !*** ./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/flate.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar USE_TYPEDARRAY = (typeof Uint8Array !== \"undefined\") && (typeof Uint16Array !== \"undefined\") && (typeof Uint32Array !== \"undefined\");\n\nvar pako = __webpack_require__(/*! pako */ \"(ssr)/./node_modules/.pnpm/pako@1.0.11/node_modules/pako/index.js\");\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\nvar GenericWorker = __webpack_require__(/*! ./stream/GenericWorker */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/GenericWorker.js\");\n\nvar ARRAY_TYPE = USE_TYPEDARRAY ? \"uint8array\" : \"array\";\n\nexports.magic = \"\\x08\\x00\";\n\n/**\n * Create a worker that uses pako to inflate/deflate.\n * @constructor\n * @param {String} action the name of the pako function to call : either \"Deflate\" or \"Inflate\".\n * @param {Object} options the options to use when (de)compressing.\n */\nfunction FlateWorker(action, options) {\n    GenericWorker.call(this, \"FlateWorker/\" + action);\n\n    this._pako = null;\n    this._pakoAction = action;\n    this._pakoOptions = options;\n    // the `meta` object from the last chunk received\n    // this allow this worker to pass around metadata\n    this.meta = {};\n}\n\nutils.inherits(FlateWorker, GenericWorker);\n\n/**\n * @see GenericWorker.processChunk\n */\nFlateWorker.prototype.processChunk = function (chunk) {\n    this.meta = chunk.meta;\n    if (this._pako === null) {\n        this._createPako();\n    }\n    this._pako.push(utils.transformTo(ARRAY_TYPE, chunk.data), false);\n};\n\n/**\n * @see GenericWorker.flush\n */\nFlateWorker.prototype.flush = function () {\n    GenericWorker.prototype.flush.call(this);\n    if (this._pako === null) {\n        this._createPako();\n    }\n    this._pako.push([], true);\n};\n/**\n * @see GenericWorker.cleanUp\n */\nFlateWorker.prototype.cleanUp = function () {\n    GenericWorker.prototype.cleanUp.call(this);\n    this._pako = null;\n};\n\n/**\n * Create the _pako object.\n * TODO: lazy-loading this object isn't the best solution but it's the\n * quickest. The best solution is to lazy-load the worker list. See also the\n * issue #446.\n */\nFlateWorker.prototype._createPako = function () {\n    this._pako = new pako[this._pakoAction]({\n        raw: true,\n        level: this._pakoOptions.level || -1 // default compression\n    });\n    var self = this;\n    this._pako.onData = function(data) {\n        self.push({\n            data : data,\n            meta : self.meta\n        });\n    };\n};\n\nexports.compressWorker = function (compressionOptions) {\n    return new FlateWorker(\"Deflate\", compressionOptions);\n};\nexports.uncompressWorker = function () {\n    return new FlateWorker(\"Inflate\", {});\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/flate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/generate/ZipFileWorker.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/generate/ZipFileWorker.js ***!
  \******************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\nvar GenericWorker = __webpack_require__(/*! ../stream/GenericWorker */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/GenericWorker.js\");\nvar utf8 = __webpack_require__(/*! ../utf8 */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utf8.js\");\nvar crc32 = __webpack_require__(/*! ../crc32 */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/crc32.js\");\nvar signature = __webpack_require__(/*! ../signature */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/signature.js\");\n\n/**\n * Transform an integer into a string in hexadecimal.\n * @private\n * @param {number} dec the number to convert.\n * @param {number} bytes the number of bytes to generate.\n * @returns {string} the result.\n */\nvar decToHex = function(dec, bytes) {\n    var hex = \"\", i;\n    for (i = 0; i < bytes; i++) {\n        hex += String.fromCharCode(dec & 0xff);\n        dec = dec >>> 8;\n    }\n    return hex;\n};\n\n/**\n * Generate the UNIX part of the external file attributes.\n * @param {Object} unixPermissions the unix permissions or null.\n * @param {Boolean} isDir true if the entry is a directory, false otherwise.\n * @return {Number} a 32 bit integer.\n *\n * adapted from http://unix.stackexchange.com/questions/14705/the-zip-formats-external-file-attribute :\n *\n * TTTTsstrwxrwxrwx0000000000ADVSHR\n * ^^^^____________________________ file type, see zipinfo.c (UNX_*)\n *     ^^^_________________________ setuid, setgid, sticky\n *        ^^^^^^^^^________________ permissions\n *                 ^^^^^^^^^^______ not used ?\n *                           ^^^^^^ DOS attribute bits : Archive, Directory, Volume label, System file, Hidden, Read only\n */\nvar generateUnixExternalFileAttr = function (unixPermissions, isDir) {\n\n    var result = unixPermissions;\n    if (!unixPermissions) {\n        // I can't use octal values in strict mode, hence the hexa.\n        //  040775 => 0x41fd\n        // 0100664 => 0x81b4\n        result = isDir ? 0x41fd : 0x81b4;\n    }\n    return (result & 0xFFFF) << 16;\n};\n\n/**\n * Generate the DOS part of the external file attributes.\n * @param {Object} dosPermissions the dos permissions or null.\n * @param {Boolean} isDir true if the entry is a directory, false otherwise.\n * @return {Number} a 32 bit integer.\n *\n * Bit 0     Read-Only\n * Bit 1     Hidden\n * Bit 2     System\n * Bit 3     Volume Label\n * Bit 4     Directory\n * Bit 5     Archive\n */\nvar generateDosExternalFileAttr = function (dosPermissions) {\n    // the dir flag is already set for compatibility\n    return (dosPermissions || 0)  & 0x3F;\n};\n\n/**\n * Generate the various parts used in the construction of the final zip file.\n * @param {Object} streamInfo the hash with information about the compressed file.\n * @param {Boolean} streamedContent is the content streamed ?\n * @param {Boolean} streamingEnded is the stream finished ?\n * @param {number} offset the current offset from the start of the zip file.\n * @param {String} platform let's pretend we are this platform (change platform dependents fields)\n * @param {Function} encodeFileName the function to encode the file name / comment.\n * @return {Object} the zip parts.\n */\nvar generateZipParts = function(streamInfo, streamedContent, streamingEnded, offset, platform, encodeFileName) {\n    var file = streamInfo[\"file\"],\n        compression = streamInfo[\"compression\"],\n        useCustomEncoding = encodeFileName !== utf8.utf8encode,\n        encodedFileName = utils.transformTo(\"string\", encodeFileName(file.name)),\n        utfEncodedFileName = utils.transformTo(\"string\", utf8.utf8encode(file.name)),\n        comment = file.comment,\n        encodedComment = utils.transformTo(\"string\", encodeFileName(comment)),\n        utfEncodedComment = utils.transformTo(\"string\", utf8.utf8encode(comment)),\n        useUTF8ForFileName = utfEncodedFileName.length !== file.name.length,\n        useUTF8ForComment = utfEncodedComment.length !== comment.length,\n        dosTime,\n        dosDate,\n        extraFields = \"\",\n        unicodePathExtraField = \"\",\n        unicodeCommentExtraField = \"\",\n        dir = file.dir,\n        date = file.date;\n\n\n    var dataInfo = {\n        crc32 : 0,\n        compressedSize : 0,\n        uncompressedSize : 0\n    };\n\n    // if the content is streamed, the sizes/crc32 are only available AFTER\n    // the end of the stream.\n    if (!streamedContent || streamingEnded) {\n        dataInfo.crc32 = streamInfo[\"crc32\"];\n        dataInfo.compressedSize = streamInfo[\"compressedSize\"];\n        dataInfo.uncompressedSize = streamInfo[\"uncompressedSize\"];\n    }\n\n    var bitflag = 0;\n    if (streamedContent) {\n        // Bit 3: the sizes/crc32 are set to zero in the local header.\n        // The correct values are put in the data descriptor immediately\n        // following the compressed data.\n        bitflag |= 0x0008;\n    }\n    if (!useCustomEncoding && (useUTF8ForFileName || useUTF8ForComment)) {\n        // Bit 11: Language encoding flag (EFS).\n        bitflag |= 0x0800;\n    }\n\n\n    var extFileAttr = 0;\n    var versionMadeBy = 0;\n    if (dir) {\n        // dos or unix, we set the dos dir flag\n        extFileAttr |= 0x00010;\n    }\n    if(platform === \"UNIX\") {\n        versionMadeBy = 0x031E; // UNIX, version 3.0\n        extFileAttr |= generateUnixExternalFileAttr(file.unixPermissions, dir);\n    } else { // DOS or other, fallback to DOS\n        versionMadeBy = 0x0014; // DOS, version 2.0\n        extFileAttr |= generateDosExternalFileAttr(file.dosPermissions, dir);\n    }\n\n    // date\n    // @see http://www.delorie.com/djgpp/doc/rbinter/it/52/13.html\n    // @see http://www.delorie.com/djgpp/doc/rbinter/it/65/16.html\n    // @see http://www.delorie.com/djgpp/doc/rbinter/it/66/16.html\n\n    dosTime = date.getUTCHours();\n    dosTime = dosTime << 6;\n    dosTime = dosTime | date.getUTCMinutes();\n    dosTime = dosTime << 5;\n    dosTime = dosTime | date.getUTCSeconds() / 2;\n\n    dosDate = date.getUTCFullYear() - 1980;\n    dosDate = dosDate << 4;\n    dosDate = dosDate | (date.getUTCMonth() + 1);\n    dosDate = dosDate << 5;\n    dosDate = dosDate | date.getUTCDate();\n\n    if (useUTF8ForFileName) {\n        // set the unicode path extra field. unzip needs at least one extra\n        // field to correctly handle unicode path, so using the path is as good\n        // as any other information. This could improve the situation with\n        // other archive managers too.\n        // This field is usually used without the utf8 flag, with a non\n        // unicode path in the header (winrar, winzip). This helps (a bit)\n        // with the messy Windows' default compressed folders feature but\n        // breaks on p7zip which doesn't seek the unicode path extra field.\n        // So for now, UTF-8 everywhere !\n        unicodePathExtraField =\n            // Version\n            decToHex(1, 1) +\n            // NameCRC32\n            decToHex(crc32(encodedFileName), 4) +\n            // UnicodeName\n            utfEncodedFileName;\n\n        extraFields +=\n            // Info-ZIP Unicode Path Extra Field\n            \"\\x75\\x70\" +\n            // size\n            decToHex(unicodePathExtraField.length, 2) +\n            // content\n            unicodePathExtraField;\n    }\n\n    if(useUTF8ForComment) {\n\n        unicodeCommentExtraField =\n            // Version\n            decToHex(1, 1) +\n            // CommentCRC32\n            decToHex(crc32(encodedComment), 4) +\n            // UnicodeName\n            utfEncodedComment;\n\n        extraFields +=\n            // Info-ZIP Unicode Path Extra Field\n            \"\\x75\\x63\" +\n            // size\n            decToHex(unicodeCommentExtraField.length, 2) +\n            // content\n            unicodeCommentExtraField;\n    }\n\n    var header = \"\";\n\n    // version needed to extract\n    header += \"\\x0A\\x00\";\n    // general purpose bit flag\n    header += decToHex(bitflag, 2);\n    // compression method\n    header += compression.magic;\n    // last mod file time\n    header += decToHex(dosTime, 2);\n    // last mod file date\n    header += decToHex(dosDate, 2);\n    // crc-32\n    header += decToHex(dataInfo.crc32, 4);\n    // compressed size\n    header += decToHex(dataInfo.compressedSize, 4);\n    // uncompressed size\n    header += decToHex(dataInfo.uncompressedSize, 4);\n    // file name length\n    header += decToHex(encodedFileName.length, 2);\n    // extra field length\n    header += decToHex(extraFields.length, 2);\n\n\n    var fileRecord = signature.LOCAL_FILE_HEADER + header + encodedFileName + extraFields;\n\n    var dirRecord = signature.CENTRAL_FILE_HEADER +\n        // version made by (00: DOS)\n        decToHex(versionMadeBy, 2) +\n        // file header (common to file and central directory)\n        header +\n        // file comment length\n        decToHex(encodedComment.length, 2) +\n        // disk number start\n        \"\\x00\\x00\" +\n        // internal file attributes TODO\n        \"\\x00\\x00\" +\n        // external file attributes\n        decToHex(extFileAttr, 4) +\n        // relative offset of local header\n        decToHex(offset, 4) +\n        // file name\n        encodedFileName +\n        // extra field\n        extraFields +\n        // file comment\n        encodedComment;\n\n    return {\n        fileRecord: fileRecord,\n        dirRecord: dirRecord\n    };\n};\n\n/**\n * Generate the EOCD record.\n * @param {Number} entriesCount the number of entries in the zip file.\n * @param {Number} centralDirLength the length (in bytes) of the central dir.\n * @param {Number} localDirLength the length (in bytes) of the local dir.\n * @param {String} comment the zip file comment as a binary string.\n * @param {Function} encodeFileName the function to encode the comment.\n * @return {String} the EOCD record.\n */\nvar generateCentralDirectoryEnd = function (entriesCount, centralDirLength, localDirLength, comment, encodeFileName) {\n    var dirEnd = \"\";\n    var encodedComment = utils.transformTo(\"string\", encodeFileName(comment));\n\n    // end of central dir signature\n    dirEnd = signature.CENTRAL_DIRECTORY_END +\n        // number of this disk\n        \"\\x00\\x00\" +\n        // number of the disk with the start of the central directory\n        \"\\x00\\x00\" +\n        // total number of entries in the central directory on this disk\n        decToHex(entriesCount, 2) +\n        // total number of entries in the central directory\n        decToHex(entriesCount, 2) +\n        // size of the central directory   4 bytes\n        decToHex(centralDirLength, 4) +\n        // offset of start of central directory with respect to the starting disk number\n        decToHex(localDirLength, 4) +\n        // .ZIP file comment length\n        decToHex(encodedComment.length, 2) +\n        // .ZIP file comment\n        encodedComment;\n\n    return dirEnd;\n};\n\n/**\n * Generate data descriptors for a file entry.\n * @param {Object} streamInfo the hash generated by a worker, containing information\n * on the file entry.\n * @return {String} the data descriptors.\n */\nvar generateDataDescriptors = function (streamInfo) {\n    var descriptor = \"\";\n    descriptor = signature.DATA_DESCRIPTOR +\n        // crc-32                          4 bytes\n        decToHex(streamInfo[\"crc32\"], 4) +\n        // compressed size                 4 bytes\n        decToHex(streamInfo[\"compressedSize\"], 4) +\n        // uncompressed size               4 bytes\n        decToHex(streamInfo[\"uncompressedSize\"], 4);\n\n    return descriptor;\n};\n\n\n/**\n * A worker to concatenate other workers to create a zip file.\n * @param {Boolean} streamFiles `true` to stream the content of the files,\n * `false` to accumulate it.\n * @param {String} comment the comment to use.\n * @param {String} platform the platform to use, \"UNIX\" or \"DOS\".\n * @param {Function} encodeFileName the function to encode file names and comments.\n */\nfunction ZipFileWorker(streamFiles, comment, platform, encodeFileName) {\n    GenericWorker.call(this, \"ZipFileWorker\");\n    // The number of bytes written so far. This doesn't count accumulated chunks.\n    this.bytesWritten = 0;\n    // The comment of the zip file\n    this.zipComment = comment;\n    // The platform \"generating\" the zip file.\n    this.zipPlatform = platform;\n    // the function to encode file names and comments.\n    this.encodeFileName = encodeFileName;\n    // Should we stream the content of the files ?\n    this.streamFiles = streamFiles;\n    // If `streamFiles` is false, we will need to accumulate the content of the\n    // files to calculate sizes / crc32 (and write them *before* the content).\n    // This boolean indicates if we are accumulating chunks (it will change a lot\n    // during the lifetime of this worker).\n    this.accumulate = false;\n    // The buffer receiving chunks when accumulating content.\n    this.contentBuffer = [];\n    // The list of generated directory records.\n    this.dirRecords = [];\n    // The offset (in bytes) from the beginning of the zip file for the current source.\n    this.currentSourceOffset = 0;\n    // The total number of entries in this zip file.\n    this.entriesCount = 0;\n    // the name of the file currently being added, null when handling the end of the zip file.\n    // Used for the emitted metadata.\n    this.currentFile = null;\n\n\n\n    this._sources = [];\n}\nutils.inherits(ZipFileWorker, GenericWorker);\n\n/**\n * @see GenericWorker.push\n */\nZipFileWorker.prototype.push = function (chunk) {\n\n    var currentFilePercent = chunk.meta.percent || 0;\n    var entriesCount = this.entriesCount;\n    var remainingFiles = this._sources.length;\n\n    if(this.accumulate) {\n        this.contentBuffer.push(chunk);\n    } else {\n        this.bytesWritten += chunk.data.length;\n\n        GenericWorker.prototype.push.call(this, {\n            data : chunk.data,\n            meta : {\n                currentFile : this.currentFile,\n                percent : entriesCount ? (currentFilePercent + 100 * (entriesCount - remainingFiles - 1)) / entriesCount : 100\n            }\n        });\n    }\n};\n\n/**\n * The worker started a new source (an other worker).\n * @param {Object} streamInfo the streamInfo object from the new source.\n */\nZipFileWorker.prototype.openedSource = function (streamInfo) {\n    this.currentSourceOffset = this.bytesWritten;\n    this.currentFile = streamInfo[\"file\"].name;\n\n    var streamedContent = this.streamFiles && !streamInfo[\"file\"].dir;\n\n    // don't stream folders (because they don't have any content)\n    if(streamedContent) {\n        var record = generateZipParts(streamInfo, streamedContent, false, this.currentSourceOffset, this.zipPlatform, this.encodeFileName);\n        this.push({\n            data : record.fileRecord,\n            meta : {percent:0}\n        });\n    } else {\n        // we need to wait for the whole file before pushing anything\n        this.accumulate = true;\n    }\n};\n\n/**\n * The worker finished a source (an other worker).\n * @param {Object} streamInfo the streamInfo object from the finished source.\n */\nZipFileWorker.prototype.closedSource = function (streamInfo) {\n    this.accumulate = false;\n    var streamedContent = this.streamFiles && !streamInfo[\"file\"].dir;\n    var record = generateZipParts(streamInfo, streamedContent, true, this.currentSourceOffset, this.zipPlatform, this.encodeFileName);\n\n    this.dirRecords.push(record.dirRecord);\n    if(streamedContent) {\n        // after the streamed file, we put data descriptors\n        this.push({\n            data : generateDataDescriptors(streamInfo),\n            meta : {percent:100}\n        });\n    } else {\n        // the content wasn't streamed, we need to push everything now\n        // first the file record, then the content\n        this.push({\n            data : record.fileRecord,\n            meta : {percent:0}\n        });\n        while(this.contentBuffer.length) {\n            this.push(this.contentBuffer.shift());\n        }\n    }\n    this.currentFile = null;\n};\n\n/**\n * @see GenericWorker.flush\n */\nZipFileWorker.prototype.flush = function () {\n\n    var localDirLength = this.bytesWritten;\n    for(var i = 0; i < this.dirRecords.length; i++) {\n        this.push({\n            data : this.dirRecords[i],\n            meta : {percent:100}\n        });\n    }\n    var centralDirLength = this.bytesWritten - localDirLength;\n\n    var dirEnd = generateCentralDirectoryEnd(this.dirRecords.length, centralDirLength, localDirLength, this.zipComment, this.encodeFileName);\n\n    this.push({\n        data : dirEnd,\n        meta : {percent:100}\n    });\n};\n\n/**\n * Prepare the next source to be read.\n */\nZipFileWorker.prototype.prepareNextSource = function () {\n    this.previous = this._sources.shift();\n    this.openedSource(this.previous.streamInfo);\n    if (this.isPaused) {\n        this.previous.pause();\n    } else {\n        this.previous.resume();\n    }\n};\n\n/**\n * @see GenericWorker.registerPrevious\n */\nZipFileWorker.prototype.registerPrevious = function (previous) {\n    this._sources.push(previous);\n    var self = this;\n\n    previous.on(\"data\", function (chunk) {\n        self.processChunk(chunk);\n    });\n    previous.on(\"end\", function () {\n        self.closedSource(self.previous.streamInfo);\n        if(self._sources.length) {\n            self.prepareNextSource();\n        } else {\n            self.end();\n        }\n    });\n    previous.on(\"error\", function (e) {\n        self.error(e);\n    });\n    return this;\n};\n\n/**\n * @see GenericWorker.resume\n */\nZipFileWorker.prototype.resume = function () {\n    if(!GenericWorker.prototype.resume.call(this)) {\n        return false;\n    }\n\n    if (!this.previous && this._sources.length) {\n        this.prepareNextSource();\n        return true;\n    }\n    if (!this.previous && !this._sources.length && !this.generatedError) {\n        this.end();\n        return true;\n    }\n};\n\n/**\n * @see GenericWorker.error\n */\nZipFileWorker.prototype.error = function (e) {\n    var sources = this._sources;\n    if(!GenericWorker.prototype.error.call(this, e)) {\n        return false;\n    }\n    for(var i = 0; i < sources.length; i++) {\n        try {\n            sources[i].error(e);\n        } catch(e) {\n            // the `error` exploded, nothing to do\n        }\n    }\n    return true;\n};\n\n/**\n * @see GenericWorker.lock\n */\nZipFileWorker.prototype.lock = function () {\n    GenericWorker.prototype.lock.call(this);\n    var sources = this._sources;\n    for(var i = 0; i < sources.length; i++) {\n        sources[i].lock();\n    }\n};\n\nmodule.exports = ZipFileWorker;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/generate/ZipFileWorker.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/generate/index.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/generate/index.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar compressions = __webpack_require__(/*! ../compressions */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/compressions.js\");\nvar ZipFileWorker = __webpack_require__(/*! ./ZipFileWorker */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/generate/ZipFileWorker.js\");\n\n/**\n * Find the compression to use.\n * @param {String} fileCompression the compression defined at the file level, if any.\n * @param {String} zipCompression the compression defined at the load() level.\n * @return {Object} the compression object to use.\n */\nvar getCompression = function (fileCompression, zipCompression) {\n\n    var compressionName = fileCompression || zipCompression;\n    var compression = compressions[compressionName];\n    if (!compression) {\n        throw new Error(compressionName + \" is not a valid compression method !\");\n    }\n    return compression;\n};\n\n/**\n * Create a worker to generate a zip file.\n * @param {JSZip} zip the JSZip instance at the right root level.\n * @param {Object} options to generate the zip file.\n * @param {String} comment the comment to use.\n */\nexports.generateWorker = function (zip, options, comment) {\n\n    var zipFileWorker = new ZipFileWorker(options.streamFiles, comment, options.platform, options.encodeFileName);\n    var entriesCount = 0;\n    try {\n\n        zip.forEach(function (relativePath, file) {\n            entriesCount++;\n            var compression = getCompression(file.options.compression, options.compression);\n            var compressionOptions = file.options.compressionOptions || options.compressionOptions || {};\n            var dir = file.dir, date = file.date;\n\n            file._compressWorker(compression, compressionOptions)\n                .withStreamInfo(\"file\", {\n                    name : relativePath,\n                    dir : dir,\n                    date : date,\n                    comment : file.comment || \"\",\n                    unixPermissions : file.unixPermissions,\n                    dosPermissions : file.dosPermissions\n                })\n                .pipe(zipFileWorker);\n        });\n        zipFileWorker.entriesCount = entriesCount;\n    } catch (e) {\n        zipFileWorker.error(e);\n    }\n\n    return zipFileWorker;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/generate/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/index.js":
/*!*************************************************************************!*\
  !*** ./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/index.js ***!
  \*************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n/**\n * Representation a of zip file in js\n * @constructor\n */\nfunction JSZip() {\n    // if this constructor is used without `new`, it adds `new` before itself:\n    if(!(this instanceof JSZip)) {\n        return new JSZip();\n    }\n\n    if(arguments.length) {\n        throw new Error(\"The constructor with parameters has been removed in JSZip 3.0, please check the upgrade guide.\");\n    }\n\n    // object containing the files :\n    // {\n    //   \"folder/\" : {...},\n    //   \"folder/data.txt\" : {...}\n    // }\n    // NOTE: we use a null prototype because we do not\n    // want filenames like \"toString\" coming from a zip file\n    // to overwrite methods and attributes in a normal Object.\n    this.files = Object.create(null);\n\n    this.comment = null;\n\n    // Where we are in the hierarchy\n    this.root = \"\";\n    this.clone = function() {\n        var newObj = new JSZip();\n        for (var i in this) {\n            if (typeof this[i] !== \"function\") {\n                newObj[i] = this[i];\n            }\n        }\n        return newObj;\n    };\n}\nJSZip.prototype = __webpack_require__(/*! ./object */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/object.js\");\nJSZip.prototype.loadAsync = __webpack_require__(/*! ./load */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/load.js\");\nJSZip.support = __webpack_require__(/*! ./support */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/support.js\");\nJSZip.defaults = __webpack_require__(/*! ./defaults */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/defaults.js\");\n\n// TODO find a better way to handle this version,\n// a require('package.json').version doesn't work with webpack, see #327\nJSZip.version = \"3.10.1\";\n\nJSZip.loadAsync = function (content, options) {\n    return new JSZip().loadAsync(content, options);\n};\n\nJSZip.external = __webpack_require__(/*! ./external */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/external.js\");\nmodule.exports = JSZip;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/load.js":
/*!************************************************************************!*\
  !*** ./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/load.js ***!
  \************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\nvar external = __webpack_require__(/*! ./external */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/external.js\");\nvar utf8 = __webpack_require__(/*! ./utf8 */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utf8.js\");\nvar ZipEntries = __webpack_require__(/*! ./zipEntries */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/zipEntries.js\");\nvar Crc32Probe = __webpack_require__(/*! ./stream/Crc32Probe */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/Crc32Probe.js\");\nvar nodejsUtils = __webpack_require__(/*! ./nodejsUtils */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/nodejsUtils.js\");\n\n/**\n * Check the CRC32 of an entry.\n * @param {ZipEntry} zipEntry the zip entry to check.\n * @return {Promise} the result.\n */\nfunction checkEntryCRC32(zipEntry) {\n    return new external.Promise(function (resolve, reject) {\n        var worker = zipEntry.decompressed.getContentWorker().pipe(new Crc32Probe());\n        worker.on(\"error\", function (e) {\n            reject(e);\n        })\n            .on(\"end\", function () {\n                if (worker.streamInfo.crc32 !== zipEntry.decompressed.crc32) {\n                    reject(new Error(\"Corrupted zip : CRC32 mismatch\"));\n                } else {\n                    resolve();\n                }\n            })\n            .resume();\n    });\n}\n\nmodule.exports = function (data, options) {\n    var zip = this;\n    options = utils.extend(options || {}, {\n        base64: false,\n        checkCRC32: false,\n        optimizedBinaryString: false,\n        createFolders: false,\n        decodeFileName: utf8.utf8decode\n    });\n\n    if (nodejsUtils.isNode && nodejsUtils.isStream(data)) {\n        return external.Promise.reject(new Error(\"JSZip can't accept a stream when loading a zip file.\"));\n    }\n\n    return utils.prepareContent(\"the loaded zip file\", data, true, options.optimizedBinaryString, options.base64)\n        .then(function (data) {\n            var zipEntries = new ZipEntries(options);\n            zipEntries.load(data);\n            return zipEntries;\n        }).then(function checkCRC32(zipEntries) {\n            var promises = [external.Promise.resolve(zipEntries)];\n            var files = zipEntries.files;\n            if (options.checkCRC32) {\n                for (var i = 0; i < files.length; i++) {\n                    promises.push(checkEntryCRC32(files[i]));\n                }\n            }\n            return external.Promise.all(promises);\n        }).then(function addFiles(results) {\n            var zipEntries = results.shift();\n            var files = zipEntries.files;\n            for (var i = 0; i < files.length; i++) {\n                var input = files[i];\n\n                var unsafeName = input.fileNameStr;\n                var safeName = utils.resolve(input.fileNameStr);\n\n                zip.file(safeName, input.decompressed, {\n                    binary: true,\n                    optimizedBinaryString: true,\n                    date: input.date,\n                    dir: input.dir,\n                    comment: input.fileCommentStr.length ? input.fileCommentStr : null,\n                    unixPermissions: input.unixPermissions,\n                    dosPermissions: input.dosPermissions,\n                    createFolders: options.createFolders\n                });\n                if (!input.dir) {\n                    zip.file(safeName).unsafeOriginalName = unsafeName;\n                }\n            }\n            if (zipEntries.zipComment.length) {\n                zip.comment = zipEntries.zipComment;\n            }\n\n            return zip;\n        });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/load.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/nodejsUtils.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/nodejsUtils.js ***!
  \*******************************************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = {\n    /**\n     * True if this is running in Nodejs, will be undefined in a browser.\n     * In a browser, browserify won't include this file and the whole module\n     * will be resolved an empty object.\n     */\n    isNode : typeof Buffer !== \"undefined\",\n    /**\n     * Create a new nodejs Buffer from an existing content.\n     * @param {Object} data the data to pass to the constructor.\n     * @param {String} encoding the encoding to use.\n     * @return {Buffer} a new Buffer.\n     */\n    newBufferFrom: function(data, encoding) {\n        if (Buffer.from && Buffer.from !== Uint8Array.from) {\n            return Buffer.from(data, encoding);\n        } else {\n            if (typeof data === \"number\") {\n                // Safeguard for old Node.js versions. On newer versions,\n                // Buffer.from(number) / Buffer(number, encoding) already throw.\n                throw new Error(\"The \\\"data\\\" argument must not be a number\");\n            }\n            return new Buffer(data, encoding);\n        }\n    },\n    /**\n     * Create a new nodejs Buffer with the specified size.\n     * @param {Integer} size the size of the buffer.\n     * @return {Buffer} a new Buffer.\n     */\n    allocBuffer: function (size) {\n        if (Buffer.alloc) {\n            return Buffer.alloc(size);\n        } else {\n            var buf = new Buffer(size);\n            buf.fill(0);\n            return buf;\n        }\n    },\n    /**\n     * Find out if an object is a Buffer.\n     * @param {Object} b the object to test.\n     * @return {Boolean} true if the object is a Buffer, false otherwise.\n     */\n    isBuffer : function(b){\n        return Buffer.isBuffer(b);\n    },\n\n    isStream : function (obj) {\n        return obj &&\n            typeof obj.on === \"function\" &&\n            typeof obj.pause === \"function\" &&\n            typeof obj.resume === \"function\";\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/nodejsUtils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/nodejs/NodejsStreamInputAdapter.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/nodejs/NodejsStreamInputAdapter.js ***!
  \***************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\nvar GenericWorker = __webpack_require__(/*! ../stream/GenericWorker */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/GenericWorker.js\");\n\n/**\n * A worker that use a nodejs stream as source.\n * @constructor\n * @param {String} filename the name of the file entry for this stream.\n * @param {Readable} stream the nodejs stream.\n */\nfunction NodejsStreamInputAdapter(filename, stream) {\n    GenericWorker.call(this, \"Nodejs stream input adapter for \" + filename);\n    this._upstreamEnded = false;\n    this._bindStream(stream);\n}\n\nutils.inherits(NodejsStreamInputAdapter, GenericWorker);\n\n/**\n * Prepare the stream and bind the callbacks on it.\n * Do this ASAP on node 0.10 ! A lazy binding doesn't always work.\n * @param {Stream} stream the nodejs stream to use.\n */\nNodejsStreamInputAdapter.prototype._bindStream = function (stream) {\n    var self = this;\n    this._stream = stream;\n    stream.pause();\n    stream\n        .on(\"data\", function (chunk) {\n            self.push({\n                data: chunk,\n                meta : {\n                    percent : 0\n                }\n            });\n        })\n        .on(\"error\", function (e) {\n            if(self.isPaused) {\n                this.generatedError = e;\n            } else {\n                self.error(e);\n            }\n        })\n        .on(\"end\", function () {\n            if(self.isPaused) {\n                self._upstreamEnded = true;\n            } else {\n                self.end();\n            }\n        });\n};\nNodejsStreamInputAdapter.prototype.pause = function () {\n    if(!GenericWorker.prototype.pause.call(this)) {\n        return false;\n    }\n    this._stream.pause();\n    return true;\n};\nNodejsStreamInputAdapter.prototype.resume = function () {\n    if(!GenericWorker.prototype.resume.call(this)) {\n        return false;\n    }\n\n    if(this._upstreamEnded) {\n        this.end();\n    } else {\n        this._stream.resume();\n    }\n\n    return true;\n};\n\nmodule.exports = NodejsStreamInputAdapter;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/nodejs/NodejsStreamInputAdapter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/nodejs/NodejsStreamOutputAdapter.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/nodejs/NodejsStreamOutputAdapter.js ***!
  \****************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar Readable = (__webpack_require__(/*! readable-stream */ \"(ssr)/./node_modules/.pnpm/readable-stream@2.3.8/node_modules/readable-stream/readable.js\").Readable);\n\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\nutils.inherits(NodejsStreamOutputAdapter, Readable);\n\n/**\n* A nodejs stream using a worker as source.\n* @see the SourceWrapper in http://nodejs.org/api/stream.html\n* @constructor\n* @param {StreamHelper} helper the helper wrapping the worker\n* @param {Object} options the nodejs stream options\n* @param {Function} updateCb the update callback.\n*/\nfunction NodejsStreamOutputAdapter(helper, options, updateCb) {\n    Readable.call(this, options);\n    this._helper = helper;\n\n    var self = this;\n    helper.on(\"data\", function (data, meta) {\n        if (!self.push(data)) {\n            self._helper.pause();\n        }\n        if(updateCb) {\n            updateCb(meta);\n        }\n    })\n        .on(\"error\", function(e) {\n            self.emit(\"error\", e);\n        })\n        .on(\"end\", function () {\n            self.push(null);\n        });\n}\n\n\nNodejsStreamOutputAdapter.prototype._read = function() {\n    this._helper.resume();\n};\n\nmodule.exports = NodejsStreamOutputAdapter;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/nodejs/NodejsStreamOutputAdapter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/object.js":
/*!**************************************************************************!*\
  !*** ./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/object.js ***!
  \**************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar utf8 = __webpack_require__(/*! ./utf8 */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utf8.js\");\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\nvar GenericWorker = __webpack_require__(/*! ./stream/GenericWorker */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/GenericWorker.js\");\nvar StreamHelper = __webpack_require__(/*! ./stream/StreamHelper */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/StreamHelper.js\");\nvar defaults = __webpack_require__(/*! ./defaults */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/defaults.js\");\nvar CompressedObject = __webpack_require__(/*! ./compressedObject */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/compressedObject.js\");\nvar ZipObject = __webpack_require__(/*! ./zipObject */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/zipObject.js\");\nvar generate = __webpack_require__(/*! ./generate */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/generate/index.js\");\nvar nodejsUtils = __webpack_require__(/*! ./nodejsUtils */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/nodejsUtils.js\");\nvar NodejsStreamInputAdapter = __webpack_require__(/*! ./nodejs/NodejsStreamInputAdapter */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/nodejs/NodejsStreamInputAdapter.js\");\n\n\n/**\n * Add a file in the current folder.\n * @private\n * @param {string} name the name of the file\n * @param {String|ArrayBuffer|Uint8Array|Buffer} data the data of the file\n * @param {Object} originalOptions the options of the file\n * @return {Object} the new file.\n */\nvar fileAdd = function(name, data, originalOptions) {\n    // be sure sub folders exist\n    var dataType = utils.getTypeOf(data),\n        parent;\n\n\n    /*\n     * Correct options.\n     */\n\n    var o = utils.extend(originalOptions || {}, defaults);\n    o.date = o.date || new Date();\n    if (o.compression !== null) {\n        o.compression = o.compression.toUpperCase();\n    }\n\n    if (typeof o.unixPermissions === \"string\") {\n        o.unixPermissions = parseInt(o.unixPermissions, 8);\n    }\n\n    // UNX_IFDIR  0040000 see zipinfo.c\n    if (o.unixPermissions && (o.unixPermissions & 0x4000)) {\n        o.dir = true;\n    }\n    // Bit 4    Directory\n    if (o.dosPermissions && (o.dosPermissions & 0x0010)) {\n        o.dir = true;\n    }\n\n    if (o.dir) {\n        name = forceTrailingSlash(name);\n    }\n    if (o.createFolders && (parent = parentFolder(name))) {\n        folderAdd.call(this, parent, true);\n    }\n\n    var isUnicodeString = dataType === \"string\" && o.binary === false && o.base64 === false;\n    if (!originalOptions || typeof originalOptions.binary === \"undefined\") {\n        o.binary = !isUnicodeString;\n    }\n\n\n    var isCompressedEmpty = (data instanceof CompressedObject) && data.uncompressedSize === 0;\n\n    if (isCompressedEmpty || o.dir || !data || data.length === 0) {\n        o.base64 = false;\n        o.binary = true;\n        data = \"\";\n        o.compression = \"STORE\";\n        dataType = \"string\";\n    }\n\n    /*\n     * Convert content to fit.\n     */\n\n    var zipObjectContent = null;\n    if (data instanceof CompressedObject || data instanceof GenericWorker) {\n        zipObjectContent = data;\n    } else if (nodejsUtils.isNode && nodejsUtils.isStream(data)) {\n        zipObjectContent = new NodejsStreamInputAdapter(name, data);\n    } else {\n        zipObjectContent = utils.prepareContent(name, data, o.binary, o.optimizedBinaryString, o.base64);\n    }\n\n    var object = new ZipObject(name, zipObjectContent, o);\n    this.files[name] = object;\n    /*\n    TODO: we can't throw an exception because we have async promises\n    (we can have a promise of a Date() for example) but returning a\n    promise is useless because file(name, data) returns the JSZip\n    object for chaining. Should we break that to allow the user\n    to catch the error ?\n\n    return external.Promise.resolve(zipObjectContent)\n    .then(function () {\n        return object;\n    });\n    */\n};\n\n/**\n * Find the parent folder of the path.\n * @private\n * @param {string} path the path to use\n * @return {string} the parent folder, or \"\"\n */\nvar parentFolder = function (path) {\n    if (path.slice(-1) === \"/\") {\n        path = path.substring(0, path.length - 1);\n    }\n    var lastSlash = path.lastIndexOf(\"/\");\n    return (lastSlash > 0) ? path.substring(0, lastSlash) : \"\";\n};\n\n/**\n * Returns the path with a slash at the end.\n * @private\n * @param {String} path the path to check.\n * @return {String} the path with a trailing slash.\n */\nvar forceTrailingSlash = function(path) {\n    // Check the name ends with a /\n    if (path.slice(-1) !== \"/\") {\n        path += \"/\"; // IE doesn't like substr(-1)\n    }\n    return path;\n};\n\n/**\n * Add a (sub) folder in the current folder.\n * @private\n * @param {string} name the folder's name\n * @param {boolean=} [createFolders] If true, automatically create sub\n *  folders. Defaults to false.\n * @return {Object} the new folder.\n */\nvar folderAdd = function(name, createFolders) {\n    createFolders = (typeof createFolders !== \"undefined\") ? createFolders : defaults.createFolders;\n\n    name = forceTrailingSlash(name);\n\n    // Does this folder already exist?\n    if (!this.files[name]) {\n        fileAdd.call(this, name, null, {\n            dir: true,\n            createFolders: createFolders\n        });\n    }\n    return this.files[name];\n};\n\n/**\n* Cross-window, cross-Node-context regular expression detection\n* @param  {Object}  object Anything\n* @return {Boolean}        true if the object is a regular expression,\n* false otherwise\n*/\nfunction isRegExp(object) {\n    return Object.prototype.toString.call(object) === \"[object RegExp]\";\n}\n\n// return the actual prototype of JSZip\nvar out = {\n    /**\n     * @see loadAsync\n     */\n    load: function() {\n        throw new Error(\"This method has been removed in JSZip 3.0, please check the upgrade guide.\");\n    },\n\n\n    /**\n     * Call a callback function for each entry at this folder level.\n     * @param {Function} cb the callback function:\n     * function (relativePath, file) {...}\n     * It takes 2 arguments : the relative path and the file.\n     */\n    forEach: function(cb) {\n        var filename, relativePath, file;\n        // ignore warning about unwanted properties because this.files is a null prototype object\n        /* eslint-disable-next-line guard-for-in */\n        for (filename in this.files) {\n            file = this.files[filename];\n            relativePath = filename.slice(this.root.length, filename.length);\n            if (relativePath && filename.slice(0, this.root.length) === this.root) { // the file is in the current root\n                cb(relativePath, file); // TODO reverse the parameters ? need to be clean AND consistent with the filter search fn...\n            }\n        }\n    },\n\n    /**\n     * Filter nested files/folders with the specified function.\n     * @param {Function} search the predicate to use :\n     * function (relativePath, file) {...}\n     * It takes 2 arguments : the relative path and the file.\n     * @return {Array} An array of matching elements.\n     */\n    filter: function(search) {\n        var result = [];\n        this.forEach(function (relativePath, entry) {\n            if (search(relativePath, entry)) { // the file matches the function\n                result.push(entry);\n            }\n\n        });\n        return result;\n    },\n\n    /**\n     * Add a file to the zip file, or search a file.\n     * @param   {string|RegExp} name The name of the file to add (if data is defined),\n     * the name of the file to find (if no data) or a regex to match files.\n     * @param   {String|ArrayBuffer|Uint8Array|Buffer} data  The file data, either raw or base64 encoded\n     * @param   {Object} o     File options\n     * @return  {JSZip|Object|Array} this JSZip object (when adding a file),\n     * a file (when searching by string) or an array of files (when searching by regex).\n     */\n    file: function(name, data, o) {\n        if (arguments.length === 1) {\n            if (isRegExp(name)) {\n                var regexp = name;\n                return this.filter(function(relativePath, file) {\n                    return !file.dir && regexp.test(relativePath);\n                });\n            }\n            else { // text\n                var obj = this.files[this.root + name];\n                if (obj && !obj.dir) {\n                    return obj;\n                } else {\n                    return null;\n                }\n            }\n        }\n        else { // more than one argument : we have data !\n            name = this.root + name;\n            fileAdd.call(this, name, data, o);\n        }\n        return this;\n    },\n\n    /**\n     * Add a directory to the zip file, or search.\n     * @param   {String|RegExp} arg The name of the directory to add, or a regex to search folders.\n     * @return  {JSZip} an object with the new directory as the root, or an array containing matching folders.\n     */\n    folder: function(arg) {\n        if (!arg) {\n            return this;\n        }\n\n        if (isRegExp(arg)) {\n            return this.filter(function(relativePath, file) {\n                return file.dir && arg.test(relativePath);\n            });\n        }\n\n        // else, name is a new folder\n        var name = this.root + arg;\n        var newFolder = folderAdd.call(this, name);\n\n        // Allow chaining by returning a new object with this folder as the root\n        var ret = this.clone();\n        ret.root = newFolder.name;\n        return ret;\n    },\n\n    /**\n     * Delete a file, or a directory and all sub-files, from the zip\n     * @param {string} name the name of the file to delete\n     * @return {JSZip} this JSZip object\n     */\n    remove: function(name) {\n        name = this.root + name;\n        var file = this.files[name];\n        if (!file) {\n            // Look for any folders\n            if (name.slice(-1) !== \"/\") {\n                name += \"/\";\n            }\n            file = this.files[name];\n        }\n\n        if (file && !file.dir) {\n            // file\n            delete this.files[name];\n        } else {\n            // maybe a folder, delete recursively\n            var kids = this.filter(function(relativePath, file) {\n                return file.name.slice(0, name.length) === name;\n            });\n            for (var i = 0; i < kids.length; i++) {\n                delete this.files[kids[i].name];\n            }\n        }\n\n        return this;\n    },\n\n    /**\n     * @deprecated This method has been removed in JSZip 3.0, please check the upgrade guide.\n     */\n    generate: function() {\n        throw new Error(\"This method has been removed in JSZip 3.0, please check the upgrade guide.\");\n    },\n\n    /**\n     * Generate the complete zip file as an internal stream.\n     * @param {Object} options the options to generate the zip file :\n     * - compression, \"STORE\" by default.\n     * - type, \"base64\" by default. Values are : string, base64, uint8array, arraybuffer, blob.\n     * @return {StreamHelper} the streamed zip file.\n     */\n    generateInternalStream: function(options) {\n        var worker, opts = {};\n        try {\n            opts = utils.extend(options || {}, {\n                streamFiles: false,\n                compression: \"STORE\",\n                compressionOptions : null,\n                type: \"\",\n                platform: \"DOS\",\n                comment: null,\n                mimeType: \"application/zip\",\n                encodeFileName: utf8.utf8encode\n            });\n\n            opts.type = opts.type.toLowerCase();\n            opts.compression = opts.compression.toUpperCase();\n\n            // \"binarystring\" is preferred but the internals use \"string\".\n            if(opts.type === \"binarystring\") {\n                opts.type = \"string\";\n            }\n\n            if (!opts.type) {\n                throw new Error(\"No output type specified.\");\n            }\n\n            utils.checkSupport(opts.type);\n\n            // accept nodejs `process.platform`\n            if(\n                opts.platform === \"darwin\" ||\n                opts.platform === \"freebsd\" ||\n                opts.platform === \"linux\" ||\n                opts.platform === \"sunos\"\n            ) {\n                opts.platform = \"UNIX\";\n            }\n            if (opts.platform === \"win32\") {\n                opts.platform = \"DOS\";\n            }\n\n            var comment = opts.comment || this.comment || \"\";\n            worker = generate.generateWorker(this, opts, comment);\n        } catch (e) {\n            worker = new GenericWorker(\"error\");\n            worker.error(e);\n        }\n        return new StreamHelper(worker, opts.type || \"string\", opts.mimeType);\n    },\n    /**\n     * Generate the complete zip file asynchronously.\n     * @see generateInternalStream\n     */\n    generateAsync: function(options, onUpdate) {\n        return this.generateInternalStream(options).accumulate(onUpdate);\n    },\n    /**\n     * Generate the complete zip file asynchronously.\n     * @see generateInternalStream\n     */\n    generateNodeStream: function(options, onUpdate) {\n        options = options || {};\n        if (!options.type) {\n            options.type = \"nodebuffer\";\n        }\n        return this.generateInternalStream(options).toNodejsStream(onUpdate);\n    }\n};\nmodule.exports = out;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/object.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/ArrayReader.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/ArrayReader.js ***!
  \**************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar DataReader = __webpack_require__(/*! ./DataReader */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/DataReader.js\");\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\n\nfunction ArrayReader(data) {\n    DataReader.call(this, data);\n    for(var i = 0; i < this.data.length; i++) {\n        data[i] = data[i] & 0xFF;\n    }\n}\nutils.inherits(ArrayReader, DataReader);\n/**\n * @see DataReader.byteAt\n */\nArrayReader.prototype.byteAt = function(i) {\n    return this.data[this.zero + i];\n};\n/**\n * @see DataReader.lastIndexOfSignature\n */\nArrayReader.prototype.lastIndexOfSignature = function(sig) {\n    var sig0 = sig.charCodeAt(0),\n        sig1 = sig.charCodeAt(1),\n        sig2 = sig.charCodeAt(2),\n        sig3 = sig.charCodeAt(3);\n    for (var i = this.length - 4; i >= 0; --i) {\n        if (this.data[i] === sig0 && this.data[i + 1] === sig1 && this.data[i + 2] === sig2 && this.data[i + 3] === sig3) {\n            return i - this.zero;\n        }\n    }\n\n    return -1;\n};\n/**\n * @see DataReader.readAndCheckSignature\n */\nArrayReader.prototype.readAndCheckSignature = function (sig) {\n    var sig0 = sig.charCodeAt(0),\n        sig1 = sig.charCodeAt(1),\n        sig2 = sig.charCodeAt(2),\n        sig3 = sig.charCodeAt(3),\n        data = this.readData(4);\n    return sig0 === data[0] && sig1 === data[1] && sig2 === data[2] && sig3 === data[3];\n};\n/**\n * @see DataReader.readData\n */\nArrayReader.prototype.readData = function(size) {\n    this.checkOffset(size);\n    if(size === 0) {\n        return [];\n    }\n    var result = this.data.slice(this.zero + this.index, this.zero + this.index + size);\n    this.index += size;\n    return result;\n};\nmodule.exports = ArrayReader;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/ArrayReader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/DataReader.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/DataReader.js ***!
  \*************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\n\nfunction DataReader(data) {\n    this.data = data; // type : see implementation\n    this.length = data.length;\n    this.index = 0;\n    this.zero = 0;\n}\nDataReader.prototype = {\n    /**\n     * Check that the offset will not go too far.\n     * @param {string} offset the additional offset to check.\n     * @throws {Error} an Error if the offset is out of bounds.\n     */\n    checkOffset: function(offset) {\n        this.checkIndex(this.index + offset);\n    },\n    /**\n     * Check that the specified index will not be too far.\n     * @param {string} newIndex the index to check.\n     * @throws {Error} an Error if the index is out of bounds.\n     */\n    checkIndex: function(newIndex) {\n        if (this.length < this.zero + newIndex || newIndex < 0) {\n            throw new Error(\"End of data reached (data length = \" + this.length + \", asked index = \" + (newIndex) + \"). Corrupted zip ?\");\n        }\n    },\n    /**\n     * Change the index.\n     * @param {number} newIndex The new index.\n     * @throws {Error} if the new index is out of the data.\n     */\n    setIndex: function(newIndex) {\n        this.checkIndex(newIndex);\n        this.index = newIndex;\n    },\n    /**\n     * Skip the next n bytes.\n     * @param {number} n the number of bytes to skip.\n     * @throws {Error} if the new index is out of the data.\n     */\n    skip: function(n) {\n        this.setIndex(this.index + n);\n    },\n    /**\n     * Get the byte at the specified index.\n     * @param {number} i the index to use.\n     * @return {number} a byte.\n     */\n    byteAt: function() {\n        // see implementations\n    },\n    /**\n     * Get the next number with a given byte size.\n     * @param {number} size the number of bytes to read.\n     * @return {number} the corresponding number.\n     */\n    readInt: function(size) {\n        var result = 0,\n            i;\n        this.checkOffset(size);\n        for (i = this.index + size - 1; i >= this.index; i--) {\n            result = (result << 8) + this.byteAt(i);\n        }\n        this.index += size;\n        return result;\n    },\n    /**\n     * Get the next string with a given byte size.\n     * @param {number} size the number of bytes to read.\n     * @return {string} the corresponding string.\n     */\n    readString: function(size) {\n        return utils.transformTo(\"string\", this.readData(size));\n    },\n    /**\n     * Get raw data without conversion, <size> bytes.\n     * @param {number} size the number of bytes to read.\n     * @return {Object} the raw data, implementation specific.\n     */\n    readData: function() {\n        // see implementations\n    },\n    /**\n     * Find the last occurrence of a zip signature (4 bytes).\n     * @param {string} sig the signature to find.\n     * @return {number} the index of the last occurrence, -1 if not found.\n     */\n    lastIndexOfSignature: function() {\n        // see implementations\n    },\n    /**\n     * Read the signature (4 bytes) at the current position and compare it with sig.\n     * @param {string} sig the expected signature\n     * @return {boolean} true if the signature matches, false otherwise.\n     */\n    readAndCheckSignature: function() {\n        // see implementations\n    },\n    /**\n     * Get the next date.\n     * @return {Date} the date.\n     */\n    readDate: function() {\n        var dostime = this.readInt(4);\n        return new Date(Date.UTC(\n            ((dostime >> 25) & 0x7f) + 1980, // year\n            ((dostime >> 21) & 0x0f) - 1, // month\n            (dostime >> 16) & 0x1f, // day\n            (dostime >> 11) & 0x1f, // hour\n            (dostime >> 5) & 0x3f, // minute\n            (dostime & 0x1f) << 1)); // second\n    }\n};\nmodule.exports = DataReader;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/DataReader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/NodeBufferReader.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/NodeBufferReader.js ***!
  \*******************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar Uint8ArrayReader = __webpack_require__(/*! ./Uint8ArrayReader */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/Uint8ArrayReader.js\");\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\n\nfunction NodeBufferReader(data) {\n    Uint8ArrayReader.call(this, data);\n}\nutils.inherits(NodeBufferReader, Uint8ArrayReader);\n\n/**\n * @see DataReader.readData\n */\nNodeBufferReader.prototype.readData = function(size) {\n    this.checkOffset(size);\n    var result = this.data.slice(this.zero + this.index, this.zero + this.index + size);\n    this.index += size;\n    return result;\n};\nmodule.exports = NodeBufferReader;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vanN6aXBAMy4xMC4xL25vZGVfbW9kdWxlcy9qc3ppcC9saWIvcmVhZGVyL05vZGVCdWZmZXJSZWFkZXIuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYix1QkFBdUIsbUJBQU8sQ0FBQyxxSEFBb0I7QUFDbkQsWUFBWSxtQkFBTyxDQUFDLHlGQUFVOztBQUU5QjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL0B3ZS1kZXYvbmV4dC8uL25vZGVfbW9kdWxlcy8ucG5wbS9qc3ppcEAzLjEwLjEvbm9kZV9tb2R1bGVzL2pzemlwL2xpYi9yZWFkZXIvTm9kZUJ1ZmZlclJlYWRlci5qcz85N2MwIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xudmFyIFVpbnQ4QXJyYXlSZWFkZXIgPSByZXF1aXJlKFwiLi9VaW50OEFycmF5UmVhZGVyXCIpO1xudmFyIHV0aWxzID0gcmVxdWlyZShcIi4uL3V0aWxzXCIpO1xuXG5mdW5jdGlvbiBOb2RlQnVmZmVyUmVhZGVyKGRhdGEpIHtcbiAgICBVaW50OEFycmF5UmVhZGVyLmNhbGwodGhpcywgZGF0YSk7XG59XG51dGlscy5pbmhlcml0cyhOb2RlQnVmZmVyUmVhZGVyLCBVaW50OEFycmF5UmVhZGVyKTtcblxuLyoqXG4gKiBAc2VlIERhdGFSZWFkZXIucmVhZERhdGFcbiAqL1xuTm9kZUJ1ZmZlclJlYWRlci5wcm90b3R5cGUucmVhZERhdGEgPSBmdW5jdGlvbihzaXplKSB7XG4gICAgdGhpcy5jaGVja09mZnNldChzaXplKTtcbiAgICB2YXIgcmVzdWx0ID0gdGhpcy5kYXRhLnNsaWNlKHRoaXMuemVybyArIHRoaXMuaW5kZXgsIHRoaXMuemVybyArIHRoaXMuaW5kZXggKyBzaXplKTtcbiAgICB0aGlzLmluZGV4ICs9IHNpemU7XG4gICAgcmV0dXJuIHJlc3VsdDtcbn07XG5tb2R1bGUuZXhwb3J0cyA9IE5vZGVCdWZmZXJSZWFkZXI7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/NodeBufferReader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/StringReader.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/StringReader.js ***!
  \***************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar DataReader = __webpack_require__(/*! ./DataReader */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/DataReader.js\");\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\n\nfunction StringReader(data) {\n    DataReader.call(this, data);\n}\nutils.inherits(StringReader, DataReader);\n/**\n * @see DataReader.byteAt\n */\nStringReader.prototype.byteAt = function(i) {\n    return this.data.charCodeAt(this.zero + i);\n};\n/**\n * @see DataReader.lastIndexOfSignature\n */\nStringReader.prototype.lastIndexOfSignature = function(sig) {\n    return this.data.lastIndexOf(sig) - this.zero;\n};\n/**\n * @see DataReader.readAndCheckSignature\n */\nStringReader.prototype.readAndCheckSignature = function (sig) {\n    var data = this.readData(4);\n    return sig === data;\n};\n/**\n * @see DataReader.readData\n */\nStringReader.prototype.readData = function(size) {\n    this.checkOffset(size);\n    // this will work because the constructor applied the \"& 0xff\" mask.\n    var result = this.data.slice(this.zero + this.index, this.zero + this.index + size);\n    this.index += size;\n    return result;\n};\nmodule.exports = StringReader;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/StringReader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/Uint8ArrayReader.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/Uint8ArrayReader.js ***!
  \*******************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar ArrayReader = __webpack_require__(/*! ./ArrayReader */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/ArrayReader.js\");\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\n\nfunction Uint8ArrayReader(data) {\n    ArrayReader.call(this, data);\n}\nutils.inherits(Uint8ArrayReader, ArrayReader);\n/**\n * @see DataReader.readData\n */\nUint8ArrayReader.prototype.readData = function(size) {\n    this.checkOffset(size);\n    if(size === 0) {\n        // in IE10, when using subarray(idx, idx), we get the array [0x00] instead of [].\n        return new Uint8Array(0);\n    }\n    var result = this.data.subarray(this.zero + this.index, this.zero + this.index + size);\n    this.index += size;\n    return result;\n};\nmodule.exports = Uint8ArrayReader;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vanN6aXBAMy4xMC4xL25vZGVfbW9kdWxlcy9qc3ppcC9saWIvcmVhZGVyL1VpbnQ4QXJyYXlSZWFkZXIuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYixrQkFBa0IsbUJBQU8sQ0FBQywyR0FBZTtBQUN6QyxZQUFZLG1CQUFPLENBQUMseUZBQVU7O0FBRTlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL0B3ZS1kZXYvbmV4dC8uL25vZGVfbW9kdWxlcy8ucG5wbS9qc3ppcEAzLjEwLjEvbm9kZV9tb2R1bGVzL2pzemlwL2xpYi9yZWFkZXIvVWludDhBcnJheVJlYWRlci5qcz8zMDg1Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xudmFyIEFycmF5UmVhZGVyID0gcmVxdWlyZShcIi4vQXJyYXlSZWFkZXJcIik7XG52YXIgdXRpbHMgPSByZXF1aXJlKFwiLi4vdXRpbHNcIik7XG5cbmZ1bmN0aW9uIFVpbnQ4QXJyYXlSZWFkZXIoZGF0YSkge1xuICAgIEFycmF5UmVhZGVyLmNhbGwodGhpcywgZGF0YSk7XG59XG51dGlscy5pbmhlcml0cyhVaW50OEFycmF5UmVhZGVyLCBBcnJheVJlYWRlcik7XG4vKipcbiAqIEBzZWUgRGF0YVJlYWRlci5yZWFkRGF0YVxuICovXG5VaW50OEFycmF5UmVhZGVyLnByb3RvdHlwZS5yZWFkRGF0YSA9IGZ1bmN0aW9uKHNpemUpIHtcbiAgICB0aGlzLmNoZWNrT2Zmc2V0KHNpemUpO1xuICAgIGlmKHNpemUgPT09IDApIHtcbiAgICAgICAgLy8gaW4gSUUxMCwgd2hlbiB1c2luZyBzdWJhcnJheShpZHgsIGlkeCksIHdlIGdldCB0aGUgYXJyYXkgWzB4MDBdIGluc3RlYWQgb2YgW10uXG4gICAgICAgIHJldHVybiBuZXcgVWludDhBcnJheSgwKTtcbiAgICB9XG4gICAgdmFyIHJlc3VsdCA9IHRoaXMuZGF0YS5zdWJhcnJheSh0aGlzLnplcm8gKyB0aGlzLmluZGV4LCB0aGlzLnplcm8gKyB0aGlzLmluZGV4ICsgc2l6ZSk7XG4gICAgdGhpcy5pbmRleCArPSBzaXplO1xuICAgIHJldHVybiByZXN1bHQ7XG59O1xubW9kdWxlLmV4cG9ydHMgPSBVaW50OEFycmF5UmVhZGVyO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/Uint8ArrayReader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/readerFor.js":
/*!************************************************************************************!*\
  !*** ./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/readerFor.js ***!
  \************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\nvar support = __webpack_require__(/*! ../support */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/support.js\");\nvar ArrayReader = __webpack_require__(/*! ./ArrayReader */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/ArrayReader.js\");\nvar StringReader = __webpack_require__(/*! ./StringReader */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/StringReader.js\");\nvar NodeBufferReader = __webpack_require__(/*! ./NodeBufferReader */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/NodeBufferReader.js\");\nvar Uint8ArrayReader = __webpack_require__(/*! ./Uint8ArrayReader */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/Uint8ArrayReader.js\");\n\n/**\n * Create a reader adapted to the data.\n * @param {String|ArrayBuffer|Uint8Array|Buffer} data the data to read.\n * @return {DataReader} the data reader.\n */\nmodule.exports = function (data) {\n    var type = utils.getTypeOf(data);\n    utils.checkSupport(type);\n    if (type === \"string\" && !support.uint8array) {\n        return new StringReader(data);\n    }\n    if (type === \"nodebuffer\") {\n        return new NodeBufferReader(data);\n    }\n    if (support.uint8array) {\n        return new Uint8ArrayReader(utils.transformTo(\"uint8array\", data));\n    }\n    return new ArrayReader(utils.transformTo(\"array\", data));\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/readerFor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/signature.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/signature.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nexports.LOCAL_FILE_HEADER = \"PK\\x03\\x04\";\nexports.CENTRAL_FILE_HEADER = \"PK\\x01\\x02\";\nexports.CENTRAL_DIRECTORY_END = \"PK\\x05\\x06\";\nexports.ZIP64_CENTRAL_DIRECTORY_LOCATOR = \"PK\\x06\\x07\";\nexports.ZIP64_CENTRAL_DIRECTORY_END = \"PK\\x06\\x06\";\nexports.DATA_DESCRIPTOR = \"PK\\x07\\x08\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vanN6aXBAMy4xMC4xL25vZGVfbW9kdWxlcy9qc3ppcC9saWIvc2lnbmF0dXJlLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IseUJBQXlCO0FBQ3pCLDJCQUEyQjtBQUMzQiw2QkFBNkI7QUFDN0IsdUNBQXVDO0FBQ3ZDLG1DQUFtQztBQUNuQyx1QkFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ad2UtZGV2L25leHQvLi9ub2RlX21vZHVsZXMvLnBucG0vanN6aXBAMy4xMC4xL25vZGVfbW9kdWxlcy9qc3ppcC9saWIvc2lnbmF0dXJlLmpzP2IwNWIiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5leHBvcnRzLkxPQ0FMX0ZJTEVfSEVBREVSID0gXCJQS1xceDAzXFx4MDRcIjtcbmV4cG9ydHMuQ0VOVFJBTF9GSUxFX0hFQURFUiA9IFwiUEtcXHgwMVxceDAyXCI7XG5leHBvcnRzLkNFTlRSQUxfRElSRUNUT1JZX0VORCA9IFwiUEtcXHgwNVxceDA2XCI7XG5leHBvcnRzLlpJUDY0X0NFTlRSQUxfRElSRUNUT1JZX0xPQ0FUT1IgPSBcIlBLXFx4MDZcXHgwN1wiO1xuZXhwb3J0cy5aSVA2NF9DRU5UUkFMX0RJUkVDVE9SWV9FTkQgPSBcIlBLXFx4MDZcXHgwNlwiO1xuZXhwb3J0cy5EQVRBX0RFU0NSSVBUT1IgPSBcIlBLXFx4MDdcXHgwOFwiO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/signature.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/ConvertWorker.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/ConvertWorker.js ***!
  \****************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar GenericWorker = __webpack_require__(/*! ./GenericWorker */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/GenericWorker.js\");\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\n\n/**\n * A worker which convert chunks to a specified type.\n * @constructor\n * @param {String} destType the destination type.\n */\nfunction ConvertWorker(destType) {\n    GenericWorker.call(this, \"ConvertWorker to \" + destType);\n    this.destType = destType;\n}\nutils.inherits(ConvertWorker, GenericWorker);\n\n/**\n * @see GenericWorker.processChunk\n */\nConvertWorker.prototype.processChunk = function (chunk) {\n    this.push({\n        data : utils.transformTo(this.destType, chunk.data),\n        meta : chunk.meta\n    });\n};\nmodule.exports = ConvertWorker;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vanN6aXBAMy4xMC4xL25vZGVfbW9kdWxlcy9qc3ppcC9saWIvc3RyZWFtL0NvbnZlcnRXb3JrZXIuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsb0JBQW9CLG1CQUFPLENBQUMsK0dBQWlCO0FBQzdDLFlBQVksbUJBQU8sQ0FBQyx5RkFBVTs7QUFFOUI7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHdlLWRldi9uZXh0Ly4vbm9kZV9tb2R1bGVzLy5wbnBtL2pzemlwQDMuMTAuMS9ub2RlX21vZHVsZXMvanN6aXAvbGliL3N0cmVhbS9Db252ZXJ0V29ya2VyLmpzPzY0N2UiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbnZhciBHZW5lcmljV29ya2VyID0gcmVxdWlyZShcIi4vR2VuZXJpY1dvcmtlclwiKTtcbnZhciB1dGlscyA9IHJlcXVpcmUoXCIuLi91dGlsc1wiKTtcblxuLyoqXG4gKiBBIHdvcmtlciB3aGljaCBjb252ZXJ0IGNodW5rcyB0byBhIHNwZWNpZmllZCB0eXBlLlxuICogQGNvbnN0cnVjdG9yXG4gKiBAcGFyYW0ge1N0cmluZ30gZGVzdFR5cGUgdGhlIGRlc3RpbmF0aW9uIHR5cGUuXG4gKi9cbmZ1bmN0aW9uIENvbnZlcnRXb3JrZXIoZGVzdFR5cGUpIHtcbiAgICBHZW5lcmljV29ya2VyLmNhbGwodGhpcywgXCJDb252ZXJ0V29ya2VyIHRvIFwiICsgZGVzdFR5cGUpO1xuICAgIHRoaXMuZGVzdFR5cGUgPSBkZXN0VHlwZTtcbn1cbnV0aWxzLmluaGVyaXRzKENvbnZlcnRXb3JrZXIsIEdlbmVyaWNXb3JrZXIpO1xuXG4vKipcbiAqIEBzZWUgR2VuZXJpY1dvcmtlci5wcm9jZXNzQ2h1bmtcbiAqL1xuQ29udmVydFdvcmtlci5wcm90b3R5cGUucHJvY2Vzc0NodW5rID0gZnVuY3Rpb24gKGNodW5rKSB7XG4gICAgdGhpcy5wdXNoKHtcbiAgICAgICAgZGF0YSA6IHV0aWxzLnRyYW5zZm9ybVRvKHRoaXMuZGVzdFR5cGUsIGNodW5rLmRhdGEpLFxuICAgICAgICBtZXRhIDogY2h1bmsubWV0YVxuICAgIH0pO1xufTtcbm1vZHVsZS5leHBvcnRzID0gQ29udmVydFdvcmtlcjtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/ConvertWorker.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/Crc32Probe.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/Crc32Probe.js ***!
  \*************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar GenericWorker = __webpack_require__(/*! ./GenericWorker */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/GenericWorker.js\");\nvar crc32 = __webpack_require__(/*! ../crc32 */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/crc32.js\");\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\n\n/**\n * A worker which calculate the crc32 of the data flowing through.\n * @constructor\n */\nfunction Crc32Probe() {\n    GenericWorker.call(this, \"Crc32Probe\");\n    this.withStreamInfo(\"crc32\", 0);\n}\nutils.inherits(Crc32Probe, GenericWorker);\n\n/**\n * @see GenericWorker.processChunk\n */\nCrc32Probe.prototype.processChunk = function (chunk) {\n    this.streamInfo.crc32 = crc32(chunk.data, this.streamInfo.crc32 || 0);\n    this.push(chunk);\n};\nmodule.exports = Crc32Probe;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vanN6aXBAMy4xMC4xL25vZGVfbW9kdWxlcy9qc3ppcC9saWIvc3RyZWFtL0NyYzMyUHJvYmUuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsb0JBQW9CLG1CQUFPLENBQUMsK0dBQWlCO0FBQzdDLFlBQVksbUJBQU8sQ0FBQyx5RkFBVTtBQUM5QixZQUFZLG1CQUFPLENBQUMseUZBQVU7O0FBRTlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHdlLWRldi9uZXh0Ly4vbm9kZV9tb2R1bGVzLy5wbnBtL2pzemlwQDMuMTAuMS9ub2RlX21vZHVsZXMvanN6aXAvbGliL3N0cmVhbS9DcmMzMlByb2JlLmpzPzhlMGMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbnZhciBHZW5lcmljV29ya2VyID0gcmVxdWlyZShcIi4vR2VuZXJpY1dvcmtlclwiKTtcbnZhciBjcmMzMiA9IHJlcXVpcmUoXCIuLi9jcmMzMlwiKTtcbnZhciB1dGlscyA9IHJlcXVpcmUoXCIuLi91dGlsc1wiKTtcblxuLyoqXG4gKiBBIHdvcmtlciB3aGljaCBjYWxjdWxhdGUgdGhlIGNyYzMyIG9mIHRoZSBkYXRhIGZsb3dpbmcgdGhyb3VnaC5cbiAqIEBjb25zdHJ1Y3RvclxuICovXG5mdW5jdGlvbiBDcmMzMlByb2JlKCkge1xuICAgIEdlbmVyaWNXb3JrZXIuY2FsbCh0aGlzLCBcIkNyYzMyUHJvYmVcIik7XG4gICAgdGhpcy53aXRoU3RyZWFtSW5mbyhcImNyYzMyXCIsIDApO1xufVxudXRpbHMuaW5oZXJpdHMoQ3JjMzJQcm9iZSwgR2VuZXJpY1dvcmtlcik7XG5cbi8qKlxuICogQHNlZSBHZW5lcmljV29ya2VyLnByb2Nlc3NDaHVua1xuICovXG5DcmMzMlByb2JlLnByb3RvdHlwZS5wcm9jZXNzQ2h1bmsgPSBmdW5jdGlvbiAoY2h1bmspIHtcbiAgICB0aGlzLnN0cmVhbUluZm8uY3JjMzIgPSBjcmMzMihjaHVuay5kYXRhLCB0aGlzLnN0cmVhbUluZm8uY3JjMzIgfHwgMCk7XG4gICAgdGhpcy5wdXNoKGNodW5rKTtcbn07XG5tb2R1bGUuZXhwb3J0cyA9IENyYzMyUHJvYmU7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/Crc32Probe.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/DataLengthProbe.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/DataLengthProbe.js ***!
  \******************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\nvar GenericWorker = __webpack_require__(/*! ./GenericWorker */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/GenericWorker.js\");\n\n/**\n * A worker which calculate the total length of the data flowing through.\n * @constructor\n * @param {String} propName the name used to expose the length\n */\nfunction DataLengthProbe(propName) {\n    GenericWorker.call(this, \"DataLengthProbe for \" + propName);\n    this.propName = propName;\n    this.withStreamInfo(propName, 0);\n}\nutils.inherits(DataLengthProbe, GenericWorker);\n\n/**\n * @see GenericWorker.processChunk\n */\nDataLengthProbe.prototype.processChunk = function (chunk) {\n    if(chunk) {\n        var length = this.streamInfo[this.propName] || 0;\n        this.streamInfo[this.propName] = length + chunk.data.length;\n    }\n    GenericWorker.prototype.processChunk.call(this, chunk);\n};\nmodule.exports = DataLengthProbe;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vanN6aXBAMy4xMC4xL25vZGVfbW9kdWxlcy9qc3ppcC9saWIvc3RyZWFtL0RhdGFMZW5ndGhQcm9iZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixZQUFZLG1CQUFPLENBQUMseUZBQVU7QUFDOUIsb0JBQW9CLG1CQUFPLENBQUMsK0dBQWlCOztBQUU3QztBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL0B3ZS1kZXYvbmV4dC8uL25vZGVfbW9kdWxlcy8ucG5wbS9qc3ppcEAzLjEwLjEvbm9kZV9tb2R1bGVzL2pzemlwL2xpYi9zdHJlYW0vRGF0YUxlbmd0aFByb2JlLmpzPzA0MWUiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbnZhciB1dGlscyA9IHJlcXVpcmUoXCIuLi91dGlsc1wiKTtcbnZhciBHZW5lcmljV29ya2VyID0gcmVxdWlyZShcIi4vR2VuZXJpY1dvcmtlclwiKTtcblxuLyoqXG4gKiBBIHdvcmtlciB3aGljaCBjYWxjdWxhdGUgdGhlIHRvdGFsIGxlbmd0aCBvZiB0aGUgZGF0YSBmbG93aW5nIHRocm91Z2guXG4gKiBAY29uc3RydWN0b3JcbiAqIEBwYXJhbSB7U3RyaW5nfSBwcm9wTmFtZSB0aGUgbmFtZSB1c2VkIHRvIGV4cG9zZSB0aGUgbGVuZ3RoXG4gKi9cbmZ1bmN0aW9uIERhdGFMZW5ndGhQcm9iZShwcm9wTmFtZSkge1xuICAgIEdlbmVyaWNXb3JrZXIuY2FsbCh0aGlzLCBcIkRhdGFMZW5ndGhQcm9iZSBmb3IgXCIgKyBwcm9wTmFtZSk7XG4gICAgdGhpcy5wcm9wTmFtZSA9IHByb3BOYW1lO1xuICAgIHRoaXMud2l0aFN0cmVhbUluZm8ocHJvcE5hbWUsIDApO1xufVxudXRpbHMuaW5oZXJpdHMoRGF0YUxlbmd0aFByb2JlLCBHZW5lcmljV29ya2VyKTtcblxuLyoqXG4gKiBAc2VlIEdlbmVyaWNXb3JrZXIucHJvY2Vzc0NodW5rXG4gKi9cbkRhdGFMZW5ndGhQcm9iZS5wcm90b3R5cGUucHJvY2Vzc0NodW5rID0gZnVuY3Rpb24gKGNodW5rKSB7XG4gICAgaWYoY2h1bmspIHtcbiAgICAgICAgdmFyIGxlbmd0aCA9IHRoaXMuc3RyZWFtSW5mb1t0aGlzLnByb3BOYW1lXSB8fCAwO1xuICAgICAgICB0aGlzLnN0cmVhbUluZm9bdGhpcy5wcm9wTmFtZV0gPSBsZW5ndGggKyBjaHVuay5kYXRhLmxlbmd0aDtcbiAgICB9XG4gICAgR2VuZXJpY1dvcmtlci5wcm90b3R5cGUucHJvY2Vzc0NodW5rLmNhbGwodGhpcywgY2h1bmspO1xufTtcbm1vZHVsZS5leHBvcnRzID0gRGF0YUxlbmd0aFByb2JlO1xuXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/DataLengthProbe.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/DataWorker.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/DataWorker.js ***!
  \*************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\nvar GenericWorker = __webpack_require__(/*! ./GenericWorker */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/GenericWorker.js\");\n\n// the size of the generated chunks\n// TODO expose this as a public variable\nvar DEFAULT_BLOCK_SIZE = 16 * 1024;\n\n/**\n * A worker that reads a content and emits chunks.\n * @constructor\n * @param {Promise} dataP the promise of the data to split\n */\nfunction DataWorker(dataP) {\n    GenericWorker.call(this, \"DataWorker\");\n    var self = this;\n    this.dataIsReady = false;\n    this.index = 0;\n    this.max = 0;\n    this.data = null;\n    this.type = \"\";\n\n    this._tickScheduled = false;\n\n    dataP.then(function (data) {\n        self.dataIsReady = true;\n        self.data = data;\n        self.max = data && data.length || 0;\n        self.type = utils.getTypeOf(data);\n        if(!self.isPaused) {\n            self._tickAndRepeat();\n        }\n    }, function (e) {\n        self.error(e);\n    });\n}\n\nutils.inherits(DataWorker, GenericWorker);\n\n/**\n * @see GenericWorker.cleanUp\n */\nDataWorker.prototype.cleanUp = function () {\n    GenericWorker.prototype.cleanUp.call(this);\n    this.data = null;\n};\n\n/**\n * @see GenericWorker.resume\n */\nDataWorker.prototype.resume = function () {\n    if(!GenericWorker.prototype.resume.call(this)) {\n        return false;\n    }\n\n    if (!this._tickScheduled && this.dataIsReady) {\n        this._tickScheduled = true;\n        utils.delay(this._tickAndRepeat, [], this);\n    }\n    return true;\n};\n\n/**\n * Trigger a tick a schedule an other call to this function.\n */\nDataWorker.prototype._tickAndRepeat = function() {\n    this._tickScheduled = false;\n    if(this.isPaused || this.isFinished) {\n        return;\n    }\n    this._tick();\n    if(!this.isFinished) {\n        utils.delay(this._tickAndRepeat, [], this);\n        this._tickScheduled = true;\n    }\n};\n\n/**\n * Read and push a chunk.\n */\nDataWorker.prototype._tick = function() {\n\n    if(this.isPaused || this.isFinished) {\n        return false;\n    }\n\n    var size = DEFAULT_BLOCK_SIZE;\n    var data = null, nextIndex = Math.min(this.max, this.index + size);\n    if (this.index >= this.max) {\n        // EOF\n        return this.end();\n    } else {\n        switch(this.type) {\n        case \"string\":\n            data = this.data.substring(this.index, nextIndex);\n            break;\n        case \"uint8array\":\n            data = this.data.subarray(this.index, nextIndex);\n            break;\n        case \"array\":\n        case \"nodebuffer\":\n            data = this.data.slice(this.index, nextIndex);\n            break;\n        }\n        this.index = nextIndex;\n        return this.push({\n            data : data,\n            meta : {\n                percent : this.max ? this.index / this.max * 100 : 0\n            }\n        });\n    }\n};\n\nmodule.exports = DataWorker;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/DataWorker.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/GenericWorker.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/GenericWorker.js ***!
  \****************************************************************************************/
/***/ ((module) => {

eval("\n\n/**\n * A worker that does nothing but passing chunks to the next one. This is like\n * a nodejs stream but with some differences. On the good side :\n * - it works on IE 6-9 without any issue / polyfill\n * - it weights less than the full dependencies bundled with browserify\n * - it forwards errors (no need to declare an error handler EVERYWHERE)\n *\n * A chunk is an object with 2 attributes : `meta` and `data`. The former is an\n * object containing anything (`percent` for example), see each worker for more\n * details. The latter is the real data (String, Uint8Array, etc).\n *\n * @constructor\n * @param {String} name the name of the stream (mainly used for debugging purposes)\n */\nfunction GenericWorker(name) {\n    // the name of the worker\n    this.name = name || \"default\";\n    // an object containing metadata about the workers chain\n    this.streamInfo = {};\n    // an error which happened when the worker was paused\n    this.generatedError = null;\n    // an object containing metadata to be merged by this worker into the general metadata\n    this.extraStreamInfo = {};\n    // true if the stream is paused (and should not do anything), false otherwise\n    this.isPaused = true;\n    // true if the stream is finished (and should not do anything), false otherwise\n    this.isFinished = false;\n    // true if the stream is locked to prevent further structure updates (pipe), false otherwise\n    this.isLocked = false;\n    // the event listeners\n    this._listeners = {\n        \"data\":[],\n        \"end\":[],\n        \"error\":[]\n    };\n    // the previous worker, if any\n    this.previous = null;\n}\n\nGenericWorker.prototype = {\n    /**\n     * Push a chunk to the next workers.\n     * @param {Object} chunk the chunk to push\n     */\n    push : function (chunk) {\n        this.emit(\"data\", chunk);\n    },\n    /**\n     * End the stream.\n     * @return {Boolean} true if this call ended the worker, false otherwise.\n     */\n    end : function () {\n        if (this.isFinished) {\n            return false;\n        }\n\n        this.flush();\n        try {\n            this.emit(\"end\");\n            this.cleanUp();\n            this.isFinished = true;\n        } catch (e) {\n            this.emit(\"error\", e);\n        }\n        return true;\n    },\n    /**\n     * End the stream with an error.\n     * @param {Error} e the error which caused the premature end.\n     * @return {Boolean} true if this call ended the worker with an error, false otherwise.\n     */\n    error : function (e) {\n        if (this.isFinished) {\n            return false;\n        }\n\n        if(this.isPaused) {\n            this.generatedError = e;\n        } else {\n            this.isFinished = true;\n\n            this.emit(\"error\", e);\n\n            // in the workers chain exploded in the middle of the chain,\n            // the error event will go downward but we also need to notify\n            // workers upward that there has been an error.\n            if(this.previous) {\n                this.previous.error(e);\n            }\n\n            this.cleanUp();\n        }\n        return true;\n    },\n    /**\n     * Add a callback on an event.\n     * @param {String} name the name of the event (data, end, error)\n     * @param {Function} listener the function to call when the event is triggered\n     * @return {GenericWorker} the current object for chainability\n     */\n    on : function (name, listener) {\n        this._listeners[name].push(listener);\n        return this;\n    },\n    /**\n     * Clean any references when a worker is ending.\n     */\n    cleanUp : function () {\n        this.streamInfo = this.generatedError = this.extraStreamInfo = null;\n        this._listeners = [];\n    },\n    /**\n     * Trigger an event. This will call registered callback with the provided arg.\n     * @param {String} name the name of the event (data, end, error)\n     * @param {Object} arg the argument to call the callback with.\n     */\n    emit : function (name, arg) {\n        if (this._listeners[name]) {\n            for(var i = 0; i < this._listeners[name].length; i++) {\n                this._listeners[name][i].call(this, arg);\n            }\n        }\n    },\n    /**\n     * Chain a worker with an other.\n     * @param {Worker} next the worker receiving events from the current one.\n     * @return {worker} the next worker for chainability\n     */\n    pipe : function (next) {\n        return next.registerPrevious(this);\n    },\n    /**\n     * Same as `pipe` in the other direction.\n     * Using an API with `pipe(next)` is very easy.\n     * Implementing the API with the point of view of the next one registering\n     * a source is easier, see the ZipFileWorker.\n     * @param {Worker} previous the previous worker, sending events to this one\n     * @return {Worker} the current worker for chainability\n     */\n    registerPrevious : function (previous) {\n        if (this.isLocked) {\n            throw new Error(\"The stream '\" + this + \"' has already been used.\");\n        }\n\n        // sharing the streamInfo...\n        this.streamInfo = previous.streamInfo;\n        // ... and adding our own bits\n        this.mergeStreamInfo();\n        this.previous =  previous;\n        var self = this;\n        previous.on(\"data\", function (chunk) {\n            self.processChunk(chunk);\n        });\n        previous.on(\"end\", function () {\n            self.end();\n        });\n        previous.on(\"error\", function (e) {\n            self.error(e);\n        });\n        return this;\n    },\n    /**\n     * Pause the stream so it doesn't send events anymore.\n     * @return {Boolean} true if this call paused the worker, false otherwise.\n     */\n    pause : function () {\n        if(this.isPaused || this.isFinished) {\n            return false;\n        }\n        this.isPaused = true;\n\n        if(this.previous) {\n            this.previous.pause();\n        }\n        return true;\n    },\n    /**\n     * Resume a paused stream.\n     * @return {Boolean} true if this call resumed the worker, false otherwise.\n     */\n    resume : function () {\n        if(!this.isPaused || this.isFinished) {\n            return false;\n        }\n        this.isPaused = false;\n\n        // if true, the worker tried to resume but failed\n        var withError = false;\n        if(this.generatedError) {\n            this.error(this.generatedError);\n            withError = true;\n        }\n        if(this.previous) {\n            this.previous.resume();\n        }\n\n        return !withError;\n    },\n    /**\n     * Flush any remaining bytes as the stream is ending.\n     */\n    flush : function () {},\n    /**\n     * Process a chunk. This is usually the method overridden.\n     * @param {Object} chunk the chunk to process.\n     */\n    processChunk : function(chunk) {\n        this.push(chunk);\n    },\n    /**\n     * Add a key/value to be added in the workers chain streamInfo once activated.\n     * @param {String} key the key to use\n     * @param {Object} value the associated value\n     * @return {Worker} the current worker for chainability\n     */\n    withStreamInfo : function (key, value) {\n        this.extraStreamInfo[key] = value;\n        this.mergeStreamInfo();\n        return this;\n    },\n    /**\n     * Merge this worker's streamInfo into the chain's streamInfo.\n     */\n    mergeStreamInfo : function () {\n        for(var key in this.extraStreamInfo) {\n            if (!Object.prototype.hasOwnProperty.call(this.extraStreamInfo, key)) {\n                continue;\n            }\n            this.streamInfo[key] = this.extraStreamInfo[key];\n        }\n    },\n\n    /**\n     * Lock the stream to prevent further updates on the workers chain.\n     * After calling this method, all calls to pipe will fail.\n     */\n    lock: function () {\n        if (this.isLocked) {\n            throw new Error(\"The stream '\" + this + \"' has already been used.\");\n        }\n        this.isLocked = true;\n        if (this.previous) {\n            this.previous.lock();\n        }\n    },\n\n    /**\n     *\n     * Pretty print the workers chain.\n     */\n    toString : function () {\n        var me = \"Worker \" + this.name;\n        if (this.previous) {\n            return this.previous + \" -> \" + me;\n        } else {\n            return me;\n        }\n    }\n};\n\nmodule.exports = GenericWorker;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/GenericWorker.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/StreamHelper.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/StreamHelper.js ***!
  \***************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\nvar ConvertWorker = __webpack_require__(/*! ./ConvertWorker */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/ConvertWorker.js\");\nvar GenericWorker = __webpack_require__(/*! ./GenericWorker */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/GenericWorker.js\");\nvar base64 = __webpack_require__(/*! ../base64 */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/base64.js\");\nvar support = __webpack_require__(/*! ../support */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/support.js\");\nvar external = __webpack_require__(/*! ../external */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/external.js\");\n\nvar NodejsStreamOutputAdapter = null;\nif (support.nodestream) {\n    try {\n        NodejsStreamOutputAdapter = __webpack_require__(/*! ../nodejs/NodejsStreamOutputAdapter */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/nodejs/NodejsStreamOutputAdapter.js\");\n    } catch(e) {\n        // ignore\n    }\n}\n\n/**\n * Apply the final transformation of the data. If the user wants a Blob for\n * example, it's easier to work with an U8intArray and finally do the\n * ArrayBuffer/Blob conversion.\n * @param {String} type the name of the final type\n * @param {String|Uint8Array|Buffer} content the content to transform\n * @param {String} mimeType the mime type of the content, if applicable.\n * @return {String|Uint8Array|ArrayBuffer|Buffer|Blob} the content in the right format.\n */\nfunction transformZipOutput(type, content, mimeType) {\n    switch(type) {\n    case \"blob\" :\n        return utils.newBlob(utils.transformTo(\"arraybuffer\", content), mimeType);\n    case \"base64\" :\n        return base64.encode(content);\n    default :\n        return utils.transformTo(type, content);\n    }\n}\n\n/**\n * Concatenate an array of data of the given type.\n * @param {String} type the type of the data in the given array.\n * @param {Array} dataArray the array containing the data chunks to concatenate\n * @return {String|Uint8Array|Buffer} the concatenated data\n * @throws Error if the asked type is unsupported\n */\nfunction concat (type, dataArray) {\n    var i, index = 0, res = null, totalLength = 0;\n    for(i = 0; i < dataArray.length; i++) {\n        totalLength += dataArray[i].length;\n    }\n    switch(type) {\n    case \"string\":\n        return dataArray.join(\"\");\n    case \"array\":\n        return Array.prototype.concat.apply([], dataArray);\n    case \"uint8array\":\n        res = new Uint8Array(totalLength);\n        for(i = 0; i < dataArray.length; i++) {\n            res.set(dataArray[i], index);\n            index += dataArray[i].length;\n        }\n        return res;\n    case \"nodebuffer\":\n        return Buffer.concat(dataArray);\n    default:\n        throw new Error(\"concat : unsupported type '\"  + type + \"'\");\n    }\n}\n\n/**\n * Listen a StreamHelper, accumulate its content and concatenate it into a\n * complete block.\n * @param {StreamHelper} helper the helper to use.\n * @param {Function} updateCallback a callback called on each update. Called\n * with one arg :\n * - the metadata linked to the update received.\n * @return Promise the promise for the accumulation.\n */\nfunction accumulate(helper, updateCallback) {\n    return new external.Promise(function (resolve, reject){\n        var dataArray = [];\n        var chunkType = helper._internalType,\n            resultType = helper._outputType,\n            mimeType = helper._mimeType;\n        helper\n            .on(\"data\", function (data, meta) {\n                dataArray.push(data);\n                if(updateCallback) {\n                    updateCallback(meta);\n                }\n            })\n            .on(\"error\", function(err) {\n                dataArray = [];\n                reject(err);\n            })\n            .on(\"end\", function (){\n                try {\n                    var result = transformZipOutput(resultType, concat(chunkType, dataArray), mimeType);\n                    resolve(result);\n                } catch (e) {\n                    reject(e);\n                }\n                dataArray = [];\n            })\n            .resume();\n    });\n}\n\n/**\n * An helper to easily use workers outside of JSZip.\n * @constructor\n * @param {Worker} worker the worker to wrap\n * @param {String} outputType the type of data expected by the use\n * @param {String} mimeType the mime type of the content, if applicable.\n */\nfunction StreamHelper(worker, outputType, mimeType) {\n    var internalType = outputType;\n    switch(outputType) {\n    case \"blob\":\n    case \"arraybuffer\":\n        internalType = \"uint8array\";\n        break;\n    case \"base64\":\n        internalType = \"string\";\n        break;\n    }\n\n    try {\n        // the type used internally\n        this._internalType = internalType;\n        // the type used to output results\n        this._outputType = outputType;\n        // the mime type\n        this._mimeType = mimeType;\n        utils.checkSupport(internalType);\n        this._worker = worker.pipe(new ConvertWorker(internalType));\n        // the last workers can be rewired without issues but we need to\n        // prevent any updates on previous workers.\n        worker.lock();\n    } catch(e) {\n        this._worker = new GenericWorker(\"error\");\n        this._worker.error(e);\n    }\n}\n\nStreamHelper.prototype = {\n    /**\n     * Listen a StreamHelper, accumulate its content and concatenate it into a\n     * complete block.\n     * @param {Function} updateCb the update callback.\n     * @return Promise the promise for the accumulation.\n     */\n    accumulate : function (updateCb) {\n        return accumulate(this, updateCb);\n    },\n    /**\n     * Add a listener on an event triggered on a stream.\n     * @param {String} evt the name of the event\n     * @param {Function} fn the listener\n     * @return {StreamHelper} the current helper.\n     */\n    on : function (evt, fn) {\n        var self = this;\n\n        if(evt === \"data\") {\n            this._worker.on(evt, function (chunk) {\n                fn.call(self, chunk.data, chunk.meta);\n            });\n        } else {\n            this._worker.on(evt, function () {\n                utils.delay(fn, arguments, self);\n            });\n        }\n        return this;\n    },\n    /**\n     * Resume the flow of chunks.\n     * @return {StreamHelper} the current helper.\n     */\n    resume : function () {\n        utils.delay(this._worker.resume, [], this._worker);\n        return this;\n    },\n    /**\n     * Pause the flow of chunks.\n     * @return {StreamHelper} the current helper.\n     */\n    pause : function () {\n        this._worker.pause();\n        return this;\n    },\n    /**\n     * Return a nodejs stream for this helper.\n     * @param {Function} updateCb the update callback.\n     * @return {NodejsStreamOutputAdapter} the nodejs stream.\n     */\n    toNodejsStream : function (updateCb) {\n        utils.checkSupport(\"nodestream\");\n        if (this._outputType !== \"nodebuffer\") {\n            // an object stream containing blob/arraybuffer/uint8array/string\n            // is strange and I don't know if it would be useful.\n            // I you find this comment and have a good usecase, please open a\n            // bug report !\n            throw new Error(this._outputType + \" is not supported by this method\");\n        }\n\n        return new NodejsStreamOutputAdapter(this, {\n            objectMode : this._outputType !== \"nodebuffer\"\n        }, updateCb);\n    }\n};\n\n\nmodule.exports = StreamHelper;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/StreamHelper.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/support.js":
/*!***************************************************************************!*\
  !*** ./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/support.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nexports.base64 = true;\nexports.array = true;\nexports.string = true;\nexports.arraybuffer = typeof ArrayBuffer !== \"undefined\" && typeof Uint8Array !== \"undefined\";\nexports.nodebuffer = typeof Buffer !== \"undefined\";\n// contains true if JSZip can read/generate Uint8Array, false otherwise.\nexports.uint8array = typeof Uint8Array !== \"undefined\";\n\nif (typeof ArrayBuffer === \"undefined\") {\n    exports.blob = false;\n}\nelse {\n    var buffer = new ArrayBuffer(0);\n    try {\n        exports.blob = new Blob([buffer], {\n            type: \"application/zip\"\n        }).size === 0;\n    }\n    catch (e) {\n        try {\n            var Builder = self.BlobBuilder || self.WebKitBlobBuilder || self.MozBlobBuilder || self.MSBlobBuilder;\n            var builder = new Builder();\n            builder.append(buffer);\n            exports.blob = builder.getBlob(\"application/zip\").size === 0;\n        }\n        catch (e) {\n            exports.blob = false;\n        }\n    }\n}\n\ntry {\n    exports.nodestream = !!(__webpack_require__(/*! readable-stream */ \"(ssr)/./node_modules/.pnpm/readable-stream@2.3.8/node_modules/readable-stream/readable.js\").Readable);\n} catch(e) {\n    exports.nodestream = false;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/support.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utf8.js":
/*!************************************************************************!*\
  !*** ./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utf8.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\nvar support = __webpack_require__(/*! ./support */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/support.js\");\nvar nodejsUtils = __webpack_require__(/*! ./nodejsUtils */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/nodejsUtils.js\");\nvar GenericWorker = __webpack_require__(/*! ./stream/GenericWorker */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/GenericWorker.js\");\n\n/**\n * The following functions come from pako, from pako/lib/utils/strings\n * released under the MIT license, see pako https://github.com/nodeca/pako/\n */\n\n// Table with utf8 lengths (calculated by first byte of sequence)\n// Note, that 5 & 6-byte values and some 4-byte values can not be represented in JS,\n// because max possible codepoint is 0x10ffff\nvar _utf8len = new Array(256);\nfor (var i=0; i<256; i++) {\n    _utf8len[i] = (i >= 252 ? 6 : i >= 248 ? 5 : i >= 240 ? 4 : i >= 224 ? 3 : i >= 192 ? 2 : 1);\n}\n_utf8len[254]=_utf8len[254]=1; // Invalid sequence start\n\n// convert string to array (typed, when possible)\nvar string2buf = function (str) {\n    var buf, c, c2, m_pos, i, str_len = str.length, buf_len = 0;\n\n    // count binary size\n    for (m_pos = 0; m_pos < str_len; m_pos++) {\n        c = str.charCodeAt(m_pos);\n        if ((c & 0xfc00) === 0xd800 && (m_pos+1 < str_len)) {\n            c2 = str.charCodeAt(m_pos+1);\n            if ((c2 & 0xfc00) === 0xdc00) {\n                c = 0x10000 + ((c - 0xd800) << 10) + (c2 - 0xdc00);\n                m_pos++;\n            }\n        }\n        buf_len += c < 0x80 ? 1 : c < 0x800 ? 2 : c < 0x10000 ? 3 : 4;\n    }\n\n    // allocate buffer\n    if (support.uint8array) {\n        buf = new Uint8Array(buf_len);\n    } else {\n        buf = new Array(buf_len);\n    }\n\n    // convert\n    for (i=0, m_pos = 0; i < buf_len; m_pos++) {\n        c = str.charCodeAt(m_pos);\n        if ((c & 0xfc00) === 0xd800 && (m_pos+1 < str_len)) {\n            c2 = str.charCodeAt(m_pos+1);\n            if ((c2 & 0xfc00) === 0xdc00) {\n                c = 0x10000 + ((c - 0xd800) << 10) + (c2 - 0xdc00);\n                m_pos++;\n            }\n        }\n        if (c < 0x80) {\n            /* one byte */\n            buf[i++] = c;\n        } else if (c < 0x800) {\n            /* two bytes */\n            buf[i++] = 0xC0 | (c >>> 6);\n            buf[i++] = 0x80 | (c & 0x3f);\n        } else if (c < 0x10000) {\n            /* three bytes */\n            buf[i++] = 0xE0 | (c >>> 12);\n            buf[i++] = 0x80 | (c >>> 6 & 0x3f);\n            buf[i++] = 0x80 | (c & 0x3f);\n        } else {\n            /* four bytes */\n            buf[i++] = 0xf0 | (c >>> 18);\n            buf[i++] = 0x80 | (c >>> 12 & 0x3f);\n            buf[i++] = 0x80 | (c >>> 6 & 0x3f);\n            buf[i++] = 0x80 | (c & 0x3f);\n        }\n    }\n\n    return buf;\n};\n\n// Calculate max possible position in utf8 buffer,\n// that will not break sequence. If that's not possible\n// - (very small limits) return max size as is.\n//\n// buf[] - utf8 bytes array\n// max   - length limit (mandatory);\nvar utf8border = function(buf, max) {\n    var pos;\n\n    max = max || buf.length;\n    if (max > buf.length) { max = buf.length; }\n\n    // go back from last position, until start of sequence found\n    pos = max-1;\n    while (pos >= 0 && (buf[pos] & 0xC0) === 0x80) { pos--; }\n\n    // Fuckup - very small and broken sequence,\n    // return max, because we should return something anyway.\n    if (pos < 0) { return max; }\n\n    // If we came to start of buffer - that means vuffer is too small,\n    // return max too.\n    if (pos === 0) { return max; }\n\n    return (pos + _utf8len[buf[pos]] > max) ? pos : max;\n};\n\n// convert array to string\nvar buf2string = function (buf) {\n    var i, out, c, c_len;\n    var len = buf.length;\n\n    // Reserve max possible length (2 words per char)\n    // NB: by unknown reasons, Array is significantly faster for\n    //     String.fromCharCode.apply than Uint16Array.\n    var utf16buf = new Array(len*2);\n\n    for (out=0, i=0; i<len;) {\n        c = buf[i++];\n        // quick process ascii\n        if (c < 0x80) { utf16buf[out++] = c; continue; }\n\n        c_len = _utf8len[c];\n        // skip 5 & 6 byte codes\n        if (c_len > 4) { utf16buf[out++] = 0xfffd; i += c_len-1; continue; }\n\n        // apply mask on first byte\n        c &= c_len === 2 ? 0x1f : c_len === 3 ? 0x0f : 0x07;\n        // join the rest\n        while (c_len > 1 && i < len) {\n            c = (c << 6) | (buf[i++] & 0x3f);\n            c_len--;\n        }\n\n        // terminated by end of string?\n        if (c_len > 1) { utf16buf[out++] = 0xfffd; continue; }\n\n        if (c < 0x10000) {\n            utf16buf[out++] = c;\n        } else {\n            c -= 0x10000;\n            utf16buf[out++] = 0xd800 | ((c >> 10) & 0x3ff);\n            utf16buf[out++] = 0xdc00 | (c & 0x3ff);\n        }\n    }\n\n    // shrinkBuf(utf16buf, out)\n    if (utf16buf.length !== out) {\n        if(utf16buf.subarray) {\n            utf16buf = utf16buf.subarray(0, out);\n        } else {\n            utf16buf.length = out;\n        }\n    }\n\n    // return String.fromCharCode.apply(null, utf16buf);\n    return utils.applyFromCharCode(utf16buf);\n};\n\n\n// That's all for the pako functions.\n\n\n/**\n * Transform a javascript string into an array (typed if possible) of bytes,\n * UTF-8 encoded.\n * @param {String} str the string to encode\n * @return {Array|Uint8Array|Buffer} the UTF-8 encoded string.\n */\nexports.utf8encode = function utf8encode(str) {\n    if (support.nodebuffer) {\n        return nodejsUtils.newBufferFrom(str, \"utf-8\");\n    }\n\n    return string2buf(str);\n};\n\n\n/**\n * Transform a bytes array (or a representation) representing an UTF-8 encoded\n * string into a javascript string.\n * @param {Array|Uint8Array|Buffer} buf the data de decode\n * @return {String} the decoded string.\n */\nexports.utf8decode = function utf8decode(buf) {\n    if (support.nodebuffer) {\n        return utils.transformTo(\"nodebuffer\", buf).toString(\"utf-8\");\n    }\n\n    buf = utils.transformTo(support.uint8array ? \"uint8array\" : \"array\", buf);\n\n    return buf2string(buf);\n};\n\n/**\n * A worker to decode utf8 encoded binary chunks into string chunks.\n * @constructor\n */\nfunction Utf8DecodeWorker() {\n    GenericWorker.call(this, \"utf-8 decode\");\n    // the last bytes if a chunk didn't end with a complete codepoint.\n    this.leftOver = null;\n}\nutils.inherits(Utf8DecodeWorker, GenericWorker);\n\n/**\n * @see GenericWorker.processChunk\n */\nUtf8DecodeWorker.prototype.processChunk = function (chunk) {\n\n    var data = utils.transformTo(support.uint8array ? \"uint8array\" : \"array\", chunk.data);\n\n    // 1st step, re-use what's left of the previous chunk\n    if (this.leftOver && this.leftOver.length) {\n        if(support.uint8array) {\n            var previousData = data;\n            data = new Uint8Array(previousData.length + this.leftOver.length);\n            data.set(this.leftOver, 0);\n            data.set(previousData, this.leftOver.length);\n        } else {\n            data = this.leftOver.concat(data);\n        }\n        this.leftOver = null;\n    }\n\n    var nextBoundary = utf8border(data);\n    var usableData = data;\n    if (nextBoundary !== data.length) {\n        if (support.uint8array) {\n            usableData = data.subarray(0, nextBoundary);\n            this.leftOver = data.subarray(nextBoundary, data.length);\n        } else {\n            usableData = data.slice(0, nextBoundary);\n            this.leftOver = data.slice(nextBoundary, data.length);\n        }\n    }\n\n    this.push({\n        data : exports.utf8decode(usableData),\n        meta : chunk.meta\n    });\n};\n\n/**\n * @see GenericWorker.flush\n */\nUtf8DecodeWorker.prototype.flush = function () {\n    if(this.leftOver && this.leftOver.length) {\n        this.push({\n            data : exports.utf8decode(this.leftOver),\n            meta : {}\n        });\n        this.leftOver = null;\n    }\n};\nexports.Utf8DecodeWorker = Utf8DecodeWorker;\n\n/**\n * A worker to endcode string chunks into utf8 encoded binary chunks.\n * @constructor\n */\nfunction Utf8EncodeWorker() {\n    GenericWorker.call(this, \"utf-8 encode\");\n}\nutils.inherits(Utf8EncodeWorker, GenericWorker);\n\n/**\n * @see GenericWorker.processChunk\n */\nUtf8EncodeWorker.prototype.processChunk = function (chunk) {\n    this.push({\n        data : exports.utf8encode(chunk.data),\n        meta : chunk.meta\n    });\n};\nexports.Utf8EncodeWorker = Utf8EncodeWorker;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utf8.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js":
/*!*************************************************************************!*\
  !*** ./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar support = __webpack_require__(/*! ./support */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/support.js\");\nvar base64 = __webpack_require__(/*! ./base64 */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/base64.js\");\nvar nodejsUtils = __webpack_require__(/*! ./nodejsUtils */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/nodejsUtils.js\");\nvar external = __webpack_require__(/*! ./external */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/external.js\");\n__webpack_require__(/*! setimmediate */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/compiled/setimmediate/setImmediate.js\");\n\n\n/**\n * Convert a string that pass as a \"binary string\": it should represent a byte\n * array but may have > 255 char codes. Be sure to take only the first byte\n * and returns the byte array.\n * @param {String} str the string to transform.\n * @return {Array|Uint8Array} the string in a binary format.\n */\nfunction string2binary(str) {\n    var result = null;\n    if (support.uint8array) {\n        result = new Uint8Array(str.length);\n    } else {\n        result = new Array(str.length);\n    }\n    return stringToArrayLike(str, result);\n}\n\n/**\n * Create a new blob with the given content and the given type.\n * @param {String|ArrayBuffer} part the content to put in the blob. DO NOT use\n * an Uint8Array because the stock browser of android 4 won't accept it (it\n * will be silently converted to a string, \"[object Uint8Array]\").\n *\n * Use only ONE part to build the blob to avoid a memory leak in IE11 / Edge:\n * when a large amount of Array is used to create the Blob, the amount of\n * memory consumed is nearly 100 times the original data amount.\n *\n * @param {String} type the mime type of the blob.\n * @return {Blob} the created blob.\n */\nexports.newBlob = function(part, type) {\n    exports.checkSupport(\"blob\");\n\n    try {\n        // Blob constructor\n        return new Blob([part], {\n            type: type\n        });\n    }\n    catch (e) {\n\n        try {\n            // deprecated, browser only, old way\n            var Builder = self.BlobBuilder || self.WebKitBlobBuilder || self.MozBlobBuilder || self.MSBlobBuilder;\n            var builder = new Builder();\n            builder.append(part);\n            return builder.getBlob(type);\n        }\n        catch (e) {\n\n            // well, fuck ?!\n            throw new Error(\"Bug : can't construct the Blob.\");\n        }\n    }\n\n\n};\n/**\n * The identity function.\n * @param {Object} input the input.\n * @return {Object} the same input.\n */\nfunction identity(input) {\n    return input;\n}\n\n/**\n * Fill in an array with a string.\n * @param {String} str the string to use.\n * @param {Array|ArrayBuffer|Uint8Array|Buffer} array the array to fill in (will be mutated).\n * @return {Array|ArrayBuffer|Uint8Array|Buffer} the updated array.\n */\nfunction stringToArrayLike(str, array) {\n    for (var i = 0; i < str.length; ++i) {\n        array[i] = str.charCodeAt(i) & 0xFF;\n    }\n    return array;\n}\n\n/**\n * An helper for the function arrayLikeToString.\n * This contains static information and functions that\n * can be optimized by the browser JIT compiler.\n */\nvar arrayToStringHelper = {\n    /**\n     * Transform an array of int into a string, chunk by chunk.\n     * See the performances notes on arrayLikeToString.\n     * @param {Array|ArrayBuffer|Uint8Array|Buffer} array the array to transform.\n     * @param {String} type the type of the array.\n     * @param {Integer} chunk the chunk size.\n     * @return {String} the resulting string.\n     * @throws Error if the chunk is too big for the stack.\n     */\n    stringifyByChunk: function(array, type, chunk) {\n        var result = [], k = 0, len = array.length;\n        // shortcut\n        if (len <= chunk) {\n            return String.fromCharCode.apply(null, array);\n        }\n        while (k < len) {\n            if (type === \"array\" || type === \"nodebuffer\") {\n                result.push(String.fromCharCode.apply(null, array.slice(k, Math.min(k + chunk, len))));\n            }\n            else {\n                result.push(String.fromCharCode.apply(null, array.subarray(k, Math.min(k + chunk, len))));\n            }\n            k += chunk;\n        }\n        return result.join(\"\");\n    },\n    /**\n     * Call String.fromCharCode on every item in the array.\n     * This is the naive implementation, which generate A LOT of intermediate string.\n     * This should be used when everything else fail.\n     * @param {Array|ArrayBuffer|Uint8Array|Buffer} array the array to transform.\n     * @return {String} the result.\n     */\n    stringifyByChar: function(array){\n        var resultStr = \"\";\n        for(var i = 0; i < array.length; i++) {\n            resultStr += String.fromCharCode(array[i]);\n        }\n        return resultStr;\n    },\n    applyCanBeUsed : {\n        /**\n         * true if the browser accepts to use String.fromCharCode on Uint8Array\n         */\n        uint8array : (function () {\n            try {\n                return support.uint8array && String.fromCharCode.apply(null, new Uint8Array(1)).length === 1;\n            } catch (e) {\n                return false;\n            }\n        })(),\n        /**\n         * true if the browser accepts to use String.fromCharCode on nodejs Buffer.\n         */\n        nodebuffer : (function () {\n            try {\n                return support.nodebuffer && String.fromCharCode.apply(null, nodejsUtils.allocBuffer(1)).length === 1;\n            } catch (e) {\n                return false;\n            }\n        })()\n    }\n};\n\n/**\n * Transform an array-like object to a string.\n * @param {Array|ArrayBuffer|Uint8Array|Buffer} array the array to transform.\n * @return {String} the result.\n */\nfunction arrayLikeToString(array) {\n    // Performances notes :\n    // --------------------\n    // String.fromCharCode.apply(null, array) is the fastest, see\n    // see http://jsperf.com/converting-a-uint8array-to-a-string/2\n    // but the stack is limited (and we can get huge arrays !).\n    //\n    // result += String.fromCharCode(array[i]); generate too many strings !\n    //\n    // This code is inspired by http://jsperf.com/arraybuffer-to-string-apply-performance/2\n    // TODO : we now have workers that split the work. Do we still need that ?\n    var chunk = 65536,\n        type = exports.getTypeOf(array),\n        canUseApply = true;\n    if (type === \"uint8array\") {\n        canUseApply = arrayToStringHelper.applyCanBeUsed.uint8array;\n    } else if (type === \"nodebuffer\") {\n        canUseApply = arrayToStringHelper.applyCanBeUsed.nodebuffer;\n    }\n\n    if (canUseApply) {\n        while (chunk > 1) {\n            try {\n                return arrayToStringHelper.stringifyByChunk(array, type, chunk);\n            } catch (e) {\n                chunk = Math.floor(chunk / 2);\n            }\n        }\n    }\n\n    // no apply or chunk error : slow and painful algorithm\n    // default browser on android 4.*\n    return arrayToStringHelper.stringifyByChar(array);\n}\n\nexports.applyFromCharCode = arrayLikeToString;\n\n\n/**\n * Copy the data from an array-like to an other array-like.\n * @param {Array|ArrayBuffer|Uint8Array|Buffer} arrayFrom the origin array.\n * @param {Array|ArrayBuffer|Uint8Array|Buffer} arrayTo the destination array which will be mutated.\n * @return {Array|ArrayBuffer|Uint8Array|Buffer} the updated destination array.\n */\nfunction arrayLikeToArrayLike(arrayFrom, arrayTo) {\n    for (var i = 0; i < arrayFrom.length; i++) {\n        arrayTo[i] = arrayFrom[i];\n    }\n    return arrayTo;\n}\n\n// a matrix containing functions to transform everything into everything.\nvar transform = {};\n\n// string to ?\ntransform[\"string\"] = {\n    \"string\": identity,\n    \"array\": function(input) {\n        return stringToArrayLike(input, new Array(input.length));\n    },\n    \"arraybuffer\": function(input) {\n        return transform[\"string\"][\"uint8array\"](input).buffer;\n    },\n    \"uint8array\": function(input) {\n        return stringToArrayLike(input, new Uint8Array(input.length));\n    },\n    \"nodebuffer\": function(input) {\n        return stringToArrayLike(input, nodejsUtils.allocBuffer(input.length));\n    }\n};\n\n// array to ?\ntransform[\"array\"] = {\n    \"string\": arrayLikeToString,\n    \"array\": identity,\n    \"arraybuffer\": function(input) {\n        return (new Uint8Array(input)).buffer;\n    },\n    \"uint8array\": function(input) {\n        return new Uint8Array(input);\n    },\n    \"nodebuffer\": function(input) {\n        return nodejsUtils.newBufferFrom(input);\n    }\n};\n\n// arraybuffer to ?\ntransform[\"arraybuffer\"] = {\n    \"string\": function(input) {\n        return arrayLikeToString(new Uint8Array(input));\n    },\n    \"array\": function(input) {\n        return arrayLikeToArrayLike(new Uint8Array(input), new Array(input.byteLength));\n    },\n    \"arraybuffer\": identity,\n    \"uint8array\": function(input) {\n        return new Uint8Array(input);\n    },\n    \"nodebuffer\": function(input) {\n        return nodejsUtils.newBufferFrom(new Uint8Array(input));\n    }\n};\n\n// uint8array to ?\ntransform[\"uint8array\"] = {\n    \"string\": arrayLikeToString,\n    \"array\": function(input) {\n        return arrayLikeToArrayLike(input, new Array(input.length));\n    },\n    \"arraybuffer\": function(input) {\n        return input.buffer;\n    },\n    \"uint8array\": identity,\n    \"nodebuffer\": function(input) {\n        return nodejsUtils.newBufferFrom(input);\n    }\n};\n\n// nodebuffer to ?\ntransform[\"nodebuffer\"] = {\n    \"string\": arrayLikeToString,\n    \"array\": function(input) {\n        return arrayLikeToArrayLike(input, new Array(input.length));\n    },\n    \"arraybuffer\": function(input) {\n        return transform[\"nodebuffer\"][\"uint8array\"](input).buffer;\n    },\n    \"uint8array\": function(input) {\n        return arrayLikeToArrayLike(input, new Uint8Array(input.length));\n    },\n    \"nodebuffer\": identity\n};\n\n/**\n * Transform an input into any type.\n * The supported output type are : string, array, uint8array, arraybuffer, nodebuffer.\n * If no output type is specified, the unmodified input will be returned.\n * @param {String} outputType the output type.\n * @param {String|Array|ArrayBuffer|Uint8Array|Buffer} input the input to convert.\n * @throws {Error} an Error if the browser doesn't support the requested output type.\n */\nexports.transformTo = function(outputType, input) {\n    if (!input) {\n        // undefined, null, etc\n        // an empty string won't harm.\n        input = \"\";\n    }\n    if (!outputType) {\n        return input;\n    }\n    exports.checkSupport(outputType);\n    var inputType = exports.getTypeOf(input);\n    var result = transform[inputType][outputType](input);\n    return result;\n};\n\n/**\n * Resolve all relative path components, \".\" and \"..\", in a path. If these relative components\n * traverse above the root then the resulting path will only contain the final path component.\n *\n * All empty components, e.g. \"//\", are removed.\n * @param {string} path A path with / or \\ separators\n * @returns {string} The path with all relative path components resolved.\n */\nexports.resolve = function(path) {\n    var parts = path.split(\"/\");\n    var result = [];\n    for (var index = 0; index < parts.length; index++) {\n        var part = parts[index];\n        // Allow the first and last component to be empty for trailing slashes.\n        if (part === \".\" || (part === \"\" && index !== 0 && index !== parts.length - 1)) {\n            continue;\n        } else if (part === \"..\") {\n            result.pop();\n        } else {\n            result.push(part);\n        }\n    }\n    return result.join(\"/\");\n};\n\n/**\n * Return the type of the input.\n * The type will be in a format valid for JSZip.utils.transformTo : string, array, uint8array, arraybuffer.\n * @param {Object} input the input to identify.\n * @return {String} the (lowercase) type of the input.\n */\nexports.getTypeOf = function(input) {\n    if (typeof input === \"string\") {\n        return \"string\";\n    }\n    if (Object.prototype.toString.call(input) === \"[object Array]\") {\n        return \"array\";\n    }\n    if (support.nodebuffer && nodejsUtils.isBuffer(input)) {\n        return \"nodebuffer\";\n    }\n    if (support.uint8array && input instanceof Uint8Array) {\n        return \"uint8array\";\n    }\n    if (support.arraybuffer && input instanceof ArrayBuffer) {\n        return \"arraybuffer\";\n    }\n};\n\n/**\n * Throw an exception if the type is not supported.\n * @param {String} type the type to check.\n * @throws {Error} an Error if the browser doesn't support the requested type.\n */\nexports.checkSupport = function(type) {\n    var supported = support[type.toLowerCase()];\n    if (!supported) {\n        throw new Error(type + \" is not supported by this platform\");\n    }\n};\n\nexports.MAX_VALUE_16BITS = 65535;\nexports.MAX_VALUE_32BITS = -1; // well, \"\\xFF\\xFF\\xFF\\xFF\\xFF\\xFF\\xFF\\xFF\" is parsed as -1\n\n/**\n * Prettify a string read as binary.\n * @param {string} str the string to prettify.\n * @return {string} a pretty string.\n */\nexports.pretty = function(str) {\n    var res = \"\",\n        code, i;\n    for (i = 0; i < (str || \"\").length; i++) {\n        code = str.charCodeAt(i);\n        res += \"\\\\x\" + (code < 16 ? \"0\" : \"\") + code.toString(16).toUpperCase();\n    }\n    return res;\n};\n\n/**\n * Defer the call of a function.\n * @param {Function} callback the function to call asynchronously.\n * @param {Array} args the arguments to give to the callback.\n */\nexports.delay = function(callback, args, self) {\n    setImmediate(function () {\n        callback.apply(self || null, args || []);\n    });\n};\n\n/**\n * Extends a prototype with an other, without calling a constructor with\n * side effects. Inspired by nodejs' `utils.inherits`\n * @param {Function} ctor the constructor to augment\n * @param {Function} superCtor the parent constructor to use\n */\nexports.inherits = function (ctor, superCtor) {\n    var Obj = function() {};\n    Obj.prototype = superCtor.prototype;\n    ctor.prototype = new Obj();\n};\n\n/**\n * Merge the objects passed as parameters into a new one.\n * @private\n * @param {...Object} var_args All objects to merge.\n * @return {Object} a new object with the data of the others.\n */\nexports.extend = function() {\n    var result = {}, i, attr;\n    for (i = 0; i < arguments.length; i++) { // arguments is not enumerable in some browsers\n        for (attr in arguments[i]) {\n            if (Object.prototype.hasOwnProperty.call(arguments[i], attr) && typeof result[attr] === \"undefined\") {\n                result[attr] = arguments[i][attr];\n            }\n        }\n    }\n    return result;\n};\n\n/**\n * Transform arbitrary content into a Promise.\n * @param {String} name a name for the content being processed.\n * @param {Object} inputData the content to process.\n * @param {Boolean} isBinary true if the content is not an unicode string\n * @param {Boolean} isOptimizedBinaryString true if the string content only has one byte per character.\n * @param {Boolean} isBase64 true if the string content is encoded with base64.\n * @return {Promise} a promise in a format usable by JSZip.\n */\nexports.prepareContent = function(name, inputData, isBinary, isOptimizedBinaryString, isBase64) {\n\n    // if inputData is already a promise, this flatten it.\n    var promise = external.Promise.resolve(inputData).then(function(data) {\n\n\n        var isBlob = support.blob && (data instanceof Blob || [\"[object File]\", \"[object Blob]\"].indexOf(Object.prototype.toString.call(data)) !== -1);\n\n        if (isBlob && typeof FileReader !== \"undefined\") {\n            return new external.Promise(function (resolve, reject) {\n                var reader = new FileReader();\n\n                reader.onload = function(e) {\n                    resolve(e.target.result);\n                };\n                reader.onerror = function(e) {\n                    reject(e.target.error);\n                };\n                reader.readAsArrayBuffer(data);\n            });\n        } else {\n            return data;\n        }\n    });\n\n    return promise.then(function(data) {\n        var dataType = exports.getTypeOf(data);\n\n        if (!dataType) {\n            return external.Promise.reject(\n                new Error(\"Can't read the data of '\" + name + \"'. Is it \" +\n                          \"in a supported JavaScript type (String, Blob, ArrayBuffer, etc) ?\")\n            );\n        }\n        // special case : it's way easier to work with Uint8Array than with ArrayBuffer\n        if (dataType === \"arraybuffer\") {\n            data = exports.transformTo(\"uint8array\", data);\n        } else if (dataType === \"string\") {\n            if (isBase64) {\n                data = base64.decode(data);\n            }\n            else if (isBinary) {\n                // optimizedBinaryString === true means that the file has already been filtered with a 0xFF mask\n                if (isOptimizedBinaryString !== true) {\n                    // this is a string, not in a base64 format.\n                    // Be sure that this is a correct \"binary string\"\n                    data = string2binary(data);\n                }\n            }\n        }\n        return data;\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/zipEntries.js":
/*!******************************************************************************!*\
  !*** ./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/zipEntries.js ***!
  \******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar readerFor = __webpack_require__(/*! ./reader/readerFor */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/readerFor.js\");\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\nvar sig = __webpack_require__(/*! ./signature */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/signature.js\");\nvar ZipEntry = __webpack_require__(/*! ./zipEntry */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/zipEntry.js\");\nvar support = __webpack_require__(/*! ./support */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/support.js\");\n//  class ZipEntries {{{\n/**\n * All the entries in the zip file.\n * @constructor\n * @param {Object} loadOptions Options for loading the stream.\n */\nfunction ZipEntries(loadOptions) {\n    this.files = [];\n    this.loadOptions = loadOptions;\n}\nZipEntries.prototype = {\n    /**\n     * Check that the reader is on the specified signature.\n     * @param {string} expectedSignature the expected signature.\n     * @throws {Error} if it is an other signature.\n     */\n    checkSignature: function(expectedSignature) {\n        if (!this.reader.readAndCheckSignature(expectedSignature)) {\n            this.reader.index -= 4;\n            var signature = this.reader.readString(4);\n            throw new Error(\"Corrupted zip or bug: unexpected signature \" + \"(\" + utils.pretty(signature) + \", expected \" + utils.pretty(expectedSignature) + \")\");\n        }\n    },\n    /**\n     * Check if the given signature is at the given index.\n     * @param {number} askedIndex the index to check.\n     * @param {string} expectedSignature the signature to expect.\n     * @return {boolean} true if the signature is here, false otherwise.\n     */\n    isSignature: function(askedIndex, expectedSignature) {\n        var currentIndex = this.reader.index;\n        this.reader.setIndex(askedIndex);\n        var signature = this.reader.readString(4);\n        var result = signature === expectedSignature;\n        this.reader.setIndex(currentIndex);\n        return result;\n    },\n    /**\n     * Read the end of the central directory.\n     */\n    readBlockEndOfCentral: function() {\n        this.diskNumber = this.reader.readInt(2);\n        this.diskWithCentralDirStart = this.reader.readInt(2);\n        this.centralDirRecordsOnThisDisk = this.reader.readInt(2);\n        this.centralDirRecords = this.reader.readInt(2);\n        this.centralDirSize = this.reader.readInt(4);\n        this.centralDirOffset = this.reader.readInt(4);\n\n        this.zipCommentLength = this.reader.readInt(2);\n        // warning : the encoding depends of the system locale\n        // On a linux machine with LANG=en_US.utf8, this field is utf8 encoded.\n        // On a windows machine, this field is encoded with the localized windows code page.\n        var zipComment = this.reader.readData(this.zipCommentLength);\n        var decodeParamType = support.uint8array ? \"uint8array\" : \"array\";\n        // To get consistent behavior with the generation part, we will assume that\n        // this is utf8 encoded unless specified otherwise.\n        var decodeContent = utils.transformTo(decodeParamType, zipComment);\n        this.zipComment = this.loadOptions.decodeFileName(decodeContent);\n    },\n    /**\n     * Read the end of the Zip 64 central directory.\n     * Not merged with the method readEndOfCentral :\n     * The end of central can coexist with its Zip64 brother,\n     * I don't want to read the wrong number of bytes !\n     */\n    readBlockZip64EndOfCentral: function() {\n        this.zip64EndOfCentralSize = this.reader.readInt(8);\n        this.reader.skip(4);\n        // this.versionMadeBy = this.reader.readString(2);\n        // this.versionNeeded = this.reader.readInt(2);\n        this.diskNumber = this.reader.readInt(4);\n        this.diskWithCentralDirStart = this.reader.readInt(4);\n        this.centralDirRecordsOnThisDisk = this.reader.readInt(8);\n        this.centralDirRecords = this.reader.readInt(8);\n        this.centralDirSize = this.reader.readInt(8);\n        this.centralDirOffset = this.reader.readInt(8);\n\n        this.zip64ExtensibleData = {};\n        var extraDataSize = this.zip64EndOfCentralSize - 44,\n            index = 0,\n            extraFieldId,\n            extraFieldLength,\n            extraFieldValue;\n        while (index < extraDataSize) {\n            extraFieldId = this.reader.readInt(2);\n            extraFieldLength = this.reader.readInt(4);\n            extraFieldValue = this.reader.readData(extraFieldLength);\n            this.zip64ExtensibleData[extraFieldId] = {\n                id: extraFieldId,\n                length: extraFieldLength,\n                value: extraFieldValue\n            };\n        }\n    },\n    /**\n     * Read the end of the Zip 64 central directory locator.\n     */\n    readBlockZip64EndOfCentralLocator: function() {\n        this.diskWithZip64CentralDirStart = this.reader.readInt(4);\n        this.relativeOffsetEndOfZip64CentralDir = this.reader.readInt(8);\n        this.disksCount = this.reader.readInt(4);\n        if (this.disksCount > 1) {\n            throw new Error(\"Multi-volumes zip are not supported\");\n        }\n    },\n    /**\n     * Read the local files, based on the offset read in the central part.\n     */\n    readLocalFiles: function() {\n        var i, file;\n        for (i = 0; i < this.files.length; i++) {\n            file = this.files[i];\n            this.reader.setIndex(file.localHeaderOffset);\n            this.checkSignature(sig.LOCAL_FILE_HEADER);\n            file.readLocalPart(this.reader);\n            file.handleUTF8();\n            file.processAttributes();\n        }\n    },\n    /**\n     * Read the central directory.\n     */\n    readCentralDir: function() {\n        var file;\n\n        this.reader.setIndex(this.centralDirOffset);\n        while (this.reader.readAndCheckSignature(sig.CENTRAL_FILE_HEADER)) {\n            file = new ZipEntry({\n                zip64: this.zip64\n            }, this.loadOptions);\n            file.readCentralPart(this.reader);\n            this.files.push(file);\n        }\n\n        if (this.centralDirRecords !== this.files.length) {\n            if (this.centralDirRecords !== 0 && this.files.length === 0) {\n                // We expected some records but couldn't find ANY.\n                // This is really suspicious, as if something went wrong.\n                throw new Error(\"Corrupted zip or bug: expected \" + this.centralDirRecords + \" records in central dir, got \" + this.files.length);\n            } else {\n                // We found some records but not all.\n                // Something is wrong but we got something for the user: no error here.\n                // console.warn(\"expected\", this.centralDirRecords, \"records in central dir, got\", this.files.length);\n            }\n        }\n    },\n    /**\n     * Read the end of central directory.\n     */\n    readEndOfCentral: function() {\n        var offset = this.reader.lastIndexOfSignature(sig.CENTRAL_DIRECTORY_END);\n        if (offset < 0) {\n            // Check if the content is a truncated zip or complete garbage.\n            // A \"LOCAL_FILE_HEADER\" is not required at the beginning (auto\n            // extractible zip for example) but it can give a good hint.\n            // If an ajax request was used without responseType, we will also\n            // get unreadable data.\n            var isGarbage = !this.isSignature(0, sig.LOCAL_FILE_HEADER);\n\n            if (isGarbage) {\n                throw new Error(\"Can't find end of central directory : is this a zip file ? \" +\n                                \"If it is, see https://stuk.github.io/jszip/documentation/howto/read_zip.html\");\n            } else {\n                throw new Error(\"Corrupted zip: can't find end of central directory\");\n            }\n\n        }\n        this.reader.setIndex(offset);\n        var endOfCentralDirOffset = offset;\n        this.checkSignature(sig.CENTRAL_DIRECTORY_END);\n        this.readBlockEndOfCentral();\n\n\n        /* extract from the zip spec :\n            4)  If one of the fields in the end of central directory\n                record is too small to hold required data, the field\n                should be set to -1 (0xFFFF or 0xFFFFFFFF) and the\n                ZIP64 format record should be created.\n            5)  The end of central directory record and the\n                Zip64 end of central directory locator record must\n                reside on the same disk when splitting or spanning\n                an archive.\n         */\n        if (this.diskNumber === utils.MAX_VALUE_16BITS || this.diskWithCentralDirStart === utils.MAX_VALUE_16BITS || this.centralDirRecordsOnThisDisk === utils.MAX_VALUE_16BITS || this.centralDirRecords === utils.MAX_VALUE_16BITS || this.centralDirSize === utils.MAX_VALUE_32BITS || this.centralDirOffset === utils.MAX_VALUE_32BITS) {\n            this.zip64 = true;\n\n            /*\n            Warning : the zip64 extension is supported, but ONLY if the 64bits integer read from\n            the zip file can fit into a 32bits integer. This cannot be solved : JavaScript represents\n            all numbers as 64-bit double precision IEEE 754 floating point numbers.\n            So, we have 53bits for integers and bitwise operations treat everything as 32bits.\n            see https://developer.mozilla.org/en-US/docs/JavaScript/Reference/Operators/Bitwise_Operators\n            and http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-262.pdf section 8.5\n            */\n\n            // should look for a zip64 EOCD locator\n            offset = this.reader.lastIndexOfSignature(sig.ZIP64_CENTRAL_DIRECTORY_LOCATOR);\n            if (offset < 0) {\n                throw new Error(\"Corrupted zip: can't find the ZIP64 end of central directory locator\");\n            }\n            this.reader.setIndex(offset);\n            this.checkSignature(sig.ZIP64_CENTRAL_DIRECTORY_LOCATOR);\n            this.readBlockZip64EndOfCentralLocator();\n\n            // now the zip64 EOCD record\n            if (!this.isSignature(this.relativeOffsetEndOfZip64CentralDir, sig.ZIP64_CENTRAL_DIRECTORY_END)) {\n                // console.warn(\"ZIP64 end of central directory not where expected.\");\n                this.relativeOffsetEndOfZip64CentralDir = this.reader.lastIndexOfSignature(sig.ZIP64_CENTRAL_DIRECTORY_END);\n                if (this.relativeOffsetEndOfZip64CentralDir < 0) {\n                    throw new Error(\"Corrupted zip: can't find the ZIP64 end of central directory\");\n                }\n            }\n            this.reader.setIndex(this.relativeOffsetEndOfZip64CentralDir);\n            this.checkSignature(sig.ZIP64_CENTRAL_DIRECTORY_END);\n            this.readBlockZip64EndOfCentral();\n        }\n\n        var expectedEndOfCentralDirOffset = this.centralDirOffset + this.centralDirSize;\n        if (this.zip64) {\n            expectedEndOfCentralDirOffset += 20; // end of central dir 64 locator\n            expectedEndOfCentralDirOffset += 12 /* should not include the leading 12 bytes */ + this.zip64EndOfCentralSize;\n        }\n\n        var extraBytes = endOfCentralDirOffset - expectedEndOfCentralDirOffset;\n\n        if (extraBytes > 0) {\n            // console.warn(extraBytes, \"extra bytes at beginning or within zipfile\");\n            if (this.isSignature(endOfCentralDirOffset, sig.CENTRAL_FILE_HEADER)) {\n                // The offsets seem wrong, but we have something at the specified offset.\n                // So… we keep it.\n            } else {\n                // the offset is wrong, update the \"zero\" of the reader\n                // this happens if data has been prepended (crx files for example)\n                this.reader.zero = extraBytes;\n            }\n        } else if (extraBytes < 0) {\n            throw new Error(\"Corrupted zip: missing \" + Math.abs(extraBytes) + \" bytes.\");\n        }\n    },\n    prepareReader: function(data) {\n        this.reader = readerFor(data);\n    },\n    /**\n     * Read a zip file and create ZipEntries.\n     * @param {String|ArrayBuffer|Uint8Array|Buffer} data the binary string representing a zip file.\n     */\n    load: function(data) {\n        this.prepareReader(data);\n        this.readEndOfCentral();\n        this.readCentralDir();\n        this.readLocalFiles();\n    }\n};\n// }}} end of ZipEntries\nmodule.exports = ZipEntries;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/zipEntries.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/zipEntry.js":
/*!****************************************************************************!*\
  !*** ./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/zipEntry.js ***!
  \****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar readerFor = __webpack_require__(/*! ./reader/readerFor */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/readerFor.js\");\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\nvar CompressedObject = __webpack_require__(/*! ./compressedObject */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/compressedObject.js\");\nvar crc32fn = __webpack_require__(/*! ./crc32 */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/crc32.js\");\nvar utf8 = __webpack_require__(/*! ./utf8 */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utf8.js\");\nvar compressions = __webpack_require__(/*! ./compressions */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/compressions.js\");\nvar support = __webpack_require__(/*! ./support */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/support.js\");\n\nvar MADE_BY_DOS = 0x00;\nvar MADE_BY_UNIX = 0x03;\n\n/**\n * Find a compression registered in JSZip.\n * @param {string} compressionMethod the method magic to find.\n * @return {Object|null} the JSZip compression object, null if none found.\n */\nvar findCompression = function(compressionMethod) {\n    for (var method in compressions) {\n        if (!Object.prototype.hasOwnProperty.call(compressions, method)) {\n            continue;\n        }\n        if (compressions[method].magic === compressionMethod) {\n            return compressions[method];\n        }\n    }\n    return null;\n};\n\n// class ZipEntry {{{\n/**\n * An entry in the zip file.\n * @constructor\n * @param {Object} options Options of the current file.\n * @param {Object} loadOptions Options for loading the stream.\n */\nfunction ZipEntry(options, loadOptions) {\n    this.options = options;\n    this.loadOptions = loadOptions;\n}\nZipEntry.prototype = {\n    /**\n     * say if the file is encrypted.\n     * @return {boolean} true if the file is encrypted, false otherwise.\n     */\n    isEncrypted: function() {\n        // bit 1 is set\n        return (this.bitFlag & 0x0001) === 0x0001;\n    },\n    /**\n     * say if the file has utf-8 filename/comment.\n     * @return {boolean} true if the filename/comment is in utf-8, false otherwise.\n     */\n    useUTF8: function() {\n        // bit 11 is set\n        return (this.bitFlag & 0x0800) === 0x0800;\n    },\n    /**\n     * Read the local part of a zip file and add the info in this object.\n     * @param {DataReader} reader the reader to use.\n     */\n    readLocalPart: function(reader) {\n        var compression, localExtraFieldsLength;\n\n        // we already know everything from the central dir !\n        // If the central dir data are false, we are doomed.\n        // On the bright side, the local part is scary  : zip64, data descriptors, both, etc.\n        // The less data we get here, the more reliable this should be.\n        // Let's skip the whole header and dash to the data !\n        reader.skip(22);\n        // in some zip created on windows, the filename stored in the central dir contains \\ instead of /.\n        // Strangely, the filename here is OK.\n        // I would love to treat these zip files as corrupted (see http://www.info-zip.org/FAQ.html#backslashes\n        // or APPNOTE#********, \"All slashes MUST be forward slashes '/'\") but there are a lot of bad zip generators...\n        // Search \"unzip mismatching \"local\" filename continuing with \"central\" filename version\" on\n        // the internet.\n        //\n        // I think I see the logic here : the central directory is used to display\n        // content and the local directory is used to extract the files. Mixing / and \\\n        // may be used to display \\ to windows users and use / when extracting the files.\n        // Unfortunately, this lead also to some issues : http://seclists.org/fulldisclosure/2009/Sep/394\n        this.fileNameLength = reader.readInt(2);\n        localExtraFieldsLength = reader.readInt(2); // can't be sure this will be the same as the central dir\n        // the fileName is stored as binary data, the handleUTF8 method will take care of the encoding.\n        this.fileName = reader.readData(this.fileNameLength);\n        reader.skip(localExtraFieldsLength);\n\n        if (this.compressedSize === -1 || this.uncompressedSize === -1) {\n            throw new Error(\"Bug or corrupted zip : didn't get enough information from the central directory \" + \"(compressedSize === -1 || uncompressedSize === -1)\");\n        }\n\n        compression = findCompression(this.compressionMethod);\n        if (compression === null) { // no compression found\n            throw new Error(\"Corrupted zip : compression \" + utils.pretty(this.compressionMethod) + \" unknown (inner file : \" + utils.transformTo(\"string\", this.fileName) + \")\");\n        }\n        this.decompressed = new CompressedObject(this.compressedSize, this.uncompressedSize, this.crc32, compression, reader.readData(this.compressedSize));\n    },\n\n    /**\n     * Read the central part of a zip file and add the info in this object.\n     * @param {DataReader} reader the reader to use.\n     */\n    readCentralPart: function(reader) {\n        this.versionMadeBy = reader.readInt(2);\n        reader.skip(2);\n        // this.versionNeeded = reader.readInt(2);\n        this.bitFlag = reader.readInt(2);\n        this.compressionMethod = reader.readString(2);\n        this.date = reader.readDate();\n        this.crc32 = reader.readInt(4);\n        this.compressedSize = reader.readInt(4);\n        this.uncompressedSize = reader.readInt(4);\n        var fileNameLength = reader.readInt(2);\n        this.extraFieldsLength = reader.readInt(2);\n        this.fileCommentLength = reader.readInt(2);\n        this.diskNumberStart = reader.readInt(2);\n        this.internalFileAttributes = reader.readInt(2);\n        this.externalFileAttributes = reader.readInt(4);\n        this.localHeaderOffset = reader.readInt(4);\n\n        if (this.isEncrypted()) {\n            throw new Error(\"Encrypted zip are not supported\");\n        }\n\n        // will be read in the local part, see the comments there\n        reader.skip(fileNameLength);\n        this.readExtraFields(reader);\n        this.parseZIP64ExtraField(reader);\n        this.fileComment = reader.readData(this.fileCommentLength);\n    },\n\n    /**\n     * Parse the external file attributes and get the unix/dos permissions.\n     */\n    processAttributes: function () {\n        this.unixPermissions = null;\n        this.dosPermissions = null;\n        var madeBy = this.versionMadeBy >> 8;\n\n        // Check if we have the DOS directory flag set.\n        // We look for it in the DOS and UNIX permissions\n        // but some unknown platform could set it as a compatibility flag.\n        this.dir = this.externalFileAttributes & 0x0010 ? true : false;\n\n        if(madeBy === MADE_BY_DOS) {\n            // first 6 bits (0 to 5)\n            this.dosPermissions = this.externalFileAttributes & 0x3F;\n        }\n\n        if(madeBy === MADE_BY_UNIX) {\n            this.unixPermissions = (this.externalFileAttributes >> 16) & 0xFFFF;\n            // the octal permissions are in (this.unixPermissions & 0x01FF).toString(8);\n        }\n\n        // fail safe : if the name ends with a / it probably means a folder\n        if (!this.dir && this.fileNameStr.slice(-1) === \"/\") {\n            this.dir = true;\n        }\n    },\n\n    /**\n     * Parse the ZIP64 extra field and merge the info in the current ZipEntry.\n     * @param {DataReader} reader the reader to use.\n     */\n    parseZIP64ExtraField: function() {\n        if (!this.extraFields[0x0001]) {\n            return;\n        }\n\n        // should be something, preparing the extra reader\n        var extraReader = readerFor(this.extraFields[0x0001].value);\n\n        // I really hope that these 64bits integer can fit in 32 bits integer, because js\n        // won't let us have more.\n        if (this.uncompressedSize === utils.MAX_VALUE_32BITS) {\n            this.uncompressedSize = extraReader.readInt(8);\n        }\n        if (this.compressedSize === utils.MAX_VALUE_32BITS) {\n            this.compressedSize = extraReader.readInt(8);\n        }\n        if (this.localHeaderOffset === utils.MAX_VALUE_32BITS) {\n            this.localHeaderOffset = extraReader.readInt(8);\n        }\n        if (this.diskNumberStart === utils.MAX_VALUE_32BITS) {\n            this.diskNumberStart = extraReader.readInt(4);\n        }\n    },\n    /**\n     * Read the central part of a zip file and add the info in this object.\n     * @param {DataReader} reader the reader to use.\n     */\n    readExtraFields: function(reader) {\n        var end = reader.index + this.extraFieldsLength,\n            extraFieldId,\n            extraFieldLength,\n            extraFieldValue;\n\n        if (!this.extraFields) {\n            this.extraFields = {};\n        }\n\n        while (reader.index + 4 < end) {\n            extraFieldId = reader.readInt(2);\n            extraFieldLength = reader.readInt(2);\n            extraFieldValue = reader.readData(extraFieldLength);\n\n            this.extraFields[extraFieldId] = {\n                id: extraFieldId,\n                length: extraFieldLength,\n                value: extraFieldValue\n            };\n        }\n\n        reader.setIndex(end);\n    },\n    /**\n     * Apply an UTF8 transformation if needed.\n     */\n    handleUTF8: function() {\n        var decodeParamType = support.uint8array ? \"uint8array\" : \"array\";\n        if (this.useUTF8()) {\n            this.fileNameStr = utf8.utf8decode(this.fileName);\n            this.fileCommentStr = utf8.utf8decode(this.fileComment);\n        } else {\n            var upath = this.findExtraFieldUnicodePath();\n            if (upath !== null) {\n                this.fileNameStr = upath;\n            } else {\n                // ASCII text or unsupported code page\n                var fileNameByteArray =  utils.transformTo(decodeParamType, this.fileName);\n                this.fileNameStr = this.loadOptions.decodeFileName(fileNameByteArray);\n            }\n\n            var ucomment = this.findExtraFieldUnicodeComment();\n            if (ucomment !== null) {\n                this.fileCommentStr = ucomment;\n            } else {\n                // ASCII text or unsupported code page\n                var commentByteArray =  utils.transformTo(decodeParamType, this.fileComment);\n                this.fileCommentStr = this.loadOptions.decodeFileName(commentByteArray);\n            }\n        }\n    },\n\n    /**\n     * Find the unicode path declared in the extra field, if any.\n     * @return {String} the unicode path, null otherwise.\n     */\n    findExtraFieldUnicodePath: function() {\n        var upathField = this.extraFields[0x7075];\n        if (upathField) {\n            var extraReader = readerFor(upathField.value);\n\n            // wrong version\n            if (extraReader.readInt(1) !== 1) {\n                return null;\n            }\n\n            // the crc of the filename changed, this field is out of date.\n            if (crc32fn(this.fileName) !== extraReader.readInt(4)) {\n                return null;\n            }\n\n            return utf8.utf8decode(extraReader.readData(upathField.length - 5));\n        }\n        return null;\n    },\n\n    /**\n     * Find the unicode comment declared in the extra field, if any.\n     * @return {String} the unicode comment, null otherwise.\n     */\n    findExtraFieldUnicodeComment: function() {\n        var ucommentField = this.extraFields[0x6375];\n        if (ucommentField) {\n            var extraReader = readerFor(ucommentField.value);\n\n            // wrong version\n            if (extraReader.readInt(1) !== 1) {\n                return null;\n            }\n\n            // the crc of the comment changed, this field is out of date.\n            if (crc32fn(this.fileComment) !== extraReader.readInt(4)) {\n                return null;\n            }\n\n            return utf8.utf8decode(extraReader.readData(ucommentField.length - 5));\n        }\n        return null;\n    }\n};\nmodule.exports = ZipEntry;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/zipEntry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/zipObject.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/zipObject.js ***!
  \*****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar StreamHelper = __webpack_require__(/*! ./stream/StreamHelper */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/StreamHelper.js\");\nvar DataWorker = __webpack_require__(/*! ./stream/DataWorker */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/DataWorker.js\");\nvar utf8 = __webpack_require__(/*! ./utf8 */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utf8.js\");\nvar CompressedObject = __webpack_require__(/*! ./compressedObject */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/compressedObject.js\");\nvar GenericWorker = __webpack_require__(/*! ./stream/GenericWorker */ \"(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/GenericWorker.js\");\n\n/**\n * A simple object representing a file in the zip file.\n * @constructor\n * @param {string} name the name of the file\n * @param {String|ArrayBuffer|Uint8Array|Buffer} data the data\n * @param {Object} options the options of the file\n */\nvar ZipObject = function(name, data, options) {\n    this.name = name;\n    this.dir = options.dir;\n    this.date = options.date;\n    this.comment = options.comment;\n    this.unixPermissions = options.unixPermissions;\n    this.dosPermissions = options.dosPermissions;\n\n    this._data = data;\n    this._dataBinary = options.binary;\n    // keep only the compression\n    this.options = {\n        compression : options.compression,\n        compressionOptions : options.compressionOptions\n    };\n};\n\nZipObject.prototype = {\n    /**\n     * Create an internal stream for the content of this object.\n     * @param {String} type the type of each chunk.\n     * @return StreamHelper the stream.\n     */\n    internalStream: function (type) {\n        var result = null, outputType = \"string\";\n        try {\n            if (!type) {\n                throw new Error(\"No output type specified.\");\n            }\n            outputType = type.toLowerCase();\n            var askUnicodeString = outputType === \"string\" || outputType === \"text\";\n            if (outputType === \"binarystring\" || outputType === \"text\") {\n                outputType = \"string\";\n            }\n            result = this._decompressWorker();\n\n            var isUnicodeString = !this._dataBinary;\n\n            if (isUnicodeString && !askUnicodeString) {\n                result = result.pipe(new utf8.Utf8EncodeWorker());\n            }\n            if (!isUnicodeString && askUnicodeString) {\n                result = result.pipe(new utf8.Utf8DecodeWorker());\n            }\n        } catch (e) {\n            result = new GenericWorker(\"error\");\n            result.error(e);\n        }\n\n        return new StreamHelper(result, outputType, \"\");\n    },\n\n    /**\n     * Prepare the content in the asked type.\n     * @param {String} type the type of the result.\n     * @param {Function} onUpdate a function to call on each internal update.\n     * @return Promise the promise of the result.\n     */\n    async: function (type, onUpdate) {\n        return this.internalStream(type).accumulate(onUpdate);\n    },\n\n    /**\n     * Prepare the content as a nodejs stream.\n     * @param {String} type the type of each chunk.\n     * @param {Function} onUpdate a function to call on each internal update.\n     * @return Stream the stream.\n     */\n    nodeStream: function (type, onUpdate) {\n        return this.internalStream(type || \"nodebuffer\").toNodejsStream(onUpdate);\n    },\n\n    /**\n     * Return a worker for the compressed content.\n     * @private\n     * @param {Object} compression the compression object to use.\n     * @param {Object} compressionOptions the options to use when compressing.\n     * @return Worker the worker.\n     */\n    _compressWorker: function (compression, compressionOptions) {\n        if (\n            this._data instanceof CompressedObject &&\n            this._data.compression.magic === compression.magic\n        ) {\n            return this._data.getCompressedWorker();\n        } else {\n            var result = this._decompressWorker();\n            if(!this._dataBinary) {\n                result = result.pipe(new utf8.Utf8EncodeWorker());\n            }\n            return CompressedObject.createWorkerFrom(result, compression, compressionOptions);\n        }\n    },\n    /**\n     * Return a worker for the decompressed content.\n     * @private\n     * @return Worker the worker.\n     */\n    _decompressWorker : function () {\n        if (this._data instanceof CompressedObject) {\n            return this._data.getContentWorker();\n        } else if (this._data instanceof GenericWorker) {\n            return this._data;\n        } else {\n            return new DataWorker(this._data);\n        }\n    }\n};\n\nvar removedMethods = [\"asText\", \"asBinary\", \"asNodeBuffer\", \"asUint8Array\", \"asArrayBuffer\"];\nvar removedFn = function () {\n    throw new Error(\"This method has been removed in JSZip 3.0, please check the upgrade guide.\");\n};\n\nfor(var i = 0; i < removedMethods.length; i++) {\n    ZipObject.prototype[removedMethods[i]] = removedFn;\n}\nmodule.exports = ZipObject;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/zipObject.js\n");

/***/ })

};
;