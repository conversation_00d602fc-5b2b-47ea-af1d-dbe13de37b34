@layer tailwind-base, antd;

@layer tailwind-base {
  @tailwind base;
}
@tailwind components;
@tailwind utilities;

/* Global scrollbar styles */
* {
  /* Firefox */
  scrollbar-width: none;
  /* IE 10+ */
  -ms-overflow-style: none;
}

/* Webkit (Chrome, Safari, Edge) */
*::-webkit-scrollbar {
  display: none;
}

/* Global cursor styles */
input, textarea, [contenteditable="true"] {
  caret-color: #000000 !important;
}

.CodeMirror-cursor {
   border-left: 2px solid #ff0000 !important; /* Set cursor color to red, width to 2px */
}

/* Dark mode cursor styles */
@media (prefers-color-scheme: dark) {
  input, textarea, [contenteditable="true"] {
    caret-color: #ffffff;
  }
}

/* If using Tailwind's dark class to control dark mode */
.dark input,
.dark textarea,
.dark [contenteditable="true"] {
  caret-color: #ffffff;
}

/* Ant Design modal styles */
.ant-modal {
  top: unset;
  padding-bottom: unset;
}

.ant-modal-root .ant-modal-wrap {
  display: flex;
  justify-content: center;
  align-items: center;
}

:root {
  color-scheme: light;
}

html,
body {
  background-color: white;
  overflow-x: hidden;
  height: 100%;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 224 71.4% 4.1%;
    --card: 0 0% 100%;
    --card-foreground: 224 71.4% 4.1%;
    --popover: 0 0% 100%;
    --popover-foreground: 224 71.4% 4.1%;
    --primary: 262.1 83.3% 57.8%;
    --primary-foreground: 210 20% 98%;
    --secondary: 220 14.3% 95.9%;
    --secondary-foreground: 220.9 39.3% 11%;
    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;
    --accent: 220 14.3% 95.9%;
    --accent-foreground: 220.9 39.3% 11%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 20% 98%;
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 262.1 83.3% 57.8%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 224 71.4% 4.1%;
    --foreground: 210 20% 98%;
    --card: 224 71.4% 4.1%;
    --card-foreground: 210 20% 98%;
    --popover: 224 71.4% 4.1%;
    --popover-foreground: 210 20% 98%;
    --primary: 263.4 70% 50.4%;
    --primary-foreground: 210 20% 98%;
    --secondary: 215 27.9% 16.9%;
    --secondary-foreground: 210 20% 98%;
    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;
    --accent: 215 27.9% 16.9%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;
    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --ring: 263.4 70% 50.4%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-[hsl(var(--border))];
  }
  body {
    @apply bg-[hsl(var(--background))] text-[hsl(var(--foreground))];
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
