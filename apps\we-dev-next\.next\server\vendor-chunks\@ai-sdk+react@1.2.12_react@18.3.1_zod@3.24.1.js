"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ai-sdk+react@1.2.12_react@18.3.1_zod@3.24.1";
exports.ids = ["vendor-chunks/@ai-sdk+react@1.2.12_react@18.3.1_zod@3.24.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@ai-sdk+react@1.2.12_react@18.3.1_zod@3.24.1/node_modules/@ai-sdk/react/dist/index.mjs":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ai-sdk+react@1.2.12_react@18.3.1_zod@3.24.1/node_modules/@ai-sdk/react/dist/index.mjs ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   experimental_useObject: () => (/* binding */ experimental_useObject),\n/* harmony export */   useAssistant: () => (/* binding */ useAssistant),\n/* harmony export */   useChat: () => (/* binding */ useChat),\n/* harmony export */   useCompletion: () => (/* binding */ useCompletion)\n/* harmony export */ });\n/* harmony import */ var _ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ai-sdk/provider-utils */ \"(ssr)/./node_modules/.pnpm/@ai-sdk+provider-utils@2.2.8_zod@3.24.1/node_modules/@ai-sdk/provider-utils/dist/index.mjs\");\n/* harmony import */ var _ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ai-sdk/ui-utils */ \"(ssr)/./node_modules/.pnpm/@ai-sdk+ui-utils@1.2.11_zod@3.24.1/node_modules/@ai-sdk/ui-utils/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var swr__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! swr */ \"(ssr)/./node_modules/.pnpm/swr@2.2.5_react@18.3.1/node_modules/swr/dist/core/index.mjs\");\n/* harmony import */ var throttleit__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! throttleit */ \"(ssr)/./node_modules/.pnpm/throttleit@2.1.0/node_modules/throttleit/index.js\");\n// src/use-assistant.ts\n\n\n\nvar getOriginalFetch = () => fetch;\nfunction useAssistant({\n  api,\n  threadId: threadIdParam,\n  credentials,\n  headers,\n  body,\n  onError,\n  fetch: fetch2\n}) {\n  const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n  const [currentThreadId, setCurrentThreadId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\n    void 0\n  );\n  const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"awaiting_message\");\n  const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(void 0);\n  const handleInputChange = (event) => {\n    setInput(event.target.value);\n  };\n  const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const stop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (abortControllerRef.current) {\n      abortControllerRef.current.abort();\n      abortControllerRef.current = null;\n    }\n  }, []);\n  const append = async (message, requestOptions) => {\n    var _a, _b;\n    setStatus(\"in_progress\");\n    setMessages((messages2) => {\n      var _a2;\n      return [\n        ...messages2,\n        {\n          ...message,\n          id: (_a2 = message.id) != null ? _a2 : (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_1__.generateId)()\n        }\n      ];\n    });\n    setInput(\"\");\n    const abortController = new AbortController();\n    try {\n      abortControllerRef.current = abortController;\n      const actualFetch = fetch2 != null ? fetch2 : getOriginalFetch();\n      const response = await actualFetch(api, {\n        method: \"POST\",\n        credentials,\n        signal: abortController.signal,\n        headers: { \"Content-Type\": \"application/json\", ...headers },\n        body: JSON.stringify({\n          ...body,\n          // always use user-provided threadId when available:\n          threadId: (_a = threadIdParam != null ? threadIdParam : currentThreadId) != null ? _a : null,\n          message: message.content,\n          // optional request data:\n          data: requestOptions == null ? void 0 : requestOptions.data\n        })\n      });\n      if (!response.ok) {\n        throw new Error(\n          (_b = await response.text()) != null ? _b : \"Failed to fetch the assistant response.\"\n        );\n      }\n      if (response.body == null) {\n        throw new Error(\"The response body is empty.\");\n      }\n      await (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.processAssistantStream)({\n        stream: response.body,\n        onAssistantMessagePart(value) {\n          setMessages((messages2) => [\n            ...messages2,\n            {\n              id: value.id,\n              role: value.role,\n              content: value.content[0].text.value,\n              parts: []\n            }\n          ]);\n        },\n        onTextPart(value) {\n          setMessages((messages2) => {\n            const lastMessage = messages2[messages2.length - 1];\n            return [\n              ...messages2.slice(0, messages2.length - 1),\n              {\n                id: lastMessage.id,\n                role: lastMessage.role,\n                content: lastMessage.content + value,\n                parts: lastMessage.parts\n              }\n            ];\n          });\n        },\n        onAssistantControlDataPart(value) {\n          setCurrentThreadId(value.threadId);\n          setMessages((messages2) => {\n            const lastMessage = messages2[messages2.length - 1];\n            lastMessage.id = value.messageId;\n            return [...messages2.slice(0, messages2.length - 1), lastMessage];\n          });\n        },\n        onDataMessagePart(value) {\n          setMessages((messages2) => {\n            var _a2;\n            return [\n              ...messages2,\n              {\n                id: (_a2 = value.id) != null ? _a2 : (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_1__.generateId)(),\n                role: \"data\",\n                content: \"\",\n                data: value.data,\n                parts: []\n              }\n            ];\n          });\n        },\n        onErrorPart(value) {\n          setError(new Error(value));\n        }\n      });\n    } catch (error2) {\n      if ((0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_1__.isAbortError)(error2) && abortController.signal.aborted) {\n        abortControllerRef.current = null;\n        return;\n      }\n      if (onError && error2 instanceof Error) {\n        onError(error2);\n      }\n      setError(error2);\n    } finally {\n      abortControllerRef.current = null;\n      setStatus(\"awaiting_message\");\n    }\n  };\n  const submitMessage = async (event, requestOptions) => {\n    var _a;\n    (_a = event == null ? void 0 : event.preventDefault) == null ? void 0 : _a.call(event);\n    if (input === \"\") {\n      return;\n    }\n    append({ role: \"user\", content: input, parts: [] }, requestOptions);\n  };\n  const setThreadId = (threadId) => {\n    setCurrentThreadId(threadId);\n    setMessages([]);\n  };\n  return {\n    append,\n    messages,\n    setMessages,\n    threadId: currentThreadId,\n    setThreadId,\n    input,\n    setInput,\n    handleInputChange,\n    submitMessage,\n    status,\n    error,\n    stop\n  };\n}\n\n// src/use-chat.ts\n\n\n\n\n// src/throttle.ts\n\nfunction throttle(fn, waitMs) {\n  return waitMs != null ? throttleit__WEBPACK_IMPORTED_MODULE_4__(fn, waitMs) : fn;\n}\n\n// src/util/use-stable-value.ts\n\n\nfunction useStableValue(latestValue) {\n  const [value, setValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(latestValue);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!(0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.isDeepEqualData)(latestValue, value)) {\n      setValue(latestValue);\n    }\n  }, [latestValue, value]);\n  return value;\n}\n\n// src/use-chat.ts\nfunction useChat({\n  api = \"/api/chat\",\n  id,\n  initialMessages,\n  initialInput = \"\",\n  sendExtraMessageFields,\n  onToolCall,\n  experimental_prepareRequestBody,\n  maxSteps = 1,\n  streamProtocol = \"data\",\n  onResponse,\n  onFinish,\n  onError,\n  credentials,\n  headers,\n  body,\n  generateId: generateId2 = _ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_1__.generateId,\n  fetch: fetch2,\n  keepLastMessageOnError = true,\n  experimental_throttle: throttleWaitMs\n} = {}) {\n  const [hookId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(generateId2);\n  const chatId = id != null ? id : hookId;\n  const chatKey = typeof api === \"string\" ? [api, chatId] : chatId;\n  const stableInitialMessages = useStableValue(initialMessages != null ? initialMessages : []);\n  const processedInitialMessages = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.fillMessageParts)(stableInitialMessages),\n    [stableInitialMessages]\n  );\n  const { data: messages, mutate } = (0,swr__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(\n    [chatKey, \"messages\"],\n    null,\n    { fallbackData: processedInitialMessages }\n  );\n  const messagesRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(messages || []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    messagesRef.current = messages || [];\n  }, [messages]);\n  const { data: streamData, mutate: mutateStreamData } = (0,swr__WEBPACK_IMPORTED_MODULE_3__[\"default\"])([chatKey, \"streamData\"], null);\n  const streamDataRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(streamData);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    streamDataRef.current = streamData;\n  }, [streamData]);\n  const { data: status = \"ready\", mutate: mutateStatus } = (0,swr__WEBPACK_IMPORTED_MODULE_3__[\"default\"])([chatKey, \"status\"], null);\n  const { data: error = void 0, mutate: setError } = (0,swr__WEBPACK_IMPORTED_MODULE_3__[\"default\"])([chatKey, \"error\"], null);\n  const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const extraMetadataRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    credentials,\n    headers,\n    body\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    extraMetadataRef.current = {\n      credentials,\n      headers,\n      body\n    };\n  }, [credentials, headers, body]);\n  const triggerRequest = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    async (chatRequest, requestType = \"generate\") => {\n      var _a, _b;\n      mutateStatus(\"submitted\");\n      setError(void 0);\n      const chatMessages = (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.fillMessageParts)(chatRequest.messages);\n      const messageCount = chatMessages.length;\n      const maxStep = (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.extractMaxToolInvocationStep)(\n        (_a = chatMessages[chatMessages.length - 1]) == null ? void 0 : _a.toolInvocations\n      );\n      try {\n        const abortController = new AbortController();\n        abortControllerRef.current = abortController;\n        const throttledMutate = throttle(mutate, throttleWaitMs);\n        const throttledMutateStreamData = throttle(\n          mutateStreamData,\n          throttleWaitMs\n        );\n        const previousMessages = messagesRef.current;\n        throttledMutate(chatMessages, false);\n        const constructedMessagesPayload = sendExtraMessageFields ? chatMessages : chatMessages.map(\n          ({\n            role,\n            content,\n            experimental_attachments,\n            data,\n            annotations,\n            toolInvocations,\n            parts\n          }) => ({\n            role,\n            content,\n            ...experimental_attachments !== void 0 && {\n              experimental_attachments\n            },\n            ...data !== void 0 && { data },\n            ...annotations !== void 0 && { annotations },\n            ...toolInvocations !== void 0 && { toolInvocations },\n            ...parts !== void 0 && { parts }\n          })\n        );\n        const existingData = streamDataRef.current;\n        await (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.callChatApi)({\n          api,\n          body: (_b = experimental_prepareRequestBody == null ? void 0 : experimental_prepareRequestBody({\n            id: chatId,\n            messages: chatMessages,\n            requestData: chatRequest.data,\n            requestBody: chatRequest.body\n          })) != null ? _b : {\n            id: chatId,\n            messages: constructedMessagesPayload,\n            data: chatRequest.data,\n            ...extraMetadataRef.current.body,\n            ...chatRequest.body\n          },\n          streamProtocol,\n          credentials: extraMetadataRef.current.credentials,\n          headers: {\n            ...extraMetadataRef.current.headers,\n            ...chatRequest.headers\n          },\n          abortController: () => abortControllerRef.current,\n          restoreMessagesOnFailure() {\n            if (!keepLastMessageOnError) {\n              throttledMutate(previousMessages, false);\n            }\n          },\n          onResponse,\n          onUpdate({ message, data, replaceLastMessage }) {\n            mutateStatus(\"streaming\");\n            throttledMutate(\n              [\n                ...replaceLastMessage ? chatMessages.slice(0, chatMessages.length - 1) : chatMessages,\n                message\n              ],\n              false\n            );\n            if (data == null ? void 0 : data.length) {\n              throttledMutateStreamData(\n                [...existingData != null ? existingData : [], ...data],\n                false\n              );\n            }\n          },\n          onToolCall,\n          onFinish,\n          generateId: generateId2,\n          fetch: fetch2,\n          lastMessage: chatMessages[chatMessages.length - 1],\n          requestType\n        });\n        abortControllerRef.current = null;\n        mutateStatus(\"ready\");\n      } catch (err) {\n        if (err.name === \"AbortError\") {\n          abortControllerRef.current = null;\n          mutateStatus(\"ready\");\n          return null;\n        }\n        if (onError && err instanceof Error) {\n          onError(err);\n        }\n        setError(err);\n        mutateStatus(\"error\");\n      }\n      const messages2 = messagesRef.current;\n      if ((0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.shouldResubmitMessages)({\n        originalMaxToolInvocationStep: maxStep,\n        originalMessageCount: messageCount,\n        maxSteps,\n        messages: messages2\n      })) {\n        await triggerRequest({ messages: messages2 });\n      }\n    },\n    [\n      mutate,\n      mutateStatus,\n      api,\n      extraMetadataRef,\n      onResponse,\n      onFinish,\n      onError,\n      setError,\n      mutateStreamData,\n      streamDataRef,\n      streamProtocol,\n      sendExtraMessageFields,\n      experimental_prepareRequestBody,\n      onToolCall,\n      maxSteps,\n      messagesRef,\n      abortControllerRef,\n      generateId2,\n      fetch2,\n      keepLastMessageOnError,\n      throttleWaitMs,\n      chatId\n    ]\n  );\n  const append = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    async (message, {\n      data,\n      headers: headers2,\n      body: body2,\n      experimental_attachments = message.experimental_attachments\n    } = {}) => {\n      var _a, _b;\n      const attachmentsForRequest = await (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.prepareAttachmentsForRequest)(\n        experimental_attachments\n      );\n      const messages2 = messagesRef.current.concat({\n        ...message,\n        id: (_a = message.id) != null ? _a : generateId2(),\n        createdAt: (_b = message.createdAt) != null ? _b : /* @__PURE__ */ new Date(),\n        experimental_attachments: attachmentsForRequest.length > 0 ? attachmentsForRequest : void 0,\n        parts: (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.getMessageParts)(message)\n      });\n      return triggerRequest({ messages: messages2, headers: headers2, body: body2, data });\n    },\n    [triggerRequest, generateId2]\n  );\n  const reload = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    async ({ data, headers: headers2, body: body2 } = {}) => {\n      const messages2 = messagesRef.current;\n      if (messages2.length === 0) {\n        return null;\n      }\n      const lastMessage = messages2[messages2.length - 1];\n      return triggerRequest({\n        messages: lastMessage.role === \"assistant\" ? messages2.slice(0, -1) : messages2,\n        headers: headers2,\n        body: body2,\n        data\n      });\n    },\n    [triggerRequest]\n  );\n  const stop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (abortControllerRef.current) {\n      abortControllerRef.current.abort();\n      abortControllerRef.current = null;\n    }\n  }, []);\n  const experimental_resume = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async () => {\n    const messages2 = messagesRef.current;\n    triggerRequest({ messages: messages2 }, \"resume\");\n  }, [triggerRequest]);\n  const setMessages = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (messages2) => {\n      if (typeof messages2 === \"function\") {\n        messages2 = messages2(messagesRef.current);\n      }\n      const messagesWithParts = (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.fillMessageParts)(messages2);\n      mutate(messagesWithParts, false);\n      messagesRef.current = messagesWithParts;\n    },\n    [mutate]\n  );\n  const setData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (data) => {\n      if (typeof data === \"function\") {\n        data = data(streamDataRef.current);\n      }\n      mutateStreamData(data, false);\n      streamDataRef.current = data;\n    },\n    [mutateStreamData]\n  );\n  const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialInput);\n  const handleSubmit = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    async (event, options = {}, metadata) => {\n      var _a;\n      (_a = event == null ? void 0 : event.preventDefault) == null ? void 0 : _a.call(event);\n      if (!input && !options.allowEmptySubmit)\n        return;\n      if (metadata) {\n        extraMetadataRef.current = {\n          ...extraMetadataRef.current,\n          ...metadata\n        };\n      }\n      const attachmentsForRequest = await (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.prepareAttachmentsForRequest)(\n        options.experimental_attachments\n      );\n      const messages2 = messagesRef.current.concat({\n        id: generateId2(),\n        createdAt: /* @__PURE__ */ new Date(),\n        role: \"user\",\n        content: input,\n        experimental_attachments: attachmentsForRequest.length > 0 ? attachmentsForRequest : void 0,\n        parts: [{ type: \"text\", text: input }]\n      });\n      const chatRequest = {\n        messages: messages2,\n        headers: options.headers,\n        body: options.body,\n        data: options.data\n      };\n      triggerRequest(chatRequest);\n      setInput(\"\");\n    },\n    [input, generateId2, triggerRequest]\n  );\n  const handleInputChange = (e) => {\n    setInput(e.target.value);\n  };\n  const addToolResult = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    ({ toolCallId, result }) => {\n      const currentMessages = messagesRef.current;\n      (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.updateToolCallResult)({\n        messages: currentMessages,\n        toolCallId,\n        toolResult: result\n      });\n      mutate(\n        [\n          ...currentMessages.slice(0, currentMessages.length - 1),\n          { ...currentMessages[currentMessages.length - 1] }\n        ],\n        false\n      );\n      if (status === \"submitted\" || status === \"streaming\") {\n        return;\n      }\n      const lastMessage = currentMessages[currentMessages.length - 1];\n      if ((0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.isAssistantMessageWithCompletedToolCalls)(lastMessage)) {\n        triggerRequest({ messages: currentMessages });\n      }\n    },\n    [mutate, status, triggerRequest]\n  );\n  return {\n    messages: messages != null ? messages : [],\n    id: chatId,\n    setMessages,\n    data: streamData,\n    setData,\n    error,\n    append,\n    reload,\n    stop,\n    experimental_resume,\n    input,\n    setInput,\n    handleInputChange,\n    handleSubmit,\n    isLoading: status === \"submitted\" || status === \"streaming\",\n    status,\n    addToolResult\n  };\n}\n\n// src/use-completion.ts\n\n\n\nfunction useCompletion({\n  api = \"/api/completion\",\n  id,\n  initialCompletion = \"\",\n  initialInput = \"\",\n  credentials,\n  headers,\n  body,\n  streamProtocol = \"data\",\n  fetch: fetch2,\n  onResponse,\n  onFinish,\n  onError,\n  experimental_throttle: throttleWaitMs\n} = {}) {\n  const hookId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useId)();\n  const completionId = id || hookId;\n  const { data, mutate } = (0,swr__WEBPACK_IMPORTED_MODULE_3__[\"default\"])([api, completionId], null, {\n    fallbackData: initialCompletion\n  });\n  const { data: isLoading = false, mutate: mutateLoading } = (0,swr__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(\n    [completionId, \"loading\"],\n    null\n  );\n  const { data: streamData, mutate: mutateStreamData } = (0,swr__WEBPACK_IMPORTED_MODULE_3__[\"default\"])([completionId, \"streamData\"], null);\n  const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(void 0);\n  const completion = data;\n  const [abortController, setAbortController] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const extraMetadataRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    credentials,\n    headers,\n    body\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    extraMetadataRef.current = {\n      credentials,\n      headers,\n      body\n    };\n  }, [credentials, headers, body]);\n  const triggerRequest = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    async (prompt, options) => (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.callCompletionApi)({\n      api,\n      prompt,\n      credentials: extraMetadataRef.current.credentials,\n      headers: { ...extraMetadataRef.current.headers, ...options == null ? void 0 : options.headers },\n      body: {\n        ...extraMetadataRef.current.body,\n        ...options == null ? void 0 : options.body\n      },\n      streamProtocol,\n      fetch: fetch2,\n      // throttle streamed ui updates:\n      setCompletion: throttle(\n        (completion2) => mutate(completion2, false),\n        throttleWaitMs\n      ),\n      onData: throttle(\n        (data2) => mutateStreamData([...streamData != null ? streamData : [], ...data2 != null ? data2 : []], false),\n        throttleWaitMs\n      ),\n      setLoading: mutateLoading,\n      setError,\n      setAbortController,\n      onResponse,\n      onFinish,\n      onError\n    }),\n    [\n      mutate,\n      mutateLoading,\n      api,\n      extraMetadataRef,\n      setAbortController,\n      onResponse,\n      onFinish,\n      onError,\n      setError,\n      streamData,\n      streamProtocol,\n      fetch2,\n      mutateStreamData,\n      throttleWaitMs\n    ]\n  );\n  const stop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (abortController) {\n      abortController.abort();\n      setAbortController(null);\n    }\n  }, [abortController]);\n  const setCompletion = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (completion2) => {\n      mutate(completion2, false);\n    },\n    [mutate]\n  );\n  const complete = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    async (prompt, options) => {\n      return triggerRequest(prompt, options);\n    },\n    [triggerRequest]\n  );\n  const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialInput);\n  const handleSubmit = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (event) => {\n      var _a;\n      (_a = event == null ? void 0 : event.preventDefault) == null ? void 0 : _a.call(event);\n      return input ? complete(input) : void 0;\n    },\n    [input, complete]\n  );\n  const handleInputChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (e) => {\n      setInput(e.target.value);\n    },\n    [setInput]\n  );\n  return {\n    completion,\n    complete,\n    error,\n    setCompletion,\n    stop,\n    input,\n    setInput,\n    handleInputChange,\n    handleSubmit,\n    isLoading,\n    data: streamData\n  };\n}\n\n// src/use-object.ts\n\n\n\n\nvar getOriginalFetch2 = () => fetch;\nfunction useObject({\n  api,\n  id,\n  schema,\n  // required, in the future we will use it for validation\n  initialValue,\n  fetch: fetch2,\n  onError,\n  onFinish,\n  headers,\n  credentials\n}) {\n  const hookId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useId)();\n  const completionId = id != null ? id : hookId;\n  const { data, mutate } = (0,swr__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(\n    [api, completionId],\n    null,\n    { fallbackData: initialValue }\n  );\n  const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(void 0);\n  const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const stop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    var _a;\n    try {\n      (_a = abortControllerRef.current) == null ? void 0 : _a.abort();\n    } catch (ignored) {\n    } finally {\n      setIsLoading(false);\n      abortControllerRef.current = null;\n    }\n  }, []);\n  const submit = async (input) => {\n    var _a;\n    try {\n      mutate(void 0);\n      setIsLoading(true);\n      setError(void 0);\n      const abortController = new AbortController();\n      abortControllerRef.current = abortController;\n      const actualFetch = fetch2 != null ? fetch2 : getOriginalFetch2();\n      const response = await actualFetch(api, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          ...headers\n        },\n        credentials,\n        signal: abortController.signal,\n        body: JSON.stringify(input)\n      });\n      if (!response.ok) {\n        throw new Error(\n          (_a = await response.text()) != null ? _a : \"Failed to fetch the response.\"\n        );\n      }\n      if (response.body == null) {\n        throw new Error(\"The response body is empty.\");\n      }\n      let accumulatedText = \"\";\n      let latestObject = void 0;\n      await response.body.pipeThrough(new TextDecoderStream()).pipeTo(\n        new WritableStream({\n          write(chunk) {\n            accumulatedText += chunk;\n            const { value } = (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.parsePartialJson)(accumulatedText);\n            const currentObject = value;\n            if (!(0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.isDeepEqualData)(latestObject, currentObject)) {\n              latestObject = currentObject;\n              mutate(currentObject);\n            }\n          },\n          close() {\n            setIsLoading(false);\n            abortControllerRef.current = null;\n            if (onFinish != null) {\n              const validationResult = (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_1__.safeValidateTypes)({\n                value: latestObject,\n                schema: (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.asSchema)(schema)\n              });\n              onFinish(\n                validationResult.success ? { object: validationResult.value, error: void 0 } : { object: void 0, error: validationResult.error }\n              );\n            }\n          }\n        })\n      );\n    } catch (error2) {\n      if ((0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_1__.isAbortError)(error2)) {\n        return;\n      }\n      if (onError && error2 instanceof Error) {\n        onError(error2);\n      }\n      setIsLoading(false);\n      setError(error2 instanceof Error ? error2 : new Error(String(error2)));\n    }\n  };\n  return {\n    submit,\n    object: data,\n    error,\n    isLoading,\n    stop\n  };\n}\nvar experimental_useObject = useObject;\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@ai-sdk+react@1.2.12_react@18.3.1_zod@3.24.1/node_modules/@ai-sdk/react/dist/index.mjs\n");

/***/ })

};
;