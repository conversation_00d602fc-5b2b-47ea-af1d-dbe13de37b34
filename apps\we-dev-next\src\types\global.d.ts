declare global {
  interface Window {
    isLoading: boolean;
    fileHashMap: Map<string, string>;
  }

  // File system related types
  interface FileStats {
    isDirectory: boolean;
    isFile: boolean;
    size: number;
    mtime: Date;
  }

  // Process related types
  interface SpawnOptions {
    cwd?: string;
    processId?: string;
  }

  interface TerminalOptions {
    cols?: number;
    rows?: number;
    processId?: string;
  }

  interface ProcessResult {
    processId: string;
  }
}

export {};
