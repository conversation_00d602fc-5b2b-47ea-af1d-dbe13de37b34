'use client';

import { 
  FileText, 
  FileCode, 
  Image, 
  <PERSON><PERSON>son, 
  Settings,
  Folder,
  FolderOpen
} from 'lucide-react';

interface FileIconProps {
  fileName: string;
  isFolder?: boolean;
  isOpen?: boolean;
}

export default function FileIcon({ fileName, isFolder = false, isOpen = false }: FileIconProps) {
  if (isFolder) {
    return isOpen ? (
      <FolderOpen size={16} className="text-blue-500" />
    ) : (
      <Folder size={16} className="text-blue-500" />
    );
  }

  const extension = fileName.split('.').pop()?.toLowerCase();
  
  const getIconByExtension = () => {
    switch (extension) {
      case 'js':
      case 'jsx':
      case 'ts':
      case 'tsx':
        return <FileCode size={16} className="text-yellow-500" />;
      case 'html':
      case 'htm':
        return <FileCode size={16} className="text-orange-500" />;
      case 'css':
      case 'scss':
      case 'sass':
      case 'less':
        return <FileCode size={16} className="text-blue-500" />;
      case 'json':
        return <FileJson size={16} className="text-green-500" />;
      case 'md':
      case 'markdown':
        return <FileText size={16} className="text-gray-600 dark:text-gray-400" />;
      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'gif':
      case 'svg':
      case 'webp':
        return <Image size={16} className="text-purple-500" />;
      case 'config':
      case 'conf':
      case 'env':
        return <Settings size={16} className="text-gray-500" />;
      case 'py':
        return <FileCode size={16} className="text-blue-600" />;
      case 'java':
        return <FileCode size={16} className="text-red-500" />;
      case 'php':
        return <FileCode size={16} className="text-purple-600" />;
      case 'rb':
        return <FileCode size={16} className="text-red-600" />;
      case 'go':
        return <FileCode size={16} className="text-cyan-500" />;
      case 'rs':
        return <FileCode size={16} className="text-orange-600" />;
      case 'c':
      case 'cpp':
      case 'h':
        return <FileCode size={16} className="text-blue-700" />;
      default:
        return <FileText size={16} className="text-gray-500 dark:text-gray-400" />;
    }
  };

  return getIconByExtension();
}
