"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/utils/messageParser.ts":
/*!************************************!*\
  !*** ./src/utils/messageParser.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StreamingMessageParser: function() { return /* binding */ StreamingMessageParser; },\n/* harmony export */   debugFileCreation: function() { return /* binding */ debugFileCreation; },\n/* harmony export */   parseMessageForFiles: function() { return /* binding */ parseMessageForFiles; },\n/* harmony export */   parseMessages: function() { return /* binding */ parseMessages; },\n/* harmony export */   testAIResponse: function() { return /* binding */ testAIResponse; },\n/* harmony export */   testCompleteFlow: function() { return /* binding */ testCompleteFlow; },\n/* harmony export */   testMessageParser: function() { return /* binding */ testMessageParser; },\n/* harmony export */   testSimplifiedFormat: function() { return /* binding */ testSimplifiedFormat; }\n/* harmony export */ });\n/* harmony import */ var _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/stores/fileStore */ \"(app-pages-browser)/./src/stores/fileStore.ts\");\n/* __next_internal_client_entry_do_not_use__ StreamingMessageParser,parseMessages,testMessageParser,testAIResponse,testSimplifiedFormat,debugFileCreation,testCompleteFlow,parseMessageForFiles auto */ \nclass StreamingMessageParser {\n    parse(messageId, content) {\n        console.log(\"\\uD83D\\uDD0D Parsing message \".concat(messageId, \" for artifacts...\"));\n        console.log(\"\\uD83D\\uDCC4 Message content preview:\", content.substring(0, 200) + \"...\");\n        // Parse boltArtifact tags - handle both formats\n        const artifactRegex = /<boltArtifact[^>]*>([\\s\\S]*?)<\\/boltArtifact>/g;\n        let artifactMatch;\n        let foundArtifacts = 0;\n        while((artifactMatch = artifactRegex.exec(content)) !== null){\n            foundArtifacts++;\n            const fullMatch = artifactMatch[0];\n            const artifactContent = artifactMatch[1];\n            console.log(\"\\uD83D\\uDCE6 Found artifact \".concat(foundArtifacts, \":\"), fullMatch.substring(0, 100) + \"...\");\n            // Check if this is a simplified format (type=\"file\" path=\"...\" or name=\"...\")\n            const simplifiedFileMatch = fullMatch.match(/<boltArtifact[^>]*type=\"file\"[^>]*(?:path|name)=\"([^\"]+)\"[^>]*>/);\n            if (simplifiedFileMatch) {\n                const fileName = simplifiedFileMatch[1];\n                console.log(\"\\uD83D\\uDCC4 Simplified format detected for file: \".concat(fileName));\n                if (this.callbacks.onFileAction) {\n                    this.callbacks.onFileAction({\n                        type: \"file\",\n                        filePath: fileName,\n                        content: artifactContent.trim()\n                    });\n                }\n            } else {\n                // Standard format with boltAction tags\n                this.parseActions(artifactContent);\n            }\n        }\n        if (foundArtifacts === 0) {\n            console.log(\"ℹ️ No artifacts found in message \".concat(messageId));\n            console.log(\"\\uD83D\\uDD0D Checking for boltArtifact tags in content...\");\n            if (content.includes(\"<boltArtifact\")) {\n                console.log(\"⚠️ Found boltArtifact text but regex didn't match. Content:\", content);\n            } else {\n                console.log(\"❌ No boltArtifact tags found in content at all\");\n            }\n        }\n    }\n    parseActions(content) {\n        // Parse boltAction tags - handle multiple formats\n        const actionRegex = /<boltAction\\s+([^>]+)>([\\s\\S]*?)<\\/boltAction>/g;\n        let actionMatch;\n        let foundActions = 0;\n        while((actionMatch = actionRegex.exec(content)) !== null){\n            foundActions++;\n            const [, attributes, actionContent] = actionMatch;\n            // Parse attributes\n            const typeMatch = attributes.match(/type=\"([^\"]+)\"/);\n            const filePathMatch = attributes.match(/filePath=\"([^\"]+)\"/);\n            const pathMatch = attributes.match(/path=\"([^\"]+)\"/);\n            const type = typeMatch ? typeMatch[1] : \"\";\n            const filePath = filePathMatch ? filePathMatch[1] : pathMatch ? pathMatch[1] : \"\";\n            console.log(\"⚡ Found action \".concat(foundActions, \": type=\").concat(type, \", filePath=\").concat(filePath));\n            // Handle different type variations\n            if (type === \"file\" || type === \"createFile\") {\n                if (filePath && this.callbacks.onFileAction) {\n                    this.callbacks.onFileAction({\n                        type: \"file\",\n                        filePath,\n                        content: actionContent.trim()\n                    });\n                }\n            } else if (type === \"shell\") {\n                if (this.callbacks.onShellAction) {\n                    this.callbacks.onShellAction({\n                        type: \"shell\",\n                        content: actionContent.trim()\n                    });\n                }\n            } else if (type === \"start\") {\n                if (this.callbacks.onStartAction) {\n                    this.callbacks.onStartAction({\n                        type: \"start\",\n                        content: actionContent.trim()\n                    });\n                }\n            }\n        }\n        if (foundActions === 0) {\n            console.log(\"ℹ️ No actions found in artifact content\");\n        }\n    }\n    constructor(callbacks = {}){\n        this.callbacks = callbacks;\n    }\n}\n// Create a global message parser instance\nconst messageParser = new StreamingMessageParser({\n    onFileAction: async (action)=>{\n        console.log(\"\\uD83D\\uDD25 onFileAction called with:\", action);\n        const { addFile } = _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__.useFileStore.getState();\n        console.log(\"\\uD83D\\uDCC1 File store addFile function:\", typeof addFile);\n        try {\n            console.log(\"\\uD83D\\uDE80 Attempting to add file: \".concat(action.filePath));\n            await addFile(action.filePath, action.content);\n            console.log(\"✅ Created/updated file: \".concat(action.filePath));\n            console.log(\"\\uD83D\\uDCC4 Content preview: \".concat(action.content.substring(0, 100), \"...\"));\n            // Verify file was added\n            const currentFiles = _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__.useFileStore.getState().files;\n            console.log(\"\\uD83D\\uDCCA Current files after addition:\", Object.keys(currentFiles));\n            console.log(\"\\uD83D\\uDD0D File exists in store:\", action.filePath in currentFiles);\n        } catch (error) {\n            console.error(\"❌ Failed to create file \".concat(action.filePath, \":\"), error);\n        }\n    },\n    onShellAction: (action)=>{\n        console.log(\"Shell command:\", action.content);\n    // TODO: Integrate with terminal store to execute commands\n    },\n    onStartAction: (action)=>{\n        console.log(\"Start command:\", action.content);\n    // TODO: Integrate with terminal store to execute start commands\n    }\n});\nconst parseMessages = async (messages)=>{\n    console.log(\"\\uD83D\\uDE80 parseMessages called with \".concat(messages.length, \" messages\"));\n    console.log(\"\\uD83D\\uDCCA Current file store state:\", _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__.useFileStore.getState().files);\n    for (const message of messages){\n        if (message.role === \"assistant\") {\n            console.log(\"\\uD83E\\uDD16 Parsing assistant message: \".concat(message.id));\n            console.log(\"\\uD83D\\uDCCB Message object:\", message);\n            console.log(\"\\uD83D\\uDCDD Message content type:\", typeof message.content);\n            console.log(\"\\uD83D\\uDCDD Message content:\", message.content);\n            // Check if content contains boltArtifact\n            if (message.content && typeof message.content === \"string\" && message.content.includes(\"<boltArtifact\")) {\n                console.log(\"✅ Message contains boltArtifact tags, proceeding with parsing...\");\n            } else {\n                console.log(\"❌ Message does not contain boltArtifact tags\");\n            }\n            messageParser.parse(message.id, message.content);\n            // Check file store state after parsing\n            console.log(\"\\uD83D\\uDCCA File store state after parsing:\", _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__.useFileStore.getState().files);\n        }\n    }\n};\n// Test function to verify parsing works\nconst testMessageParser = ()=>{\n    console.log(\"\\uD83E\\uDDEA Testing message parser...\");\n    const testContent1 = 'Here\\'s a simple HTML file with \\'Hello World\\' using the boltArtifact format:\\n\\n<boltArtifact type=\"file\" name=\"test.html\">\\n<!DOCTYPE html>\\n<html lang=\"en\">\\n<head>\\n    <meta charset=\"UTF-8\">\\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\\n    <title>Hello World</title>\\n</head>\\n<body>\\n    <h1>Hello World</h1>\\n</body>\\n</html>\\n</boltArtifact>';\n    const testContent2 = 'Here\\'s a simple HTML file in the boltArtifact format:\\n\\n<boltArtifact>\\n<boltAction type=\"createFile\" path=\"index.html\">\\n<!DOCTYPE html>\\n<html lang=\"en\">\\n<head>\\n    <meta charset=\"UTF-8\">\\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\\n    <title>Hello World</title>\\n</head>\\n<body>\\n    <h1>Hello World</h1>\\n</body>\\n</html>\\n</boltAction>\\n</boltArtifact>';\n    console.log(\"\\uD83E\\uDDEA Testing message parser with format 1...\");\n    messageParser.parse(\"test-message-1\", testContent1);\n    console.log(\"\\uD83E\\uDDEA Testing message parser with format 2...\");\n    messageParser.parse(\"test-message-2\", testContent2);\n};\n// Test function with the exact AI response format\nconst testAIResponse = ()=>{\n    const aiResponse = '<boltArtifact><boltAction type=\"file\" filePath=\"test.html\"><!DOCTYPE html>\\n<html>\\n<head><title>Test</title></head>\\n<body><h1>Hello World</h1></body>\\n</html></boltAction></boltArtifact>';\n    console.log(\"\\uD83E\\uDDEA Testing with exact AI response format...\");\n    console.log(\"\\uD83D\\uDCC4 Test content:\", aiResponse);\n    messageParser.parse(\"ai-response-test\", aiResponse);\n};\n// Test function with the simplified format that the AI is actually using\nconst testSimplifiedFormat = ()=>{\n    const simplifiedResponse = 'Here\\'s a simple HTML file with \\'Hello World\\' using the boltArtifact format:\\n\\n<boltArtifact type=\"file\" path=\"index.html\">\\n<!DOCTYPE html>\\n<html lang=\"en\">\\n<head>\\n    <meta charset=\"UTF-8\">\\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\\n    <title>Hello World</title>\\n</head>\\n<body>\\n    <h1>Hello World</h1>\\n</body>\\n</html>\\n</boltArtifact>';\n    console.log(\"\\uD83E\\uDDEA Testing with simplified AI response format...\");\n    console.log(\"\\uD83D\\uDCC4 Test content:\", simplifiedResponse);\n    messageParser.parse(\"simplified-test\", simplifiedResponse);\n};\n// Debug function to test file creation\nconst debugFileCreation = ()=>{\n    console.log(\"\\uD83D\\uDD27 Testing file creation directly...\");\n    const { addFile } = _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__.useFileStore.getState();\n    addFile(\"debug-test.html\", \"<h1>Debug Test</h1>\").then(()=>{\n        console.log(\"✅ Direct file creation successful\");\n        const files = _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__.useFileStore.getState().files;\n        console.log(\"\\uD83D\\uDCC1 Current files:\", Object.keys(files));\n        console.log(\"\\uD83D\\uDCC4 Debug file content:\", files[\"debug-test.html\"]);\n    }).catch((error)=>{\n        console.error(\"❌ Direct file creation failed:\", error);\n    });\n};\n// Function to test the complete flow\nconst testCompleteFlow = ()=>{\n    console.log(\"\\uD83E\\uDDEA Testing complete AI response flow...\");\n    // Simulate an AI response with boltArtifact\n    const mockAIResponse = {\n        id: \"test-ai-response\",\n        role: \"assistant\",\n        content: 'I\\'ll create a simple HTML file for you:\\n\\n<boltArtifact id=\"simple-html\" title=\"Simple HTML File\">\\n<boltAction type=\"file\" filePath=\"index.html\"><!DOCTYPE html>\\n<html lang=\"en\">\\n<head>\\n    <meta charset=\"UTF-8\">\\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\\n    <title>Test Page</title>\\n</head>\\n<body>\\n    <h1>Hello from AI!</h1>\\n    <p>This file was created by the AI response parser.</p>\\n</body>\\n</html></boltAction>\\n</boltArtifact>\\n\\nThe HTML file has been created successfully.'\n    };\n    console.log(\"\\uD83D\\uDCE4 Simulating AI response:\", mockAIResponse);\n    parseMessages([\n        mockAIResponse\n    ]);\n};\n// Make test functions available globally for debugging\nif (true) {\n    window.testMessageParser = testMessageParser;\n    window.testAIResponse = testAIResponse;\n    window.testSimplifiedFormat = testSimplifiedFormat;\n    window.debugFileCreation = debugFileCreation;\n    window.testCompleteFlow = testCompleteFlow;\n}\n// Simple function to extract files from message content\nfunction parseMessageForFiles(content) {\n    const files = {};\n    // Parse boltArtifact and boltAction tags\n    const artifactRegex = /<boltArtifact[^>]*>([\\s\\S]*?)<\\/boltArtifact>/g;\n    let artifactMatch;\n    while((artifactMatch = artifactRegex.exec(content)) !== null){\n        const artifactContent = artifactMatch[1];\n        // Extract file actions\n        const fileActionRegex = /<boltAction\\s+type=\"file\"\\s+filePath=\"([^\"]+)\"\\s*>([\\s\\S]*?)<\\/boltAction>/g;\n        let fileMatch;\n        while((fileMatch = fileActionRegex.exec(artifactContent)) !== null){\n            const [, filePath, fileContent] = fileMatch;\n            files[filePath] = fileContent.trim();\n        }\n    }\n    return files;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/messageParser.ts\n"));

/***/ })

});