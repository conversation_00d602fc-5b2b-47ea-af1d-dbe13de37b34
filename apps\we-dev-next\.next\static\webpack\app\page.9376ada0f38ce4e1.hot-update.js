"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/utils/messageParser.ts":
/*!************************************!*\
  !*** ./src/utils/messageParser.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StreamingMessageParser: function() { return /* binding */ StreamingMessageParser; },\n/* harmony export */   debugFileCreation: function() { return /* binding */ debugFileCreation; },\n/* harmony export */   parseMessageForFiles: function() { return /* binding */ parseMessageForFiles; },\n/* harmony export */   parseMessages: function() { return /* binding */ parseMessages; },\n/* harmony export */   testAIResponse: function() { return /* binding */ testAIResponse; },\n/* harmony export */   testMessageParser: function() { return /* binding */ testMessageParser; }\n/* harmony export */ });\n/* harmony import */ var _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/stores/fileStore */ \"(app-pages-browser)/./src/stores/fileStore.ts\");\n/* __next_internal_client_entry_do_not_use__ StreamingMessageParser,parseMessages,testMessageParser,testAIResponse,debugFileCreation,parseMessageForFiles auto */ \nclass StreamingMessageParser {\n    parse(messageId, content) {\n        console.log(\"\\uD83D\\uDD0D Parsing message \".concat(messageId, \" for artifacts...\"));\n        console.log(\"\\uD83D\\uDCC4 Message content preview:\", content.substring(0, 200) + \"...\");\n        // Parse boltArtifact tags - handle both formats\n        const artifactRegex = /<boltArtifact[^>]*>([\\s\\S]*?)<\\/boltArtifact>/g;\n        let artifactMatch;\n        let foundArtifacts = 0;\n        while((artifactMatch = artifactRegex.exec(content)) !== null){\n            foundArtifacts++;\n            const fullMatch = artifactMatch[0];\n            const artifactContent = artifactMatch[1];\n            console.log(\"\\uD83D\\uDCE6 Found artifact \".concat(foundArtifacts, \":\"), fullMatch.substring(0, 100) + \"...\");\n            // Check if this is a simplified format (type=\"file\" name=\"...\")\n            const simplifiedMatch = fullMatch.match(/<boltArtifact[^>]*type=\"file\"[^>]*name=\"([^\"]+)\"[^>]*>/);\n            if (simplifiedMatch) {\n                const fileName = simplifiedMatch[1];\n                console.log(\"\\uD83D\\uDCC4 Simplified format detected for file: \".concat(fileName));\n                if (this.callbacks.onFileAction) {\n                    this.callbacks.onFileAction({\n                        type: \"file\",\n                        filePath: fileName,\n                        content: artifactContent.trim()\n                    });\n                }\n            } else {\n                // Standard format with boltAction tags\n                this.parseActions(artifactContent);\n            }\n        }\n        if (foundArtifacts === 0) {\n            console.log(\"ℹ️ No artifacts found in message \".concat(messageId));\n            console.log(\"\\uD83D\\uDD0D Checking for boltArtifact tags in content...\");\n            if (content.includes(\"<boltArtifact\")) {\n                console.log(\"⚠️ Found boltArtifact text but regex didn't match. Content:\", content);\n            } else {\n                console.log(\"❌ No boltArtifact tags found in content at all\");\n            }\n        }\n    }\n    parseActions(content) {\n        // Parse boltAction tags - handle multiple formats\n        const actionRegex = /<boltAction\\s+([^>]+)>([\\s\\S]*?)<\\/boltAction>/g;\n        let actionMatch;\n        let foundActions = 0;\n        while((actionMatch = actionRegex.exec(content)) !== null){\n            foundActions++;\n            const [, attributes, actionContent] = actionMatch;\n            // Parse attributes\n            const typeMatch = attributes.match(/type=\"([^\"]+)\"/);\n            const filePathMatch = attributes.match(/filePath=\"([^\"]+)\"/);\n            const pathMatch = attributes.match(/path=\"([^\"]+)\"/);\n            const type = typeMatch ? typeMatch[1] : \"\";\n            const filePath = filePathMatch ? filePathMatch[1] : pathMatch ? pathMatch[1] : \"\";\n            console.log(\"⚡ Found action \".concat(foundActions, \": type=\").concat(type, \", filePath=\").concat(filePath));\n            // Handle different type variations\n            if (type === \"file\" || type === \"createFile\") {\n                if (filePath && this.callbacks.onFileAction) {\n                    this.callbacks.onFileAction({\n                        type: \"file\",\n                        filePath,\n                        content: actionContent.trim()\n                    });\n                }\n            } else if (type === \"shell\") {\n                if (this.callbacks.onShellAction) {\n                    this.callbacks.onShellAction({\n                        type: \"shell\",\n                        content: actionContent.trim()\n                    });\n                }\n            } else if (type === \"start\") {\n                if (this.callbacks.onStartAction) {\n                    this.callbacks.onStartAction({\n                        type: \"start\",\n                        content: actionContent.trim()\n                    });\n                }\n            }\n        }\n        if (foundActions === 0) {\n            console.log(\"ℹ️ No actions found in artifact content\");\n        }\n    }\n    constructor(callbacks = {}){\n        this.callbacks = callbacks;\n    }\n}\n// Create a global message parser instance\nconst messageParser = new StreamingMessageParser({\n    onFileAction: async (action)=>{\n        const { addFile } = _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__.useFileStore.getState();\n        try {\n            await addFile(action.filePath, action.content);\n            console.log(\"✅ Created/updated file: \".concat(action.filePath));\n            console.log(\"\\uD83D\\uDCC4 Content preview: \".concat(action.content.substring(0, 100), \"...\"));\n        } catch (error) {\n            console.error(\"❌ Failed to create file \".concat(action.filePath, \":\"), error);\n        }\n    },\n    onShellAction: (action)=>{\n        console.log(\"Shell command:\", action.content);\n    // TODO: Integrate with terminal store to execute commands\n    },\n    onStartAction: (action)=>{\n        console.log(\"Start command:\", action.content);\n    // TODO: Integrate with terminal store to execute start commands\n    }\n});\nconst parseMessages = async (messages)=>{\n    console.log(\"\\uD83D\\uDE80 parseMessages called with \".concat(messages.length, \" messages\"));\n    for (const message of messages){\n        if (message.role === \"assistant\") {\n            console.log(\"\\uD83E\\uDD16 Parsing assistant message: \".concat(message.id));\n            console.log(\"\\uD83D\\uDCCB Message object:\", message);\n            console.log(\"\\uD83D\\uDCDD Message content type:\", typeof message.content);\n            console.log(\"\\uD83D\\uDCDD Message content:\", message.content);\n            messageParser.parse(message.id, message.content);\n        }\n    }\n};\n// Test function to verify parsing works\nconst testMessageParser = ()=>{\n    console.log(\"\\uD83E\\uDDEA Testing message parser...\");\n    const testContent1 = 'Here\\'s a simple HTML file with \\'Hello World\\' using the boltArtifact format:\\n\\n<boltArtifact type=\"file\" name=\"test.html\">\\n<!DOCTYPE html>\\n<html lang=\"en\">\\n<head>\\n    <meta charset=\"UTF-8\">\\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\\n    <title>Hello World</title>\\n</head>\\n<body>\\n    <h1>Hello World</h1>\\n</body>\\n</html>\\n</boltArtifact>';\n    const testContent2 = 'Here\\'s a simple HTML file in the boltArtifact format:\\n\\n<boltArtifact>\\n<boltAction type=\"createFile\" path=\"index.html\">\\n<!DOCTYPE html>\\n<html lang=\"en\">\\n<head>\\n    <meta charset=\"UTF-8\">\\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\\n    <title>Hello World</title>\\n</head>\\n<body>\\n    <h1>Hello World</h1>\\n</body>\\n</html>\\n</boltAction>\\n</boltArtifact>';\n    console.log(\"\\uD83E\\uDDEA Testing message parser with format 1...\");\n    messageParser.parse(\"test-message-1\", testContent1);\n    console.log(\"\\uD83E\\uDDEA Testing message parser with format 2...\");\n    messageParser.parse(\"test-message-2\", testContent2);\n};\n// Test function with the exact AI response format\nconst testAIResponse = ()=>{\n    const aiResponse = '<boltArtifact><boltAction type=\"file\" filePath=\"test.html\"><!DOCTYPE html>\\n<html>\\n<head><title>Test</title></head>\\n<body><h1>Hello World</h1></body>\\n</html></boltAction></boltArtifact>';\n    console.log(\"\\uD83E\\uDDEA Testing with exact AI response format...\");\n    console.log(\"\\uD83D\\uDCC4 Test content:\", aiResponse);\n    messageParser.parse(\"ai-response-test\", aiResponse);\n};\n// Debug function to test file creation\nconst debugFileCreation = ()=>{\n    console.log(\"\\uD83D\\uDD27 Testing file creation directly...\");\n    const { addFile } = _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__.useFileStore.getState();\n    addFile(\"debug-test.html\", \"<h1>Debug Test</h1>\").then(()=>{\n        console.log(\"✅ Direct file creation successful\");\n        const files = _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__.useFileStore.getState().files;\n        console.log(\"\\uD83D\\uDCC1 Current files:\", Object.keys(files));\n    }).catch((error)=>{\n        console.error(\"❌ Direct file creation failed:\", error);\n    });\n};\n// Make test functions available globally for debugging\nif (true) {\n    window.testMessageParser = testMessageParser;\n    window.testAIResponse = testAIResponse;\n    window.debugFileCreation = debugFileCreation;\n}\n// Simple function to extract files from message content\nfunction parseMessageForFiles(content) {\n    const files = {};\n    // Parse boltArtifact and boltAction tags\n    const artifactRegex = /<boltArtifact[^>]*>([\\s\\S]*?)<\\/boltArtifact>/g;\n    let artifactMatch;\n    while((artifactMatch = artifactRegex.exec(content)) !== null){\n        const artifactContent = artifactMatch[1];\n        // Extract file actions\n        const fileActionRegex = /<boltAction\\s+type=\"file\"\\s+filePath=\"([^\"]+)\"\\s*>([\\s\\S]*?)<\\/boltAction>/g;\n        let fileMatch;\n        while((fileMatch = fileActionRegex.exec(artifactContent)) !== null){\n            const [, filePath, fileContent] = fileMatch;\n            files[filePath] = fileContent.trim();\n        }\n    }\n    return files;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/messageParser.ts\n"));

/***/ })

});