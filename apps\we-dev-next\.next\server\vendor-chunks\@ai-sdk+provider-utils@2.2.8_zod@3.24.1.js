"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ai-sdk+provider-utils@2.2.8_zod@3.24.1";
exports.ids = ["vendor-chunks/@ai-sdk+provider-utils@2.2.8_zod@3.24.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@ai-sdk+provider-utils@2.2.8_zod@3.24.1/node_modules/@ai-sdk/provider-utils/dist/index.mjs":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ai-sdk+provider-utils@2.2.8_zod@3.24.1/node_modules/@ai-sdk/provider-utils/dist/index.mjs ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   asValidator: () => (/* binding */ asValidator),\n/* harmony export */   combineHeaders: () => (/* binding */ combineHeaders),\n/* harmony export */   convertAsyncIteratorToReadableStream: () => (/* binding */ convertAsyncIteratorToReadableStream),\n/* harmony export */   convertBase64ToUint8Array: () => (/* binding */ convertBase64ToUint8Array),\n/* harmony export */   convertUint8ArrayToBase64: () => (/* binding */ convertUint8ArrayToBase64),\n/* harmony export */   createBinaryResponseHandler: () => (/* binding */ createBinaryResponseHandler),\n/* harmony export */   createEventSourceParserStream: () => (/* binding */ createEventSourceParserStream),\n/* harmony export */   createEventSourceResponseHandler: () => (/* binding */ createEventSourceResponseHandler),\n/* harmony export */   createIdGenerator: () => (/* binding */ createIdGenerator),\n/* harmony export */   createJsonErrorResponseHandler: () => (/* binding */ createJsonErrorResponseHandler),\n/* harmony export */   createJsonResponseHandler: () => (/* binding */ createJsonResponseHandler),\n/* harmony export */   createJsonStreamResponseHandler: () => (/* binding */ createJsonStreamResponseHandler),\n/* harmony export */   createStatusCodeErrorResponseHandler: () => (/* binding */ createStatusCodeErrorResponseHandler),\n/* harmony export */   delay: () => (/* binding */ delay),\n/* harmony export */   extractResponseHeaders: () => (/* binding */ extractResponseHeaders),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   getErrorMessage: () => (/* binding */ getErrorMessage),\n/* harmony export */   getFromApi: () => (/* binding */ getFromApi),\n/* harmony export */   isAbortError: () => (/* binding */ isAbortError),\n/* harmony export */   isParsableJson: () => (/* binding */ isParsableJson),\n/* harmony export */   isValidator: () => (/* binding */ isValidator),\n/* harmony export */   loadApiKey: () => (/* binding */ loadApiKey),\n/* harmony export */   loadOptionalSetting: () => (/* binding */ loadOptionalSetting),\n/* harmony export */   loadSetting: () => (/* binding */ loadSetting),\n/* harmony export */   parseJSON: () => (/* binding */ parseJSON),\n/* harmony export */   parseProviderOptions: () => (/* binding */ parseProviderOptions),\n/* harmony export */   postFormDataToApi: () => (/* binding */ postFormDataToApi),\n/* harmony export */   postJsonToApi: () => (/* binding */ postJsonToApi),\n/* harmony export */   postToApi: () => (/* binding */ postToApi),\n/* harmony export */   removeUndefinedEntries: () => (/* binding */ removeUndefinedEntries),\n/* harmony export */   resolve: () => (/* binding */ resolve),\n/* harmony export */   safeParseJSON: () => (/* binding */ safeParseJSON),\n/* harmony export */   safeValidateTypes: () => (/* binding */ safeValidateTypes),\n/* harmony export */   validateTypes: () => (/* binding */ validateTypes),\n/* harmony export */   validator: () => (/* binding */ validator),\n/* harmony export */   validatorSymbol: () => (/* binding */ validatorSymbol),\n/* harmony export */   withoutTrailingSlash: () => (/* binding */ withoutTrailingSlash),\n/* harmony export */   zodValidator: () => (/* binding */ zodValidator)\n/* harmony export */ });\n/* harmony import */ var _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ai-sdk/provider */ \"(ssr)/./node_modules/.pnpm/@ai-sdk+provider@1.1.3/node_modules/@ai-sdk/provider/dist/index.mjs\");\n/* harmony import */ var nanoid_non_secure__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! nanoid/non-secure */ \"(ssr)/./node_modules/.pnpm/nanoid@3.3.8/node_modules/nanoid/non-secure/index.js\");\n/* harmony import */ var secure_json_parse__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! secure-json-parse */ \"(ssr)/./node_modules/.pnpm/secure-json-parse@2.7.0/node_modules/secure-json-parse/index.js\");\n// src/combine-headers.ts\nfunction combineHeaders(...headers) {\n  return headers.reduce(\n    (combinedHeaders, currentHeaders) => ({\n      ...combinedHeaders,\n      ...currentHeaders != null ? currentHeaders : {}\n    }),\n    {}\n  );\n}\n\n// src/convert-async-iterator-to-readable-stream.ts\nfunction convertAsyncIteratorToReadableStream(iterator) {\n  return new ReadableStream({\n    /**\n     * Called when the consumer wants to pull more data from the stream.\n     *\n     * @param {ReadableStreamDefaultController<T>} controller - The controller to enqueue data into the stream.\n     * @returns {Promise<void>}\n     */\n    async pull(controller) {\n      try {\n        const { value, done } = await iterator.next();\n        if (done) {\n          controller.close();\n        } else {\n          controller.enqueue(value);\n        }\n      } catch (error) {\n        controller.error(error);\n      }\n    },\n    /**\n     * Called when the consumer cancels the stream.\n     */\n    cancel() {\n    }\n  });\n}\n\n// src/delay.ts\nasync function delay(delayInMs) {\n  return delayInMs == null ? Promise.resolve() : new Promise((resolve2) => setTimeout(resolve2, delayInMs));\n}\n\n// src/event-source-parser-stream.ts\nfunction createEventSourceParserStream() {\n  let buffer = \"\";\n  let event = void 0;\n  let data = [];\n  let lastEventId = void 0;\n  let retry = void 0;\n  function parseLine(line, controller) {\n    if (line === \"\") {\n      dispatchEvent(controller);\n      return;\n    }\n    if (line.startsWith(\":\")) {\n      return;\n    }\n    const colonIndex = line.indexOf(\":\");\n    if (colonIndex === -1) {\n      handleField(line, \"\");\n      return;\n    }\n    const field = line.slice(0, colonIndex);\n    const valueStart = colonIndex + 1;\n    const value = valueStart < line.length && line[valueStart] === \" \" ? line.slice(valueStart + 1) : line.slice(valueStart);\n    handleField(field, value);\n  }\n  function dispatchEvent(controller) {\n    if (data.length > 0) {\n      controller.enqueue({\n        event,\n        data: data.join(\"\\n\"),\n        id: lastEventId,\n        retry\n      });\n      data = [];\n      event = void 0;\n      retry = void 0;\n    }\n  }\n  function handleField(field, value) {\n    switch (field) {\n      case \"event\":\n        event = value;\n        break;\n      case \"data\":\n        data.push(value);\n        break;\n      case \"id\":\n        lastEventId = value;\n        break;\n      case \"retry\":\n        const parsedRetry = parseInt(value, 10);\n        if (!isNaN(parsedRetry)) {\n          retry = parsedRetry;\n        }\n        break;\n    }\n  }\n  return new TransformStream({\n    transform(chunk, controller) {\n      const { lines, incompleteLine } = splitLines(buffer, chunk);\n      buffer = incompleteLine;\n      for (let i = 0; i < lines.length; i++) {\n        parseLine(lines[i], controller);\n      }\n    },\n    flush(controller) {\n      parseLine(buffer, controller);\n      dispatchEvent(controller);\n    }\n  });\n}\nfunction splitLines(buffer, chunk) {\n  const lines = [];\n  let currentLine = buffer;\n  for (let i = 0; i < chunk.length; ) {\n    const char = chunk[i++];\n    if (char === \"\\n\") {\n      lines.push(currentLine);\n      currentLine = \"\";\n    } else if (char === \"\\r\") {\n      lines.push(currentLine);\n      currentLine = \"\";\n      if (chunk[i] === \"\\n\") {\n        i++;\n      }\n    } else {\n      currentLine += char;\n    }\n  }\n  return { lines, incompleteLine: currentLine };\n}\n\n// src/extract-response-headers.ts\nfunction extractResponseHeaders(response) {\n  const headers = {};\n  response.headers.forEach((value, key) => {\n    headers[key] = value;\n  });\n  return headers;\n}\n\n// src/generate-id.ts\n\n\nvar createIdGenerator = ({\n  prefix,\n  size: defaultSize = 16,\n  alphabet = \"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz\",\n  separator = \"-\"\n} = {}) => {\n  const generator = (0,nanoid_non_secure__WEBPACK_IMPORTED_MODULE_0__.customAlphabet)(alphabet, defaultSize);\n  if (prefix == null) {\n    return generator;\n  }\n  if (alphabet.includes(separator)) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.InvalidArgumentError({\n      argument: \"separator\",\n      message: `The separator \"${separator}\" must not be part of the alphabet \"${alphabet}\".`\n    });\n  }\n  return (size) => `${prefix}${separator}${generator(size)}`;\n};\nvar generateId = createIdGenerator();\n\n// src/get-error-message.ts\nfunction getErrorMessage(error) {\n  if (error == null) {\n    return \"unknown error\";\n  }\n  if (typeof error === \"string\") {\n    return error;\n  }\n  if (error instanceof Error) {\n    return error.message;\n  }\n  return JSON.stringify(error);\n}\n\n// src/get-from-api.ts\n\n\n// src/remove-undefined-entries.ts\nfunction removeUndefinedEntries(record) {\n  return Object.fromEntries(\n    Object.entries(record).filter(([_key, value]) => value != null)\n  );\n}\n\n// src/is-abort-error.ts\nfunction isAbortError(error) {\n  return error instanceof Error && (error.name === \"AbortError\" || error.name === \"TimeoutError\");\n}\n\n// src/get-from-api.ts\nvar getOriginalFetch = () => globalThis.fetch;\nvar getFromApi = async ({\n  url,\n  headers = {},\n  successfulResponseHandler,\n  failedResponseHandler,\n  abortSignal,\n  fetch = getOriginalFetch()\n}) => {\n  try {\n    const response = await fetch(url, {\n      method: \"GET\",\n      headers: removeUndefinedEntries(headers),\n      signal: abortSignal\n    });\n    const responseHeaders = extractResponseHeaders(response);\n    if (!response.ok) {\n      let errorInformation;\n      try {\n        errorInformation = await failedResponseHandler({\n          response,\n          url,\n          requestBodyValues: {}\n        });\n      } catch (error) {\n        if (isAbortError(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError.isInstance(error)) {\n          throw error;\n        }\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n          message: \"Failed to process error response\",\n          cause: error,\n          statusCode: response.status,\n          url,\n          responseHeaders,\n          requestBodyValues: {}\n        });\n      }\n      throw errorInformation.value;\n    }\n    try {\n      return await successfulResponseHandler({\n        response,\n        url,\n        requestBodyValues: {}\n      });\n    } catch (error) {\n      if (error instanceof Error) {\n        if (isAbortError(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError.isInstance(error)) {\n          throw error;\n        }\n      }\n      throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: \"Failed to process successful response\",\n        cause: error,\n        statusCode: response.status,\n        url,\n        responseHeaders,\n        requestBodyValues: {}\n      });\n    }\n  } catch (error) {\n    if (isAbortError(error)) {\n      throw error;\n    }\n    if (error instanceof TypeError && error.message === \"fetch failed\") {\n      const cause = error.cause;\n      if (cause != null) {\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n          message: `Cannot connect to API: ${cause.message}`,\n          cause,\n          url,\n          isRetryable: true,\n          requestBodyValues: {}\n        });\n      }\n    }\n    throw error;\n  }\n};\n\n// src/load-api-key.ts\n\nfunction loadApiKey({\n  apiKey,\n  environmentVariableName,\n  apiKeyParameterName = \"apiKey\",\n  description\n}) {\n  if (typeof apiKey === \"string\") {\n    return apiKey;\n  }\n  if (apiKey != null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadAPIKeyError({\n      message: `${description} API key must be a string.`\n    });\n  }\n  if (typeof process === \"undefined\") {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadAPIKeyError({\n      message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter. Environment variables is not supported in this environment.`\n    });\n  }\n  apiKey = process.env[environmentVariableName];\n  if (apiKey == null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadAPIKeyError({\n      message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter or the ${environmentVariableName} environment variable.`\n    });\n  }\n  if (typeof apiKey !== \"string\") {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadAPIKeyError({\n      message: `${description} API key must be a string. The value of the ${environmentVariableName} environment variable is not a string.`\n    });\n  }\n  return apiKey;\n}\n\n// src/load-optional-setting.ts\nfunction loadOptionalSetting({\n  settingValue,\n  environmentVariableName\n}) {\n  if (typeof settingValue === \"string\") {\n    return settingValue;\n  }\n  if (settingValue != null || typeof process === \"undefined\") {\n    return void 0;\n  }\n  settingValue = process.env[environmentVariableName];\n  if (settingValue == null || typeof settingValue !== \"string\") {\n    return void 0;\n  }\n  return settingValue;\n}\n\n// src/load-setting.ts\n\nfunction loadSetting({\n  settingValue,\n  environmentVariableName,\n  settingName,\n  description\n}) {\n  if (typeof settingValue === \"string\") {\n    return settingValue;\n  }\n  if (settingValue != null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadSettingError({\n      message: `${description} setting must be a string.`\n    });\n  }\n  if (typeof process === \"undefined\") {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadSettingError({\n      message: `${description} setting is missing. Pass it using the '${settingName}' parameter. Environment variables is not supported in this environment.`\n    });\n  }\n  settingValue = process.env[environmentVariableName];\n  if (settingValue == null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadSettingError({\n      message: `${description} setting is missing. Pass it using the '${settingName}' parameter or the ${environmentVariableName} environment variable.`\n    });\n  }\n  if (typeof settingValue !== \"string\") {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadSettingError({\n      message: `${description} setting must be a string. The value of the ${environmentVariableName} environment variable is not a string.`\n    });\n  }\n  return settingValue;\n}\n\n// src/parse-json.ts\n\n\n\n// src/validate-types.ts\n\n\n// src/validator.ts\nvar validatorSymbol = Symbol.for(\"vercel.ai.validator\");\nfunction validator(validate) {\n  return { [validatorSymbol]: true, validate };\n}\nfunction isValidator(value) {\n  return typeof value === \"object\" && value !== null && validatorSymbol in value && value[validatorSymbol] === true && \"validate\" in value;\n}\nfunction asValidator(value) {\n  return isValidator(value) ? value : zodValidator(value);\n}\nfunction zodValidator(zodSchema) {\n  return validator((value) => {\n    const result = zodSchema.safeParse(value);\n    return result.success ? { success: true, value: result.data } : { success: false, error: result.error };\n  });\n}\n\n// src/validate-types.ts\nfunction validateTypes({\n  value,\n  schema: inputSchema\n}) {\n  const result = safeValidateTypes({ value, schema: inputSchema });\n  if (!result.success) {\n    throw _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TypeValidationError.wrap({ value, cause: result.error });\n  }\n  return result.value;\n}\nfunction safeValidateTypes({\n  value,\n  schema\n}) {\n  const validator2 = asValidator(schema);\n  try {\n    if (validator2.validate == null) {\n      return { success: true, value };\n    }\n    const result = validator2.validate(value);\n    if (result.success) {\n      return result;\n    }\n    return {\n      success: false,\n      error: _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TypeValidationError.wrap({ value, cause: result.error })\n    };\n  } catch (error) {\n    return {\n      success: false,\n      error: _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TypeValidationError.wrap({ value, cause: error })\n    };\n  }\n}\n\n// src/parse-json.ts\nfunction parseJSON({\n  text,\n  schema\n}) {\n  try {\n    const value = secure_json_parse__WEBPACK_IMPORTED_MODULE_2__.parse(text);\n    if (schema == null) {\n      return value;\n    }\n    return validateTypes({ value, schema });\n  } catch (error) {\n    if (_ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.JSONParseError.isInstance(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TypeValidationError.isInstance(error)) {\n      throw error;\n    }\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.JSONParseError({ text, cause: error });\n  }\n}\nfunction safeParseJSON({\n  text,\n  schema\n}) {\n  try {\n    const value = secure_json_parse__WEBPACK_IMPORTED_MODULE_2__.parse(text);\n    if (schema == null) {\n      return { success: true, value, rawValue: value };\n    }\n    const validationResult = safeValidateTypes({ value, schema });\n    return validationResult.success ? { ...validationResult, rawValue: value } : validationResult;\n  } catch (error) {\n    return {\n      success: false,\n      error: _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.JSONParseError.isInstance(error) ? error : new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.JSONParseError({ text, cause: error })\n    };\n  }\n}\nfunction isParsableJson(input) {\n  try {\n    secure_json_parse__WEBPACK_IMPORTED_MODULE_2__.parse(input);\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\n// src/parse-provider-options.ts\n\nfunction parseProviderOptions({\n  provider,\n  providerOptions,\n  schema\n}) {\n  if ((providerOptions == null ? void 0 : providerOptions[provider]) == null) {\n    return void 0;\n  }\n  const parsedProviderOptions = safeValidateTypes({\n    value: providerOptions[provider],\n    schema\n  });\n  if (!parsedProviderOptions.success) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.InvalidArgumentError({\n      argument: \"providerOptions\",\n      message: `invalid ${provider} provider options`,\n      cause: parsedProviderOptions.error\n    });\n  }\n  return parsedProviderOptions.value;\n}\n\n// src/post-to-api.ts\n\nvar getOriginalFetch2 = () => globalThis.fetch;\nvar postJsonToApi = async ({\n  url,\n  headers,\n  body,\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch\n}) => postToApi({\n  url,\n  headers: {\n    \"Content-Type\": \"application/json\",\n    ...headers\n  },\n  body: {\n    content: JSON.stringify(body),\n    values: body\n  },\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch\n});\nvar postFormDataToApi = async ({\n  url,\n  headers,\n  formData,\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch\n}) => postToApi({\n  url,\n  headers,\n  body: {\n    content: formData,\n    values: Object.fromEntries(formData.entries())\n  },\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch\n});\nvar postToApi = async ({\n  url,\n  headers = {},\n  body,\n  successfulResponseHandler,\n  failedResponseHandler,\n  abortSignal,\n  fetch = getOriginalFetch2()\n}) => {\n  try {\n    const response = await fetch(url, {\n      method: \"POST\",\n      headers: removeUndefinedEntries(headers),\n      body: body.content,\n      signal: abortSignal\n    });\n    const responseHeaders = extractResponseHeaders(response);\n    if (!response.ok) {\n      let errorInformation;\n      try {\n        errorInformation = await failedResponseHandler({\n          response,\n          url,\n          requestBodyValues: body.values\n        });\n      } catch (error) {\n        if (isAbortError(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError.isInstance(error)) {\n          throw error;\n        }\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n          message: \"Failed to process error response\",\n          cause: error,\n          statusCode: response.status,\n          url,\n          responseHeaders,\n          requestBodyValues: body.values\n        });\n      }\n      throw errorInformation.value;\n    }\n    try {\n      return await successfulResponseHandler({\n        response,\n        url,\n        requestBodyValues: body.values\n      });\n    } catch (error) {\n      if (error instanceof Error) {\n        if (isAbortError(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError.isInstance(error)) {\n          throw error;\n        }\n      }\n      throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: \"Failed to process successful response\",\n        cause: error,\n        statusCode: response.status,\n        url,\n        responseHeaders,\n        requestBodyValues: body.values\n      });\n    }\n  } catch (error) {\n    if (isAbortError(error)) {\n      throw error;\n    }\n    if (error instanceof TypeError && error.message === \"fetch failed\") {\n      const cause = error.cause;\n      if (cause != null) {\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n          message: `Cannot connect to API: ${cause.message}`,\n          cause,\n          url,\n          requestBodyValues: body.values,\n          isRetryable: true\n          // retry when network error\n        });\n      }\n    }\n    throw error;\n  }\n};\n\n// src/resolve.ts\nasync function resolve(value) {\n  if (typeof value === \"function\") {\n    value = value();\n  }\n  return Promise.resolve(value);\n}\n\n// src/response-handler.ts\n\nvar createJsonErrorResponseHandler = ({\n  errorSchema,\n  errorToMessage,\n  isRetryable\n}) => async ({ response, url, requestBodyValues }) => {\n  const responseBody = await response.text();\n  const responseHeaders = extractResponseHeaders(response);\n  if (responseBody.trim() === \"\") {\n    return {\n      responseHeaders,\n      value: new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: response.statusText,\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n        isRetryable: isRetryable == null ? void 0 : isRetryable(response)\n      })\n    };\n  }\n  try {\n    const parsedError = parseJSON({\n      text: responseBody,\n      schema: errorSchema\n    });\n    return {\n      responseHeaders,\n      value: new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: errorToMessage(parsedError),\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n        data: parsedError,\n        isRetryable: isRetryable == null ? void 0 : isRetryable(response, parsedError)\n      })\n    };\n  } catch (parseError) {\n    return {\n      responseHeaders,\n      value: new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: response.statusText,\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n        isRetryable: isRetryable == null ? void 0 : isRetryable(response)\n      })\n    };\n  }\n};\nvar createEventSourceResponseHandler = (chunkSchema) => async ({ response }) => {\n  const responseHeaders = extractResponseHeaders(response);\n  if (response.body == null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.EmptyResponseBodyError({});\n  }\n  return {\n    responseHeaders,\n    value: response.body.pipeThrough(new TextDecoderStream()).pipeThrough(createEventSourceParserStream()).pipeThrough(\n      new TransformStream({\n        transform({ data }, controller) {\n          if (data === \"[DONE]\") {\n            return;\n          }\n          controller.enqueue(\n            safeParseJSON({\n              text: data,\n              schema: chunkSchema\n            })\n          );\n        }\n      })\n    )\n  };\n};\nvar createJsonStreamResponseHandler = (chunkSchema) => async ({ response }) => {\n  const responseHeaders = extractResponseHeaders(response);\n  if (response.body == null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.EmptyResponseBodyError({});\n  }\n  let buffer = \"\";\n  return {\n    responseHeaders,\n    value: response.body.pipeThrough(new TextDecoderStream()).pipeThrough(\n      new TransformStream({\n        transform(chunkText, controller) {\n          if (chunkText.endsWith(\"\\n\")) {\n            controller.enqueue(\n              safeParseJSON({\n                text: buffer + chunkText,\n                schema: chunkSchema\n              })\n            );\n            buffer = \"\";\n          } else {\n            buffer += chunkText;\n          }\n        }\n      })\n    )\n  };\n};\nvar createJsonResponseHandler = (responseSchema) => async ({ response, url, requestBodyValues }) => {\n  const responseBody = await response.text();\n  const parsedResult = safeParseJSON({\n    text: responseBody,\n    schema: responseSchema\n  });\n  const responseHeaders = extractResponseHeaders(response);\n  if (!parsedResult.success) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n      message: \"Invalid JSON response\",\n      cause: parsedResult.error,\n      statusCode: response.status,\n      responseHeaders,\n      responseBody,\n      url,\n      requestBodyValues\n    });\n  }\n  return {\n    responseHeaders,\n    value: parsedResult.value,\n    rawValue: parsedResult.rawValue\n  };\n};\nvar createBinaryResponseHandler = () => async ({ response, url, requestBodyValues }) => {\n  const responseHeaders = extractResponseHeaders(response);\n  if (!response.body) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n      message: \"Response body is empty\",\n      url,\n      requestBodyValues,\n      statusCode: response.status,\n      responseHeaders,\n      responseBody: void 0\n    });\n  }\n  try {\n    const buffer = await response.arrayBuffer();\n    return {\n      responseHeaders,\n      value: new Uint8Array(buffer)\n    };\n  } catch (error) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n      message: \"Failed to read response as array buffer\",\n      url,\n      requestBodyValues,\n      statusCode: response.status,\n      responseHeaders,\n      responseBody: void 0,\n      cause: error\n    });\n  }\n};\nvar createStatusCodeErrorResponseHandler = () => async ({ response, url, requestBodyValues }) => {\n  const responseHeaders = extractResponseHeaders(response);\n  const responseBody = await response.text();\n  return {\n    responseHeaders,\n    value: new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n      message: response.statusText,\n      url,\n      requestBodyValues,\n      statusCode: response.status,\n      responseHeaders,\n      responseBody\n    })\n  };\n};\n\n// src/uint8-utils.ts\nvar { btoa, atob } = globalThis;\nfunction convertBase64ToUint8Array(base64String) {\n  const base64Url = base64String.replace(/-/g, \"+\").replace(/_/g, \"/\");\n  const latin1string = atob(base64Url);\n  return Uint8Array.from(latin1string, (byte) => byte.codePointAt(0));\n}\nfunction convertUint8ArrayToBase64(array) {\n  let latin1string = \"\";\n  for (let i = 0; i < array.length; i++) {\n    latin1string += String.fromCodePoint(array[i]);\n  }\n  return btoa(latin1string);\n}\n\n// src/without-trailing-slash.ts\nfunction withoutTrailingSlash(url) {\n  return url == null ? void 0 : url.replace(/\\/$/, \"\");\n}\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@ai-sdk+provider-utils@2.2.8_zod@3.24.1/node_modules/@ai-sdk/provider-utils/dist/index.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@ai-sdk+provider-utils@2.2.8_zod@3.24.1/node_modules/@ai-sdk/provider-utils/dist/index.mjs":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ai-sdk+provider-utils@2.2.8_zod@3.24.1/node_modules/@ai-sdk/provider-utils/dist/index.mjs ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   asValidator: () => (/* binding */ asValidator),\n/* harmony export */   combineHeaders: () => (/* binding */ combineHeaders),\n/* harmony export */   convertAsyncIteratorToReadableStream: () => (/* binding */ convertAsyncIteratorToReadableStream),\n/* harmony export */   convertBase64ToUint8Array: () => (/* binding */ convertBase64ToUint8Array),\n/* harmony export */   convertUint8ArrayToBase64: () => (/* binding */ convertUint8ArrayToBase64),\n/* harmony export */   createBinaryResponseHandler: () => (/* binding */ createBinaryResponseHandler),\n/* harmony export */   createEventSourceParserStream: () => (/* binding */ createEventSourceParserStream),\n/* harmony export */   createEventSourceResponseHandler: () => (/* binding */ createEventSourceResponseHandler),\n/* harmony export */   createIdGenerator: () => (/* binding */ createIdGenerator),\n/* harmony export */   createJsonErrorResponseHandler: () => (/* binding */ createJsonErrorResponseHandler),\n/* harmony export */   createJsonResponseHandler: () => (/* binding */ createJsonResponseHandler),\n/* harmony export */   createJsonStreamResponseHandler: () => (/* binding */ createJsonStreamResponseHandler),\n/* harmony export */   createStatusCodeErrorResponseHandler: () => (/* binding */ createStatusCodeErrorResponseHandler),\n/* harmony export */   delay: () => (/* binding */ delay),\n/* harmony export */   extractResponseHeaders: () => (/* binding */ extractResponseHeaders),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   getErrorMessage: () => (/* binding */ getErrorMessage),\n/* harmony export */   getFromApi: () => (/* binding */ getFromApi),\n/* harmony export */   isAbortError: () => (/* binding */ isAbortError),\n/* harmony export */   isParsableJson: () => (/* binding */ isParsableJson),\n/* harmony export */   isValidator: () => (/* binding */ isValidator),\n/* harmony export */   loadApiKey: () => (/* binding */ loadApiKey),\n/* harmony export */   loadOptionalSetting: () => (/* binding */ loadOptionalSetting),\n/* harmony export */   loadSetting: () => (/* binding */ loadSetting),\n/* harmony export */   parseJSON: () => (/* binding */ parseJSON),\n/* harmony export */   parseProviderOptions: () => (/* binding */ parseProviderOptions),\n/* harmony export */   postFormDataToApi: () => (/* binding */ postFormDataToApi),\n/* harmony export */   postJsonToApi: () => (/* binding */ postJsonToApi),\n/* harmony export */   postToApi: () => (/* binding */ postToApi),\n/* harmony export */   removeUndefinedEntries: () => (/* binding */ removeUndefinedEntries),\n/* harmony export */   resolve: () => (/* binding */ resolve),\n/* harmony export */   safeParseJSON: () => (/* binding */ safeParseJSON),\n/* harmony export */   safeValidateTypes: () => (/* binding */ safeValidateTypes),\n/* harmony export */   validateTypes: () => (/* binding */ validateTypes),\n/* harmony export */   validator: () => (/* binding */ validator),\n/* harmony export */   validatorSymbol: () => (/* binding */ validatorSymbol),\n/* harmony export */   withoutTrailingSlash: () => (/* binding */ withoutTrailingSlash),\n/* harmony export */   zodValidator: () => (/* binding */ zodValidator)\n/* harmony export */ });\n/* harmony import */ var _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ai-sdk/provider */ \"(rsc)/./node_modules/.pnpm/@ai-sdk+provider@1.1.3/node_modules/@ai-sdk/provider/dist/index.mjs\");\n/* harmony import */ var nanoid_non_secure__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! nanoid/non-secure */ \"(rsc)/./node_modules/.pnpm/nanoid@3.3.8/node_modules/nanoid/non-secure/index.js\");\n/* harmony import */ var secure_json_parse__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! secure-json-parse */ \"(rsc)/./node_modules/.pnpm/secure-json-parse@2.7.0/node_modules/secure-json-parse/index.js\");\n// src/combine-headers.ts\nfunction combineHeaders(...headers) {\n  return headers.reduce(\n    (combinedHeaders, currentHeaders) => ({\n      ...combinedHeaders,\n      ...currentHeaders != null ? currentHeaders : {}\n    }),\n    {}\n  );\n}\n\n// src/convert-async-iterator-to-readable-stream.ts\nfunction convertAsyncIteratorToReadableStream(iterator) {\n  return new ReadableStream({\n    /**\n     * Called when the consumer wants to pull more data from the stream.\n     *\n     * @param {ReadableStreamDefaultController<T>} controller - The controller to enqueue data into the stream.\n     * @returns {Promise<void>}\n     */\n    async pull(controller) {\n      try {\n        const { value, done } = await iterator.next();\n        if (done) {\n          controller.close();\n        } else {\n          controller.enqueue(value);\n        }\n      } catch (error) {\n        controller.error(error);\n      }\n    },\n    /**\n     * Called when the consumer cancels the stream.\n     */\n    cancel() {\n    }\n  });\n}\n\n// src/delay.ts\nasync function delay(delayInMs) {\n  return delayInMs == null ? Promise.resolve() : new Promise((resolve2) => setTimeout(resolve2, delayInMs));\n}\n\n// src/event-source-parser-stream.ts\nfunction createEventSourceParserStream() {\n  let buffer = \"\";\n  let event = void 0;\n  let data = [];\n  let lastEventId = void 0;\n  let retry = void 0;\n  function parseLine(line, controller) {\n    if (line === \"\") {\n      dispatchEvent(controller);\n      return;\n    }\n    if (line.startsWith(\":\")) {\n      return;\n    }\n    const colonIndex = line.indexOf(\":\");\n    if (colonIndex === -1) {\n      handleField(line, \"\");\n      return;\n    }\n    const field = line.slice(0, colonIndex);\n    const valueStart = colonIndex + 1;\n    const value = valueStart < line.length && line[valueStart] === \" \" ? line.slice(valueStart + 1) : line.slice(valueStart);\n    handleField(field, value);\n  }\n  function dispatchEvent(controller) {\n    if (data.length > 0) {\n      controller.enqueue({\n        event,\n        data: data.join(\"\\n\"),\n        id: lastEventId,\n        retry\n      });\n      data = [];\n      event = void 0;\n      retry = void 0;\n    }\n  }\n  function handleField(field, value) {\n    switch (field) {\n      case \"event\":\n        event = value;\n        break;\n      case \"data\":\n        data.push(value);\n        break;\n      case \"id\":\n        lastEventId = value;\n        break;\n      case \"retry\":\n        const parsedRetry = parseInt(value, 10);\n        if (!isNaN(parsedRetry)) {\n          retry = parsedRetry;\n        }\n        break;\n    }\n  }\n  return new TransformStream({\n    transform(chunk, controller) {\n      const { lines, incompleteLine } = splitLines(buffer, chunk);\n      buffer = incompleteLine;\n      for (let i = 0; i < lines.length; i++) {\n        parseLine(lines[i], controller);\n      }\n    },\n    flush(controller) {\n      parseLine(buffer, controller);\n      dispatchEvent(controller);\n    }\n  });\n}\nfunction splitLines(buffer, chunk) {\n  const lines = [];\n  let currentLine = buffer;\n  for (let i = 0; i < chunk.length; ) {\n    const char = chunk[i++];\n    if (char === \"\\n\") {\n      lines.push(currentLine);\n      currentLine = \"\";\n    } else if (char === \"\\r\") {\n      lines.push(currentLine);\n      currentLine = \"\";\n      if (chunk[i] === \"\\n\") {\n        i++;\n      }\n    } else {\n      currentLine += char;\n    }\n  }\n  return { lines, incompleteLine: currentLine };\n}\n\n// src/extract-response-headers.ts\nfunction extractResponseHeaders(response) {\n  const headers = {};\n  response.headers.forEach((value, key) => {\n    headers[key] = value;\n  });\n  return headers;\n}\n\n// src/generate-id.ts\n\n\nvar createIdGenerator = ({\n  prefix,\n  size: defaultSize = 16,\n  alphabet = \"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz\",\n  separator = \"-\"\n} = {}) => {\n  const generator = (0,nanoid_non_secure__WEBPACK_IMPORTED_MODULE_0__.customAlphabet)(alphabet, defaultSize);\n  if (prefix == null) {\n    return generator;\n  }\n  if (alphabet.includes(separator)) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.InvalidArgumentError({\n      argument: \"separator\",\n      message: `The separator \"${separator}\" must not be part of the alphabet \"${alphabet}\".`\n    });\n  }\n  return (size) => `${prefix}${separator}${generator(size)}`;\n};\nvar generateId = createIdGenerator();\n\n// src/get-error-message.ts\nfunction getErrorMessage(error) {\n  if (error == null) {\n    return \"unknown error\";\n  }\n  if (typeof error === \"string\") {\n    return error;\n  }\n  if (error instanceof Error) {\n    return error.message;\n  }\n  return JSON.stringify(error);\n}\n\n// src/get-from-api.ts\n\n\n// src/remove-undefined-entries.ts\nfunction removeUndefinedEntries(record) {\n  return Object.fromEntries(\n    Object.entries(record).filter(([_key, value]) => value != null)\n  );\n}\n\n// src/is-abort-error.ts\nfunction isAbortError(error) {\n  return error instanceof Error && (error.name === \"AbortError\" || error.name === \"TimeoutError\");\n}\n\n// src/get-from-api.ts\nvar getOriginalFetch = () => globalThis.fetch;\nvar getFromApi = async ({\n  url,\n  headers = {},\n  successfulResponseHandler,\n  failedResponseHandler,\n  abortSignal,\n  fetch = getOriginalFetch()\n}) => {\n  try {\n    const response = await fetch(url, {\n      method: \"GET\",\n      headers: removeUndefinedEntries(headers),\n      signal: abortSignal\n    });\n    const responseHeaders = extractResponseHeaders(response);\n    if (!response.ok) {\n      let errorInformation;\n      try {\n        errorInformation = await failedResponseHandler({\n          response,\n          url,\n          requestBodyValues: {}\n        });\n      } catch (error) {\n        if (isAbortError(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError.isInstance(error)) {\n          throw error;\n        }\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n          message: \"Failed to process error response\",\n          cause: error,\n          statusCode: response.status,\n          url,\n          responseHeaders,\n          requestBodyValues: {}\n        });\n      }\n      throw errorInformation.value;\n    }\n    try {\n      return await successfulResponseHandler({\n        response,\n        url,\n        requestBodyValues: {}\n      });\n    } catch (error) {\n      if (error instanceof Error) {\n        if (isAbortError(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError.isInstance(error)) {\n          throw error;\n        }\n      }\n      throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: \"Failed to process successful response\",\n        cause: error,\n        statusCode: response.status,\n        url,\n        responseHeaders,\n        requestBodyValues: {}\n      });\n    }\n  } catch (error) {\n    if (isAbortError(error)) {\n      throw error;\n    }\n    if (error instanceof TypeError && error.message === \"fetch failed\") {\n      const cause = error.cause;\n      if (cause != null) {\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n          message: `Cannot connect to API: ${cause.message}`,\n          cause,\n          url,\n          isRetryable: true,\n          requestBodyValues: {}\n        });\n      }\n    }\n    throw error;\n  }\n};\n\n// src/load-api-key.ts\n\nfunction loadApiKey({\n  apiKey,\n  environmentVariableName,\n  apiKeyParameterName = \"apiKey\",\n  description\n}) {\n  if (typeof apiKey === \"string\") {\n    return apiKey;\n  }\n  if (apiKey != null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadAPIKeyError({\n      message: `${description} API key must be a string.`\n    });\n  }\n  if (typeof process === \"undefined\") {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadAPIKeyError({\n      message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter. Environment variables is not supported in this environment.`\n    });\n  }\n  apiKey = process.env[environmentVariableName];\n  if (apiKey == null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadAPIKeyError({\n      message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter or the ${environmentVariableName} environment variable.`\n    });\n  }\n  if (typeof apiKey !== \"string\") {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadAPIKeyError({\n      message: `${description} API key must be a string. The value of the ${environmentVariableName} environment variable is not a string.`\n    });\n  }\n  return apiKey;\n}\n\n// src/load-optional-setting.ts\nfunction loadOptionalSetting({\n  settingValue,\n  environmentVariableName\n}) {\n  if (typeof settingValue === \"string\") {\n    return settingValue;\n  }\n  if (settingValue != null || typeof process === \"undefined\") {\n    return void 0;\n  }\n  settingValue = process.env[environmentVariableName];\n  if (settingValue == null || typeof settingValue !== \"string\") {\n    return void 0;\n  }\n  return settingValue;\n}\n\n// src/load-setting.ts\n\nfunction loadSetting({\n  settingValue,\n  environmentVariableName,\n  settingName,\n  description\n}) {\n  if (typeof settingValue === \"string\") {\n    return settingValue;\n  }\n  if (settingValue != null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadSettingError({\n      message: `${description} setting must be a string.`\n    });\n  }\n  if (typeof process === \"undefined\") {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadSettingError({\n      message: `${description} setting is missing. Pass it using the '${settingName}' parameter. Environment variables is not supported in this environment.`\n    });\n  }\n  settingValue = process.env[environmentVariableName];\n  if (settingValue == null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadSettingError({\n      message: `${description} setting is missing. Pass it using the '${settingName}' parameter or the ${environmentVariableName} environment variable.`\n    });\n  }\n  if (typeof settingValue !== \"string\") {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadSettingError({\n      message: `${description} setting must be a string. The value of the ${environmentVariableName} environment variable is not a string.`\n    });\n  }\n  return settingValue;\n}\n\n// src/parse-json.ts\n\n\n\n// src/validate-types.ts\n\n\n// src/validator.ts\nvar validatorSymbol = Symbol.for(\"vercel.ai.validator\");\nfunction validator(validate) {\n  return { [validatorSymbol]: true, validate };\n}\nfunction isValidator(value) {\n  return typeof value === \"object\" && value !== null && validatorSymbol in value && value[validatorSymbol] === true && \"validate\" in value;\n}\nfunction asValidator(value) {\n  return isValidator(value) ? value : zodValidator(value);\n}\nfunction zodValidator(zodSchema) {\n  return validator((value) => {\n    const result = zodSchema.safeParse(value);\n    return result.success ? { success: true, value: result.data } : { success: false, error: result.error };\n  });\n}\n\n// src/validate-types.ts\nfunction validateTypes({\n  value,\n  schema: inputSchema\n}) {\n  const result = safeValidateTypes({ value, schema: inputSchema });\n  if (!result.success) {\n    throw _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TypeValidationError.wrap({ value, cause: result.error });\n  }\n  return result.value;\n}\nfunction safeValidateTypes({\n  value,\n  schema\n}) {\n  const validator2 = asValidator(schema);\n  try {\n    if (validator2.validate == null) {\n      return { success: true, value };\n    }\n    const result = validator2.validate(value);\n    if (result.success) {\n      return result;\n    }\n    return {\n      success: false,\n      error: _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TypeValidationError.wrap({ value, cause: result.error })\n    };\n  } catch (error) {\n    return {\n      success: false,\n      error: _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TypeValidationError.wrap({ value, cause: error })\n    };\n  }\n}\n\n// src/parse-json.ts\nfunction parseJSON({\n  text,\n  schema\n}) {\n  try {\n    const value = secure_json_parse__WEBPACK_IMPORTED_MODULE_2__.parse(text);\n    if (schema == null) {\n      return value;\n    }\n    return validateTypes({ value, schema });\n  } catch (error) {\n    if (_ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.JSONParseError.isInstance(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TypeValidationError.isInstance(error)) {\n      throw error;\n    }\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.JSONParseError({ text, cause: error });\n  }\n}\nfunction safeParseJSON({\n  text,\n  schema\n}) {\n  try {\n    const value = secure_json_parse__WEBPACK_IMPORTED_MODULE_2__.parse(text);\n    if (schema == null) {\n      return { success: true, value, rawValue: value };\n    }\n    const validationResult = safeValidateTypes({ value, schema });\n    return validationResult.success ? { ...validationResult, rawValue: value } : validationResult;\n  } catch (error) {\n    return {\n      success: false,\n      error: _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.JSONParseError.isInstance(error) ? error : new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.JSONParseError({ text, cause: error })\n    };\n  }\n}\nfunction isParsableJson(input) {\n  try {\n    secure_json_parse__WEBPACK_IMPORTED_MODULE_2__.parse(input);\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\n// src/parse-provider-options.ts\n\nfunction parseProviderOptions({\n  provider,\n  providerOptions,\n  schema\n}) {\n  if ((providerOptions == null ? void 0 : providerOptions[provider]) == null) {\n    return void 0;\n  }\n  const parsedProviderOptions = safeValidateTypes({\n    value: providerOptions[provider],\n    schema\n  });\n  if (!parsedProviderOptions.success) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.InvalidArgumentError({\n      argument: \"providerOptions\",\n      message: `invalid ${provider} provider options`,\n      cause: parsedProviderOptions.error\n    });\n  }\n  return parsedProviderOptions.value;\n}\n\n// src/post-to-api.ts\n\nvar getOriginalFetch2 = () => globalThis.fetch;\nvar postJsonToApi = async ({\n  url,\n  headers,\n  body,\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch\n}) => postToApi({\n  url,\n  headers: {\n    \"Content-Type\": \"application/json\",\n    ...headers\n  },\n  body: {\n    content: JSON.stringify(body),\n    values: body\n  },\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch\n});\nvar postFormDataToApi = async ({\n  url,\n  headers,\n  formData,\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch\n}) => postToApi({\n  url,\n  headers,\n  body: {\n    content: formData,\n    values: Object.fromEntries(formData.entries())\n  },\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch\n});\nvar postToApi = async ({\n  url,\n  headers = {},\n  body,\n  successfulResponseHandler,\n  failedResponseHandler,\n  abortSignal,\n  fetch = getOriginalFetch2()\n}) => {\n  try {\n    const response = await fetch(url, {\n      method: \"POST\",\n      headers: removeUndefinedEntries(headers),\n      body: body.content,\n      signal: abortSignal\n    });\n    const responseHeaders = extractResponseHeaders(response);\n    if (!response.ok) {\n      let errorInformation;\n      try {\n        errorInformation = await failedResponseHandler({\n          response,\n          url,\n          requestBodyValues: body.values\n        });\n      } catch (error) {\n        if (isAbortError(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError.isInstance(error)) {\n          throw error;\n        }\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n          message: \"Failed to process error response\",\n          cause: error,\n          statusCode: response.status,\n          url,\n          responseHeaders,\n          requestBodyValues: body.values\n        });\n      }\n      throw errorInformation.value;\n    }\n    try {\n      return await successfulResponseHandler({\n        response,\n        url,\n        requestBodyValues: body.values\n      });\n    } catch (error) {\n      if (error instanceof Error) {\n        if (isAbortError(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError.isInstance(error)) {\n          throw error;\n        }\n      }\n      throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: \"Failed to process successful response\",\n        cause: error,\n        statusCode: response.status,\n        url,\n        responseHeaders,\n        requestBodyValues: body.values\n      });\n    }\n  } catch (error) {\n    if (isAbortError(error)) {\n      throw error;\n    }\n    if (error instanceof TypeError && error.message === \"fetch failed\") {\n      const cause = error.cause;\n      if (cause != null) {\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n          message: `Cannot connect to API: ${cause.message}`,\n          cause,\n          url,\n          requestBodyValues: body.values,\n          isRetryable: true\n          // retry when network error\n        });\n      }\n    }\n    throw error;\n  }\n};\n\n// src/resolve.ts\nasync function resolve(value) {\n  if (typeof value === \"function\") {\n    value = value();\n  }\n  return Promise.resolve(value);\n}\n\n// src/response-handler.ts\n\nvar createJsonErrorResponseHandler = ({\n  errorSchema,\n  errorToMessage,\n  isRetryable\n}) => async ({ response, url, requestBodyValues }) => {\n  const responseBody = await response.text();\n  const responseHeaders = extractResponseHeaders(response);\n  if (responseBody.trim() === \"\") {\n    return {\n      responseHeaders,\n      value: new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: response.statusText,\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n        isRetryable: isRetryable == null ? void 0 : isRetryable(response)\n      })\n    };\n  }\n  try {\n    const parsedError = parseJSON({\n      text: responseBody,\n      schema: errorSchema\n    });\n    return {\n      responseHeaders,\n      value: new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: errorToMessage(parsedError),\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n        data: parsedError,\n        isRetryable: isRetryable == null ? void 0 : isRetryable(response, parsedError)\n      })\n    };\n  } catch (parseError) {\n    return {\n      responseHeaders,\n      value: new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: response.statusText,\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n        isRetryable: isRetryable == null ? void 0 : isRetryable(response)\n      })\n    };\n  }\n};\nvar createEventSourceResponseHandler = (chunkSchema) => async ({ response }) => {\n  const responseHeaders = extractResponseHeaders(response);\n  if (response.body == null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.EmptyResponseBodyError({});\n  }\n  return {\n    responseHeaders,\n    value: response.body.pipeThrough(new TextDecoderStream()).pipeThrough(createEventSourceParserStream()).pipeThrough(\n      new TransformStream({\n        transform({ data }, controller) {\n          if (data === \"[DONE]\") {\n            return;\n          }\n          controller.enqueue(\n            safeParseJSON({\n              text: data,\n              schema: chunkSchema\n            })\n          );\n        }\n      })\n    )\n  };\n};\nvar createJsonStreamResponseHandler = (chunkSchema) => async ({ response }) => {\n  const responseHeaders = extractResponseHeaders(response);\n  if (response.body == null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.EmptyResponseBodyError({});\n  }\n  let buffer = \"\";\n  return {\n    responseHeaders,\n    value: response.body.pipeThrough(new TextDecoderStream()).pipeThrough(\n      new TransformStream({\n        transform(chunkText, controller) {\n          if (chunkText.endsWith(\"\\n\")) {\n            controller.enqueue(\n              safeParseJSON({\n                text: buffer + chunkText,\n                schema: chunkSchema\n              })\n            );\n            buffer = \"\";\n          } else {\n            buffer += chunkText;\n          }\n        }\n      })\n    )\n  };\n};\nvar createJsonResponseHandler = (responseSchema) => async ({ response, url, requestBodyValues }) => {\n  const responseBody = await response.text();\n  const parsedResult = safeParseJSON({\n    text: responseBody,\n    schema: responseSchema\n  });\n  const responseHeaders = extractResponseHeaders(response);\n  if (!parsedResult.success) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n      message: \"Invalid JSON response\",\n      cause: parsedResult.error,\n      statusCode: response.status,\n      responseHeaders,\n      responseBody,\n      url,\n      requestBodyValues\n    });\n  }\n  return {\n    responseHeaders,\n    value: parsedResult.value,\n    rawValue: parsedResult.rawValue\n  };\n};\nvar createBinaryResponseHandler = () => async ({ response, url, requestBodyValues }) => {\n  const responseHeaders = extractResponseHeaders(response);\n  if (!response.body) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n      message: \"Response body is empty\",\n      url,\n      requestBodyValues,\n      statusCode: response.status,\n      responseHeaders,\n      responseBody: void 0\n    });\n  }\n  try {\n    const buffer = await response.arrayBuffer();\n    return {\n      responseHeaders,\n      value: new Uint8Array(buffer)\n    };\n  } catch (error) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n      message: \"Failed to read response as array buffer\",\n      url,\n      requestBodyValues,\n      statusCode: response.status,\n      responseHeaders,\n      responseBody: void 0,\n      cause: error\n    });\n  }\n};\nvar createStatusCodeErrorResponseHandler = () => async ({ response, url, requestBodyValues }) => {\n  const responseHeaders = extractResponseHeaders(response);\n  const responseBody = await response.text();\n  return {\n    responseHeaders,\n    value: new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n      message: response.statusText,\n      url,\n      requestBodyValues,\n      statusCode: response.status,\n      responseHeaders,\n      responseBody\n    })\n  };\n};\n\n// src/uint8-utils.ts\nvar { btoa, atob } = globalThis;\nfunction convertBase64ToUint8Array(base64String) {\n  const base64Url = base64String.replace(/-/g, \"+\").replace(/_/g, \"/\");\n  const latin1string = atob(base64Url);\n  return Uint8Array.from(latin1string, (byte) => byte.codePointAt(0));\n}\nfunction convertUint8ArrayToBase64(array) {\n  let latin1string = \"\";\n  for (let i = 0; i < array.length; i++) {\n    latin1string += String.fromCodePoint(array[i]);\n  }\n  return btoa(latin1string);\n}\n\n// src/without-trailing-slash.ts\nfunction withoutTrailingSlash(url) {\n  return url == null ? void 0 : url.replace(/\\/$/, \"\");\n}\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@ai-sdk+provider-utils@2.2.8_zod@3.24.1/node_modules/@ai-sdk/provider-utils/dist/index.mjs\n");

/***/ })

};
;