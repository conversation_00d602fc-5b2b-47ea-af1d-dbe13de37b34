{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Bt++iSO8b8VIlH5VY8a9Ofq+EYxZ6e9FQGyn+Xu7MoM="}}}, "functions": {}, "sortedMiddleware": ["/"]}