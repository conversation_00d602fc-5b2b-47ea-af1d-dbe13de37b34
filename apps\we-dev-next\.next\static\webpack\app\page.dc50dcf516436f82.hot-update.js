"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/AiChat/chat/index.tsx":
/*!**********************************************!*\
  !*** ./src/components/AiChat/chat/index.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseChat: function() { return /* binding */ BaseChat; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ai_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ai/react */ \"(app-pages-browser)/./node_modules/.pnpm/ai@4.3.17_react@18.3.1_zod@3.24.1/node_modules/ai/react/dist/index.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _stores_chatSlice__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/stores/chatSlice */ \"(app-pages-browser)/./src/stores/chatSlice.ts\");\n/* harmony import */ var _stores_fileStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/stores/fileStore */ \"(app-pages-browser)/./src/stores/fileStore.ts\");\n/* harmony import */ var _utils_indexDB__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/indexDB */ \"(app-pages-browser)/./src/utils/indexDB.ts\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/.pnpm/uuid@11.0.5/node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _components_MessageItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/MessageItem */ \"(app-pages-browser)/./src/components/AiChat/chat/components/MessageItem.tsx\");\n/* harmony import */ var _components_ChatInput__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/ChatInput */ \"(app-pages-browser)/./src/components/AiChat/chat/components/ChatInput/index.tsx\");\n/* harmony import */ var _components_Tips__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./components/Tips */ \"(app-pages-browser)/./src/components/AiChat/chat/components/Tips.tsx\");\n/* harmony import */ var _utils_messagepParseJson__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/messagepParseJson */ \"(app-pages-browser)/./src/utils/messagepParseJson.ts\");\n/* harmony import */ var _utils_messageParserNew__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/messageParserNew */ \"(app-pages-browser)/./src/utils/messageParserNew.ts\");\n/* harmony import */ var _stores_userSlice__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/stores/userSlice */ \"(app-pages-browser)/./src/stores/userSlice.ts\");\n/* harmony import */ var _UserModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../UserModal */ \"(app-pages-browser)/./src/components/UserModal/index.tsx\");\n/* harmony import */ var _stores_chatModeSlice__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/stores/chatModeSlice */ \"(app-pages-browser)/./src/stores/chatModeSlice.ts\");\n/* __next_internal_client_entry_do_not_use__ BaseChat auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst BaseChat = (param)=>{\n    let { uuid: propUuid } = param;\n    _s();\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { otherConfig, uploadedImages, clearImages, addImages, removeImage } = (0,_stores_chatSlice__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    const [checkCount, setCheckCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [baseModal, setBaseModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        value: \"anthropic/claude-3.5-sonnet\",\n        label: \"Claude 3.5 Sonnet\",\n        useImage: true,\n        from: \"default\",\n        quota: 2,\n        functionCall: true\n    });\n    const { files, updateContent } = (0,_stores_fileStore__WEBPACK_IMPORTED_MODULE_4__.useFileStore)();\n    const { user, token } = (0,_stores_userSlice__WEBPACK_IMPORTED_MODULE_11__[\"default\"])();\n    const { openModal } = (0,_UserModal__WEBPACK_IMPORTED_MODULE_12__.useLimitModalStore)();\n    const { mode } = (0,_stores_chatModeSlice__WEBPACK_IMPORTED_MODULE_13__[\"default\"])();\n    const [messages, setMessagesa] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const parseTimeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(Date.now());\n    const refUuidMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const chatUuid = propUuid || (0,uuid__WEBPACK_IMPORTED_MODULE_14__[\"default\"])();\n    const baseChatUrl =  true ? window.location.origin : 0;\n    const clearErrors = ()=>setErrors([]);\n    const scrollToBottom = ()=>{\n        setTimeout(()=>{\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }, 100);\n    };\n    // Parse messages and create files\n    const parseMessagesAndCreateFiles = async (messages)=>{\n        try {\n            await (0,_utils_messageParserNew__WEBPACK_IMPORTED_MODULE_10__.parseMessages)(messages);\n        } catch (error) {\n            console.error(\"Error parsing messages:\", error);\n        }\n    };\n    const updateFileSystemNow = ()=>{\n        // This would update the file system in desktop app\n        // For web, this is a no-op\n        return Promise.resolve();\n    };\n    const createMpIcon = (files)=>{\n    // This would create mini program icons in desktop app\n    // For web, this is a no-op\n    };\n    const checkExecList = ()=>{\n        // This would check execution list in desktop app\n        return [];\n    };\n    const checkFinish = ()=>{\n        // This would check if execution is finished in desktop app\n        return true;\n    };\n    const { messages: realMessages, input, handleInputChange, isLoading, setMessages, append, setInput, stop, reload } = (0,ai_react__WEBPACK_IMPORTED_MODULE_15__.useChat)({\n        api: \"\".concat(baseChatUrl, \"/api/chat\"),\n        headers: {\n            ...token && {\n                Authorization: \"Bearer \".concat(token)\n            }\n        },\n        body: {\n            model: baseModal.value,\n            mode: mode,\n            otherConfig: {\n                ...otherConfig,\n                extra: {\n                    ...otherConfig.extra,\n                    isBackEnd: otherConfig.isBackEnd,\n                    backendLanguage: otherConfig.backendLanguage\n                }\n            }\n        },\n        onFinish: async (message)=>{\n            try {\n                var _find_content, _find;\n                // Parse message for files (like we-dev-client does)\n                if (message && message.content) {\n                    console.log(\"\\uD83C\\uDFAF onFinish: Processing message for files\");\n                    const { files: messageFiles } = (0,_utils_messagepParseJson__WEBPACK_IMPORTED_MODULE_9__.parseMessage)(message.content);\n                    const fileKeys = messageFiles ? Object.keys(messageFiles) : [];\n                    console.log(\"\\uD83D\\uDCC1 Found \".concat(fileKeys.length, \" files:\"), fileKeys);\n                    if (messageFiles) {\n                        for(let filePath in messageFiles){\n                            console.log(\"\\uD83D\\uDCC4 Creating file: \".concat(filePath));\n                            await updateContent(filePath, messageFiles[filePath], false, true);\n                        }\n                    }\n                }\n                // Also parse with the streaming parser for any missed files\n                const needParseMessages = [\n                    ...messages,\n                    message\n                ].filter((m)=>!refUuidMessages.current.includes(m.id));\n                refUuidMessages.current = [\n                    ...refUuidMessages.current,\n                    ...needParseMessages.map((m)=>m.id)\n                ];\n                if (needParseMessages.length > 0) {\n                    console.log(\"\\uD83D\\uDD04 Also parsing with streaming parser for \".concat(needParseMessages.length, \" messages\"));\n                    parseMessagesAndCreateFiles(needParseMessages);\n                }\n                const initMessage = messages.filter((m)=>m.role === \"system\");\n                await _utils_indexDB__WEBPACK_IMPORTED_MODULE_5__.db.insert(chatUuid, {\n                    messages: [\n                        ...messages,\n                        ...initMessage,\n                        message\n                    ],\n                    title: ((_find = [\n                        ...initMessage,\n                        ...messages\n                    ].find((m)=>m.role === \"user\" && !m.content.includes(\"<boltArtifact\"))) === null || _find === void 0 ? void 0 : (_find_content = _find.content) === null || _find_content === void 0 ? void 0 : _find_content.slice(0, 50)) || \"New Chat\"\n                });\n            } catch (error) {\n                console.error(\"Failed to save chat history:\", error);\n            }\n            setCheckCount((checkCount)=>checkCount + 1);\n        },\n        onError: (error)=>{\n            var _error_errors_, _error_errors;\n            const msg = (error === null || error === void 0 ? void 0 : (_error_errors = error.errors) === null || _error_errors === void 0 ? void 0 : (_error_errors_ = _error_errors[0]) === null || _error_errors_ === void 0 ? void 0 : _error_errors_.responseBody) || String(error);\n            console.log(\"error\", error, msg);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(msg);\n            if (String(error).includes(\"Quota not enough\")) {\n                openModal(\"limit\");\n            }\n            if (String(error).includes(\"Authentication required\")) {\n                openModal(\"login\");\n            }\n        }\n    });\n    const filterMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return messages.filter((message)=>{\n            if (message.role === \"system\") return false;\n            return true;\n        });\n    }, [\n        messages\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only update messages during streaming, but don't parse yet\n        if (Date.now() - parseTimeRef.current > 200 && isLoading) {\n            setMessagesa(realMessages);\n            parseTimeRef.current = Date.now();\n            scrollToBottom();\n        }\n        if (errors.length > 0 && isLoading) {\n            clearErrors();\n        }\n        // Only parse messages when streaming is complete\n        if (!isLoading) {\n            setMessagesa(realMessages);\n            createMpIcon(files);\n            // Parse messages when loading is complete\n            const needParseMessages = messages.filter((m)=>!refUuidMessages.current.includes(m.id) && m.role === \"assistant\" && m.content && typeof m.content === \"string\" && m.content.trim().length > 0);\n            if (needParseMessages.length > 0) {\n                console.log(\"\\uD83D\\uDCE8 Processing \".concat(needParseMessages.length, \" new messages (loading complete):\"), needParseMessages.map((m)=>{\n                    var _m_content;\n                    return {\n                        id: m.id,\n                        role: m.role,\n                        contentLength: ((_m_content = m.content) === null || _m_content === void 0 ? void 0 : _m_content.length) || 0,\n                        hasContent: !!m.content\n                    };\n                }));\n                parseMessagesAndCreateFiles(needParseMessages);\n                // Update tracked message IDs\n                refUuidMessages.current = [\n                    ...refUuidMessages.current,\n                    ...needParseMessages.map((m)=>m.id)\n                ];\n            }\n        }\n    }, [\n        realMessages,\n        isLoading\n    ]);\n    const handleFileSelect = async (e)=>{\n        const files = Array.from(e.target.files || []);\n        if (files.length === 0) return;\n        setIsUploading(true);\n        try {\n            const newImages = files.map((file)=>({\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(),\n                    file,\n                    url: URL.createObjectURL(file),\n                    localUrl: URL.createObjectURL(file),\n                    status: \"done\"\n                }));\n            addImages(newImages);\n        } catch (error) {\n            console.error(\"File upload failed:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to upload files\");\n        } finally{\n            setIsUploading(false);\n        }\n    };\n    const handleSubmitWithFiles = async (e, text)=>{\n        e === null || e === void 0 ? void 0 : e.preventDefault();\n        if (!input.trim() && !(text === null || text === void 0 ? void 0 : text.trim()) || isLoading) return;\n        try {\n            const currentAttachments = uploadedImages.map((img)=>({\n                    id: img.id,\n                    name: img.id,\n                    type: img.file.type,\n                    localUrl: img.localUrl,\n                    contentType: img.file.type,\n                    url: img.url\n                }));\n            clearImages();\n            append({\n                role: \"user\",\n                content: text || input\n            }, {\n                experimental_attachments: currentAttachments\n            });\n            setInput(\"\");\n            setTimeout(()=>{\n                scrollToBottom();\n            }, 100);\n        } catch (error) {\n            console.error(\"Upload failed:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to upload files\");\n        }\n    };\n    const handleKeySubmit = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmitWithFiles(e);\n        }\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        const files = Array.from(e.dataTransfer.files);\n        if (files.length > 0) {\n            const fileInput = document.createElement(\"input\");\n            fileInput.type = \"file\";\n            fileInput.multiple = true;\n            fileInput.files = e.dataTransfer.files;\n            handleFileSelect({\n                target: fileInput\n            });\n        }\n    };\n    const handleScroll = ()=>{\n    // Handle scroll events if needed\n    };\n    const showJsx = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 overflow-y-auto px-1 py-2 message-container [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]\",\n            onScroll: handleScroll,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Tips__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    append: append,\n                    setInput: setInput,\n                    handleFileSelect: handleFileSelect\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                    lineNumber: 320,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-[640px] w-full mx-auto space-y-3\",\n                    children: [\n                        filterMessages.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MessageItem__WEBPACK_IMPORTED_MODULE_6__.MessageItem, {\n                                handleRetry: ()=>{\n                                    reload();\n                                },\n                                message: message,\n                                isEndMessage: filterMessages[filterMessages.length - 1].id === message.id,\n                                isLoading: isLoading,\n                                onUpdateMessage: (messageId, content)=>{\n                                    var _content_;\n                                    append({\n                                        role: \"user\",\n                                        content: \" \".concat(content === null || content === void 0 ? void 0 : (_content_ = content[0]) === null || _content_ === void 0 ? void 0 : _content_.text)\n                                    });\n                                }\n                            }, \"\".concat(message.id, \"-\").concat(index), false, {\n                                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 25\n                            }, undefined)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: messagesEndRef\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                    lineNumber: 325,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n            lineNumber: 316,\n            columnNumber: 13\n        }, undefined);\n    }, [\n        filterMessages,\n        isLoading,\n        append,\n        setInput,\n        handleFileSelect,\n        reload\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-full flex-col dark:bg-[#18181a] max-w-full\",\n        onDragOver: handleDragOver,\n        onDrop: handleDrop,\n        children: [\n            showJsx,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatInput__WEBPACK_IMPORTED_MODULE_7__.ChatInput, {\n                input: input,\n                setMessages: setMessages,\n                append: append,\n                messages: messages,\n                stopRuning: stop,\n                setInput: setInput,\n                isLoading: isLoading,\n                isUploading: isUploading,\n                uploadedImages: uploadedImages,\n                baseModal: baseModal,\n                handleInputChange: handleInputChange,\n                handleKeySubmit: handleKeySubmit,\n                handleSubmitWithFiles: handleSubmitWithFiles,\n                handleFileSelect: handleFileSelect,\n                removeImage: removeImage,\n                addImages: addImages,\n                setIsUploading: setIsUploading,\n                setBaseModal: setBaseModal\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                lineNumber: 358,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n        lineNumber: 352,\n        columnNumber: 9\n    }, undefined);\n};\n_s(BaseChat, \"CSTRmbkoHFlvd0xfpq7TKuR+img=\", false, function() {\n    return [\n        _stores_chatSlice__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        _stores_fileStore__WEBPACK_IMPORTED_MODULE_4__.useFileStore,\n        _stores_userSlice__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        _UserModal__WEBPACK_IMPORTED_MODULE_12__.useLimitModalStore,\n        _stores_chatModeSlice__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        ai_react__WEBPACK_IMPORTED_MODULE_15__.useChat\n    ];\n});\n_c = BaseChat;\nvar _c;\n$RefreshReg$(_c, \"BaseChat\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AiChat/chat/index.tsx\n"));

/***/ })

});