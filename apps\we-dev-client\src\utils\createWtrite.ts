import { createFileWithContent } from "@/components/WeIde/components/IDEContent/FileExplorer/utils/fileSystem"



const needInFile = {
    'components/weicon/base64.js': `/* eslint-disable */
var b64chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
var cb_encode = function(ccc) {
    var padlen = [0, 2, 1][ccc.length % 3],
    ord = ccc.charCodeAt(0) << 16
        | ((ccc.length > 1 ? ccc.charCodeAt(1) : 0) << 8)
        | ((ccc.length > 2 ? ccc.charCodeAt(2) : 0)),
    chars = [
        b64chars.charAt( ord >>> 18),
        b64chars.charAt((ord >>> 12) & 63),
        padlen >= 2 ? '=' : b64chars.charAt((ord >>> 6) & 63),
        padlen >= 1 ? '=' : b64chars.charAt(ord & 63)
    ];
    return chars.join('');
};
// @ts-ignore
var btoa = global.btoa ? function(b) {
    // @ts-ignore
    return global.btoa(b);
} : function(b) {
    return b.replace(/[\\s\\S]{1,3}/g, cb_encode);
};
var fromCharCode = String.fromCharCode;
var cb_utob = function(c) {
    var cc
    if (c.length < 2) {
        cc = c.charCodeAt(0);
        return cc < 0x80 ? c
            : cc < 0x800 ? (fromCharCode(0xc0 | (cc >>> 6))
                            + fromCharCode(0x80 | (cc & 0x3f)))
            : (fromCharCode(0xe0 | ((cc >>> 12) & 0x0f))
                + fromCharCode(0x80 | ((cc >>>  6) & 0x3f))
                + fromCharCode(0x80 | ( cc         & 0x3f)));
    } else {
        cc = 0x10000
            + (c.charCodeAt(0) - 0xD800) * 0x400
            + (c.charCodeAt(1) - 0xDC00);
        return (fromCharCode(0xf0 | ((cc >>> 18) & 0x07))
                + fromCharCode(0x80 | ((cc >>> 12) & 0x3f))
                + fromCharCode(0x80 | ((cc >>>  6) & 0x3f))
                + fromCharCode(0x80 | ( cc         & 0x3f)));
    }
};
var re_utob = /[\\uD800-\\uDBFF][\\uDC00-\\uDFFFF]|[^\\x00-\\x7F]/g;
var utob = function(u) {
    return u.replace(re_utob, cb_utob);
};
var _encode = function(u) {
    const isUint8Array = Object.prototype.toString.call(u) === '[object Uint8Array]';
    return isUint8Array ? u.toString('base64')
        : btoa(utob(String(u)));
}
var encode = function(u, urisafe = false) {
    return !urisafe
        ? _encode(u)
        : _encode(String(u)).replace(/[+\\/]/g, function(m0) {
            return m0 == '+' ? '-' : '_';
        }).replace(/=/g, '');
};
export default {
    encode
}

`,
    'components/weicon/index.css': `
    .weui-icon {
  vertical-align: middle;
  display: inline-block;
  background: black;
  mask-repeat: no-repeat;
  -webkit-mask-repeat: no-repeat;
  -moz-mask-repeat: no-repeat;
  mask-size: cover;
  -webkit-mask-size: cover;
  -moz-mask-size: cover;
}
`,
    'components/weicon/index.js': `
    import Base64 from './base64'
import iconData from './icondata'

const getFixedIconType = function (type) {
    // 兼容旧版本 typo
    return type === 'field' ? 'filled' : type
}

Component({
    properties: {
        extClass: {
            type: String,
            value: ''
        },
        svgSrc: {
            type: String,
            value: '',
            observer: '_genSrcBySvg'
        },
        type: {
            type: String,
            value: 'outline',
            observer: '_genSrcByType'
        },
        icon: {
            type: String,
            value: '',
            observer: '_genSrcByIcon'
        },
        size: {
            type: Number,
            value: 20
        },
        color: {
            type: String,
            value: '#000000'
        }
    },
    data: {
        src: '',
        height: 20,
        width: 20
    },
    methods: {
      _genSrcBySvg(v){
        this._genSrc(this.data.svgSrc)
      },
        _genSrcByIcon(v) {
            if (!this.data.svgSrc) {
                this._genSrc(iconData[v][getFixedIconType(this.data.type)])
            } 
           
        },
        _genSrcByType(v) {
            const iconDataItem = iconData[this.data.icon]
            if (iconDataItem) this._genSrc(iconDataItem[getFixedIconType(v)])
        },
        _genSrc(rawData) {
            if (!rawData) return // type 不存在
            const base64 = Base64.encode(rawData)
            this.setData({
                src: 'data:image/svg+xml;base64,' + base64
            })
        }
    }
})


`,
    'components/weicon/index.json': `{
  "component": true,
  "styleIsolation": "apply-shared",
  "usingComponents": {}
}
`,
    'components/weicon/index.wxml': `
<wxs module="utils">
    var double = function(a) {
        return 2*a
    };
    var ifSpecialIcon = function(v) {
      return v === 'arrow' || v === 'back'
    }
    module.exports = {
      double: double,
      ifSpecialIcon: ifSpecialIcon
    }
</wxs>

<view class="{{extClass}} weui-icon" style="background:{{color}};width:{{size}}px;height:{{ utils.ifSpecialIcon(icon) ? utils.double(size) : size}}px;mask-image:url({{src}});-webkit-mask-image:url({{src}});-moz-mask-image:url({{src}})"></view>

`,
'components/weicon/icondata.js': `
/* eslint-disable */
export default {
    "add-friends": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>3.Icons/Outlined/add-friends</title><desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/add-friends" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon13" transform="translate(1.000000, 3.000000)" fill="#000000">            <path d="M6.83191269,9.35013798 C6.09631763,8.47828515 5.5,6.84949648 5.5,5.70929053 L5.5,3.99958038 C5.5,1.79067313 7.29535615,0 9.5,0 C11.709139,0 13.5,1.79298022 13.5,4.00020747 L13.5,5.71018568 C13.5,6.84929595 12.9009324,8.48286035 12.1680872,9.35157303 L11.8065546,9.78013273 C11.2170324,10.4789507 11.4011877,11.3683976 12.225549,11.7705104 L17.8859024,14.5315576 C18.5012015,14.8316925 19,15.6251701 19,16.3154633 L19,17.0015619 C19,17.552984 18.5490746,18 17.9985704,18 L1.00142961,18 C0.448355308,18 0,17.5557555 0,17.0015619 L0,16.3154633 C0,15.6303744 0.498150907,14.8319079 1.11409761,14.5313327 L6.77445076,11.769143 C7.59537712,11.36854 7.78625906,10.4812624 7.19344522,9.7786389 L6.83191269,9.35013798 Z M1.2,16.8 L17.8,16.8 L17.8,16.3154633 C17.8,16.0860594 17.564679,15.7100199 17.3598095,15.6100873 L11.6994561,12.8490401 C10.1728743,12.1043932 9.79557277,10.302913 10.8893366,9.00636847 L11.2508692,8.57780877 C11.802195,7.92426863 12.3,6.56439093 12.3,5.71018568 L12.3,4.00020747 C12.3,2.4549142 11.0455898,1.2 9.5,1.2 C7.95630885,1.2 6.7,2.4552027 6.7,3.99958038 L6.7,5.70929053 C6.7,6.56566389 7.19574673,7.92048381 7.74907842,8.57631176 L8.11061095,9.00481267 C9.20663642,10.3038601 8.82521432,12.1036503 7.30071945,12.8475869 L1.6403663,15.6097766 C1.43701709,15.7090088 1.2,16.0886771 1.2,16.3154633 L1.2,16.8 Z M17.8999939,7.8999939 L17.8999939,5 L19.0999939,5 L19.0999939,7.8999939 L22,7.8999939 L22,9.09999394 L19.0999939,9.09999394 L19.0999939,12 L17.8999939,12 L17.8999939,9.09999394 L15,9.09999394 L15,7.8999939 L17.8999939,7.8999939 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/add-friends</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/add-friends" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon13" fill="#000000">            <path d="M18.7499939,10.75 L18.7499939,8 L20.2499939,8 L20.2499939,10.75 L23,10.75 L23,12.25 L20.2499939,12.25 L20.2499939,15 L18.7499939,15 L18.7499939,12.25 L16,12.25 L16,11.5 L16,10.75 L18.7499939,10.75 Z M7.83191269,12.350138 C7.09631763,11.4782852 6.5,9.84949648 6.5,8.70929053 L6.5,6.99958038 C6.5,4.79067313 8.29535615,3 10.5,3 C12.709139,3 14.5,4.79298022 14.5,7.00020747 L14.5,8.71018568 C14.5,9.84929595 13.9009324,11.4828603 13.1680872,12.351573 L12.8065546,12.7801327 C12.2170324,13.4789507 12.4011877,14.3683976 13.225549,14.7705104 L18.8859024,17.5315576 C19.5012015,17.8316925 20,18.6251701 20,19.3154633 L20,20.0015619 C20,20.552984 19.5490746,21 18.9985704,21 L2.00142961,21 C1.44835531,21 1,20.5557555 1,20.0015619 L1,19.3154633 C1,18.6303744 1.49815091,17.8319079 2.11409761,17.5313327 L7.77445076,14.769143 C8.59537712,14.36854 8.78625906,13.4812624 8.19344522,12.7786389 L7.83191269,12.350138 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "add": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/add</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/add" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Icons/Tint-Color/Black" transform="translate(3.000000, 3.000000)" fill="#000000">            <path d="M8.25,8.25 L8.25,0.5 L9.75,0.5 L9.75,8.25 L17.5,8.25 L17.5,9.75 L9.75,9.75 L9.75,17.5 L8.25,17.5 L8.25,9.75 L0.5,9.75 L0.5,8.25 L8.25,8.25 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/add</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/add" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="common_icons_add" transform="translate(4.000000, 4.000000)" fill="#000000">            <path d="M7,7 L7,0 L9,0 L9,7 L16,7 L16,9 L9,9 L9,16 L7,16 L7,9 L0,9 L0,7 L7,7 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "add2": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/add2</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/add2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Icons/Tint-Color/Black" transform="translate(2.000000, 2.000000)" fill="#000000">            <path d="M9.3999939,9.3999939 L9.3999939,5 L10.5999939,5 L10.5999939,9.3999939 L15,9.3999939 L15,10.5999939 L10.5999939,10.5999939 L10.5999939,15 L9.3999939,15 L9.3999939,10.5999939 L5,10.5999939 L5,9.3999939 L9.3999939,9.3999939 Z M10,20 C4.4771525,20 0,15.5228475 0,10 C0,4.4771525 4.4771525,0 10,0 C15.5228475,0 20,4.4771525 20,10 C20,15.5228475 15.5228475,20 10,20 Z M10,18.8 C14.8601058,18.8 18.8,14.8601058 18.8,10 C18.8,5.1398942 14.8601058,1.2 10,1.2 C5.1398942,1.2 1.2,5.1398942 1.2,10 C1.2,14.8601058 5.1398942,18.8 10,18.8 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/add2</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/add2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon21" fill="#000000">            <path d="M11.25,11.25 L7,11.25 L7,12.75 L11.25,12.75 L11.25,17 L12.75,17 L12.75,12.75 L17,12.75 L17,11.25 L12.75,11.25 L12.75,7 L11.25,7 L11.25,11.25 Z M12,22 C6.4771525,22 2,17.5228475 2,12 C2,6.4771525 6.4771525,2 12,2 C17.5228475,2 22,6.4771525 22,12 C22,17.5228475 17.5228475,22 12,22 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "album": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/album</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/album" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="common_icons_album" transform="translate(2.000000, 4.000000)" fill="#000000">            <path d="M18.8,10.4088466 L18.8,1.2 L1.2,1.2 L1.2,10.0922199 L6.19924553,6.04925865 C6.66179904,5.67518501 7.40462562,5.69332718 7.85049542,6.08508368 L12.3269229,10.0182264 L14.6091025,8.07749766 C15.0713175,7.68443756 15.8067229,7.69242258 16.2554314,8.0950837 L18.8,10.4088466 Z M18.7999996,11.9936093 L15.4261838,8.95789191 L13.2379757,10.8187086 L14.5824388,12 L12.8111672,12 L7.00605625,6.94008323 L1.2,11.6355239 L1.2,14.8 L18.8,14.8 L18.8,11.9936092 Z M0.99180311,0 L19.0081969,0 C19.5446944,0 20,0.481137002 20,1.07464957 L20,14.9253504 C20,15.5211518 19.5559546,16 19.0081969,16 L0.99180311,16 C0.455305576,16 0,15.518863 0,14.9253504 L0,1.07464957 C0,0.478848219 0.444045377,0 0.99180311,0 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/album</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/album" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="common_icons_album" fill="#000000">            <path d="M20.5,14.1360582 L20.5,5.5 L3.5,5.5 L3.5,13.8496056 L8.19924553,10.0492586 C8.66179904,9.67518501 9.40462562,9.69332718 9.85049542,10.0850837 L14.3269229,14.0182264 L16.6091025,12.0774977 C17.0713175,11.6844376 17.8067229,11.6924226 18.2554314,12.0950837 L20.5,14.1360582 Z M2.99180311,4 L21.0081969,4 C21.5446944,4 22,4.481137 22,5.07464957 L22,18.9253504 C22,19.5211518 21.5559546,20 21.0081969,20 L2.99180311,20 C2.45530558,20 2,19.518863 2,18.9253504 L2,5.07464957 C2,4.47884822 2.44404538,4 2.99180311,4 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "arrow": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="12px" height="24px" viewBox="0 0 12 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/arrow</title>    <desc>Created with Sketch.</desc>    <defs>        <path d="M7.58750873,12.4298916 L6.52684856,13.4905518 L0.747951526,7.71165473 C0.357826227,7.32152943 0.354365786,6.69247179 0.747951526,6.29888605 L6.52684856,0.519989014 L7.58750873,1.58064919 L2.16288753,7.00527039 L7.58750873,12.4298916 Z" id="path-1"></path>    </defs>    <g id="3.Icons/Outlined/arrow" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">        <g id="Group" transform="translate(2.000000, 5.000000)">            <mask id="mask-2" fill="white">                <use xlink:href="#path-1"></use>            </mask>            <use id="图标颜色" fill-opacity="0.9" fill="#000000" transform="translate(4.020784, 7.005270) rotate(-180.000000) translate(-4.020784, -7.005270) " xlink:href="#path-1"></use>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="12px" height="24px" viewBox="0 0 12 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/arrow</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/arrow" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" transform="translate(-2.000000, 5.000000)" fill="#000000">            <path d="M3,2.5039609 L11,2.5039609 L11,4.5039609 L4,4.5039609 L4,11.5039609 L2,11.5039609 L2,3.5039609 C2,2.95167615 2.44771525,2.5039609 3,2.5039609 Z" id="图标颜色" transform="translate(6.500000, 7.003961) rotate(135.000000) translate(-6.500000, -7.003961) "></path>        </g>    </g></svg>\` },
    "at": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/at</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/at" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Icons/Tint-Color/Black" transform="translate(2.000000, 2.000000)" fill="#000000">            <path d="M9.26953125,6.1875 C7.5703125,6.1875 6.46875,7.62890625 6.46875,9.84375 C6.46875,12.046875 7.55859375,13.4765625 9.2578125,13.4765625 C10.9921875,13.4765625 12.1054688,12.0234375 12.1054688,9.78515625 C12.1054688,7.58203125 11.015625,6.1875 9.26953125,6.1875 Z M9.64453125,-3.67705866e-13 C14.779824,-3.67705866e-13 19.0195312,3.52734375 19.0195312,8.71875 C19.0195312,12.4453125 17.4726562,14.8359375 14.8945312,14.8359375 C13.5,14.8359375 12.3984375,13.9921875 12.2226562,12.6796875 L12.140625,12.6796875 C11.6132812,14.015625 10.5234375,14.7539062 9.0703125,14.7539062 C6.65625,14.7539062 5.015625,12.7617187 5.015625,9.80859375 C5.015625,6.92578125 6.6796875,4.93359375 9.0703125,4.93359375 C10.4296875,4.93359375 11.6132812,5.671875 12.046875,6.8203125 L12.1289062,6.8203125 L12.1289062,5.16796875 L13.4882812,5.16796875 L13.4882812,11.8710937 C13.4882812,12.9375 14.0507812,13.640625 15.1171875,13.640625 C16.6757812,13.640625 17.7421875,11.8476562 17.7421875,8.6953125 C17.7421875,4.21875 14.2381961,1.17570471 9.64453125,1.17570471 C5.05086641,1.17570471 1.2890625,4.52563768 1.2890625,9.7734375 C1.2890625,14.6481839 5.3031848,18.3632812 9.7734375,18.3632812 C11.3554688,18.3632812 12.8671875,18.1640625 13.6640625,17.8476562 L13.6640625,19.0078125 C12.7148438,19.3359375 11.3320312,19.5234375 9.76171875,19.5234375 C4.19794948,19.5234375 0,15.3256061 0,9.7265625 C0,4.12751891 4.50923846,-3.67705866e-13 9.64453125,-3.67705866e-13 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/at</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/at" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" transform="translate(1.000000, 2.000000)" fill="#000000">            <path d="M10.4648438,7.14453125 C9.22265625,7.14453125 8.484375,8.1640625 8.484375,9.875 C8.484375,11.5742188 9.22265625,12.6054688 10.453125,12.6054688 C11.71875,12.6054688 12.5039062,11.5625 12.5039062,9.875 C12.5039062,8.1875 11.71875,7.14453125 10.4648438,7.14453125 Z M10.8515625,0.01953125 C16.40625,0.01953125 20.3085938,3.51171875 20.3085938,8.7265625 C20.3085938,12.4179688 18.5625,14.796875 15.703125,14.796875 C14.25,14.796875 13.1367188,14.09375 12.8789062,12.9570312 L12.7148437,12.9570312 C12.234375,14.1289062 11.25,14.7617188 9.890625,14.7617188 C7.453125,14.7617188 5.82421875,12.78125 5.82421875,9.8046875 C5.82421875,6.95703125 7.4296875,5.01171875 9.7734375,5.01171875 C11.0390625,5.01171875 12.09375,5.64453125 12.5507812,6.67578125 L12.7148437,6.67578125 L12.7148437,5.26953125 L15.09375,5.26953125 L15.09375,11.5390625 C15.09375,12.3476562 15.46875,12.8632812 16.2070312,12.8632812 C17.3554688,12.8632812 18.1523438,11.3984375 18.1523438,8.90234375 C18.1523438,4.63671875 15.2109375,1.8828125 10.7695312,1.8828125 C6.2578125,1.8828125 3.09375,5.140625 3.09375,9.8515625 C3.09375,14.7851562 6.38671875,17.6679688 11.2148438,17.6679688 C12.46875,17.6679688 13.7460938,17.5039062 14.4140625,17.2578125 L14.4140625,19.1328125 C13.5,19.4023438 12.3046875,19.5664062 11.0390625,19.5664062 C5.109375,19.5664062 0.9375,15.8515625 0.9375,9.79296875 C0.9375,3.98046875 5.00390625,0.01953125 10.8515625,0.01953125 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "back": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="12px" height="24px" viewBox="0 0 12 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/back</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/back" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Icons/Tint-Color/Black" transform="translate(1.000000, 3.000000)" fill="#000000">            <path d="M9,16.4375 L7.95453228,17.5 L0.289492864,9.71008525 C-0.0963897671,9.317916 -0.0915538699,8.67716932 0.289492864,8.28991475 L7.95453228,0.5 L9,1.5625 L1.68172599,9 L9,16.4375 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="12px" height="24px" viewBox="0 0 12 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/back</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/back" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="图标颜色" transform="translate(0.000000, 3.000000)" fill="#000000">            <path d="M3.34314575,9 L10.4142136,16.0710678 L9,17.4852814 L1.22182541,9.70710678 C0.831301115,9.31658249 0.831301115,8.68341751 1.22182541,8.29289322 L9,0.514718626 L10.4142136,1.92893219 L3.34314575,9 Z"></path>        </g>    </g></svg>\` },
    "back2": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/back2</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/back2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Icons/Tint-Color/Black" transform="translate(2.000000, 2.000000)" fill="#000000">            <path d="M10,20 C4.4771525,20 0,15.5228475 0,10 C0,4.4771525 4.4771525,0 10,0 C15.5228475,0 20,4.4771525 20,10 C20,15.5228475 15.5228475,20 10,20 Z M10,18.8 C14.8601058,18.8 18.8,14.8601058 18.8,10 C18.8,5.1398942 14.8601058,1.2 10,1.2 C5.1398942,1.2 1.2,5.1398942 1.2,10 C1.2,14.8601058 5.1398942,18.8 10,18.8 Z M11.998534,13.4375 L10.9530663,14.5 L7.29043516,10.7230029 C6.90322246,10.3236994 6.9031541,9.6763711 7.29043516,9.27699715 L10.9530663,5.5 L11.998534,6.5625 L8.68025999,10 L11.998534,13.4375 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/back2</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/back2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" transform="translate(2.000000, 2.000000)" fill="#000000">            <path d="M10,20 C4.4771525,20 0,15.5228475 0,10 C0,4.4771525 4.4771525,0 10,0 C15.5228475,0 20,4.4771525 20,10 C20,15.5228475 15.5228475,20 10,20 Z M11.998534,13.4375 L8.68025999,10 L11.998534,6.5625 L10.9530663,5.5 L7.29043516,9.27699715 C6.9031541,9.6763711 6.90322246,10.3236994 7.29043516,10.7230029 L10.9530663,14.5 L11.998534,13.4375 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "bellring-off": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/bellring_off</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/bellring_off" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="common_icons_mute" fill="#000000">            <path d="M18.8048808,17.107824 L22.4249789,20.7279221 L21.5764507,21.5764502 L4.84852868,4.84852817 L5.69705685,4 L7.60857571,5.91151886 C8.26739702,5.20419447 9.09506285,4.6562304 10.0273967,4.33180314 C10.0093774,4.22388264 10,4.11303643 10,4 C10,2.8954305 10.8954305,2 12,2 C13.1045695,2 14,2.8954305 14,4 C14,4.11303643 13.9906226,4.22388264 13.9726033,4.33180314 C16.317288,5.14769073 18,7.37733614 18,10 L18,14 C18,15.0359413 18.2682936,16.0718826 18.8048808,17.107824 Z M8.45731125,6.7602544 L16.9005511,15.2034943 C16.8336616,14.8040214 16.8,14.4027783 16.8,14 L16.8,10 C16.8,7.94086096 15.4913875,6.13087529 13.5782297,5.46514753 L12.6222987,5.13250909 L12.7889885,4.13417698 C12.7962818,4.09049657 12.8,4.04574037 12.8,4 C12.8,3.5581722 12.4418278,3.2 12,3.2 C11.5581722,3.2 11.2,3.5581722 11.2,4 C11.2,4.04574037 11.2037182,4.09049657 11.2110115,4.13417698 L11.3777013,5.13250909 L10.4217703,5.46514753 C9.65461156,5.73209825 8.9846599,6.18303836 8.45731125,6.7602544 Z M18.3029714,20.0000282 L12,20.0000282 L5.18670673,20.0000282 C4.97372563,20.0000282 4.76630774,19.9319983 4.59466757,19.8059007 C4.14960038,19.4789265 4.05386708,18.8530633 4.38084128,18.4079961 C5.46027096,16.9386502 6,15.4693251 6,14 L6,10 C6,9.30936012 6.11668844,8.64597429 6.33142549,8.02848234 L7.30447337,9.00153022 C7.23589042,9.32480971 7.2,9.65898818 7.2,10 L7.2,14 C7.2,15.6303277 6.64849214,17.2355113 5.57422114,18.8000266 L12.000005,18.8000266 L17.1029698,18.8000266 L18.3029714,20.0000282 Z M11,20 L13,20 L13,20.2 C13,20.7522848 12.5522847,21.2 12,21.2 C11.4477153,21.2 11,20.7522848 11,20.2 L11,20 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/bell-ring_off</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/bell-ring_off" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" transform="translate(4.000000, 2.000000)" fill="#000000">            <path d="M14.4530995,14.3317792 L18.0918831,17.9705627 L17.0312229,19.0312229 L0.0606601718,2.06066017 L1.12132034,1 L3.81845045,3.69713011 C4.4398094,3.09318282 5.19153957,2.62265908 6.02739671,2.33180314 C6.0093774,2.22388264 6,2.11303643 6,2 C6,0.8954305 6.8954305,0 8,0 C9.1045695,0 10,0.8954305 10,2 C10,2.11303643 9.9906226,2.22388264 9.97260329,2.33180314 C12.317288,3.14769073 14,5.37733614 14,8 L14,12 C14,12.7772597 14.1510332,13.5545194 14.4530995,14.3317792 Z M13.8787079,18.0000282 L8,18.0000282 L1.18670673,18.0000282 C0.973725629,18.0000282 0.766307737,17.9319983 0.594667574,17.8059007 C0.149600376,17.4789265 0.0538670836,16.8530633 0.380841281,16.4079961 C1.46027096,14.9386502 2,13.4693251 2,12 L2,8 C2,7.42805163 2.08002731,6.87479463 2.22949529,6.35081564 L13.8787079,18.0000282 Z M7,18 L9,18 L9,18.2 C9,18.7522848 8.55228475,19.2 8,19.2 C7.44771525,19.2 7,18.7522848 7,18.2 L7,18 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "bellring-on": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/bellring_on</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/bellring_on" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" transform="translate(4.000000, 2.000000)" fill="#000000">            <path d="M6,2 C6,0.8954305 6.8954305,0 8,0 C9.1045695,0 10,0.8954305 10,2 C10,2.11303643 9.9906226,2.22388264 9.97260329,2.33180314 C12.317288,3.14769073 14,5.37733614 14,8 L14,12 C14,13.4693091 14.5397173,14.9386183 15.619152,16.4079274 C15.7452557,16.5795777 15.8132606,16.7870072 15.8132606,17 C15.8132606,17.5523003 15.3655327,18.0000282 14.8132324,18.0000282 L8,18.0000282 L1.18670673,18.0000282 C0.973725629,18.0000282 0.766307737,17.9319983 0.594667574,17.8059007 C0.149600376,17.4789265 0.0538670836,16.8530633 0.380841281,16.4079961 C1.46027096,14.9386502 2,13.4693251 2,12 L2,8 C2,5.37733614 3.68271203,3.14769073 6.02739671,2.33180314 C6.0093774,2.22388264 6,2.11303643 6,2 Z M8.00000497,16.8000266 L14.4258309,16.8000266 C13.3515289,15.2355634 12.8,13.6303581 12.8,12 L12.8,8 C12.8,5.94086096 11.4913875,4.13087529 9.57822974,3.46514753 L8.6222987,3.13250909 L8.78898853,2.13417698 C8.79628178,2.09049657 8.8,2.04574037 8.8,2 C8.8,1.5581722 8.4418278,1.2 8,1.2 C7.5581722,1.2 7.2,1.5581722 7.2,2 C7.2,2.04574037 7.20371822,2.09049657 7.21101147,2.13417698 L7.3777013,3.13250909 L6.42177026,3.46514753 C4.50861248,4.13087529 3.2,5.94086096 3.2,8 L3.2,12 C3.2,13.6303277 2.64849214,15.2355113 1.57422114,16.8000266 L8.00000497,16.8000266 Z M7,18 L9,18 L9,18.2 C9,18.7522848 8.55228475,19.2 8,19.2 C7.44771525,19.2 7,18.7522848 7,18.2 L7,18 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/bell-ring_on</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/bell-ring_on" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" fill="#000000">            <path d="M11,20 L5.18670673,20 C4.97372563,20 4.76630774,19.9319983 4.59466757,19.8059007 C4.14960038,19.4789265 4.05386708,18.8530633 4.38084128,18.4079961 L4.38081287,18.4079752 C5.46027096,16.9386502 6,15.4693251 6,14 L6,10 C6,7.37733614 7.68271203,5.14769073 10.0273967,4.33180314 C10.0093774,4.22388264 10,4.11303643 10,4 C10,2.8954305 10.8954305,2 12,2 C13.1045695,2 14,2.8954305 14,4 C14,4.11303643 13.9906226,4.22388264 13.9726033,4.33180314 C16.317288,5.14769073 18,7.37733614 18,10 L18,14 C18,15.4693091 18.5397173,16.9386183 19.619152,18.4079274 C19.7452557,18.5795777 19.8132606,18.7870072 19.8132606,19 C19.8132606,19.5523003 19.3655327,20.0000282 18.8132324,20.0000282 L13,20 L13,20.2 C13,20.7522848 12.5522847,21.2 12,21.2 C11.4477153,21.2 11,20.7522848 11,20.2 L11,20 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "camera": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/camera</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/camera" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon20" transform="translate(2.000000, 4.000000)" fill="#000000">            <path d="M12.3577795,1.2 L7.64222051,1.2 L5.64222051,4.2 L1.2,4.2 L1.2,14.8 L18.8,14.8 L18.8,4.2 L14.3577795,4.2 L12.3577795,1.2 Z M12.7324081,0 C12.8995847,0 13.0557004,0.0835505677 13.1484333,0.222649902 L15,3 L19,3 C19.5522847,3 20,3.44771525 20,4 L20,15 C20,15.5522847 19.5522847,16 19,16 L1,16 C0.44771525,16 0,15.5522847 0,15 L0,4 C0,3.44771525 0.44771525,3 1,3 L5,3 L6.85156673,0.222649902 C6.94429962,0.0835505677 7.10041529,0 7.26759188,0 L12.7324081,0 Z M10,11.8 C11.5463973,11.8 12.8,10.5463973 12.8,9 C12.8,7.4536027 11.5463973,6.2 10,6.2 C8.4536027,6.2 7.2,7.4536027 7.2,9 C7.2,10.5463973 8.4536027,11.8 10,11.8 Z M10,13 C7.790861,13 6,11.209139 6,9 C6,6.790861 7.790861,5 10,5 C12.209139,5 14,6.790861 14,9 C14,11.209139 12.209139,13 10,13 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/camera</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/camera" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon20" transform="translate(2.000000, 4.000000)" fill="#000000">            <path d="M12.7324081,0 C12.8995847,5.6322073e-16 13.0557004,0.0835505677 13.1484333,0.222649902 L15,3 L19,3 C19.5522847,3 20,3.44771525 20,4 L20,15 C20,15.5522847 19.5522847,16 19,16 L1,16 C0.44771525,16 6.76353751e-17,15.5522847 0,15 L0,4 C-6.76353751e-17,3.44771525 0.44771525,3 1,3 L5,3 L6.85156673,0.222649902 C6.94429962,0.0835505677 7.10041529,3.07098421e-17 7.26759188,0 L12.7324081,0 Z M10,12.5 C11.9329966,12.5 13.5,10.9329966 13.5,9 C13.5,7.06700338 11.9329966,5.5 10,5.5 C8.06700338,5.5 6.5,7.06700338 6.5,9 C6.5,10.9329966 8.06700338,12.5 10,12.5 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "cellphone": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/cellphone</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/cellphone" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" transform="translate(5.000000, 2.000000)" fill="#000000">            <path d="M0,1 C0,0.44771525 0.44771525,0 1,0 L13,0 C13.5522847,0 14,0.44771525 14,1 L14,19 C14,19.5522847 13.5522847,20 13,20 L1,20 C0.44771525,20 0,19.5522847 0,19 L0,1 Z M1.2,1.2 L1.2,18.8 L12.8,18.8 L12.8,1.2 L1.2,1.2 Z M7,18 C6.44771525,18 6,17.5522847 6,17 C6,16.4477153 6.44771525,16 7,16 C7.55228475,16 8,16.4477153 8,17 C8,17.5522847 7.55228475,18 7,18 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/cellphone</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/cellphone" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" fill="#000000">            <path d="M5,3 C5,2.44771525 5.44771525,2 6,2 L18,2 C18.5522847,2 19,2.44771525 19,3 L19,21 C19,21.5522847 18.5522847,22 18,22 L6,22 C5.44771525,22 5,21.5522847 5,21 L5,3 Z M12,20 C12.5522847,20 13,19.5522847 13,19 C13,18.4477153 12.5522847,18 12,18 C11.4477153,18 11,18.4477153 11,19 C11,19.5522847 11.4477153,20 12,20 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "clip": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/clip</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/clip" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" transform="translate(3.000000, 3.000000)" fill="#000000">            <path d="M4.20000005,4.20000005 L4.20000005,13.8 L13.8,13.8 L13.8,4.20000005 L4.20000005,4.20000005 Z M3,4.20000005 L0,4.20000005 L0,3 L3,3 L3,0 L4.20000005,0 L4.20000005,3 L13.8,3 L14,3 C14.5522847,3 15,3.44771525 15,4 L15,13.8 L18,13.8 L18,15 L15,15 L15,18 L13.8,18 L13.8,15 L4,15 C3.44771525,15 3,14.5522847 3,14 L3,4.20000005 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/clip</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/clip" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" transform="translate(3.000000, 3.000000)" fill="#000000">            <path d="M13,15 L4,15 C3.44771525,15 3,14.5522847 3,14 L3,5 L0,5 L0,3 L3,3 L3,0 L5,0 L5,3 L14,3 C14.5522847,3 15,3.44771525 15,4 L15,13 L18,13 L18,15 L15,15 L15,18 L13,18 L13,15 Z M13,13 L13,5 L5,5 L5,13 L13,13 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "close": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/close</title>    <desc>Created with Sketch.</desc>    <defs>        <polygon id="path-1" points="8 6.94318182 1.80681818 0.75 0.75 1.80681818 6.94318182 8 0.75 14.1931818 1.80681818 15.25 8 9.05681818 14.1931818 15.25 15.25 14.1931818 9.05681818 8 15.25 1.80681818 14.1931818 0.75"></polygon>    </defs>    <g id="3.Icons/Outlined/close" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">        <g id="Icons/Tint-Color/Black" transform="translate(4.000000, 4.000000)">            <mask id="mask-2" fill="white">                <use xlink:href="#path-1"></use>            </mask>            <use id="图标颜色" fill-opacity="0.9" fill="#000000" xlink:href="#path-1"></use>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/close</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/close" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" transform="translate(4.000000, 4.000000)" fill="#000000">            <polygon id="图标颜色" points="8 6.58578644 13.6568542 0.928932188 15.0710678 2.34314575 9.41421356 8 15.0710678 13.6568542 13.6568542 15.0710678 8 9.41421356 2.34314575 15.0710678 0.928932188 13.6568542 6.58578644 8 0.928932188 2.34314575 2.34314575 0.928932188"></polygon>        </g>    </g></svg>\` },
    "close2": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/close2</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/close2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Icons/Tint-Color/Black" transform="translate(2.000000, 2.000000)" fill="#000000">            <path d="M10,20 C4.4771525,20 0,15.5228475 0,10 C0,4.4771525 4.4771525,0 10,0 C15.5228475,0 20,4.4771525 20,10 C20,15.5228475 15.5228475,20 10,20 Z M10,18.8 C14.8601058,18.8 18.8,14.8601058 18.8,10 C18.8,5.1398942 14.8601058,1.2 10,1.2 C5.1398942,1.2 1.2,5.1398942 1.2,10 C1.2,14.8601058 5.1398942,18.8 10,18.8 Z M10.8485282,9.99999949 L13.9597982,13.1112696 L13.1112701,13.9597977 L10,10.8485277 L6.88872993,13.9597977 L6.04020176,13.1112696 L9.15147183,9.99999949 L6.04020176,6.88872993 L6.88873043,6.04020176 L10,9.15147132 L13.1112696,6.04020176 L13.9597982,6.88872993 L10.8485282,9.99999949 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/close2</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/close2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="common_icons_miniprogram" fill="#000000">            <path d="M13.0606602,12 L16.065864,8.99479618 L15.0052038,7.93413601 L12,10.9393398 L8.99479618,7.93413601 L7.93413601,8.99479618 L10.9393398,12 L7.93413601,15.0052038 L8.99479618,16.065864 L12,13.0606602 L15.0052038,16.065864 L16.065864,15.0052038 L13.0606602,12 Z M12,22 C6.4771525,22 2,17.5228475 2,12 C2,6.4771525 6.4771525,2 12,2 C17.5228475,2 22,6.4771525 22,12 C22,17.5228475 17.5228475,22 12,22 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "comment": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/comment</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/comment" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group-22" transform="translate(2.000000, 4.000000)" fill="#000000">            <path d="M8.50294373,13.8 L18.5,13.8 C18.6656854,13.8 18.8,13.6656854 18.8,13.5 L18.8,1.5 C18.8,1.33431458 18.6656854,1.2 18.5,1.2 L1.5,1.2 C1.33431458,1.2 1.2,1.33431458 1.2,1.5 L1.2,13.5 C1.2,13.6656854 1.33431458,13.8 1.5,13.8 L6.2,13.8 L6.2,16.1029437 L8.50294373,13.8 Z M9,15 L6.70710678,17.2928932 C6.31658249,17.6834175 5.68341751,17.6834175 5.29289322,17.2928932 C5.10535684,17.1053568 5,16.8510029 5,16.5857864 L5,15 L1.5,15 C0.671572875,15 0,14.3284271 0,13.5 L0,1.5 C0,0.671572875 0.671572875,0 1.5,0 L18.5,0 C19.3284271,0 20,0.671572875 20,1.5 L20,13.5 C20,14.3284271 19.3284271,15 18.5,15 L9,15 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/comment</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/comment" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group-22" fill="#000000">            <path d="M11,19 L8.70710678,21.2928932 C8.31658249,21.6834175 7.68341751,21.6834175 7.29289322,21.2928932 C7.10535684,21.1053568 7,20.8510029 7,20.5857864 L7,19 L3.5,19 C2.67157288,19 2,18.3284271 2,17.5 L2,5.5 C2,4.67157288 2.67157288,4 3.5,4 L20.5,4 C21.3284271,4 22,4.67157288 22,5.5 L22,17.5 C22,18.3284271 21.3284271,19 20.5,19 L11,19 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "contacts": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/contacts</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/contacts" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon12" transform="translate(1.000000, 3.000000)" fill="#000000">            <path d="M17.8,16.8 L17.8,16.3154633 C17.8,16.0860594 17.564679,15.7100199 17.3598095,15.6100873 L11.6994561,12.8490401 C10.1728743,12.1043932 9.79557277,10.302913 10.8893366,9.00636847 L11.2508692,8.57780877 C11.802195,7.92426863 12.3,6.56439093 12.3,5.71018568 L12.3,4.00020747 C12.3,2.4549142 11.0455898,1.2 9.5,1.2 C7.95630885,1.2 6.7,2.4552027 6.7,3.99958038 L6.7,5.70929053 C6.7,6.56566389 7.19574673,7.92048381 7.74907842,8.57631176 L8.11061095,9.00481267 C9.20663642,10.3038601 8.82521432,12.1036503 7.30071945,12.8475869 L1.6403663,15.6097766 C1.43701709,15.7090088 1.2,16.0886771 1.2,16.3154633 L1.2,16.8 L17.8,16.8 Z M0,17 L0,16.3154633 C0,15.6303744 0.498150907,14.8319079 1.11409761,14.5313327 L6.77445076,11.769143 C7.59537712,11.36854 7.78625906,10.4812624 7.19344522,9.7786389 L6.83191269,9.35013798 C6.09631763,8.47828515 5.5,6.84949648 5.5,5.70929053 L5.5,3.99958038 C5.5,1.79067313 7.29535615,0 9.5,0 C11.709139,0 13.5,1.79298022 13.5,4.00020747 L13.5,5.71018568 C13.5,6.84929595 12.9009324,8.48286035 12.1680872,9.35157303 L11.8065546,9.78013273 C11.2170324,10.4789507 11.4011877,11.3683976 12.225549,11.7705104 L17.8859024,14.5315576 C18.5012015,14.8316925 19,15.6251701 19,16.3154633 L19,17 C19,17.5522847 18.5522847,18 18,18 L1,18 C0.44771525,18 0,17.5522847 0,17 Z M19,11.5 L22,11.5 L22,12.7 L19,12.7 L19,11.5 Z M17,8.5 L22,8.5 L22,9.70000005 L17,9.70000005 L17,8.5 Z M15,5.5 L22,5.5 L22,6.70000005 L15,6.70000005 L15,5.5 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/contacts</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/contacts" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon12" fill="#000000">            <path d="M1,20 L1,19.3154633 C1,18.6303744 1.49815091,17.8319079 2.11409761,17.5313327 L7.77445076,14.769143 C8.59537712,14.36854 8.78625906,13.4812624 8.19344522,12.7786389 L7.83191269,12.350138 C7.09631763,11.4782852 6.5,9.84949648 6.5,8.70929053 L6.5,6.99958038 C6.5,4.79067313 8.29535615,3 10.5,3 C12.709139,3 14.5,4.79298022 14.5,7.00020747 L14.5,8.71018568 C14.5,9.84929595 13.9009324,11.4828603 13.1680872,12.351573 L12.8065546,12.7801327 C12.2170324,13.4789507 12.4011877,14.3683976 13.225549,14.7705104 L18.8859024,17.5315576 C19.5012015,17.8316925 20,18.6251701 20,19.3154633 L20,20 C20,20.5522847 19.5522847,21 19,21 L2,21 C1.44771525,21 1,20.5522847 1,20 Z M20,14.5 L23,14.5 L23,16 L20,16 L20,14.5 Z M18,11.5 L23,11.5 L23,13 L18,13 L18,11.5 Z M16,8.5 L23,8.5 L23,10 L16,10 L16,8.5 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "copy": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/copy</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/copy" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" transform="translate(5.000000, 2.000000)" fill="#000000">            <path d="M7.4,1.2 L1.2,1.2 L1.2,18.8 L13.8,18.8 L13.8,7.6 L9,7.6 C8.1163444,7.6 7.4,6.8836556 7.4,6 L7.4,1.2 Z M8.6,1.2989947 L8.6,6 C8.6,6.2209139 8.7790861,6.4 9,6.4 L13.702787,6.4 L8.6,1.2989947 Z M0.995808514,0 L8.99790426,0 L15,6 L15,19.0013542 C15,19.5542301 14.5541613,20 14.0041915,20 L0.995808514,20 C0.448920205,20 0,19.552891 0,19.0013542 L0,0.998645811 C0,0.445769913 0.445838658,0 0.995808514,0 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/copy</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/copy" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" transform="translate(5.000000, 2.000000)" fill="#000000">            <path d="M8.6,1.2989947 L8.6,6 C8.6,6.2209139 8.7790861,6.4 9,6.4 L13.702787,6.4 L8.6,1.2989947 Z M0.995808514,0 L8.99790426,0 L15,6 L15,19.0013542 C15,19.5542301 14.5541613,20 14.0041915,20 L0.995808514,20 C0.448920205,20 0,19.552891 0,19.0013542 L0,0.998645811 C0,0.445769913 0.445838658,0 0.995808514,0 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "delete-on": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/delete_on</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/delete_on" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon39" fill="#000000">            <path d="M6.77355253,6.399997 L7.58590583,20.0475349 C7.61106164,20.4701525 7.96112672,20.8 8.38449235,20.8 L15.6155076,20.8 C16.0388733,20.8 16.3889384,20.4701525 16.4140942,20.0475349 L17.2264475,6.399997 L18.4285714,6.399997 L17.611974,20.1188373 C17.5490844,21.1753813 16.6739217,22 15.6155076,22 L8.38449235,22 C7.32607828,22 6.45091556,21.1753813 6.38802605,20.1188373 L5.57142856,6.399997 L6.77355253,6.399997 Z M9.5,9 L10.7000122,9 L11.2000122,18 L10,18 L9.5,9 Z M13.2999878,9 L14.5,9 L14,18 L12.7999878,18 L13.2999878,9 Z M4.4590499,2.35303271 L20.2159739,5.13140356 C20.4879211,5.17935518 20.6695054,5.43868437 20.6215537,5.71063152 L20.5,6.399997 L3.7582682,3.44797798 L3.87982193,2.7586125 C3.92777355,2.48666535 4.18710275,2.30508109 4.4590499,2.35303271 Z M10.7499171,1.2283746 L14.6891481,1.92296731 C14.9610953,1.97091893 15.1426796,2.23024812 15.0947279,2.50219528 L14.9731747,3.1915577 L10.049136,2.32331681 L10.1706892,1.63395439 C10.2186408,1.36200724 10.47797,1.18042298 10.7499171,1.2283746 Z" id="Path-2"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/delete_on</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/delete_on" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon39" fill="#000000">            <path d="M9.77436653,5 L3.7582682,3.93919955 L3.93191675,2.95438967 C3.97986837,2.68244252 4.23919756,2.50085826 4.51114472,2.54880988 L18.4125347,5 L18.5,5 L18.4125347,5 L20.2680688,5.32718073 C20.5400159,5.37513235 20.7216002,5.63446154 20.6736486,5.90640869 L20.5,6.89121857 L18.4104253,6.52277017 L17.6107386,20.117444 C17.5485547,21.1745693 16.6731425,22 15.6141898,22 L8.38581016,22 C7.32685754,22 6.45144525,21.1745693 6.38926141,20.117444 L5.58823542,6.50000215 L18.2813015,6.50000215 L9.77436653,5 L5.5,5 L9.77436653,5 Z M9,8.99998133 L9.5,17.9999943 L11,17.9999943 L10.6000004,8.99998133 L9,8.99998133 Z M13.5,8.99998133 L13,17.9999943 L14.5,17.9999943 L15,8.99998133 L13.5,8.99998133 Z M10.8541058,1.12871315 L14.7933368,1.82330587 C15.0652839,1.87125749 15.2468682,2.13058668 15.1989166,2.40253383 L15.0252743,3.387308 L10.1012355,2.51906711 L10.2748778,1.53429294 C10.3228294,1.26234579 10.5821586,1.08076153 10.8541058,1.12871315 L10.8541058,1.12871315 Z" id="Shape"></path>        </g>    </g></svg>\` },
    "delete": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/delete</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/delete" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon39" transform="translate(3.000000, 3.000000)" fill="#000000">            <path d="M3.77355253,3.39999695 L4.58590583,17.0475349 C4.61106164,17.4701525 4.96112672,17.8 5.38449235,17.8 L12.6155076,17.8 C13.0388733,17.8 13.3889384,17.4701525 13.4140942,17.0475349 L14.2264475,3.39999695 L3.77355253,3.39999695 Z M15.4285714,3.399997 L14.611974,17.1188373 C14.5490844,18.1753813 13.6739217,19 12.6155076,19 L5.38449235,19 C4.32607828,19 3.45091556,18.1753813 3.38802605,17.1188373 L2.57142856,3.399997 L0.5,3.399997 L0.5,2.69999695 C0.5,2.42385457 0.723857625,2.19999695 1,2.19999695 L17,2.19999695 C17.2761424,2.19999695 17.5,2.42385457 17.5,2.69999695 L17.5,3.399997 L15.4285714,3.399997 Z M11,2.72855691e-05 C11.2761424,2.72855691e-05 11.5,0.223884911 11.5,0.500027286 L11.5,1.20002423 L6.5,1.20002423 L6.5,0.500027286 C6.5,0.223884911 6.72385763,2.72855691e-05 7,2.72855691e-05 L11,2.72855691e-05 Z M6.5,6 L7.70001221,6 L8.20001221,15 L7,15 L6.5,6 Z M10.2999878,6 L11.5,6 L11,15 L9.79998779,15 L10.2999878,6 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/delete</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/delete" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon39" fill="#000000">            <g id="Group-3" transform="translate(3.000000, 2.000000)">                <path d="M15.4117647,4.5 L14.6107386,18.117444 C14.5485547,19.1745693 13.6731425,20 12.6141898,20 L5.38581016,20 C4.32685754,20 3.45144525,19.1745693 3.38926141,18.117444 L2.58823529,4.5 L0.5,4.5 L0.5,3.5 C0.5,3.22385763 0.723857625,3 1,3 L17,3 C17.2761424,3 17.5,3.22385763 17.5,3.5 L17.5,4.5 L15.4117647,4.5 Z M7,0.500034106 L11,0.500034106 C11.2761424,0.500034106 11.5,0.723891731 11.5,1.00003411 L11.5,2 L6.5,2 L6.5,1.00003411 C6.5,0.723891731 6.72385763,0.500034106 7,0.500034106 Z M6,6.99998133 L6.5,15.9999943 L8,15.9999943 L7.60000038,6.99998133 L6,6.99998133 Z M10.5,6.99998133 L10,15.9999943 L11.5,15.9999943 L12,6.99998133 L10.5,6.99998133 Z" id="图标颜色"></path>            </g>        </g>    </g></svg>\` },
    "discover": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/discover</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/discover" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Icons/Tint-Color/Black" transform="translate(2.000000, 2.000000)" fill="#000000">            <path d="M10,18.8 C14.8601058,18.8 18.8,14.8601058 18.8,10 C18.8,5.1398942 14.8601058,1.2 10,1.2 C5.1398942,1.2 1.2,5.1398942 1.2,10 C1.2,14.8601058 5.1398942,18.8 10,18.8 Z M10,20 C4.4771525,20 0,15.5228475 0,10 C0,4.4771525 4.4771525,0 10,0 C15.5228475,0 20,4.4771525 20,10 C20,15.5228475 15.5228475,20 10,20 Z M9.12335563,9.12335563 L7.26736162,12.7326384 L10.8766444,10.8766444 L12.7326384,7.26736162 L9.12335563,9.12335563 Z M8.23223305,8.23223305 L14.1243501,5.20234187 C14.2678549,5.12854767 14.4381526,5.12854767 14.5816575,5.20234187 C14.8272333,5.32862381 14.9239401,5.63007409 14.7976581,5.87564991 L11.767767,11.767767 L5.87564991,14.7976581 C5.73214506,14.8714523 5.56184738,14.8714523 5.41834253,14.7976581 C5.17276672,14.6713762 5.07605992,14.3699259 5.20234187,14.1243501 L8.23223305,8.23223305 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/discover</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/discover" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon22" fill="#000000">            <path d="M12,22 C6.4771525,22 2,17.5228475 2,12 C2,6.4771525 6.4771525,2 12,2 C17.5228475,2 22,6.4771525 22,12 C22,17.5228475 17.5228475,22 12,22 Z M10.6035341,10.6035341 L7.64699622,16.3530038 L13.3964659,13.3964659 L16.3530038,7.64699622 L10.6035341,10.6035341 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "display": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/display</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/display" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="common_icons_tv&amp;display" transform="translate(2.000000, 4.000000)" fill="#000000">            <path d="M1.2,1.2 L1.2,13.8 L18.8,13.8 L18.8,1.2 L1.2,1.2 Z M0,1 C0,0.44771525 0.44771525,0 1,0 L19,0 C19.5522847,0 20,0.44771525 20,1 L20,14 C20,14.5522847 19.5522847,15 19,15 L1,15 C0.44771525,15 0,14.5522847 0,14 L0,1 Z M6,16.8999993 C6,16.5686284 6.26617432,16.2999992 6.60130024,16.2999992 L13.3986998,16.2999992 C13.7307887,16.2999992 14,16.5783196 14,16.8999993 L14,17.4999993 L6,17.4999993 L6,16.8999993 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/display</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/display" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="common_icons_tv&amp;display" fill="#000000">            <g id="Pic" transform="translate(2.000000, 3.500000)">                <path d="M0,1.5 C0,0.94771525 0.44771525,0.5 1,0.5 L19,0.5 C19.5522847,0.5 20,0.94771525 20,1.5 L20,14.5 C20,15.0522847 19.5522847,15.5 19,15.5 L1,15.5 C0.44771525,15.5 0,15.0522847 0,14.5 L0,1.5 Z M6,17.5499992 C6,17.1357857 6.34375,16.7999992 6.75262058,16.7999992 L13.2473794,16.7999992 C13.6630403,16.7999992 14,17.1328979 14,17.5499992 L14,18.2999992 L6,18.2999992 L6,17.5499992 Z" id="图标颜色"></path>            </g>        </g>    </g></svg>\` },
    "done": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/done</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/done" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Icons/Tint-Color/Black" transform="translate(2.000000, 5.000000)" fill="#000000">            <path d="M6.86396103,11.6170094 L1.56066017,6.3137085 L0.5,7.37436867 L6.15685425,13.0312229 C6.54737854,13.4217472 7.18054352,13.4217472 7.57106781,13.0312229 L19.2383297,1.36396103 L18.1776695,0.303300859 L6.86396103,11.6170094 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/done</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/done" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="common_icons_done" fill="#000000">            <path d="M8.65685425,18.4350288 L3,12.7781746 L4.41421356,11.363961 L9.36396103,16.3137085 L20.6776695,5 L22.0918831,6.41421356 L10.0710678,18.4350288 C9.68054352,18.8255531 9.04737854,18.8255531 8.65685425,18.4350288 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "done2": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/done2</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/done2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Icons/Tint-Color/Black" transform="translate(2.000000, 2.000000)" fill="#000000">            <path d="M10,20 C4.4771525,20 0,15.5228475 0,10 C0,4.4771525 4.4771525,0 10,0 C15.5228475,0 20,4.4771525 20,10 C20,15.5228475 15.5228475,20 10,20 Z M10,18.8 C14.8601058,18.8 18.8,14.8601058 18.8,10 C18.8,5.1398942 14.8601058,1.2 10,1.2 C5.1398942,1.2 1.2,5.1398942 1.2,10 C1.2,14.8601058 5.1398942,18.8 10,18.8 Z M8.82842729,12.5583261 L14.6367534,6.75 L15.4852815,7.59852817 L9.53553407,13.5482756 C9.14500978,13.9387999 8.5118448,13.9387999 8.12132051,13.5482756 L5,10.4269551 L5.84852817,9.57842696 L8.82842729,12.5583261 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/done2</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/done2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="common_icons_miniprogram" fill="#000000">            <path d="M12,22 C6.4771525,22 2,17.5228475 2,12 C2,6.4771525 6.4771525,2 12,2 C17.5228475,2 22,6.4771525 22,12 C22,17.5228475 17.5228475,22 12,22 Z M10.8234373,14.1393166 L8.05753759,11.3734169 L7,12.4309545 L10.1188091,15.5520194 C10.1187366,15.5521643 10.1187728,15.5522005 10.118809,15.5522368 C10.5092522,15.9428422 11.1424171,15.9429738 11.5329139,15.5524219 L17.4852815,9.60252752 L16.422754,8.53999996 L10.8234373,14.1393166 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "download": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/download</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/download" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon38" transform="translate(4.000000, 2.000000)" fill="#000000">            <path d="M14.8,6.2 L12,6.2 L12,5 L15.0010434,5 C15.5508697,5 16,5.44583866 16,5.99580851 L16,19.0041915 C16,19.5510798 15.5541613,20 15.0041915,20 L0.995808514,20 C0.448920205,20 0,19.5541613 0,19.0041915 L0,5.99580851 C0,5.44892021 0.447248087,5 0.998956561,5 L4,5 L4,6.2 L1.2,6.2 L1.2,18.8 L14.8,18.8 L14.8,6.2 Z M8.59999394,10.6171633 L10.6870057,8.53015149 L11.5355339,9.37867966 L8.70710678,12.2071068 C8.31658249,12.5976311 7.68341751,12.5976311 7.29289322,12.2071068 L4.46446609,9.37867966 L5.31299427,8.53015149 L7.3999939,10.6171511 L7.3999939,0 L8.59999394,0 L8.59999394,10.6171633 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/download</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/download" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon38" fill="#000000">            <path d="M11.25,12.0397591 L9.53050029,10.3199997 L8.47000027,11.3806599 L11.2978402,14.2091404 C11.688335,14.5996941 12.3215,14.5997419 12.7120537,14.2090336 L15.5400004,11.3806599 L14.4795004,10.3199997 L12.75,12.0497613 L12.75,7 L11.25,7 L11.25,12.0397591 Z M11.25,7 L11.25,2 L12.75,2 L12.75,7 L19.0010434,7 C19.5508697,7 20,7.44583866 20,7.99580851 L20,21.0041915 C20,21.5510798 19.5541613,22 19.0041915,22 L4.99580851,22 C4.44892021,22 4,21.5541613 4,21.0041915 L4,7.99580851 C4,7.44892021 4.44724809,7 4.99895656,7 L11.25,7 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "email": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/email</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/email" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="common_icons_email" transform="translate(2.000000, 4.000000)" fill="#000000">            <path d="M2.2,1.2 L9.76,6.87 C9.90222222,6.97666667 10.0977778,6.97666667 10.24,6.87 L17.8,1.2 L2.2,1.2 Z M18.8,1.95 L10.96,7.83 C10.3911111,8.25666667 9.60888889,8.25666667 9.04,7.83 L1.2,1.95 L1.2,14.8 L18.8,14.8 L18.8,1.95 Z M1,0 L19,0 C19.5522847,0 20,0.44771525 20,1 L20,15 C20,15.5522847 19.5522847,16 19,16 L1,16 C0.44771525,16 0,15.5522847 0,15 L0,1 C0,0.44771525 0.44771525,0 1,0 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/email</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/email" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="common_icons_email" fill="#000000">            <path d="M3,4 L21,4 C21.5522847,4 22,4.44771525 22,5 L22,19 C22,19.5522847 21.5522847,20 21,20 L3,20 C2.44771525,20 2,19.5522847 2,19 L2,5 C2,4.44771525 2.44771525,4 3,4 Z M19.0314787,5.91434839 L12.1561738,11.4145924 C12.0648691,11.4876361 11.9351309,11.4876361 11.8438262,11.4145924 L4.96852129,5.91434839 L4.03147871,7.08565161 L10.9067837,12.5858956 C11.5459163,13.0972017 12.4540837,13.0972017 13.0932163,12.5858956 L19.9685213,7.08565161 L19.0314787,5.91434839 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "error": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/error</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/error" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Icons/Tint-Color/Black" transform="translate(2.000000, 2.000000)" fill="#000000">            <path d="M10,20 C4.4771525,20 0,15.5228475 0,10 C0,4.4771525 4.4771525,0 10,0 C15.5228475,0 20,4.4771525 20,10 C20,15.5228475 15.5228475,20 10,20 Z M10,18.8 C14.8601058,18.8 18.8,14.8601058 18.8,10 C18.8,5.1398942 14.8601058,1.2 10,1.2 C5.1398942,1.2 1.2,5.1398942 1.2,10 C1.2,14.8601058 5.1398942,18.8 10,18.8 Z M9.34082031,4.43115234 L10.6591797,4.43115234 L10.5712891,11.4916992 L9.42871094,11.4916992 L9.34082031,4.43115234 Z M10,15.0732422 C9.53125,15.0732422 9.16503906,14.7070312 9.16503906,14.2382812 C9.16503906,13.762207 9.53125,13.4033203 10,13.4033203 C10.4760742,13.4033203 10.8349609,13.762207 10.8349609,14.2382812 C10.8349609,14.7070312 10.4760742,15.0732422 10,15.0732422 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/error</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/error" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="common_icons_info" fill="#000000">            <path d="M12,22 C6.4771525,22 2,17.5228475 2,12 C2,6.4771525 6.4771525,2 12,2 C17.5228475,2 22,6.4771525 22,12 C22,17.5228475 17.5228475,22 12,22 Z M11.2367191,6.13574219 L11.3460941,13.7324219 L12.6517582,13.7324219 L12.7611332,6.13574219 L11.2367191,6.13574219 Z M11.9955082,17.1025391 C12.5082035,17.1025391 12.8978519,16.7197266 12.8978519,16.2207031 C12.8978519,15.7216797 12.5082035,15.3388672 11.9955082,15.3388672 C11.4964848,15.3388672 11.1000004,15.7216797 11.1000004,16.2207031 C11.1000004,16.7197266 11.4964848,17.1025391 11.9955082,17.1025391 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "eyes-off": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/eyes_off</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/eyes_off" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon21" transform="translate(1.000000, 3.000000)" fill="#000000">            <path d="M17.6701381,13.9730813 L20.4249789,16.7279221 L19.5764507,17.5764502 L2.84852868,0.848528171 L3.69705685,0 L6.55246809,2.85541124 C7.93196844,2.3029864 9.43174921,2 11,2 C15.8077906,2 19.9720635,4.84762755 22,9 C21.0088953,11.0293711 19.5075207,12.7471002 17.6701381,13.9730813 Z M7.48629025,3.7892334 L9.14977359,5.45271674 C9.70307173,5.1635303 10.3324386,5 11,5 C13.209139,5 15,6.790861 15,9 C15,9.66756141 14.8364697,10.2969283 14.5472833,10.8502264 L16.8030731,13.1060162 C18.3773063,12.1062525 19.7070071,10.7026267 20.6478722,9 C18.6862248,5.45012523 15.0343177,3.2 11,3.2 C9.78190146,3.2 8.59866509,3.40513103 7.48629025,3.7892334 Z M13.6379795,9.94092264 C13.7428778,9.64685352 13.7999997,9.33009741 13.7999997,8.99999976 C13.7999997,7.45360249 12.546397,6.19999981 10.9999998,6.19999981 C10.6699021,6.19999981 10.353146,6.25712171 10.0590769,6.36202004 L13.6379795,9.94092264 Z M15.4475319,15.1445888 C14.0680316,15.6970136 12.5682508,16 11,16 C6.1922094,16 2.0279365,13.1523724 -2.72848411e-12,9 C0.991104672,6.9706289 2.49247928,5.25289977 4.32986187,4.02691872 L5.19692691,4.89398376 C3.62269375,5.89374754 2.29299288,7.29737333 1.35212783,9 C3.31377519,12.5498748 6.96568232,14.8 11,14.8 C12.2180985,14.8 13.4013349,14.594869 14.5137097,14.2107666 L15.4475319,15.1445888 Z M7.45271674,7.14977359 L8.36202004,8.05907689 C8.25712171,8.353146 8.19999981,8.66990212 8.19999981,8.99999976 C8.19999981,10.546397 9.45360249,11.7999997 10.9999998,11.7999997 C11.3300974,11.7999997 11.6468535,11.7428778 11.9409226,11.6379795 L12.8502264,12.5472833 C12.2969283,12.8364697 11.6675614,13 11,13 C8.790861,13 7,11.209139 7,9 C7,8.33243859 7.1635303,7.70307173 7.45271674,7.14977359 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/eyes_off</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/eyes_off" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon21" fill="#000000">            <path d="M18.9218787,16.8005584 L22.0918831,19.9705627 L21.0312229,21.0312229 L4.06066017,4.06066017 L5.12132034,3 L7.8588755,5.73755516 C9.15302069,5.26004417 10.5471275,5 12,5 C16.8077906,5 20.9720635,7.84762755 23,12 C22.0545468,13.9358958 20.6447484,15.5881976 18.9218787,16.8005584 Z M10.8238458,8.70252549 L15.2974745,13.1761542 C15.4285976,12.8085676 15.5,12.4126223 15.5,12 C15.5,10.0670034 13.9329966,8.5 12,8.5 C11.5873777,8.5 11.1914324,8.57140244 10.8238458,8.70252549 Z M16.1411245,18.2624448 C14.8469793,18.7399558 13.4528725,19 12,19 C7.1922094,19 3.0279365,16.1523724 1,12 C1.94545318,10.0641042 3.35525158,8.41180238 5.07812128,7.19944162 L8.70252549,10.8238458 C8.57140244,11.1914324 8.5,11.5873777 8.5,12 C8.5,13.9329966 10.0670034,15.5 12,15.5 C12.4126223,15.5 12.8085676,15.4285976 13.1761542,15.2974745 L16.1411245,18.2624448 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "eyes-on": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/eyes_on</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/eyes_on" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon21" transform="translate(1.000000, 5.000000)" fill="#000000">            <path d="M11,12.8 C15.0343177,12.8 18.6862248,10.5498748 20.6478722,7 C18.6862248,3.45012523 15.0343177,1.2 11,1.2 C6.96568232,1.2 3.31377519,3.45012523 1.35212783,7 C3.31377519,10.5498748 6.96568232,12.8 11,12.8 Z M11,0 C15.8077906,0 19.9720635,2.84762755 22,7 C19.9720635,11.1523724 15.8077906,14 11,14 C6.1922094,14 2.0279365,11.1523724 -1.09139364e-11,7 C2.0279365,2.84762755 6.1922094,0 11,0 Z M11,9.8 C12.5463973,9.8 13.8,8.5463973 13.8,7 C13.8,5.4536027 12.5463973,4.2 11,4.2 C9.4536027,4.2 8.2,5.4536027 8.2,7 C8.2,8.5463973 9.4536027,9.8 11,9.8 Z M11,11 C8.790861,11 7,9.209139 7,7 C7,4.790861 8.790861,3 11,3 C13.209139,3 15,4.790861 15,7 C15,9.209139 13.209139,11 11,11 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/eyes_on</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/eyes_on" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon21" fill="#000000">            <path d="M1,12 C3.0279365,7.84762755 7.1922094,5 12,5 C16.8077906,5 20.9720635,7.84762755 23,12 C20.9720635,16.1523724 16.8077906,19 12,19 C7.1922094,19 3.0279365,16.1523724 1,12 Z M12,15.5 C13.9329966,15.5 15.5,13.9329966 15.5,12 C15.5,10.0670034 13.9329966,8.5 12,8.5 C10.0670034,8.5 8.5,10.0670034 8.5,12 C8.5,13.9329966 10.0670034,15.5 12,15.5 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "folder": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/folder</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/folder" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Picture" transform="translate(2.000000, 4.000000)" fill="#000000">            <path d="M18.8,5 L18.8,2.7 L8.20406273,2.7 L6.32906273,1.2 L1.2,1.2 L1.2,5 L18.8,5 Z M18.8,6.20000005 L1.2,6.20000005 L1.2,14.8 L18.8,14.8 L18.8,6.20000005 Z M1,0 L6.75,0 L8.625,1.5 L19,1.5 C19.5522847,1.5 20,1.94771525 20,2.5 L20,15 C20,15.5522847 19.5522847,16 19,16 L1,16 C0.44771525,16 0,15.5522847 0,15 L0,1 C0,0.44771525 0.44771525,0 1,0 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/folder</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/folder" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Picture" transform="translate(2.000000, 4.000000)" fill="#000000">            <path d="M1,0 L6.75,0 L8.625,1.5 L19,1.5 C19.5522847,1.5 20,1.94771525 20,2.5 L20,15 C20,15.5522847 19.5522847,16 19,16 L1,16 C0.44771525,16 6.76353751e-17,15.5522847 0,15 L0,1 C-6.76353751e-17,0.44771525 0.44771525,1.01453063e-16 1,0 Z M1.5,5 L1.5,6.5 L18.5,6.5 L18.5,5 L1.5,5 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "group-detail": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/group-detail</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/group-detail" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon14" transform="translate(1.000000, 3.000000)" fill="#000000">            <path d="M1.2,16.8 L17.8,16.8 L17.8,16.3154633 C17.8,16.0860594 17.564679,15.7100199 17.3598095,15.6100873 L11.6994561,12.8490401 C10.1728743,12.1043932 9.79557277,10.302913 10.8893366,9.00636847 L11.2508692,8.57780877 C11.802195,7.92426863 12.3,6.56439093 12.3,5.71018568 L12.3,4.00020747 C12.3,2.4549142 11.0455898,1.2 9.5,1.2 C7.95630885,1.2 6.7,2.4552027 6.7,3.99958038 L6.7,5.70929053 C6.7,6.56566389 7.19574673,7.92048381 7.74907842,8.57631176 L8.11061095,9.00481267 C9.20663642,10.3038601 8.82521432,12.1036503 7.30071945,12.8475869 L1.6403663,15.6097766 C1.43701709,15.7090088 1.2,16.0886771 1.2,16.3154633 L1.2,16.8 Z M12.4960944,1.35064233 C12.9490802,1.12626413 13.4599583,1 14,1 C15.8603276,1 17.3684211,2.49415019 17.3684211,4.33350622 L17.3684211,5.75848807 C17.3684211,6.70774662 16.8639431,8.06905029 16.2468102,8.79297753 L15.9423618,9.15011061 C15.445922,9.73245889 15.6010002,10.4736647 16.2951991,10.8087587 L21.0618125,13.1096313 C21.5799592,13.3597438 22,14.0209751 22,14.5962194 L22,15.1679682 C22,15.6274867 21.6202734,16 21.1566909,16 L18.9676453,16 C18.9887855,16.1061337 19,16.2119585 19,16.3154633 L19,17.0015619 C19,17.552984 18.5490746,18 17.9985704,18 L1.00142961,18 C0.448355308,18 0,17.5557555 0,17.0015619 L0,16.3154633 C0,15.6303744 0.498150907,14.8319079 1.11409761,14.5313327 L6.77445076,11.769143 C7.59537712,11.36854 7.78625906,10.4812624 7.19344522,9.7786389 L6.83191269,9.35013798 C6.09631763,8.47828515 5.5,6.84949648 5.5,5.70929053 L5.5,3.99958038 C5.5,1.79067313 7.29535615,0 9.5,0 C10.6925681,0 11.7632414,0.522511424 12.4960868,1.35063371 Z M13.1531568,2.36930666 C13.3760552,2.86753487 13.5,3.41953623 13.5,4.00020747 L13.5,5.71018568 C13.5,6.84929595 12.9009324,8.48286035 12.1680872,9.35157303 L11.8065546,9.78013273 C11.2170324,10.4789507 11.4011877,11.3683976 12.225549,11.7705104 L17.8859024,14.5315576 C18.0233929,14.5986237 18.1550664,14.690323 18.2766575,14.8 L20.8,14.8 L20.8,14.5962194 C20.8,14.4845959 20.6463054,14.2415527 20.5401598,14.1903156 L15.7735464,11.8894429 C14.3727962,11.2132924 14.0215331,9.55360651 15.0291527,8.37161899 L15.3336012,8.01448591 C15.76679,7.50633402 16.1684211,6.42060801 16.1684211,5.75848807 L16.1684211,4.33350622 C16.1684211,3.15902864 15.1996866,2.2 14,2.2 C13.6994787,2.2 13.4132922,2.26035387 13.1531562,2.36930543 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/group-detail</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/group-detail" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon14" fill="#000000">            <path d="M15.1110698,4.00178 C16.9199852,4.05984018 18.3684211,5.53089815 18.3684211,7.33350622 L18.3684211,8.75848807 C18.3684211,9.70774662 17.8639431,11.0690503 17.2468102,11.7929775 L16.9423618,12.1501106 C16.445922,12.7324589 16.6010002,13.4736647 17.2951991,13.8087587 L22.0618125,16.1096313 C22.5799592,16.3597438 23,17.0209751 23,17.5962194 L23,18.1679682 C23,18.6274867 22.6202734,19 22.1566909,19 L21.4832067,19 C21.3608065,17.8393567 20.5814047,16.689663 19.5435184,16.1833954 L14.1269298,13.5412535 L14.3146097,13.3187784 C15.276491,12.1785669 16,10.2021228 16,8.71018568 L16,7.00020747 C16,5.89435381 15.6732669,4.86441043 15.1110698,4.00178 L15.1110698,4.00178 Z M7.83191269,12.350138 C7.09631763,11.4782852 6.5,9.84949648 6.5,8.70929053 L6.5,6.99958038 C6.5,4.79067313 8.29535615,3 10.5,3 C12.709139,3 14.5,4.79298022 14.5,7.00020747 L14.5,8.71018568 C14.5,9.84929595 13.9009324,11.4828603 13.1680872,12.351573 L12.8065546,12.7801327 C12.2170324,13.4789507 12.4011877,14.3683976 13.225549,14.7705104 L18.8859024,17.5315576 C19.5012015,17.8316925 20,18.6251701 20,19.3154633 L20,20.0015619 C20,20.552984 19.5490746,21 18.9985704,21 L2.00142961,21 C1.44835531,21 1,20.5557555 1,20.0015619 L1,19.3154633 C1,18.6303744 1.49815091,17.8319079 2.11409761,17.5313327 L7.77445076,14.769143 C8.59537712,14.36854 8.78625906,13.4812624 8.19344522,12.7786389 L7.83191269,12.350138 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "help": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/help</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/help" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Icons/Tint-Color/Black" transform="translate(2.000000, 2.000000)" fill="#000000">            <path d="M10,20 C4.4771525,20 0,15.5228475 0,10 C0,4.4771525 4.4771525,0 10,0 C15.5228475,0 20,4.4771525 20,10 C20,15.5228475 15.5228475,20 10,20 Z M10,18.8 C14.8601058,18.8 18.8,14.8601058 18.8,10 C18.8,5.1398942 14.8601058,1.2 10,1.2 C5.1398942,1.2 1.2,5.1398942 1.2,10 C1.2,14.8601058 5.1398942,18.8 10,18.8 Z M7,7.70697674 C7.07674419,6.2 8.08139535,5 10.0418605,5 C11.8,5 13,6.08837209 13,7.5255814 C13,8.62093023 12.4348837,9.39534884 11.5418605,9.93255814 C10.655814,10.455814 10.4046512,10.8465116 10.4046512,11.5790698 L10.4046512,12.0325581 L9.18372093,12.0325581 L9.18372093,11.3906977 C9.17674419,10.4697674 9.62325581,9.84186047 10.5790698,9.26976744 C11.3883721,8.7744186 11.7023256,8.33488372 11.7023256,7.58837209 C11.7023256,6.72325581 11.0325581,6.08837209 9.99302326,6.08837209 C8.93953488,6.08837209 8.26976744,6.70930233 8.19302326,7.70697674 L7,7.70697674 Z M9.79767442,15.2139535 C9.35116279,15.2139535 9.00232558,14.8651163 9.00232558,14.4186047 C9.00232558,13.9651163 9.35116279,13.6232558 9.79767442,13.6232558 C10.2511628,13.6232558 10.5930233,13.9651163 10.5930233,14.4186047 C10.5930233,14.8651163 10.2511628,15.2139535 9.79767442,15.2139535 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/help</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/help" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon10" fill="#000000">            <path d="M12,22 C6.4771525,22 2,17.5228475 2,12 C2,6.4771525 6.4771525,2 12,2 C17.5228475,2 22,6.4771525 22,12 C22,17.5228475 17.5228475,22 12,22 Z M9,9.78222656 L10.6064453,9.78222656 C10.6611328,8.93457031 11.2285156,8.39453125 12.1103516,8.39453125 C12.9716797,8.39453125 13.5458984,8.92089844 13.5458984,9.64550781 C13.5458984,10.3222656 13.2587891,10.6914062 12.4111328,11.2041016 C11.4677734,11.7578125 11.0712891,12.3730469 11.1328125,13.3847656 L11.1396484,13.8701172 L12.7255859,13.8701172 L12.7255859,13.4736328 C12.7255859,12.7900391 12.9785156,12.4345703 13.8740234,11.9150391 C14.8037109,11.3613281 15.3232422,10.6298828 15.3232422,9.58398438 C15.3232422,8.08007812 14.0722656,7 12.1992188,7 C10.1689453,7 9.0546875,8.17578125 9,9.78222656 Z M11.953125,17.0830078 C12.5068359,17.0830078 12.9443359,16.6523438 12.9443359,16.1123047 C12.9443359,15.5722656 12.5068359,15.1484375 11.953125,15.1484375 C11.3994141,15.1484375 10.9550781,15.5722656 10.9550781,16.1123047 C10.9550781,16.6523438 11.3994141,17.0830078 11.953125,17.0830078 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "home": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/home</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/home" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon31" transform="translate(2.000000, 3.000000)" fill="#000000">            <path d="M11,16.8 L14.8,16.8 L14.8,9.8 L18.1029437,9.8 L10,1.69705627 L1.89705627,9.8 L5.2,9.8 L5.2,16.8 L9,16.8 L9,14 L11,14 L11,16.8 Z M16,17 C16,17.5522847 15.5522847,18 15,18 L5,18 C4.44771525,18 4,17.5522847 4,17 L4,11 L1.41421356,11 C1.14899707,11 0.89464316,10.8946432 0.707106781,10.7071068 C0.316582489,10.3165825 0.316582489,9.68341751 0.707106781,9.29289322 L9.29289322,0.707106781 C9.68341751,0.316582489 10.3165825,0.316582489 10.7071068,0.707106781 L19.2928932,9.29289322 C19.4804296,9.4804296 19.5857864,9.73478351 19.5857864,10 C19.5857864,10.5522847 19.1380712,11 18.5857864,11 L16,11 L16,17 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/home</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/home" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon31" fill="#000000">            <path d="M13.5,21 L13.5,17 L10.5,17 L10.5,21 L7,21 C6.44771525,21 6,20.5522847 6,20 L6,14.0000062 L3.41421259,14.0000062 C3.1489962,14.0000062 2.89464237,13.8946495 2.70710601,13.7071132 C2.31658161,13.316589 2.31658143,12.6834241 2.70710561,12.2928997 L11.2928934,3.70710618 C11.6834178,3.316582 12.3165828,3.31658218 12.7071066,3.70710698 L21.2928944,12.2928997 C21.4804306,12.480436 21.5857874,12.7347899 21.5857874,13.0000062 C21.5857874,13.552291 21.1380722,14.0000062 20.5857874,14.0000062 L18,14.0000062 L18,20 C18,20.5522847 17.5522847,21 17,21 L13.5,21 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "imac": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/imac</title>    <desc>Created with Sketch.</desc>    <defs>        <path d="M18.8,11 L18.8,1.2 L1.2,1.2 L1.2,11 L18.8,11 Z M18.8,12.2 L1.2,12.2 L1.2,13.8 L18.8,13.8 L18.8,12.2 Z M13.1554082,15 L13.7770168,16.8399675 C13.7944347,16.8915248 13.803319,16.9455799 13.803319,17 C13.803319,17.2761424 13.5794614,17.5 13.303319,17.5 L6.69668101,17.5 C6.64226093,17.5 6.58820584,17.4911158 6.5366485,17.4736978 C6.27503244,17.3853143 6.13459971,17.1015836 6.22298322,16.8399675 L6.84459181,15 L1,15 C0.44771525,15 0,14.5522847 0,14 L0,1 C0,0.44771525 0.44771525,0 1,0 L19,0 C19.5522847,0 20,0.44771525 20,1 L20,14 C20,14.5522847 19.5522847,15 19,15 L13.1554082,15 Z M8.11122629,14.9999878 L7.67203442,16.3 L12.3279656,16.3 L11.8887737,14.9999878 L8.11122629,14.9999878 Z" id="path-1"></path>    </defs>    <g id="3.Icons/Outlined/imac" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">        <g id="icon31" transform="translate(2.000000, 4.000000)">            <mask id="mask-2" fill="white">                <use xlink:href="#path-1"></use>            </mask>            <use id="图标颜色" fill-opacity="0.9" fill="#000000" xlink:href="#path-1"></use>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/imac</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/imac" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon31" fill="#000000">            <g id="Group-2" transform="translate(2.000000, 4.000000)">                <path d="M13.1554082,15 L13.7770168,16.8399675 C13.7944347,16.8915248 13.803319,16.9455799 13.803319,17 C13.803319,17.2761424 13.5794614,17.5 13.303319,17.5 L6.69668101,17.5 C6.64226093,17.5 6.58820584,17.4911158 6.5366485,17.4736978 C6.27503244,17.3853143 6.13459971,17.1015836 6.22298322,16.8399675 L6.84459181,15 L1,15 C0.44771525,15 0,14.5522847 0,14 L0,1 C0,0.44771525 0.44771525,0 1,0 L19,0 C19.5522847,0 20,0.44771525 20,1 L20,14 C20,14.5522847 19.5522847,15 19,15 L13.1554082,15 L13.1554082,15 Z M18.5,12 L1.5,12 L1.5,13.5 L18.5,13.5 L18.5,12 Z" id="图标颜色"></path>            </g>        </g>    </g></svg>\` },
    "info": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/info</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/info" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Icons/Tint-Color/Black" transform="translate(2.000000, 2.000000)" fill="#000000">            <path d="M10,20 C4.4771525,20 0,15.5228475 0,10 C0,4.4771525 4.4771525,0 10,0 C15.5228475,0 20,4.4771525 20,10 C20,15.5228475 15.5228475,20 10,20 Z M10,18.8 C14.8601058,18.8 18.8,14.8601058 18.8,10 C18.8,5.1398942 14.8601058,1.2 10,1.2 C5.1398942,1.2 1.2,5.1398942 1.2,10 C1.2,14.8601058 5.1398942,18.8 10,18.8 Z M9.39999962,8 L10.5999997,8 L10.5999997,15 L9.39999962,15 L9.39999962,8 Z M10,7 C9.44771525,7 9,6.55228475 9,6 C9,5.44771525 9.44771525,5 10,5 C10.5522847,5 11,5.44771525 11,6 C11,6.55228475 10.5522847,7 10,7 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/info</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/info" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="common_icons_info" fill="#000000">            <g id="Group-19" transform="translate(2.000000, 2.000000)">                <path d="M10,20 C4.4771525,20 0,15.5228475 0,10 C0,4.4771525 4.4771525,0 10,0 C15.5228475,0 20,4.4771525 20,10 C20,15.5228475 15.5228475,20 10,20 Z M9.25,8 L9.25,15 L10.75,15 L10.75,8 L9.25,8 Z M10,7 C10.5522847,7 11,6.55228475 11,6 C11,5.44771525 10.5522847,5 10,5 C9.44771525,5 9,5.44771525 9,6 C9,6.55228475 9.44771525,7 10,7 Z" id="图标颜色"></path>            </g>        </g>    </g></svg>\` },
    "keyboard": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/keyboard</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/keyboard" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="common_icons_info" transform="translate(2.000000, 2.000000)" fill="#000000">            <path d="M10,20 C4.4771525,20 0,15.5228475 0,10 C0,4.4771525 4.4771525,0 10,0 C15.5228475,0 20,4.4771525 20,10 C20,15.5228475 15.5228475,20 10,20 Z M10,18.8 C14.8601058,18.8 18.8,14.8601058 18.8,10 C18.8,5.1398942 14.8601058,1.2 10,1.2 C5.1398942,1.2 1.2,5.1398942 1.2,10 C1.2,14.8601058 5.1398942,18.8 10,18.8 Z M4.5,6 L6.5,6 L6.5,8 L4.5,8 L4.5,6 Z M7.5,6 L9.5,6 L9.5,8 L7.5,8 L7.5,6 Z M10.5,6 L12.5,6 L12.5,8 L10.5,8 L10.5,6 Z M13.5,6 L15.5,6 L15.5,8 L13.5,8 L13.5,6 Z M4.5,9 L6.5,9 L6.5,11 L4.5,11 L4.5,9 Z M7.5,9 L9.5,9 L9.5,11 L7.5,11 L7.5,9 Z M7,13 L13,13 L13,15 L7,15 L7,13 Z M10.5,9 L12.5,9 L12.5,11 L10.5,11 L10.5,9 Z M13.5,9 L15.5,9 L15.5,11 L13.5,11 L13.5,9 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/keyboard</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/keyboard" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="common_icons_info" fill="#000000">            <path d="M12,22 C6.4771525,22 2,17.5228475 2,12 C2,6.4771525 6.4771525,2 12,2 C17.5228475,2 22,6.4771525 22,12 C22,17.5228475 17.5228475,22 12,22 Z M6.5,8 L6.5,10 L8.5,10 L8.5,8 L6.5,8 Z M9.5,8 L9.5,10 L11.5,10 L11.5,8 L9.5,8 Z M12.5,8 L12.5,10 L14.5,10 L14.5,8 L12.5,8 Z M15.5,8 L15.5,10 L17.5,10 L17.5,8 L15.5,8 Z M6.5,11 L6.5,13 L8.5,13 L8.5,11 L6.5,11 Z M9.5,11 L9.5,13 L11.5,13 L11.5,11 L9.5,11 Z M9,15 L9,17 L15,17 L15,15 L9,15 Z M12.5,11 L12.5,13 L14.5,13 L14.5,11 L12.5,11 Z M15.5,11 L15.5,13 L17.5,13 L17.5,11 L15.5,11 Z" id="Mask"></path>        </g>    </g></svg>\` },
    "like": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/like</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/like" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="common_icons_like" transform="translate(3.000000, 5.000000)" fill="#000000">            <path d="M16.2846386,7.64509528 C17.7287556,6.15675186 17.7118979,3.7815372 16.243355,2.31299423 C14.7593626,0.829001923 12.3533357,0.829001923 10.8693434,2.31299423 C10.6770786,2.50525906 10.4332063,2.74049974 10.1366138,3.0199452 L9.3137085,3.79527533 L8.49080324,3.0199452 C8.1942107,2.74049974 7.95033841,2.50525906 7.75807358,2.31299423 C6.27408127,0.829001923 3.86805435,0.829001923 2.38406204,2.31299423 C0.915537104,3.78151917 0.898583145,6.15687038 2.32967362,7.63261735 L9.313666,14.6166823 L16.2846386,7.64509528 Z M1.53553391,1.46446609 C3.48815536,-0.488155365 6.65398026,-0.488155365 8.60660172,1.46446609 C8.78940843,1.6472728 9.02511069,1.87463262 9.3137085,2.14654555 C9.60230631,1.87463262 9.83800857,1.6472728 10.0208153,1.46446609 C11.9734367,-0.488155365 15.1392616,-0.488155365 17.0918831,1.46446609 C19.0263413,3.39892429 19.0443356,6.5241205 17.145866,8.48073116 L10.0208153,15.6066017 C9.63031143,15.9971464 8.99714645,15.9971796 8.60660172,15.6066757 L1.48114108,8.48114108 C-0.416918598,6.5241205 -0.398924294,3.39892429 1.53553391,1.46446609 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/like</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/like" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="common_icons_like" fill="#000000">            <path d="M4.53553391,5.77817459 C6.48815536,3.82555313 9.65398026,3.82555313 11.6066017,5.77817459 C11.7894084,5.9609813 12.0251107,6.18834112 12.3137085,6.46025405 C12.6023063,6.18834112 12.8380086,5.9609813 13.0208153,5.77817459 C14.9734367,3.82555313 18.1392616,3.82555313 20.0918831,5.77817459 C22.0263413,7.71263279 22.0443356,10.837829 20.145866,12.7944397 L13.0207783,19.9202732 C12.6303114,20.3108549 11.9971464,20.3108881 11.6066017,19.9203842 C11.6065894,19.9203719 11.606577,19.9203596 11.6066017,19.9203102 L4.48114108,12.7948496 C2.5830814,10.837829 2.60107571,7.71263279 4.53553391,5.77817459 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "link": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/link</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/link" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" transform="translate(3.000000, 3.000000)" fill="#000000">            <path d="M14.6568542,9 L13.8083261,8.15147186 L15.9296465,6.03015152 C17.0231145,4.9366835 17.0231145,3.16382156 15.9296465,2.07035354 C14.8361784,0.976885527 13.0633165,0.976885527 11.9698485,2.07035354 L8.08076118,5.95944084 C6.98729317,7.05290886 6.98729317,8.8257708 8.08076118,9.91923882 L7.23223305,10.767767 C5.67013588,9.20566979 5.67013588,6.67300987 7.23223305,5.1109127 L11.1213203,1.22182541 C12.6834175,-0.34027176 15.2160774,-0.34027176 16.7781746,1.22182541 C18.3402718,2.78392257 18.3402718,5.31658249 16.7781746,6.87867966 L14.6568542,9 Z M3.34314575,9 L4.19167389,9.84852814 L2.07035354,11.9698485 C0.976885527,13.0633165 0.976885527,14.8361784 2.07035354,15.9296465 C3.16382156,17.0231145 4.9366835,17.0231145 6.03015152,15.9296465 L9.91923882,12.0405592 C11.0127068,10.9470911 11.0127068,9.1742292 9.91923882,8.08076118 L10.767767,7.23223305 C12.3298641,8.79433021 12.3298641,11.3269901 10.767767,12.8890873 L6.87867966,16.7781746 C5.31658249,18.3402718 2.78392257,18.3402718 1.22182541,16.7781746 C-0.34027176,15.2160774 -0.34027176,12.6834175 1.22182541,11.1213203 L3.34314575,9 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/link</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/link" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" transform="translate(3.000000, 3.000000)" fill="#000000">            <path d="M14.6568542,9 L13.2426407,7.58578644 L15.363961,5.46446609 C16.1450096,4.68341751 16.1450096,3.41708755 15.363961,2.63603897 C14.5829124,1.85499039 13.3165825,1.85499039 12.5355339,2.63603897 L8.29289322,6.87867966 C7.51184464,7.65972824 7.51184464,8.9260582 8.29289322,9.70710678 L6.87867966,11.1213203 C5.31658249,9.55922318 5.31658249,7.02656326 6.87867966,5.46446609 L11.1213203,1.22182541 C12.6834175,-0.34027176 15.2160774,-0.34027176 16.7781746,1.22182541 C18.3402718,2.78392257 18.3402718,5.31658249 16.7781746,6.87867966 L14.6568542,9 Z M3.34314575,9 L4.75735931,10.4142136 L2.63603897,12.5355339 C1.85499039,13.3165825 1.85499039,14.5829124 2.63603897,15.363961 C3.41708755,16.1450096 4.68341751,16.1450096 5.46446609,15.363961 L9.70710678,11.1213203 C10.4881554,10.3402718 10.4881554,9.0739418 9.70710678,8.29289322 L11.1213203,6.87867966 C12.6834175,8.44077682 12.6834175,10.9734367 11.1213203,12.5355339 L6.87867966,16.7781746 C5.31658249,18.3402718 2.78392257,18.3402718 1.22182541,16.7781746 C-0.34027176,15.2160774 -0.34027176,12.6834175 1.22182541,11.1213203 L3.34314575,9 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "location": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/location</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/location" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon22" transform="translate(4.000000, 2.000000)" fill="#000000">            <path d="M8.06519014,19.2431663 C8.06300986,19.2412009 8.06085569,19.2393247 8.05872818,19.2375353 L8.06519014,19.2431663 Z M8.24683684,18.9686065 C8.48293324,18.7557774 8.74732343,18.5078114 9.03284437,18.2278923 C9.85063366,17.4261479 10.6688921,16.5335242 11.4301661,15.5768797 C13.4693431,13.0143765 14.7113924,10.487102 14.7954866,8.23968166 C14.7984932,8.15933104 14.8,8.07943664 14.8,8 C14.8,4.2444637 11.7555363,1.2 8,1.2 C4.2444637,1.2 1.2,4.2444637 1.2,8 C1.2,8.07943664 1.20150681,8.15933104 1.20451338,8.23968166 C1.28860758,10.487102 2.5306569,13.0143765 4.56983386,15.5768797 C5.33110795,16.5335242 6.14936634,17.4261479 6.96715563,18.2278923 C7.25267657,18.5078114 7.51706676,18.7557774 7.75316316,18.9686065 C7.86349262,19.068063 7.94697177,19.1413912 8,19.1870148 C8.05302823,19.1413912 8.13650738,19.068063 8.24683684,18.9686065 Z M7.26171875,20.1344765 C7.26171875,20.1344765 0,14.018278 0,8 C0,3.581722 3.581722,0 8,0 C12.418278,0 16,3.581722 16,8 C16,14.018278 8.73828125,20.1344765 8.73828125,20.1344765 C8.33356488,20.5060199 7.66946023,20.502035 7.26171875,20.1344765 Z M8,10.8 C9.5463973,10.8 10.8,9.5463973 10.8,8 C10.8,6.4536027 9.5463973,5.2 8,5.2 C6.4536027,5.2 5.2,6.4536027 5.2,8 C5.2,9.5463973 6.4536027,10.8 8,10.8 Z M8,12 C5.790861,12 4,10.209139 4,8 C4,5.790861 5.790861,4 8,4 C10.209139,4 12,5.790861 12,8 C12,10.209139 10.209139,12 8,12 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/location</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/location" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon22" fill="#000000">            <path d="M11.2617188,22.1344765 C11.2617188,22.1344765 4,16.018278 4,10 C4,5.581722 7.581722,2 12,2 C16.418278,2 20,5.581722 20,10 C20,16.018278 12.7382812,22.1344765 12.7382812,22.1344765 C12.3335649,22.5060199 11.6694602,22.502035 11.2617188,22.1344765 Z M12,13.5 C13.9329966,13.5 15.5,11.9329966 15.5,10 C15.5,8.06700338 13.9329966,6.5 12,6.5 C10.0670034,6.5 8.5,8.06700338 8.5,10 C8.5,11.9329966 10.0670034,13.5 12,13.5 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "lock": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/lock</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/lock" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon37" transform="translate(4.000000, 2.000000)" fill="#000000">            <path d="M4,7 L4,4 C4,1.790861 5.790861,0 8,0 C10.209139,0 12,1.790861 12,4 L12,7 L14.9991283,7 C15.5518945,7 16,7.44748943 16,7.99850233 L16,19.0014977 C16,19.5529553 15.5553691,20 14.9991283,20 L1.00087166,20 C0.448105505,20 0,19.5525106 0,19.0014977 L0,7.99850233 C0,7.44704472 0.444630861,7 1.00087166,7 L4,7 Z M5.20000005,7 L10.7999992,7 L10.7999992,4 C10.7999969,2.45359963 9.54639417,1.19999695 8,1.19999695 C6.45359963,1.19999695 5.19999695,2.45359963 5.19999695,4 L5.20000005,7 Z M1.2,8.2 L1.2,18.8 L14.8,18.8 L14.8,8.2 L1.2,8.2 Z M7.39999962,13.8751867 C6.87022839,13.6437197 6.5,13.1150956 6.5,12.5 C6.5,11.6715729 7.17157288,11 8,11 C8.82842712,11 9.5,11.6715729 9.5,12.5 C9.5,13.1150958 9.12977128,13.6437201 8.59999967,13.875187 L8.59999967,16 L7.39999962,16 L7.39999962,13.8751867 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/lock</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/lock" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon37" fill="#000000">            <path d="M7.5,9 L7.5,6.5 C7.5,4.01471863 9.51471863,2 12,2 C14.4852814,2 16.5,4.01471863 16.5,6.5 L16.5,9 L18.9991283,9 C19.5518945,9 20,9.44748943 20,9.99850233 L20,21.0014977 C20,21.5529553 19.5553691,22 18.9991283,22 L5.00087166,22 C4.4481055,22 4,21.5525106 4,21.0014977 L4,9.99850233 C4,9.44704472 4.44463086,9 5.00087166,9 L7.5,9 Z M9,9 L15,9 L15,6.5 C15,4.84314575 13.6568542,3.5 12,3.5 C10.3431458,3.5 9,4.84314575 9,6.5 L9,9 Z M11.3999996,15.8751867 L11.3999996,18 L12.5999997,18 L12.5999997,15.875187 C13.1297713,15.6437201 13.5,15.1150958 13.5,14.5 C13.5,13.6715729 12.8284271,13 12,13 C11.1715729,13 10.5,13.6715729 10.5,14.5 C10.5,15.1150956 10.8702284,15.6437197 11.3999996,15.8751867 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "max-window": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/max-window</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/max-window" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Max-icon" transform="translate(3.000000, 2.000000)" fill="#000000">            <path d="M18,14 L18,18 C18,18.5522847 17.5522847,19 17,19 L13,19 L13,17.8 L16.8,17.8 L16.8,14 L18,14 Z M5,1 L5,2.2 L1.2,2.2 L1.2,6 L0,6 L0,2 C0,1.44771525 0.44771525,1 1,1 L5,1 Z M16.7968544,3.04562642 L11.3249201,8.51756067 L10.4763919,7.6690325 L15.9483176,2.19710685 L12.9968544,2.19710685 L12.9968544,0.997106803 L16.9968544,0.997106803 C17.5491392,0.997106803 17.9968544,1.44482205 17.9968544,1.9971068 L17.9968544,5.9971068 L16.7968544,5.9971068 L16.7968544,3.04562642 Z M1.20314565,16.9543736 L6.67507989,11.4824393 L7.52360806,12.3309675 L2.05168241,17.8028931 L5.0031456,17.8028931 L5.0031456,19.0028932 L1.0031456,19.0028932 C0.450860848,19.0028932 0.00314559792,18.5551779 0.00314559792,18.0028932 L0.00314559792,14.0028932 L1.20314565,14.0028932 L1.20314565,16.9543736 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/max-window</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/max-window" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Max-icon" transform="translate(3.000000, 3.000000)" fill="#000000">            <path d="M18,12 L18,17 C18,17.5522847 17.5522847,18 17,18 L12,18 L12,16 L16,16 L16,12 L18,12 Z M6,0 L6,2 L2,2 L2,6 L0,6 L0,1 C0,0.44771525 0.44771525,0 1,0 L6,0 Z M14.5839613,2 L12,2 L12,0 L17,0 C17.5522847,0 18,0.44771525 18,1 L18,6 L16,6 L16,3.41364079 L11.6342138,7.77817459 L10.2200003,6.36396103 L14.5839613,2 Z M3.4160387,15.9981749 L6,15.9981749 L6,17.9981749 L1,17.9981749 C0.44771525,17.9981749 0,17.5504596 0,16.9981749 L0,11.9981749 L2,11.9981749 L2,14.5845341 L6.36578617,10.2200003 L7.77999973,11.6342138 L3.4160387,15.9981749 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "me": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/me</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/me" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon11" transform="translate(2.000000, 3.000000)" fill="#000000">            <path d="M18.3,16.8 L18.3,16.3154633 C18.3,16.0860594 18.064679,15.7100199 17.8598095,15.6100873 L12.1994561,12.8490401 C10.6728743,12.1043932 10.2955728,10.302913 11.3893366,9.00636847 L11.7508692,8.57780877 C12.302195,7.92426863 12.8,6.56439093 12.8,5.71018568 L12.8,4.00020747 C12.8,2.4549142 11.5455898,1.2 10,1.2 C8.45630885,1.2 7.2,2.4552027 7.2,3.99958038 L7.2,5.70929053 C7.2,6.56566389 7.69574673,7.92048381 8.24907842,8.57631176 L8.61061095,9.00481267 C9.70663642,10.3038601 9.32521432,12.1036503 7.80071945,12.8475869 L2.1403663,15.6097766 C1.93701709,15.7090088 1.7,16.0886771 1.7,16.3154633 L1.7,16.8 L18.3,16.8 Z M0.5,17 L0.5,16.3154633 C0.5,15.6303744 0.998150907,14.8319079 1.61409761,14.5313327 L7.27445076,11.769143 C8.09537712,11.36854 8.28625906,10.4812624 7.69344522,9.7786389 L7.33191269,9.35013798 C6.59631763,8.47828515 6,6.84949648 6,5.70929053 L6,3.99958038 C6,1.79067313 7.79535615,0 10,0 C12.209139,0 14,1.79298022 14,4.00020747 L14,5.71018568 C14,6.84929595 13.4009324,8.48286035 12.6680872,9.35157303 L12.3065546,9.78013273 C11.7170324,10.4789507 11.9011877,11.3683976 12.725549,11.7705104 L18.3859024,14.5315576 C19.0012015,14.8316925 19.5,15.6251701 19.5,16.3154633 L19.5,17 C19.5,17.5522847 19.0522847,18 18.5,18 L1.5,18 C0.94771525,18 0.5,17.5522847 0.5,17 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/me</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/me" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon11" fill="#000000">            <path d="M2.5,20 L2.5,19.3154633 C2.5,18.6303744 2.99815091,17.8319079 3.61409761,17.5313327 L9.27445076,14.769143 C10.0953771,14.36854 10.2862591,13.4812624 9.69344522,12.7786389 L9.33191269,12.350138 C8.59631763,11.4782852 8,9.84949648 8,8.70929053 L8,6.99958038 C8,4.79067313 9.79535615,3 12,3 C14.209139,3 16,4.79298022 16,7.00020747 L16,8.71018568 C16,9.84929595 15.4009324,11.4828603 14.6680872,12.351573 L14.3065546,12.7801327 C13.7170324,13.4789507 13.9011877,14.3683976 14.725549,14.7705104 L20.3859024,17.5315576 C21.0012015,17.8316925 21.5,18.6251701 21.5,19.3154633 L21.5,20 C21.5,20.5522847 21.0522847,21 20.5,21 L3.5,21 C2.94771525,21 2.5,20.5522847 2.5,20 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "mike": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/mike</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/mike" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" transform="translate(4.000000, 2.000000)" fill="#000000">            <path d="M4.7,4.5 L4.7,10.5 C4.7,12.3225397 6.17746033,13.8 8,13.8 C9.82253967,13.8 11.3,12.3225397 11.3,10.5 L11.3,4.5 C11.3,2.67746033 9.82253967,1.2 8,1.2 C6.17746033,1.2 4.7,2.67746033 4.7,4.5 Z M8.59999967,17.9763496 L8.59999967,21 L7.39999962,21 L7.39999962,17.9763495 C3.53839884,17.670687 0.5,14.4401715 0.5,10.5 L0.5,9 L1.7,9 L1.7,10.5 C1.7,13.9793939 4.52060608,16.8 8,16.8 C11.4793939,16.8 14.3,13.9793939 14.3,10.5 L14.3,9 L15.5,9 L15.5,10.5 C15.5,14.4401718 12.4616008,17.6706874 8.59999967,17.9763496 Z M3.5,4.5 C3.5,2.01471863 5.51471863,0 8,0 C10.4852814,0 12.5,2.01471863 12.5,4.5 L12.5,10.5 C12.5,12.9852814 10.4852814,15 8,15 C5.51471863,15 3.5,12.9852814 3.5,10.5 L3.5,4.5 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/mike</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/mike" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" fill="#000000">            <path d="M12.75,18.9602923 L12.75,22 L11.25,22 L11.25,18.9602923 C7.736675,18.5860629 5,15.6126238 5,12 L5,10 L6.5,10 L6.5,12 C6.5,15.0375661 8.96243388,17.5 12,17.5 C15.0375661,17.5 17.5,15.0375661 17.5,12 L17.5,10 L19,10 L19,12 C19,15.6126238 16.263325,18.5860629 12.75,18.9602923 Z M8,6 C8,3.790861 9.790861,2 12,2 C14.209139,2 16,3.790861 16,6 L16,12 C16,14.209139 14.209139,16 12,16 C9.790861,16 8,14.209139 8,12 L8,6 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "mike2": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/mike2</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/mike2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Icons/Tint-Color/Black" transform="translate(2.000000, 2.000000)" fill="#000000">            <path d="M10,18.8 C14.8601058,18.8 18.8,14.8601058 18.8,10 C18.8,5.1398942 14.8601058,1.2 10,1.2 C5.1398942,1.2 1.2,5.1398942 1.2,10 C1.2,14.8601058 5.1398942,18.8 10,18.8 Z M10,20 C4.4771525,20 0,15.5228475 0,10 C0,4.4771525 4.4771525,0 10,0 C15.5228475,0 20,4.4771525 20,10 C20,15.5228475 15.5228475,20 10,20 Z M10,5.7 C9.28202983,5.7 8.7,6.28202983 8.7,7 L8.7,10.5 C8.7,11.2179702 9.28202983,11.8 10,11.8 C10.7179702,11.8 11.3,11.2179702 11.3,10.5 L11.3,7 C11.3,6.28202983 10.7179702,5.7 10,5.7 Z M10.6000245,14.9603384 L10.6000245,16.5 L9.40002441,16.5 L9.40002441,14.9603399 C7.19839688,14.6669239 5.5,12.7811214 5.5,10.4975018 L5.5,9 L6.7,9 L6.7,10.4975018 C6.7,12.3219031 8.17739974,13.8 10,13.8 C11.8227257,13.8 13.3,12.3217816 13.3,10.4975018 L13.3,9 L14.5,9 L14.5,10.4975018 C14.5,12.7806453 12.8020307,14.6668791 10.6000245,14.9603384 L10.6000245,14.9603384 Z M10,4.5 C11.3807119,4.5 12.5,5.61928813 12.5,7 L12.5,10.5 C12.5,11.8807119 11.3807119,13 10,13 C8.61928813,13 7.5,11.8807119 7.5,10.5 L7.5,7 C7.5,5.61928813 8.61928813,4.5 10,4.5 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/mike2</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/mike2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon24" fill="#000000">            <path d="M12.6000245,16.4603384 C14.8020307,16.1668791 16.5,14.2806453 16.5,11.9975018 L16.5,10.5 L15.3,10.5 L15.3,11.9975018 C15.3,13.8217816 13.8227257,15.3 12,15.3 C10.1773997,15.3 8.7,13.8219031 8.7,11.9975018 L8.7,10.5 L7.5,10.5 L7.5,11.9975018 C7.5,14.2811214 9.19839688,16.1669239 11.4000244,16.4603399 L11.4000244,18 L12.6000245,18 L12.6000245,16.4603384 Z M12,22 C6.4771525,22 2,17.5228475 2,12 C2,6.4771525 6.4771525,2 12,2 C17.5228475,2 22,6.4771525 22,12 C22,17.5228475 17.5228475,22 12,22 Z M12,6.5 C10.8954305,6.5 10,7.3954305 10,8.5 L10,12 C10,13.1045695 10.8954305,14 12,14 C13.1045695,14 14,13.1045695 14,12 L14,8.5 C14,7.3954305 13.1045695,6.5 12,6.5 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "mobile-contacts": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/mobile-contacts</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/mobile-contacts" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" transform="translate(4.000000, 2.000000)" fill="#000000">            <path d="M0,1 C0,0.44771525 0.44771525,0 1,0 L15,0 C15.5522847,0 16,0.44771525 16,1 L16,19 C16,19.5522847 15.5522847,20 15,20 L1,20 C0.44771525,20 0,19.5522847 0,19 L0,1 Z M1.2,1.2 L1.2,18.8 L14.8,18.8 L14.8,1.2 L1.2,1.2 Z M10.9093627,11.3061065 L12.6521261,11.9605864 C12.852621,12.0452838 12.9837138,12.2377779 12.9991365,12.4687708 C13.0068479,12.561168 12.9605798,12.6689647 12.9528685,12.692064 C12.6752601,13.4466408 11.9581053,13.9856243 11.1098576,13.9856243 C10.870806,13.9856243 10.6471771,13.931726 10.5931977,13.9163264 C8.97381581,13.5467378 7.5317948,12.722863 6.39822747,11.5909978 C5.26466015,10.4591325 4.43954651,9.01927668 4.06940208,7.40232629 C4.06169074,7.34842795 4,7.1251348 4,6.88644212 C4,6.03946811 4.53979396,5.32339008 5.29550551,5.04619858 C5.31863954,5.03849882 5.42659833,5 5.51913444,5 C5.75047471,5.00769976 5.94325827,5.13859575 6.02808304,5.33878961 L6.68354714,7.07893621 C6.71439251,7.16363362 6.69896982,7.2098322 6.65270177,7.2945296 C6.49847492,7.56402133 6.12833049,8.24160054 5.92012424,8.64968802 C5.86614485,8.74978495 5.83529948,8.86528141 5.83529948,8.98077786 C5.83529948,9.10397408 5.87385619,9.2271703 5.93554693,9.32726723 C6.26713465,9.88934999 6.65270177,10.4206337 7.10767097,10.8749197 C7.56264017,11.3369056 8.09472279,11.7218938 8.65765078,12.0529836 C8.75789823,12.1145817 8.87356836,12.1530805 9.00466118,12.1530805 C9.12804266,12.1530805 9.23600145,12.1222815 9.33624891,12.0683831 C9.73723871,11.8604895 10.4158368,11.4909008 10.6934452,11.3369056 C10.7782699,11.290707 10.8168266,11.2753075 10.9093627,11.3061065 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/mobile-contacts</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/mobile-contacts" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" fill="#000000">            <path d="M4,3 C4,2.44771525 4.44771525,2 5,2 L19,2 C19.5522847,2 20,2.44771525 20,3 L20,21 C20,21.5522847 19.5522847,22 19,22 L5,22 C4.44771525,22 4,21.5522847 4,21 L4,3 Z M14.9093627,13.8061065 C14.8168266,13.7753075 14.7782699,13.790707 14.6934452,13.8369056 C14.4158368,13.9909008 13.7372387,14.3604895 13.3362489,14.5683831 C13.2360015,14.6222815 13.1280427,14.6530805 13.0046612,14.6530805 C12.8735684,14.6530805 12.7578982,14.6145817 12.6576508,14.5529836 C12.0947228,14.2218938 11.5626402,13.8369056 11.107671,13.3749197 C10.6527018,12.9206337 10.2671347,12.38935 9.93554693,11.8272672 C9.87385619,11.7271703 9.83529948,11.6039741 9.83529948,11.4807779 C9.83529948,11.3652814 9.86614485,11.2497849 9.92012424,11.149688 C10.1283305,10.7416005 10.4984749,10.0640213 10.6527018,9.7945296 C10.6989698,9.7098322 10.7143925,9.66363362 10.6835471,9.57893621 L10.028083,7.83878961 C9.94325827,7.63859575 9.75047471,7.50769976 9.51913444,7.5 C9.42659833,7.5 9.31863954,7.53849882 9.29550551,7.54619858 C8.53979396,7.82339008 8,8.53946811 8,9.38644212 C8,9.6251348 8.06169074,9.84842795 8.06940208,9.90232629 C8.43954651,11.5192767 9.26466015,12.9591325 10.3982275,14.0909978 C11.5317948,15.222863 12.9738158,16.0467378 14.5931977,16.4163264 C14.6471771,16.431726 14.870806,16.4856243 15.1098576,16.4856243 C15.9581053,16.4856243 16.6752601,15.9466408 16.9528685,15.192064 C16.9605798,15.1689647 17.0068479,15.061168 16.9991365,14.9687708 C16.9837138,14.7377779 16.852621,14.5452838 16.6521261,14.4605864 L14.9093627,13.8061065 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "more": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/more</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/more" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Icons/Tint-Color/Black" transform="translate(3.000000, 10.000000)" fill="#000000">            <path d="M3.75,2 C3.75,2.96635 2.96635,3.75 2,3.75 C1.03365,3.75 0.25,2.96635 0.25,2 C0.25,1.0333 1.03365,0.25 2,0.25 C2.96635,0.25 3.75,1.0333 3.75,2 Z M9,0.25 C9.96635,0.25 10.75,1.0333 10.75,2 C10.75,2.96635 9.96635,3.75 9,3.75 C8.03365,3.75 7.25,2.96635 7.25,2 C7.25,1.0333 8.03365,0.25 9,0.25 Z M16,0.25 C16.96635,0.25 17.75,1.0333 17.75,2 C17.75,2.96635 16.96635,3.75 16,3.75 C15.03365,3.75 14.25,2.96635 14.25,2 C14.25,1.0333 15.03365,0.25 16,0.25 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/more</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/more" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="common_icons_more" fill="#000000">            <path d="M7,12 C7,13.1044 6.1044,14 5,14 C3.8956,14 3,13.1044 3,12 C3,10.8952 3.8956,10 5,10 C6.1044,10 7,10.8952 7,12 Z M12,10 C13.1044,10 14,10.8952 14,12 C14,13.1044 13.1044,14 12,14 C10.8956,14 10,13.1044 10,12 C10,10.8952 10.8956,10 12,10 Z M19,10 C20.1044,10 21,10.8952 21,12 C21,13.1044 20.1044,14 19,14 C17.8956,14 17,13.1044 17,12 C17,10.8952 17.8956,10 19,10 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "more2": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/more2</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/more2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Icons/Tint-Color/Black" transform="translate(2.000000, 2.000000)" fill="#000000">            <path d="M10,20 C4.4771525,20 0,15.5228475 0,10 C0,4.4771525 4.4771525,0 10,0 C15.5228475,0 20,4.4771525 20,10 C20,15.5228475 15.5228475,20 10,20 Z M10,18.8 C14.8601058,18.8 18.8,14.8601058 18.8,10 C18.8,5.1398942 14.8601058,1.2 10,1.2 C5.1398942,1.2 1.2,5.1398942 1.2,10 C1.2,14.8601058 5.1398942,18.8 10,18.8 Z M10,11 C9.44771525,11 9,10.5522847 9,10 C9,9.44771525 9.44771525,9 10,9 C10.5522847,9 11,9.44771525 11,10 C11,10.5522847 10.5522847,11 10,11 Z M14,11 C13.4477153,11 13,10.5522847 13,10 C13,9.44771525 13.4477153,9 14,9 C14.5522847,9 15,9.44771525 15,10 C15,10.5522847 14.5522847,11 14,11 Z M6,11 C5.44771525,11 5,10.5522847 5,10 C5,9.44771525 5.44771525,9 6,9 C6.55228475,9 7,9.44771525 7,10 C7,10.5522847 6.55228475,11 6,11 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/more2</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/more2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="common_icons_miniprogram" fill="#000000">            <path d="M12,22 C6.4771525,22 2,17.5228475 2,12 C2,6.4771525 6.4771525,2 12,2 C17.5228475,2 22,6.4771525 22,12 C22,17.5228475 17.5228475,22 12,22 Z M12,13.5 C12.8284271,13.5 13.5,12.8284271 13.5,12 C13.5,11.1715729 12.8284271,10.5 12,10.5 C11.1715729,10.5 10.5,11.1715729 10.5,12 C10.5,12.8284271 11.1715729,13.5 12,13.5 Z M16.5,13.5 C17.3284271,13.5 18,12.8284271 18,12 C18,11.1715729 17.3284271,10.5 16.5,10.5 C15.6715729,10.5 15,11.1715729 15,12 C15,12.8284271 15.6715729,13.5 16.5,13.5 Z M7.5,13.5 C8.32842712,13.5 9,12.8284271 9,12 C9,11.1715729 8.32842712,10.5 7.5,10.5 C6.67157288,10.5 6,11.1715729 6,12 C6,12.8284271 6.67157288,13.5 7.5,13.5 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "mosaic": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/mosaic</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/mosaic" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="图标颜色" transform="translate(3.000000, 3.000000)" fill="#000000">            <path d="M8,12 L10,12 L10,14 L8,14 L8,12 Z M4,12 L6,12 L6,14 L4,14 L4,12 Z M12,12 L14,12 L14,14 L12,14 L12,12 Z M12,4 L14,4 L14,6 L12,6 L12,4 Z M4,8 L6,8 L6,10 L4,10 L4,8 Z M4,4 L6,4 L6,6 L4,6 L4,4 Z M6,10 L8,10 L8,12 L6,12 L6,10 Z M6,6 L8,6 L8,8 L6,8 L6,6 Z M10,10 L12,10 L12,12 L10,12 L10,10 Z M10,6 L12,6 L12,8 L10,8 L10,6 Z M8,4 L10,4 L10,6 L8,6 L8,4 Z M8,8 L10,8 L10,10 L8,10 L8,8 Z M12,8 L14,8 L14,10 L12,10 L12,8 Z M1.2,1.2 L1.2,16.8 L16.8,16.8 L16.8,1.2 L1.2,1.2 Z M1,0 L17,0 C17.5522847,0 18,0.44771525 18,1 L18,17 C18,17.5522847 17.5522847,18 17,18 L1,18 C0.44771525,18 0,17.5522847 0,17 L0,1 C0,0.44771525 0.44771525,0 1,0 Z"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/mask</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/mask" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" transform="translate(3.000000, 3.000000)" fill="#000000">            <path d="M1,0 L17,0 C17.5522847,-1.01453063e-16 18,0.44771525 18,1 L18,17 C18,17.5522847 17.5522847,18 17,18 L1,18 C0.44771525,18 6.76353751e-17,17.5522847 0,17 L0,1 C-6.76353751e-17,0.44771525 0.44771525,1.01453063e-16 1,0 Z M7.80000019,12.6000004 L7.80000019,15.0000005 L10.2000003,15.0000005 L10.2000003,12.6000004 L7.80000019,12.6000004 Z M3,12.6000004 L3,15.0000005 L5.4000001,15.0000005 L5.4000001,12.6000004 L3,12.6000004 Z M12.6000004,12.6000004 L12.6000004,15.0000005 L15.0000005,15.0000005 L15.0000005,12.6000004 L12.6000004,12.6000004 Z M12.6000004,3 L12.6000004,5.4000001 L15.0000005,5.4000001 L15.0000005,3 L12.6000004,3 Z M3,7.80000019 L3,10.2000003 L5.4000001,10.2000003 L5.4000001,7.80000019 L3,7.80000019 Z M3,3 L3,5.4000001 L5.4000001,5.4000001 L5.4000001,3 L3,3 Z M5.4000001,10.2000003 L5.4000001,12.6000004 L7.80000019,12.6000004 L7.80000019,10.2000003 L5.4000001,10.2000003 Z M5.4000001,5.4000001 L5.4000001,7.80000019 L7.80000019,7.80000019 L7.80000019,5.4000001 L5.4000001,5.4000001 Z M10.2000003,10.2000003 L10.2000003,12.6000004 L12.6000004,12.6000004 L12.6000004,10.2000003 L10.2000003,10.2000003 Z M10.2000003,5.4000001 L10.2000003,7.80000019 L12.6000004,7.80000019 L12.6000004,5.4000001 L10.2000003,5.4000001 Z M7.80000019,3 L7.80000019,5.4000001 L10.2000003,5.4000001 L10.2000003,3 L7.80000019,3 Z M7.80000019,7.80000019 L7.80000019,10.2000003 L10.2000003,10.2000003 L10.2000003,7.80000019 L7.80000019,7.80000019 Z M12.6000004,7.80000019 L12.6000004,10.2000003 L15.0000005,10.2000003 L15.0000005,7.80000019 L12.6000004,7.80000019 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "music-off": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/music_off</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/music_off" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="common_icons_music" fill="#000000">            <path d="M7.53207892,4.83502207 L18.8772863,2.34318067 C19.2025252,2.27476488 19.5226771,2.47930633 19.5972863,2.80318067 C19.605581,2.84598886 19.6089363,2.88960748 19.6072863,2.93318067 L19.6072863,16.5831807 C19.6038814,16.6843639 19.594782,16.7844909 19.580239,16.8831821 L22.4249789,19.7279221 L21.5764507,20.5764502 L4.84852868,3.84852817 L5.69705685,3 L7.53207892,4.83502207 Z M17.3893408,14.692284 L17.6672863,14.6331807 C18.0911755,14.5476455 18.396326,14.1756127 18.3972863,13.7431807 L18.3972863,3.83318067 L8.66738839,5.97033154 L17.3893408,14.692284 L17.3893408,14.692284 Z M6.40728625,7.1043431 L7.60728625,8.3043431 L7.60728625,19.2031807 C7.58608355,20.213505 6.9983734,21.1260023 6.08728625,21.5631807 C5.90728625,21.6431807 4.85728625,21.8831807 4.78728625,21.8831807 C4.66102239,21.8974666 4.53355012,21.8974666 4.40728625,21.8831807 C3.32225343,21.8373813 2.45308559,20.9682135 2.40728625,19.8831807 C2.3216979,18.8458811 2.99829854,17.8986402 4.00728625,17.6431807 L5.67728625,17.2931807 C6.10332728,17.2112417 6.41027368,16.8370194 6.40728625,16.4031807 L6.40728625,7.1043431 L6.40728625,7.1043431 Z M14.9201196,15.6171765 L15.7639875,16.4610444 C15.6205529,16.6523256 15.5467342,16.8934569 15.5672863,17.1431807 C15.5788722,17.5942966 15.9193125,17.9687809 16.3672863,18.0231807 L16.5172863,18.0231807 C16.6128978,18.0052535 16.9120521,17.9409043 17.1800217,17.8770786 L18.1670541,18.8641109 C18.134238,18.8811052 18.1009781,18.8974682 18.0672863,18.9131807 C17.636529,19.0454344 17.1992916,19.1555782 16.7572863,19.2431807 C16.6277067,19.2579667 16.4968658,19.2579667 16.3672863,19.2431807 C15.2843027,19.1927286 14.4177383,18.3261642 14.3672863,17.2431807 C14.3136552,16.6295529 14.5272058,16.0455188 14.9201196,15.6171765 L14.9201196,15.6171765 Z M6.40728625,18.3031807 C6.25171355,18.382502 6.08721189,18.4429312 5.91728625,18.4831807 L4.19728625,18.8231807 C3.80742967,18.9965193 3.57048788,19.3981156 3.60728625,19.8231807 C3.60649702,20.0665755 3.70770255,20.2991715 3.88633215,20.4644989 C4.06496176,20.6298263 4.30467933,20.7127655 4.54728625,20.6931807 C4.69728625,20.6531807 5.42728625,20.4931807 5.61728625,20.4331807 C6.077472,20.1894078 6.37699373,19.7230638 6.40728625,19.2031807 L6.40728625,18.3031807 Z" id="Shape"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/music-off</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/music-off" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="common_icons_music" fill="#000000">            <path d="M9.33073588,6.20941554 L17.6563348,14.5350145 C17.8692631,14.3487944 18,14.0758571 18,13.7782081 L18,4.30884616 L9.33073588,6.20941554 Z M19.5,16.3786797 L22.0918831,18.9705627 L21.0312229,20.0312229 L4.06066017,3.06066017 L5.12132034,2 L7.95778239,4.83646204 L18.8930356,2.44165489 C19.1627851,2.38258008 19.4293498,2.55336564 19.4884246,2.82311512 C19.4961191,2.8582498 19.5,2.89411216 19.5,2.93007952 L19.5,16.3786797 Z M8,9.12132034 L8,19.2012686 C8,20.1376561 7.37291357,21.0506958 6.5294712,21.4440654 C6.37267856,21.5171912 5.382193,21.7385747 5.25286076,21.7645934 C4.01016532,22.0145956 3.05534487,20.9501591 3.00276162,19.8737811 C2.95017837,18.7974031 3.65606327,17.8998615 4.47506962,17.7350959 L5.73958069,17.4178208 C6.18656759,17.3056685 6.5,16.9038781 6.5,16.443036 L6.5,7.62132034 L8,9.12132034 Z M17.7441316,18.8654519 C17.3884565,18.959172 16.824758,19.0835157 16.7290072,19.1026128 C15.4755213,19.352615 14.5124101,18.2881785 14.4593703,17.2118005 C14.4354581,16.7265322 14.5670444,16.2776127 14.7944808,15.9158011 L17.7441316,18.8654519 Z" id="Mask"></path>        </g>    </g></svg>\` },
    "music": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/music</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/music" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="common_icons_music" transform="translate(2.000000, 2.000000)" fill="#000000">            <path d="M17.5972863,0.803180666 C17.5226771,0.479306335 17.2025252,0.27476488 16.8772863,0.343180666 L5.26728625,2.89318067 C4.75103261,3.00829488 4.38979938,3.47453776 4.40728625,4.00318067 L4.40728625,14.4031807 C4.41027368,14.8370194 4.10332728,15.2112417 3.67728625,15.2931807 L2.00728625,15.6431807 C0.998298543,15.8986402 0.321697899,16.8458811 0.407286253,17.8831807 C0.453085593,18.9682135 1.32225343,19.8373813 2.40728625,19.8831807 C2.53355012,19.8974666 2.66102239,19.8974666 2.78728625,19.8831807 C2.85728625,19.8831807 3.90728625,19.6431807 4.08728625,19.5631807 C4.9983734,19.1260023 5.58608355,18.213505 5.60728625,17.2031807 L5.60728625,4.20318067 L16.3972863,1.83318067 L16.3972863,11.7431807 C16.396326,12.1756127 16.0911755,12.5476455 15.6672863,12.6331807 L13.9272863,13.0031807 C12.9343035,13.274758 12.277654,14.2176393 12.3672863,15.2431807 C12.4177383,16.3261642 13.2843027,17.1927286 14.3672863,17.2431807 C14.4968658,17.2579667 14.6277067,17.2579667 14.7572863,17.2431807 C15.1992916,17.1555782 15.636529,17.0454344 16.0672863,16.9131807 C16.978293,16.488326 17.5734799,15.5878159 17.6072863,14.5831807 L17.6072863,0.933180666 C17.6089363,0.889607483 17.605581,0.84598886 17.5972863,0.803180666 Z M16.4072863,13.6431807 L16.4072863,14.5431807 C16.3734998,15.0621621 16.0697874,15.5253236 15.6072863,15.7631807 C15.4072863,15.8331807 14.6772863,15.9931807 14.5172863,16.0231807 L14.3672863,16.0231807 C13.9193125,15.9687809 13.5788722,15.5942966 13.5672863,15.1431807 C13.5321017,14.7156597 13.7735044,14.3133218 14.1672863,14.1431807 L15.9072863,13.7931807 C16.0792293,13.7628381 16.2470352,13.7124964 16.4072863,13.6431807 Z M4.40728625,16.3031807 L4.40728625,17.2031807 C4.37699373,17.7230638 4.077472,18.1894078 3.61728625,18.4331807 C3.42728625,18.4931807 2.69728625,18.6531807 2.54728625,18.6931807 C2.30467933,18.7127655 2.06496176,18.6298263 1.88633215,18.4644989 C1.70770255,18.2991715 1.60649702,18.0665755 1.60728625,17.8231807 C1.57048788,17.3981156 1.80742967,16.9965193 2.19728625,16.8231807 L3.91728625,16.4831807 C4.08721189,16.4429312 4.25171355,16.382502 4.40728625,16.3031807 L4.40728625,16.3031807 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/music</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/music" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="common_icons_music" fill="#000000">            <path d="M18,4.30884616 L8,6.50115378 L8,18.1957627 L8,19.2012686 C8,20.1376561 7.37291357,21.0506958 6.5294712,21.4440654 C6.37267856,21.5171912 5.382193,21.7385747 5.25286076,21.7645934 C4.01016532,22.0145956 3.05534487,20.9501591 3.00276162,19.8737811 C2.95017837,18.7974031 3.65606327,17.8998615 4.47506962,17.7350959 L5.73958069,17.4178208 C6.18656759,17.3056685 6.5,16.9038781 6.5,16.443036 L6.5,6.82999992 L6.5,5.96041523 C6.5,5.49056505 6.8270984,5.08408046 7.2860712,4.98356598 L18.8930356,2.44165489 C19.1627851,2.38258008 19.4293498,2.55336564 19.4884246,2.82311512 C19.4961191,2.8582498 19.5,2.89411216 19.5,2.93007952 L19.5,3.73369813 L19.5,15.5337821 L19.5,16.5392879 C19.5,17.4756754 18.8674685,18.3887152 18.0167025,18.7820848 C17.8585484,18.8552106 16.8594624,19.0765941 16.7290072,19.1026128 C15.4755213,19.352615 14.5124101,18.2881785 14.4593703,17.2118005 C14.4063304,16.1354225 15.1183446,15.2378809 15.9444624,15.0731153 L17.23615,14.7538472 C17.6848085,14.6429517 18,14.2403684 18,13.7782081 L18,4.30884616 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "note": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/note</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/note" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" transform="translate(4.000000, 2.000000)" fill="#000000">            <path d="M4.20000005,1.2 L4.20000005,15.8092926 L14,15.8092926 C14.4418278,15.8092926 14.8,15.4511204 14.8,15.0092926 L14.8,13.2000122 L14.8,2 C14.8,1.5581722 14.4418278,1.2 14,1.2 L4.20000005,1.2 Z M3,1.2 L1.2,1.2 L1.2,15.8092926 L3,15.8092926 L3,1.2 Z M14.8,16.8428752 C14.5550151,16.9499136 14.2844419,17.0092926 14,17.0092926 L1.2,17.0092926 L1.2,18.8 L14,18.8 C14.4418278,18.8 14.8,18.4418278 14.8,18 L14.8,16.8428752 Z M0,0 L14,0 C15.1045695,0 16,0.8954305 16,2 L16,18 C16,19.1045695 15.1045695,20 14,20 L0,20 L0,0 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/note</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/note" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" transform="translate(4.000000, 2.000000)" fill="#000000">            <path d="M14.5,16.9462839 C14.34019,16.9874161 14.1726498,17.0092926 14,17.0092926 L1.5,17.0092926 L1.5,18.5 L14,18.5 C14.2761424,18.5 14.5,18.2761424 14.5,18 L14.5,16.9462839 L14.5,16.9462839 Z M0,0 L14,0 C15.1045695,0 16,0.8954305 16,2 L16,18 C16,19.1045695 15.1045695,20 14,20 L0,20 L0,0 Z M3,1.5 L3,15.5 L4.5,15.5 L4.5,1.5 L3,1.5 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "pad": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/pad</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/pad" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" fill="#000000">            <path d="M5.2,3.2 L5.2,20.8 L18.8,20.8 L18.8,3.2 L5.2,3.2 Z M4,3 C4,2.44771525 4.44771525,2 5,2 L19,2 C19.5522847,2 20,2.44771525 20,3 L20,21 C20,21.5522847 19.5522847,22 19,22 L5,22 C4.44771525,22 4,21.5522847 4,21 L4,3 Z M12,20 C11.4477153,20 11,19.5522847 11,19 C11,18.4477153 11.4477153,18 12,18 C12.5522847,18 13,18.4477153 13,19 C13,19.5522847 12.5522847,20 12,20 Z" id="Combined-Shape"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/pad</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/pad" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" fill="#000000">            <path d="M4,3 C4,2.44771525 4.44771525,2 5,2 L19,2 C19.5522847,2 20,2.44771525 20,3 L20,21 C20,21.5522847 19.5522847,22 19,22 L5,22 C4.44771525,22 4,21.5522847 4,21 L4,3 Z M12,20 C12.5522847,20 13,19.5522847 13,19 C13,18.4477153 12.5522847,18 12,18 C11.4477153,18 11,18.4477153 11,19 C11,19.5522847 11.4477153,20 12,20 Z" id="形状结合"></path>        </g>    </g></svg>\` },
    "pause": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/pause</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/pause" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" fill="#000000">            <path d="M7,5 L9,5 C9.55228475,5 10,5.41786757 10,5.93333333 L10,18.0666667 C10,18.5821324 9.55228475,19 9,19 L7,19 C6.44771525,19 6,18.5821324 6,18.0666667 L6,5.93333333 C6,5.41786757 6.44771525,5 7,5 Z M7.2,17.8 L8.8,17.8 L8.8,6.2 L7.2,6.2 L7.2,17.8 Z M15,5 L17,5 C17.5522847,5 18,5.41786757 18,5.93333333 L18,18.0666667 C18,18.5821324 17.5522847,19 17,19 L15,19 C14.4477153,19 14,18.5821324 14,18.0666667 L14,5.93333333 C14,5.41786757 14.4477153,5 15,5 Z M15.2,17.8 L16.8,17.8 L16.8,6.2 L15.2,6.2 L15.2,17.8 Z" id="形状结合"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/pause</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/pause" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" fill="#000000">            <path d="M7,5 L9,5 C9.55228475,5 10,5.41786757 10,5.93333333 L10,18.0666667 C10,18.5821324 9.55228475,19 9,19 L7,19 C6.44771525,19 6,18.5821324 6,18.0666667 L6,5.93333333 C6,5.41786757 6.44771525,5 7,5 Z M15,5 L17,5 C17.5522847,5 18,5.41786757 18,5.93333333 L18,18.0666667 C18,18.5821324 17.5522847,19 17,19 L15,19 C14.4477153,19 14,18.5821324 14,18.0666667 L14,5.93333333 C14,5.41786757 14.4477153,5 15,5 Z" id="形状结合"></path>        </g>    </g></svg>\` },
    "pencil": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/pencil</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/pencil" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" transform="translate(3.000000, 2.000000)" fill="#000000">            <path d="M11,1 L11,2.2 L1.2,2.2 L1.2,17.8 L16.8,17.8 L16.8,8 L18,8 L18,18 C18,18.5522847 17.5522847,19 17,19 L1,19 C0.44771525,19 0,18.5522847 0,18 L0,2 C0,1.44771525 0.44771525,1 1,1 L11,1 Z M16.8608816,0.74608004 C17.0556551,0.551306526 17.3714454,0.551306526 17.5662189,0.74608004 L18.2715562,1.45141733 C18.4663297,1.64619084 18.4663297,1.9619811 18.2715562,2.15675462 L8.76190575,11.6664051 L6.445312,12.9618781 C6.30106541,13.0425463 6.11873594,12.9910058 6.03806772,12.8467592 C5.98683746,12.755152 5.98734579,12.6434011 6.03940732,12.5522637 L7.35135595,10.2556057 L16.8608816,0.74608004 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/pencil</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/pencil" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" fill="#000000">            <path d="M14,3 L14,5 L5,5 L5,19 L19,19 L19,10 L21,10 L21,20 C21,20.5522847 20.5522847,21 20,21 L4,21 C3.44771525,21 3,20.5522847 3,20 L3,4 C3,3.44771525 3.44771525,3 4,3 L14,3 Z M19.9403667,3.35355339 L20.6474735,4.06066017 C20.8427356,4.25592232 20.8427356,4.57250481 20.6474735,4.76776695 L11.3142761,14.1009644 L9.32169414,15.0809343 C9.17301667,15.1540552 8.99321354,15.0928045 8.92009261,14.9441271 C8.87903177,14.8606377 8.87903177,14.7628221 8.92009261,14.6793327 L9.90006249,12.6867508 L19.2332599,3.35355339 C19.4285221,3.15829124 19.7451046,3.15829124 19.9403667,3.35355339 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "photo-wall": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/photo-wall</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/photo-wall" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon34" transform="translate(2.000000, 4.000000)" fill="#000000">            <path d="M4,4 L4,1 C4,0.44771525 4.44771525,0 5,0 L19,0 C19.5522847,0 20,0.44771525 20,1 L20,11 C20,11.5522847 19.5522847,12 19,12 L16,12 L16,10.8 L18.8,10.8 L18.8,1.2 L5.2,1.2 L5.2,4 L4,4 Z M1,4 L15,4 C15.5522847,4 16,4.44771525 16,5 L16,15 C16,15.5522847 15.5522847,16 15,16 L1,16 C0.44771525,16 0,15.5522847 0,15 L0,5 C0,4.44771525 0.44771525,4 1,4 Z M1.2,5.2 L1.2,14.8 L14.8,14.8 L14.8,5.2 L1.2,5.2 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/photo-wall</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/photo-wall" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon34" fill="#000000">            <path d="M6,6.5 L6,5 C6,4.44771525 6.44771525,4 7,4 L21,4 C21.5522847,4 22,4.44771525 22,5 L22,15 C22,15.5522847 21.5522847,16 21,16 L19.5,16 L19.5,8 C19.5,7.17157288 18.8284271,6.5 18,6.5 L6,6.5 Z M3,8 L17,8 C17.5522847,8 18,8.44771525 18,9 L18,19 C18,19.5522847 17.5522847,20 17,20 L3,20 C2.44771525,20 2,19.5522847 2,19 L2,9 C2,8.44771525 2.44771525,8 3,8 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "play": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/play</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/play" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" fill="#000000">            <path d="M9.52409743,4.93790611 L19.6160552,11.1483417 C20.0864131,11.4377927 20.2330671,12.0537395 19.9436161,12.5240974 C19.8615728,12.6574179 19.7493757,12.769615 19.6160552,12.8516583 L9.52409743,19.0620939 C9.05373953,19.3515449 8.4377927,19.2048909 8.14834168,18.734533 C8.05135233,18.5769253 8,18.3954954 8,18.2104356 L8,5.78956442 C8,5.23727967 8.44771525,4.78956442 9,4.78956442 C9.18505978,4.78956442 9.36648973,4.84091676 9.52409743,4.93790611 Z M9.2,6.14747731 L9.2,17.8525227 L18.7103494,12 L9.2,6.14747731 Z" id="矩形"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/play</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/play" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">        <g id="Group"></g>        <path d="M9.52409743,4.93790611 L19.6160552,11.1483417 C20.0864131,11.4377927 20.2330671,12.0537395 19.9436161,12.5240974 C19.8615728,12.6574179 19.7493757,12.769615 19.6160552,12.8516583 L9.52409743,19.0620939 C9.05373953,19.3515449 8.4377927,19.2048909 8.14834168,18.734533 C8.05135233,18.5769253 8,18.3954954 8,18.2104356 L8,5.78956442 C8,5.23727967 8.44771525,4.78956442 9,4.78956442 C9.18505978,4.78956442 9.36648973,4.84091676 9.52409743,4.93790611 Z" id="矩形" fill-opacity="0.9" fill="#000000"></path>    </g></svg>\` },
    "play2": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/play2</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/play2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Icons/Tint-Color/Black" transform="translate(2.000000, 2.000000)" fill="#000000">            <path d="M10,18.8 C14.8601058,18.8 18.8,14.8601058 18.8,10 C18.8,5.1398942 14.8601058,1.2 10,1.2 C5.1398942,1.2 1.2,5.1398942 1.2,10 C1.2,14.8601058 5.1398942,18.8 10,18.8 Z M10,20 C4.4771525,20 0,15.5228475 0,10 C0,4.4771525 4.4771525,0 10,0 C15.5228475,0 20,4.4771525 20,10 C20,15.5228475 15.5228475,20 10,20 Z M8.7,12.8349028 L13.2358445,10 L8.7,7.16509717 L8.7,12.8349028 Z M8.26499947,5.47812467 L14.8216014,9.57600085 C15.0557696,9.72235601 15.1269562,10.0308312 14.980601,10.2649995 C14.9403607,10.329384 14.8859859,10.3837588 14.8216014,10.4239992 L8.26499947,14.5218753 C8.0308312,14.6682305 7.72235601,14.5970439 7.57600085,14.3628756 C7.52633472,14.2834098 7.5,14.191586 7.5,14.0978762 L7.5,5.90212382 C7.5,5.62598145 7.72385763,5.40212382 8,5.40212382 C8.09370986,5.40212382 8.18553367,5.42845854 8.26499947,5.47812467 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/play2</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/play2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon21" fill="#000000">            <path d="M12,22 C6.4771525,22 2,17.5228475 2,12 C2,6.4771525 6.4771525,2 12,2 C17.5228475,2 22,6.4771525 22,12 C22,17.5228475 17.5228475,22 12,22 Z M10.7649995,8.38024849 C10.6855337,8.33058236 10.5937099,8.30424764 10.5,8.30424764 C10.2238576,8.30424764 10,8.52810527 10,8.80424764 L10,15.1957524 C10,15.2894622 10.0263347,15.381286 10.0760008,15.4607518 C10.222356,15.6949201 10.5308312,15.7661067 10.7649995,15.6197515 L15.8782032,12.4239992 C15.9425878,12.3837588 15.9969626,12.329384 16.0372029,12.2649995 C16.1835581,12.0308312 16.1123715,11.722356 15.8782032,11.5760008 L10.7649995,8.38024849 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "previous": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/previous</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/previous" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Icons/Tint-Color/Black" transform="translate(4.000000, 3.000000)" fill="#000000">            <path d="M6.44089928,5.5 L8.87132034,7.93042107 L7.81066017,8.99108124 L4.62867966,5.80910072 L3.92157288,5.10199394 C3.72631073,4.9067318 3.72631073,4.59014931 3.92157288,4.39488716 L7.81066017,0.505799865 L8.87132034,1.56646004 L6.43778038,4 L11,4 C14.3137085,4 17,6.6862915 17,10 C17,13.3137085 14.3137085,16 11,16 L0,16 L0,14.5 L11,14.5 C13.4852814,14.5 15.5,12.4852814 15.5,10 C15.5,7.51471863 13.4852814,5.5 11,5.5 L6.44089928,5.5 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/previous</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/previous" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon4" transform="translate(4.000000, 3.000000)" fill="#000000">            <path d="M6.58578644,6 L8.41421356,7.82842712 L7,9.24264069 L3.46446609,5.70710678 C3.0739418,5.31658249 3.0739418,4.68341751 3.46446609,4.29289322 L7,0.757359313 L8.41421356,2.17157288 L6.58578644,4 L11,4 C14.3137085,4 17,6.6862915 17,10 C17,13.3137085 14.3137085,16 11,16 L0,16 L0,14 L11,14 C13.209139,14 15,12.209139 15,10 C15,7.790861 13.209139,6 11,6 L6.58578644,6 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "previous2": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/previous2</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/previous2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Icons/Tint-Color/Black" transform="translate(2.000000, 2.000000)" fill="#000000">            <path d="M7.26862907,9.20000005 L8.6769553,10.6083263 L7.82842712,11.4568544 L5.84852817,9.47695549 L5.35355339,8.98198071 C5.15829124,8.78671856 5.15829124,8.47013607 5.35355339,8.27487392 L7.82842712,5.80000019 L8.6769553,6.64852836 L7.32548366,8 L12,8 C13.6568542,8 15,9.34314575 15,11 C15,12.6568542 13.6568542,14 12,14 L10,14.0000002 L10,12.8000002 L12,12.8000002 C12.9941125,12.8 13.8,11.9941125 13.8,11 C13.8,10.0058875 12.9941125,9.2 12,9.2 L7.26862907,9.20000005 Z M10,20 C4.4771525,20 0,15.5228475 0,10 C0,4.4771525 4.4771525,0 10,0 C15.5228475,0 20,4.4771525 20,10 C20,15.5228475 15.5228475,20 10,20 Z M10,18.8 C14.8601058,18.8 18.8,14.8601058 18.8,10 C18.8,5.1398942 14.8601058,1.2 10,1.2 C5.1398942,1.2 1.2,5.1398942 1.2,10 C1.2,14.8601058 5.1398942,18.8 10,18.8 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/previous2</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/previous2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="common_icons_miniprogram" fill="#000000">            <path d="M12,22 C6.4771525,22 2,17.5228475 2,12 C2,6.4771525 6.4771525,2 12,2 C17.5228475,2 22,6.4771525 22,12 C22,17.5228475 17.5228475,22 12,22 Z M9.26862907,11.2 L14,11.2 C14.9941125,11.2 15.8,12.0058875 15.8,13 C15.8,13.9941125 14.9941125,14.8000002 14,14.8000002 L12,14.8000002 L12,16.0000002 L14,16.0000002 C15.6568542,16.0000002 17,14.6568542 17,13 C17,11.3431458 15.6568542,10 14,10 L9.32548366,10 L10.6769553,8.64852836 L9.82842712,7.80000019 L7.35355339,10.2748739 C7.15829124,10.4701361 7.15829124,10.7867186 7.35355339,10.9819807 L9.82842712,13.4568544 L10.6769553,12.6083263 L9.26862907,11.2 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "qr-code": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/qr-code</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/qr-code" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="图标颜色" transform="translate(3.000000, 3.000000)" fill="#000000">            <path d="M8,16 L10,16 L10,18 L8,18 L8,16 Z M16,8 L18,8 L18,10 L16,10 L16,8 Z M8,12 L10,12 L10,14 L8,14 L8,12 Z M12,8 L14,8 L14,10 L12,10 L12,8 Z M1,0 L9,0 C9.55228475,0 10,0.44771525 10,1 L10,9 C10,9.55228475 9.55228475,10 9,10 L1,10 C0.44771525,10 0,9.55228475 0,9 L0,1 C0,0.44771525 0.44771525,0 1,0 Z M1.2,1.2 L1.2,8.8 L8.8,8.8 L8.8,1.2 L1.2,1.2 Z M4,4 L6,4 L6,6 L4,6 L4,4 Z M13,0 L17,0 C17.5522847,0 18,0.44771525 18,1 L18,5 C18,5.55228475 17.5522847,6 17,6 L13,6 C12.4477153,6 12,5.55228475 12,5 L12,1 C12,0.44771525 12.4477153,0 13,0 Z M13.2,4.8 L16.8,4.8 L16.8,1.2 L13.2,1.2 L13.2,4.8 Z M13,12 L17,12 C17.5522847,12 18,12.4477153 18,13 L18,17 C18,17.5522847 17.5522847,18 17,18 L13,18 C12.4477153,18 12,17.5522847 12,17 L12,13 C12,12.4477153 12.4477153,12 13,12 Z M13.2,16.8 L16.8,16.8 L16.8,13.2 L13.2,13.2 L13.2,16.8 Z M1,12 L5,12 C5.55228475,12 6,12.4477153 6,13 L6,17 C6,17.5522847 5.55228475,18 5,18 L1,18 C0.44771525,18 0,17.5522847 0,17 L0,13 C0,12.4477153 0.44771525,12 1,12 Z M1.2,16.8 L4.8,16.8 L4.8,13.2 L1.2,13.2 L1.2,16.8 Z"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/qr-code</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/qr-code" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" transform="translate(3.000000, 3.000000)" fill="#000000">            <path d="M14,2 L14,4 L16,4 L16,2 L14,2 Z M8,16 L10,16 L10,18 L8,18 L8,16 Z M16,8 L18,8 L18,10 L16,10 L16,8 Z M8,12 L10,12 L10,14 L8,14 L8,12 Z M12,8 L14,8 L14,10 L12,10 L12,8 Z M1,0 L9,0 C9.55228475,0 10,0.44771525 10,1 L10,9 C10,9.55228475 9.55228475,10 9,10 L1,10 C0.44771525,10 0,9.55228475 0,9 L0,1 C0,0.44771525 0.44771525,0 1,0 Z M2,2 L2,8 L8,8 L8,2 L2,2 Z M4,4 L6,4 L6,6 L4,6 L4,4 Z M13,0 L17,0 C17.5522847,0 18,0.44771525 18,1 L18,5 C18,5.55228475 17.5522847,6 17,6 L13,6 C12.4477153,6 12,5.55228475 12,5 L12,1 C12,0.44771525 12.4477153,0 13,0 Z M13,12 L17,12 C17.5522847,12 18,12.4477153 18,13 L18,17 C18,17.5522847 17.5522847,18 17,18 L13,18 C12.4477153,18 12,17.5522847 12,17 L12,13 C12,12.4477153 12.4477153,12 13,12 Z M14,14 L14,16 L16,16 L16,14 L14,14 Z M1,12 L5,12 C5.55228475,12 6,12.4477153 6,13 L6,17 C6,17.5522847 5.55228475,18 5,18 L1,18 C0.44771525,18 0,17.5522847 0,17 L0,13 C0,12.4477153 0.44771525,12 1,12 Z M2,14 L2,16 L4,16 L4,14 L2,14 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "refresh": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/refresh</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/refresh" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Icons/Tint-Color/Black" transform="translate(3.000000, 2.000000)" fill="#000000">            <path d="M8.57694763,3.2112774 C4.46584819,3.43100725 1.2,6.83411377 1.2,11 C1.2,15.307821 4.69217895,18.8 9,18.8 C13.307821,18.8 16.8,15.307821 16.8,11 C16.8,8.90208215 15.9695547,6.93868889 14.5154329,5.48456711 L15.363961,4.63603897 C16.9926407,6.26471863 18,8.51471863 18,11 C18,15.9705627 13.9705627,20 9,20 C4.02943725,20 0,15.9705627 0,11 C0,6.30954147 3.58809453,2.45712733 8.16920597,2.03783519 L6.97989895,0.848528171 L7.82842712,3.1485925e-13 L10.3033009,2.47487373 C10.498563,2.67013588 10.498563,2.98671837 10.3033009,3.18198052 L9.80832608,3.6769553 L7.82842712,5.65685425 L6.97989895,4.80832608 L8.57694763,3.2112774 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/refresh</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/refresh" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="common_icons_miniprogram" fill="#000000">            <path d="M10.5460197,5.13180618 L8.82842712,3.41421356 L10.2426407,2 L14.131728,5.8890873 C14.3269901,6.08434944 14.3269901,6.40093193 14.131728,6.59619408 L10.2426407,10.4852814 L8.82842712,9.07106781 L10.7741707,7.12532426 C8.04836137,7.69118678 6,10.1063832 6,13 C6,16.3137085 8.6862915,19 12,19 C15.3137085,19 18,16.3137085 18,13 C18,11.3852639 17.3618946,9.87661321 16.2426407,8.75735931 L17.6568542,7.34314575 C19.1045695,8.790861 20,10.790861 20,13 C20,17.418278 16.418278,21 12,21 C7.581722,21 4,17.418278 4,13 C4,9.07824577 6.821932,5.81558908 10.5460197,5.13180618 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "report-problem": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/report_problem</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/report_problem" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" transform="translate(1.000000, 3.000000)" fill="#000000">            <path d="M10.9983807,1.40008709 L2.07828678,16.8542285 L19.9214654,16.8527331 L10.9983807,1.40008709 Z M1.73198946,18.0542575 C1.17970471,18.0542575 0.73190565,17.6066261 0.73190565,17.0543413 C0.73190565,16.8788151 0.778077283,16.7063769 0.865823011,16.5543567 L10.1322606,0.50017172 C10.4083483,0.0218474871 11.0199199,-0.142097683 11.4982441,0.133989935 C11.6502857,0.221747967 11.7765459,0.347984576 11.8643324,0.500009721 L21.133877,16.5526412 C21.4100541,17.0309138 21.2462233,17.6425161 20.7679507,17.9186932 C20.6159412,18.0064706 20.4435045,18.0526893 20.2679717,18.052704 L1.73198946,18.0542575 Z M10.3725586,6.95410156 L11.6274414,6.95410156 L11.5297852,11.6757812 L10.4702148,11.6757812 L10.3725586,6.95410156 Z M10.9975586,14.0976562 C10.6020508,14.0976562 10.2895508,13.7900391 10.2895508,13.4042969 C10.2895508,13.0185547 10.6020508,12.7158203 10.9975586,12.7158203 C11.3979492,12.7158203 11.7104492,13.0185547 11.7104492,13.4042969 C11.7104492,13.7900391 11.3979492,14.0976562 10.9975586,14.0976562 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/report-problem</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/report-problem" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" transform="translate(1.000000, 3.000000)" fill="#000000">            <path d="M20.2679717,18.052704 L1.73198946,18.0542575 C1.17970471,18.0543038 0.731951939,17.6066261 0.731905653,17.0543413 C0.731890943,16.8788151 0.778077283,16.7063769 0.865823011,16.5543567 L10.1322606,0.50017172 C10.4083483,0.0218474871 11.0199199,-0.142097683 11.4982441,0.133989935 C11.6502857,0.221747967 11.7765459,0.347984576 11.8643324,0.500009721 L21.133877,16.5526412 C21.4100541,17.0309138 21.2462233,17.6425161 20.7679507,17.9186932 C20.6159412,18.0064706 20.4435045,18.0526893 20.2679717,18.052704 Z M10.2470703,6.54492188 L10.3642578,12.2109375 L11.6357422,12.2109375 L11.7529297,6.54492188 L10.2470703,6.54492188 Z M10.9970703,15.1171875 C11.4775391,15.1171875 11.8525391,14.7480469 11.8525391,14.2851563 C11.8525391,13.8222656 11.4775391,13.4589844 10.9970703,13.4589844 C10.5224609,13.4589844 10.1474609,13.8222656 10.1474609,14.2851563 C10.1474609,14.7480469 10.5224609,15.1171875 10.9970703,15.1171875 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "search": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/search</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/search" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Icons/Tint-Color/Black" transform="translate(4.000000, 4.000000)" fill="#000000">            <path d="M12.3099312,11.561403 L16.4242641,15.6757359 L15.5757359,16.5242641 L11.4528509,12.4013791 C10.2428099,13.4000674 8.69146727,14 7,14 C3.134,14 0,10.866 0,7 C0,3.134 3.134,0 7,0 C10.866,0 14,3.134 14,7 C14,8.74248408 13.3633321,10.3362634 12.3099312,11.561403 Z M12.7999973,7.0000124 C12.7999973,3.79675515 10.2032544,1.20001221 6.99999714,1.20001221 C3.79673989,1.20001221 1.19999695,3.79675515 1.19999695,7.0000124 C1.19999695,10.2032696 3.79673989,12.8000126 6.99999714,12.8000126 C10.2032544,12.8000126 12.7999973,10.2032696 12.7999973,7.0000124 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/search</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/search" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="common_icons_search" fill="#000000">            <path d="M16.6216248,15.171751 L20.8662058,19.4163321 L19.4519923,20.8305456 L15.21253,16.5910833 C14.0404668,17.4755514 12.5814804,18 11,18 C7.134,18 4,14.866 4,11 C4,7.134 7.134,4 11,4 C14.866,4 18,7.134 18,11 C18,12.5631909 17.4876115,14.0067046 16.6216248,15.171751 Z M16,11 C16,8.23857143 13.7614286,6 11,6 C8.23857143,6 6,8.23857143 6,11 C6,13.7614286 8.23857143,16 11,16 C13.7614286,16 16,13.7614286 16,11 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "sending": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/sending</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/sending" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Icons/Tint-Color/Black" transform="translate(5.000000, 7.000000)" fill="#000000">            <path d="M2.87132034,4.25 L8,4.25 L8,5.75 L2.87132034,5.75 L5.30330086,8.18198052 L4.24264069,9.24264069 L1.06066017,6.06066017 L0.353553391,5.35355339 C0.158291245,5.15829124 0.158291245,4.84170876 0.353553391,4.64644661 L4.24264069,0.757359313 L5.30330086,1.81801948 L2.87132034,4.25 Z M9.5,4.25 L11,4.25 L11,5.75 L9.5,5.75 L9.5,4.25 Z M12.5,4.25 L14,4.25 L14,5.75 L12.5,5.75 L12.5,4.25 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/sending</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/sending" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group-11" transform="translate(5.000000, 7.000000)" fill="#000000">            <path d="M3.82842712,4 L8,4 L8,6 L3.82842712,6 L5.65685425,7.82842712 L4.24264069,9.24264069 L1.41421356,6.41421356 L0.353553391,5.35355339 C0.158291245,5.15829124 0.158291245,4.84170876 0.353553391,4.64644661 L4.24264069,0.757359313 L5.65685425,2.17157288 L3.82842712,4 Z M9,4 L11,4 L11,6 L9,6 L9,4 Z M12,4 L14,4 L14,6 L12,6 L12,4 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "setting": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/setting</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/setting" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Icons/Tint-Color/Black" transform="translate(2.000000, 2.000000)" fill="#000000">            <path d="M10.5630683,1.2 L9.43693169,1.2 L8.79240518,3.77810602 L8.14519264,3.97714227 C7.76740015,4.0933242 7.40218123,4.24492893 7.05375494,4.42967378 L6.45518089,4.74705359 L4.17560975,3.3793109 L3.3793109,4.17560975 L4.74705359,6.45518089 L4.42967378,7.05375494 C4.24492893,7.40218123 4.0933242,7.76740015 3.97714227,8.14519264 L3.77810602,8.79240518 L1.2,9.43693169 L1.2,10.5630683 L3.77810602,11.2075948 L3.97714227,11.8548074 C4.0933242,12.2325999 4.24492893,12.5978188 4.42967378,12.9462451 L4.74705359,13.5448191 L3.3793109,15.8243903 L4.17560975,16.6206891 L6.45518089,15.2529464 L7.05375494,15.5703262 C7.40218123,15.7550711 7.76740015,15.9066758 8.14519264,16.0228577 L8.79240518,16.221894 L9.43693169,18.8 L10.5630683,18.8 L11.2075948,16.221894 L11.8548074,16.0228577 C12.2325999,15.9066758 12.5978188,15.7550711 12.9462451,15.5703262 L13.5448191,15.2529464 L15.8243903,16.6206891 L16.6206891,15.8243903 L15.2529464,13.5448191 L15.5703262,12.9462451 C15.7550711,12.5978188 15.9066758,12.2325999 16.0228577,11.8548074 L16.221894,11.2075948 L18.8,10.5630683 L18.8,9.43693169 L16.221894,8.79240518 L16.0228577,8.14519264 C15.9066758,7.76740015 15.7550711,7.40218123 15.5703262,7.05375494 L15.2529464,6.45518089 L16.6206891,4.17560975 L15.8243903,3.3793109 L13.5448191,4.74705359 L12.9462451,4.42967378 C12.5978188,4.24492893 12.2325999,4.0933242 11.8548074,3.97714227 L11.2075948,3.77810602 L10.5630683,1.2 Z M13.5083849,3.36948567 L15.3408974,2.26997816 C15.7343732,2.03389271 16.2380315,2.09589589 16.5624999,2.42036431 L17.5796357,3.43750007 C17.9041041,3.76196849 17.9661073,4.26562685 17.7300218,4.6591026 L16.6305143,6.49161512 C16.8491215,6.90390529 17.0303999,7.33902209 17.1698454,7.79246134 L19.2425356,8.31063391 C19.6877026,8.42192566 20,8.82190876 20,9.28077641 L20,10.7192236 C20,11.1780912 19.6877026,11.5780743 19.2425356,11.6893661 L17.1698454,12.2075387 C17.0303999,12.6609779 16.8491215,13.0960947 16.6305143,13.5083849 L17.7300218,15.3408974 C17.9661073,15.7343732 17.9041041,16.2380315 17.5796357,16.5624999 L16.5624999,17.5796357 C16.2380315,17.9041041 15.7343732,17.9661073 15.3408974,17.7300218 L13.5083849,16.6305143 C13.0960947,16.8491215 12.6609779,17.0303999 12.2075387,17.1698454 L11.6893661,19.2425356 C11.5780743,19.6877026 11.1780912,20 10.7192236,20 L9.28077641,20 C8.82190876,20 8.42192566,19.6877026 8.31063391,19.2425356 L7.79246134,17.1698454 C7.33902209,17.0303999 6.90390529,16.8491215 6.49161512,16.6305143 L4.6591026,17.7300218 C4.26562685,17.9661073 3.76196849,17.9041041 3.43750007,17.5796357 L2.42036431,16.5624999 C2.09589589,16.2380315 2.03389271,15.7343732 2.26997816,15.3408974 L3.36948567,13.5083849 C3.1508785,13.0960947 2.9696001,12.6609779 2.83015464,12.2075387 L0.757464375,11.6893661 C0.312297374,11.5780743 0,11.1780912 0,10.7192236 L0,9.28077641 C0,8.82190876 0.312297374,8.42192566 0.757464375,8.31063391 L2.83015464,7.79246134 C2.9696001,7.33902209 3.1508785,6.90390529 3.36948567,6.49161512 L2.26997816,4.6591026 C2.03389271,4.26562685 2.09589589,3.76196849 2.42036431,3.43750007 L3.43750007,2.42036431 C3.76196849,2.09589589 4.26562685,2.03389271 4.6591026,2.26997816 L6.49161512,3.36948567 C6.90390529,3.1508785 7.33902209,2.9696001 7.79246134,2.83015464 L8.31063391,0.757464375 C8.42192566,0.312297374 8.82190876,0 9.28077641,0 L10.7192236,0 C11.1780912,0 11.5780743,0.312297374 11.6893661,0.757464375 L12.2075387,2.83015464 C12.6609779,2.9696001 13.0960947,3.1508785 13.5083849,3.36948567 Z M10,12.8 C11.5463973,12.8 12.8,11.5463973 12.8,10 C12.8,8.4536027 11.5463973,7.2 10,7.2 C8.4536027,7.2 7.2,8.4536027 7.2,10 C7.2,11.5463973 8.4536027,12.8 10,12.8 Z M10,14 C7.790861,14 6,12.209139 6,10 C6,7.790861 7.790861,6 10,6 C12.209139,6 14,7.790861 14,10 C14,12.209139 12.209139,14 10,14 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/setting</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/setting" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon26" fill="#000000">            <path d="M14.2075387,4.83015464 C14.6609779,4.9696001 15.0960947,5.1508785 15.5083849,5.36948567 L17.3408974,4.26997816 C17.7343732,4.03389271 18.2380315,4.09589589 18.5624999,4.42036431 L19.5796357,5.43750007 C19.9041041,5.76196849 19.9661073,6.26562685 19.7300218,6.6591026 L18.6305143,8.49161512 C18.8491215,8.90390529 19.0303999,9.33902209 19.1698454,9.79246134 L21.2425356,10.3106339 C21.6877026,10.4219257 22,10.8219088 22,11.2807764 L22,12.7192236 C22,13.1780912 21.6877026,13.5780743 21.2425356,13.6893661 L19.1698454,14.2075387 C19.0303999,14.6609779 18.8491215,15.0960947 18.6305143,15.5083849 L19.7300218,17.3408974 C19.9661073,17.7343732 19.9041041,18.2380315 19.5796357,18.5624999 L18.5624999,19.5796357 C18.2380315,19.9041041 17.7343732,19.9661073 17.3408974,19.7300218 L15.5083849,18.6305143 C15.0960947,18.8491215 14.6609779,19.0303999 14.2075387,19.1698454 L13.6893661,21.2425356 C13.5780743,21.6877026 13.1780912,22 12.7192236,22 L11.2807764,22 C10.8219088,22 10.4219257,21.6877026 10.3106339,21.2425356 L9.79246134,19.1698454 C9.33902209,19.0303999 8.90390529,18.8491215 8.49161512,18.6305143 L6.6591026,19.7300218 C6.26562685,19.9661073 5.76196849,19.9041041 5.43750007,19.5796357 L4.42036431,18.5624999 C4.09589589,18.2380315 4.03389271,17.7343732 4.26997816,17.3408974 L5.36948567,15.5083849 C5.1508785,15.0960947 4.9696001,14.6609779 4.83015464,14.2075387 L2.75746437,13.6893661 C2.31229737,13.5780743 2,13.1780912 2,12.7192236 L2,11.2807764 C2,10.8219088 2.31229737,10.4219257 2.75746437,10.3106339 L4.83015464,9.79246134 C4.9696001,9.33902209 5.1508785,8.90390529 5.36948567,8.49161512 L4.26997816,6.6591026 C4.03389271,6.26562685 4.09589589,5.76196849 4.42036431,5.43750007 L5.43750007,4.42036431 C5.76196849,4.09589589 6.26562685,4.03389271 6.6591026,4.26997816 L8.49161512,5.36948567 C8.90390529,5.1508785 9.33902209,4.9696001 9.79246134,4.83015464 L10.3106339,2.75746437 C10.4219257,2.31229737 10.8219088,2 11.2807764,2 L12.7192236,2 C13.1780912,2 13.5780743,2.31229737 13.6893661,2.75746437 L14.2075387,4.83015464 Z M12,16 C14.209139,16 16,14.209139 16,12 C16,9.790861 14.209139,8 12,8 C9.790861,8 8,9.790861 8,12 C8,14.209139 9.790861,16 12,16 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "share": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/share</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/share" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon38" transform="translate(4.000000, 2.000000)" fill="#000000">            <path d="M14.8,6.2 L12,6.2 L12,5 L15.0010434,5 C15.5508697,5 16,5.44583866 16,5.99580851 L16,19.0041915 C16,19.5510798 15.5541613,20 15.0041915,20 L0.995808514,20 C0.448920205,20 0,19.5541613 0,19.0041915 L0,5.99580851 C0,5.44892021 0.447248087,5 0.998956561,5 L4,5 L4,6.2 L1.2,6.2 L1.2,18.8 L14.8,18.8 L14.8,6.2 Z M8.59999394,2.26151638 L8.59999394,11.5 L7.3999939,11.5 L7.3999939,2.26152854 L5.31299427,4.34852817 L4.46446609,3.5 L7.29289322,0.671572875 C7.68341751,0.281048584 8.31658249,0.281048584 8.70710678,0.671572875 L11.5355339,3.5 L10.6870057,4.34852817 L8.59999394,2.26151638 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/share</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/share" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon38" fill="#000000">            <path d="M12.75,4.36643249 L12.75,7 L19.0010434,7 C19.5508697,7 20,7.44583866 20,7.99580851 L20,21.0041915 C20,21.5510798 19.5541613,22 19.0041915,22 L4.99580851,22 C4.44892021,22 4,21.5541613 4,21.0041915 L4,7.99580851 C4,7.44892021 4.44724809,7 4.99895656,7 L11.25,7 L11.25,4.37643471 L9.53050029,6.09619408 L8.47000027,5.03553391 L11.297947,2.20716017 C11.688335,1.81649963 12.3215,1.81645183 12.7120537,2.20694664 C12.7120893,2.20698223 12.7121249,2.20701782 12.7120537,2.20716017 L15.5400004,5.03553391 L14.4795004,6.09619408 L12.75,4.36643249 Z M11.25,7 L11.25,13.5 L12.75,13.5 L12.75,7 L11.25,7 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "shop": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/shop</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/shop" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon37" transform="translate(4.000000, 2.000000)" fill="#000000">            <path d="M4,5 L4,4 C4,1.790861 5.790861,0 8,0 C10.209139,0 12,1.790861 12,4 L12,5 L14.9991283,5 C15.5518945,5 16,5.44892021 16,6.00748397 L16,18.0081158 C16,19.1082031 15.1054862,20 14.0059397,20 L1.99406028,20 C0.892771196,20 0,19.1066027 0,18.0081158 L0,6.00748397 C0,5.45106594 0.444630861,5 1.00087166,5 L4,5 Z M5.19999757,5 L10.7999992,5 L10.7999992,4 C10.7999969,2.45359963 9.54639417,1.19999695 8,1.19999695 C6.45359963,1.19999695 5.19999695,2.45359963 5.19999695,4 L5.19999757,5 Z M4,6.2 L1.2,6.2 L1.2,18.0081158 C1.2,18.4442694 1.5559217,18.8 1.99406028,18.8 L14.0059397,18.8 C14.4438014,18.8 14.8,18.4444047 14.8,18.0081158 L14.8,6.2 L12,6.2 L12,9 L10.7999992,9 L10.7999992,6.2 L5.19999831,6.2 L5.20000005,9 L4,9 L4,6.2 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/shop</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/shop" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon37" fill="#000000">            <path d="M7.5,7 L7.5,6.5 C7.5,4.01471863 9.51471863,2 12,2 C14.4852814,2 16.5,4.01471863 16.5,6.5 L16.5,7 L18.9991283,7 C19.5518945,7 20,7.44892021 20,8.00748397 L20,20.0081158 C20,21.1082031 19.1054862,22 18.0059397,22 L5.99406028,22 C4.8927712,22 4,21.1066027 4,20.0081158 L4,8.00748397 C4,7.45106594 4.44463086,7 5.00087166,7 L7.5,7 Z M9,7 L15,7 L15,6.5 C15,4.84314575 13.6568542,3.5 12,3.5 C10.3431458,3.5 9,4.84314575 9,6.5 L9,7 Z M7.5,7 L7.5,11 L9,11 L9,7 L7.5,7 Z M15,7 L15,11 L16.5,11 L16.5,7 L15,7 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "star": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/star</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/star" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group-25" transform="translate(2.000000, 3.000000)" fill="#000000">            <path d="M13.9414342,11.2806496 L17.8828683,7.43870082 L12.4359403,6.64721585 L10,1.71146175 L7.56405973,6.64721585 L2.11713169,7.43870082 L6.05856585,11.2806496 L5.12811946,16.7055683 L10,14.1442691 L14.8718805,16.7055683 L13.9414342,11.2806496 Z M10,15.5 L5.32783438,17.9563028 C4.83898979,18.2133036 4.23436264,18.0253571 3.97736183,17.5365125 C3.87502276,17.3418521 3.83970808,17.118884 3.87688493,16.9021263 L4.76918916,11.6995935 L0.989327772,8.01513923 C0.593844194,7.62963801 0.585751887,6.99652475 0.971253099,6.60104117 C1.1247617,6.44355754 1.32590411,6.34107036 1.54354115,6.30944585 L6.76718111,5.55040653 L9.10326392,0.816985751 C9.34768622,0.321732091 9.94731205,0.118393092 10.4425657,0.362815385 C10.6397783,0.460145624 10.7994058,0.619773146 10.8967361,0.816985751 L13.2328189,5.55040653 L18.4564589,6.30944585 C19.0030037,6.38886347 19.3816852,6.89630632 19.3022676,7.44285118 C19.270643,7.66048821 19.1681559,7.86163062 19.0106722,8.01513923 L15.2308108,11.6995935 L16.1231151,16.9021263 C16.2164761,17.4464628 15.8508883,17.9634187 15.3065518,18.0567797 C15.0897942,18.0939566 14.8668261,18.0586419 14.6721656,17.9563028 L10,15.5 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/star</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/star" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group-25" fill="#000000">            <path d="M12,18.5 L7.32783438,20.9563028 C6.83898979,21.2133036 6.23436264,21.0253571 5.97736183,20.5365125 C5.87502276,20.3418521 5.83970808,20.118884 5.87688493,19.9021263 L6.76918916,14.6995935 L2.98932777,11.0151392 C2.59384419,10.629638 2.58575189,9.99652475 2.9712531,9.60104117 C3.1247617,9.44355754 3.32590411,9.34107036 3.54354115,9.30944585 L8.76718111,8.55040653 L11.1032639,3.81698575 C11.3476862,3.32173209 11.9473121,3.11839309 12.4425657,3.36281539 C12.6397783,3.46014562 12.7994058,3.61977315 12.8967361,3.81698575 L15.2328189,8.55040653 L20.4564589,9.30944585 C21.0030037,9.38886347 21.3816852,9.89630632 21.3022676,10.4428512 C21.270643,10.6604882 21.1681559,10.8616306 21.0106722,11.0151392 L17.2308108,14.6995935 L18.1231151,19.9021263 C18.2164761,20.4464628 17.8508883,20.9634187 17.3065518,21.0567797 C17.0897942,21.0939566 16.8668261,21.0586419 16.6721656,20.9563028 L12,18.5 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "sticker": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/sticker</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/sticker" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Icons/Tint-Color/Black" transform="translate(2.000000, 2.000000)" fill="#000000">            <path d="M10,20 C4.4771525,20 0,15.5228475 0,10 C0,4.4771525 4.4771525,0 10,0 C15.5228475,0 20,4.4771525 20,10 C20,15.5228475 15.5228475,20 10,20 Z M10,18.8 C14.8601058,18.8 18.8,14.8601058 18.8,10 C18.8,5.1398942 14.8601058,1.2 10,1.2 C5.1398942,1.2 1.2,5.1398942 1.2,10 C1.2,14.8601058 5.1398942,18.8 10,18.8 Z M4,10.5 L16,10.5 C16,13.8137085 13.3137085,16.5 10,16.5 C6.6862915,16.5 4,13.8137085 4,10.5 Z M10,15.3 C12.2366073,15.3 14.1159277,13.7702727 14.648779,11.7 L5.35122098,11.7 C5.88407233,13.7702727 7.76339268,15.3 10,15.3 Z M6.5,8.5 C5.67157288,8.5 5,7.82842712 5,7 C5,6.17157288 5.67157288,5.5 6.5,5.5 C7.32842712,5.5 8,6.17157288 8,7 C8,7.82842712 7.32842712,8.5 6.5,8.5 Z M13.5,8.5 C12.6715729,8.5 12,7.82842712 12,7 C12,6.17157288 12.6715729,5.5 13.5,5.5 C14.3284271,5.5 15,6.17157288 15,7 C15,7.82842712 14.3284271,8.5 13.5,8.5 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/sticker</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/sticker" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon23" fill="#000000">            <g id="Group-19" transform="translate(2.000000, 2.000000)">                <path d="M10,20 C4.4771525,20 0,15.5228475 0,10 C0,4.4771525 4.4771525,0 10,0 C15.5228475,0 20,4.4771525 20,10 C20,15.5228475 15.5228475,20 10,20 Z M10,16 C12.8690213,16 15.2249641,13.80325 15.4775785,11 L4.52242151,11 C4.7750359,13.80325 7.13097872,16 10,16 Z M6.5,8.5 C7.32842712,8.5 8,7.82842712 8,7 C8,6.17157288 7.32842712,5.5 6.5,5.5 C5.67157288,5.5 5,6.17157288 5,7 C5,7.82842712 5.67157288,8.5 6.5,8.5 Z M13.5,8.5 C14.3284271,8.5 15,7.82842712 15,7 C15,6.17157288 14.3284271,5.5 13.5,5.5 C12.6715729,5.5 12,6.17157288 12,7 C12,7.82842712 12.6715729,8.5 13.5,8.5 Z" id="图标颜色"></path>            </g>        </g>    </g></svg>\` },
    "tag": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/tag</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/tag" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon28" transform="translate(2.000000, 3.000000)" fill="#000000">            <path d="M1.20710678,11.2071068 C0.816582489,10.8165825 0.816582489,10.1834175 1.20710678,9.79289322 L10.7071068,0.292893219 C10.8946432,0.10535684 11.1489971,0 11.4142136,0 L18,0 C18.5522847,0 19,0.44771525 19,1 L19,7.58578644 C19,7.85100293 18.8946432,8.10535684 18.7071068,8.29289322 L9.20710678,17.7928932 C8.81658249,18.1834175 8.18341751,18.1834175 7.79289322,17.7928932 L1.20710678,11.2071068 Z M17.8,7.50294373 L17.8,1.2 L11.4970563,1.2 L2.19705627,10.5 L8.5,16.8029437 L17.8,7.50294373 Z M14,6.5 C13.1715729,6.5 12.5,5.82842712 12.5,5 C12.5,4.17157288 13.1715729,3.5 14,3.5 C14.8284271,3.5 15.5,4.17157288 15.5,5 C15.5,5.82842712 14.8284271,6.5 14,6.5 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/tag</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/tag" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon28" fill="#000000">            <path d="M3.20710678,14.2071068 C2.81658249,13.8165825 2.81658249,13.1834175 3.20710678,12.7928932 L12.7071068,3.29289322 C12.8946432,3.10535684 13.1489971,3 13.4142136,3 L20,3 C20.5522847,3 21,3.44771525 21,4 L21,10.5857864 C21,10.8510029 20.8946432,11.1053568 20.7071068,11.2928932 L11.2071068,20.7928932 C10.8165825,21.1834175 10.1834175,21.1834175 9.79289322,20.7928932 L3.20710678,14.2071068 Z M16,10 C17.1045695,10 18,9.1045695 18,8 C18,6.8954305 17.1045695,6 16,6 C14.8954305,6 14,6.8954305 14,8 C14,9.1045695 14.8954305,10 16,10 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "text": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/text</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/text" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" transform="translate(3.000000, 3.000000)" fill="#000000">            <path d="M1,0 L17,0 C17.5522847,0 18,0.44771525 18,1 L18,17 C18,17.5522847 17.5522847,18 17,18 L1,18 C0.44771525,18 0,17.5522847 0,17 L0,1 C0,0.44771525 0.44771525,0 1,0 Z M1.2,1.2 L1.2,16.8 L16.8,16.8 L16.8,1.2 L1.2,1.2 Z M9.59999394,5.70000005 L9.59999394,14.5 L8.3999939,14.5 L8.3999939,5.70000005 L4.5,5.70000005 L4.5,4.5 L13.5,4.5 L13.5,5.70000005 L9.59999394,5.70000005 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/text</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/text" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" fill="#000000">            <path d="M4,3 L20,3 C20.5522847,3 21,3.44771525 21,4 L21,20 C21,20.5522847 20.5522847,21 20,21 L4,21 C3.44771525,21 3,20.5522847 3,20 L3,4 C3,3.44771525 3.44771525,3 4,3 Z M13,9 L16.5,9 L16.5,7 L7.5,7 L7.5,9 L11,9 L11,17 L13,17 L13,9 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "time": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/time</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/time" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Icons/Tint-Color/Black" transform="translate(2.000000, 2.000000)" fill="#000000">            <path d="M10.5999997,9.50294371 L14.4911685,13.3941125 L13.6426403,14.2426407 L9.39999962,10 L9.39999962,4 L10.5999997,4 L10.5999997,9.50294371 Z M10,20 C4.4771525,20 0,15.5228475 0,10 C0,4.4771525 4.4771525,0 10,0 C15.5228475,0 20,4.4771525 20,10 C20,15.5228475 15.5228475,20 10,20 Z M10,18.8 C14.8601058,18.8 18.8,14.8601058 18.8,10 C18.8,5.1398942 14.8601058,1.2 10,1.2 C5.1398942,1.2 1.2,5.1398942 1.2,10 C1.2,14.8601058 5.1398942,18.8 10,18.8 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/time</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/time" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon21" fill="#000000">            <path d="M12.75,11.3793394 L12.75,6 L11.25,6 L11.25,12 L15.4926407,16.2433004 L16.5533009,15.1826403 L12.75,11.3793394 Z M12,22 C6.4771525,22 2,17.5228475 2,12 C2,6.4771525 6.4771525,2 12,2 C17.5228475,2 22,6.4771525 22,12 C22,17.5228475 17.5228475,22 12,22 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "transfer-text": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/transfer-text</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/transfer-text" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="图标颜色" transform="translate(3.000000, 3.000000)" fill="#000000">            <path d="M1,0 L17,0 C17.5522847,0 18,0.44771525 18,1 L18,17 C18,17.5522847 17.5522847,18 17,18 L1,18 C0.44771525,18 0,17.5522847 0,17 L0,1 C0,0.44771525 0.44771525,0 1,0 Z M1.2,1.2 L1.2,16.8 L16.8,16.8 L16.8,1.2 L1.2,1.2 Z M4.57,5.86 L8.53,5.86 C8.28,5.4 7.97,4.97 7.61,4.56 L8.66,4.18 C9.02,4.65 9.35,5.21 9.65,5.86 L13.43,5.86 L13.43,6.9 L11.92,6.9 C11.44,8.4 10.74,9.65 9.82,10.66 C10.85,11.48 12.13,12.16 13.65,12.72 L13.07,13.61 C11.47,13.01 10.14,12.27 9.08,11.39 C7.96,12.38 6.58,13.11 4.95,13.57 L4.39,12.65 C5.98,12.24 7.3,11.6 8.34,10.72 C7.26,9.62 6.51,8.35 6.09,6.9 L4.57,6.9 L4.57,5.86 Z M7.09,6.9 C7.5,8.08 8.16,9.11 9.08,10.01 C9.86,9.15 10.45,8.11 10.84,6.9 L7.09,6.9 Z"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/transfer-text</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/transfer-text" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="图标颜色" transform="translate(3.000000, 3.000000)" fill="#000000">            <path d="M1,0 L17,0 C17.5522847,0 18,0.44771525 18,1 L18,17 C18,17.5522847 17.5522847,18 17,18 L1,18 C0.44771525,18 0,17.5522847 0,17 L0,1 C0,0.44771525 0.44771525,0 1,0 Z M4.198,5.848 L4.198,6.992 L5.87,6.992 C6.332,8.587 7.157,9.984 8.345,11.194 C7.201,12.162 5.749,12.866 4,13.317 L4.616,14.329 C6.409,13.823 7.927,13.02 9.159,11.931 C10.325,12.899 11.788,13.713 13.548,14.373 L14.186,13.394 C12.514,12.778 11.106,12.03 9.973,11.128 C10.985,10.017 11.755,8.642 12.283,6.992 L13.944,6.992 L13.944,5.848 L9.786,5.848 C9.456,5.133 9.093,4.517 8.697,4 L7.542,4.418 C7.938,4.869 8.279,5.342 8.554,5.848 L4.198,5.848 Z M6.97,6.992 L11.095,6.992 C10.666,8.323 10.017,9.467 9.159,10.413 C8.147,9.423 7.421,8.29 6.97,6.992 Z"></path>        </g>    </g></svg>\` },
    "transfer2": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/transfer2</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/transfer2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Icons/Tint-Color/Black" transform="translate(2.000000, 2.000000)" fill="#000000">            <path d="M10,20 C4.4771525,20 0,15.5228475 0,10 C0,4.4771525 4.4771525,0 10,0 C15.5228475,0 20,4.4771525 20,10 C20,15.5228475 15.5228475,20 10,20 Z M10,18.8 C14.8601058,18.8 18.8,14.8601058 18.8,10 C18.8,5.1398942 14.8601058,1.2 10,1.2 C5.1398942,1.2 1.2,5.1398942 1.2,10 C1.2,14.8601058 5.1398942,18.8 10,18.8 Z M7.78990146,7.92367324 L15,7.92367324 L15,9.12367328 L7,9.12367328 L5.54588248,9.12367328 C5.2697401,9.12367328 5.04588248,8.89981542 5.04588248,8.62367305 C5.04588248,8.47568977 5.11143476,8.33530829 5.22489809,8.24030765 L8.3863121,5.59331722 C8.55569359,5.4514974 8.8079721,5.47384054 8.94979192,5.64322203 C9.06317978,5.778646 9.07432456,5.97240774 8.97721173,6.11993928 L7.78990146,7.92367324 Z M12.255981,12.0800002 L5.04588248,12.0800002 L5.04588248,10.8800001 L13.0458825,10.8800001 L14.5,10.8800001 C14.7761424,10.8800001 15,11.103858 15,11.3800001 C15,11.5279836 14.9344477,11.6683651 14.8209844,11.7633657 L11.6595704,14.4103562 C11.4901889,14.552176 11.2379104,14.5298329 11.0960906,14.3604514 C10.9827027,14.2250274 10.9715579,14.0312657 11.0686708,13.8837341 L12.255981,12.0800002 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/transfer2</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/transfer2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="common_icons_miniprogram" fill="#000000">            <path d="M13.0150338,14.5536734 L11.9008658,16.9301632 C11.8391719,17.0617546 11.853708,17.2164553 11.938835,17.334251 C12.0682302,17.5133036 12.3182766,17.5535588 12.4973291,17.4241636 L17.2974838,13.9552544 C17.4275709,13.8612449 17.5046203,13.7105008 17.5046203,13.5500002 C17.5046203,13.2738578 17.2807627,13.0500002 17.0046203,13.0500002 L13.7199998,13.0500002 L13.7182777,13.0536734 L7,13.0536734 L7,14.5536734 L13.0150338,14.5536734 Z M11.2095863,9.44631611 L12.3237543,7.0698263 C12.3854483,6.93823484 12.3709122,6.7835342 12.2857852,6.66573842 C12.1563899,6.48668585 11.9063436,6.44643061 11.727291,6.57582585 L6.9271363,10.0447351 C6.79704919,10.1387446 6.71999979,10.2894886 6.71999979,10.4499893 C6.71999979,10.7261316 6.94385742,10.9499893 7.21999979,10.9499893 L10.5046203,10.9499893 L10.5063424,10.9463161 L17.2199998,10.9463161 L17.2199998,9.44631611 L11.2095863,9.44631611 L11.2095863,9.44631611 Z M12,22 C6.4771525,22 2,17.5228475 2,12 C2,6.4771525 6.4771525,2 12,2 C17.5228475,2 22,6.4771525 22,12 C22,17.5228475 17.5228475,22 12,22 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "translate": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/translate</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/translate" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group-9" transform="translate(2.000000, 2.000000)" fill="#000000">            <path d="M1.2,9.2 L1.2,18.8 L10.8,18.8 L10.8,9.2 L1.2,9.2 Z M8,8 L8,1.00247329 C8,0.455760956 8.44882258,0 9.00247329,0 L18.9975267,0 C19.544239,0 20,0.448822582 20,1.00247329 L20,10.9975267 C20,11.544239 19.5511774,12 18.9975267,12 L12,12 L12,18.9975267 C12,19.544239 11.5511774,20 10.9975267,20 L1.00247329,20 C0.455760956,20 0,19.5511774 0,18.9975267 L0,9.00247329 C0,8.45576096 0.448822582,8 1.00247329,8 L8,8 Z M9.2,8 L10.9975267,8 C11.544239,8 12,8.44882258 12,9.00247329 L12,10.8 L18.8,10.8 L18.8,1.2 L9.2,1.2 L9.2,8 Z M6.94499638,12.6968862 L4.78057032,12.6968862 C5.06288676,13.3628635 5.44654757,13.9564519 5.93155274,14.4704126 C6.35864685,13.9781686 6.69887435,13.391819 6.94499638,12.6968862 Z M9.22524457,12.6968862 L8.13217322,12.6968862 C7.79194571,13.6813744 7.32865719,14.4993681 6.73506877,15.1725843 C7.45895708,15.6937839 8.33486194,16.0774447 9.3845,16.3090889 C9.14561686,16.5407332 8.83434488,17.0040217 8.67508945,17.293577 C7.56030145,17.0040217 6.65544105,16.555211 5.90983609,15.9543837 C5.12079783,16.5769276 4.16526525,17.0329772 3.02152172,17.3514881 C2.90569959,17.0981272 2.58718873,16.6058831 2.3845,16.3597611 C3.49204912,16.1136391 4.39690951,15.7372172 5.12803671,15.2015398 C4.52720941,14.506607 4.06392089,13.6741355 3.68749897,12.6968862 L2.54375543,12.6968862 L2.54375543,11.6689648 L5.52617528,11.6689648 C5.45378645,11.3794095 5.32348656,11.0102265 5.18594778,10.7206711 L6.31521355,10.3876825 C6.49618563,10.72791 6.69887435,11.1839597 6.77126319,11.4879928 L6.18491365,11.6689648 L9.22524457,11.6689648 L9.22524457,12.6968862 Z M15.5507812,8.63671875 L15.1210938,7.26953125 L13.140625,7.26953125 L12.7109375,8.63671875 L11.5,8.63671875 L13.4570312,3 L14.84375,3 L16.8046875,8.63671875 L15.5507812,8.63671875 Z M14.1171875,4.078125 L13.3945312,6.38671875 L14.8671875,6.38671875 L14.1445312,4.078125 L14.1171875,4.078125 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/translate</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/translate" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group-9" fill="#000000">            <path d="M15.4745785,9.26953125 L17.1210938,9.26953125 L17.5507812,10.6367188 L18.8046875,10.6367188 L16.84375,5 L15.4570312,5 L14.2418545,8.5 L10,8.5 L10,3.00247329 C10,2.45576096 10.4488226,2 11.0024733,2 L20.9975267,2 C21.544239,2 22,2.44882258 22,3.00247329 L22,12.9975267 C22,13.544239 21.5511774,14 20.9975267,14 L15.5,14 L15.5,9.49268723 C15.5,9.41595736 15.4912019,9.34126077 15.4745785,9.26953125 L15.4745785,9.26953125 Z M16.1171875,6.078125 L16.1445312,6.078125 L16.8671875,8.38671875 L15.3945312,8.38671875 L16.1171875,6.078125 Z M3.00247329,10 L12.9975267,10 C13.544239,10 14,10.4488226 14,11.0024733 L14,20.9975267 C14,21.544239 13.5511774,22 12.9975267,22 L3.00247329,22 C2.45576096,22 2,21.5511774 2,20.9975267 L2,11.0024733 C2,10.455761 2.44882258,10 3.00247329,10 Z M11.2252446,14.6968862 L11.2252446,13.6689648 L8.18491365,13.6689648 L8.77126319,13.4879928 C8.69887435,13.1839597 8.49618563,12.72791 8.31521355,12.3876825 L7.18594778,12.7206711 C7.32348656,13.0102265 7.45378645,13.3794095 7.52617528,13.6689648 L4.54375543,13.6689648 L4.54375543,14.6968862 L5.68749897,14.6968862 C6.06392089,15.6741355 6.52720941,16.506607 7.12803671,17.2015398 C6.39690951,17.7372172 5.49204912,18.1136391 4.3845,18.3597611 C4.58718873,18.6058831 4.90569959,19.0981272 5.02152172,19.3514881 C6.16526525,19.0329772 7.12079783,18.5769276 7.90983609,17.9543837 C8.65544105,18.555211 9.56030145,19.0040217 10.6750895,19.293577 C10.8343449,19.0040217 11.1456169,18.5407332 11.3845,18.3090889 C10.3348619,18.0774447 9.45895708,17.6937839 8.73506877,17.1725843 C9.32865719,16.4993681 9.79194571,15.6813744 10.1321732,14.6968862 L11.2252446,14.6968862 Z M8.94499638,14.6968862 C8.69887435,15.391819 8.35864685,15.9781686 7.93155274,16.4704126 C7.44654757,15.9564519 7.06288676,15.3628635 6.78057032,14.6968862 L8.94499638,14.6968862 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "tv": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/tv</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/tv" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="common_icons_tv&amp;display" transform="translate(2.000000, 4.000000)" fill="#000000">            <path d="M1.2,1.2 L1.2,13.8 L18.8,13.8 L18.8,1.2 L1.2,1.2 Z M0,1 C0,0.44771525 0.44771525,0 1,0 L19,0 C19.5522847,0 20,0.44771525 20,1 L20,14 C20,14.5522847 19.5522847,15 19,15 L1,15 C0.44771525,15 0,14.5522847 0,14 L0,1 Z M6,16.8999993 C6,16.5686284 6.26617432,16.2999992 6.60130024,16.2999992 L13.3986998,16.2999992 C13.7307887,16.2999992 14,16.5783196 14,16.8999993 L14,17.4999993 L6,17.4999993 L6,16.8999993 Z M9.66543361,4.89648438 L7.80957031,4.89648438 L7.80957031,11 L6.71582031,11 L6.71582031,4.89648438 L4.55761719,4.89648438 L4.55761719,3.95410156 L9.32910156,3.95410156 L10.515625,3.95410156 L12.4345703,9.76953125 L12.4638672,9.76953125 L14.3876953,3.95410156 L15.5546875,3.95410156 L13.0400391,11 L11.84375,11 L9.66543361,4.89648438 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/tv</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/tv" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="common_icons_tv&amp;display" fill="#000000">            <path d="M11.2179689,7.95410156 L6.30000019,7.95410156 L6.30000019,9.16992188 L8.336133,9.16992188 L8.336133,15 L9.81074238,15 L9.81074238,9.16992188 L11.6266126,9.16992188 L13.586133,15 L15.3634768,15 L17.7316408,7.95410156 L16.1251955,7.95410156 L14.5138674,13.4814453 L14.4796877,13.4814453 L12.8683596,7.95410156 L11.2179689,7.95410156 Z M2,5 C2,4.44771525 2.44771525,4 3,4 L21,4 C21.5522847,4 22,4.44771525 22,5 L22,18 C22,18.5522847 21.5522847,19 21,19 L3,19 C2.44771525,19 2,18.5522847 2,18 L2,5 Z M8,21.0499992 C8,20.6357857 8.34375,20.2999992 8.75262058,20.2999992 L15.2473794,20.2999992 C15.6630403,20.2999992 16,20.6328979 16,21.0499992 L16,21.7999992 L8,21.7999992 L8,21.0499992 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "video-call": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/video-call</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/video-call" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="图标颜色" transform="translate(2.000000, 5.000000)" fill="#000000">            <path d="M1.2,1.7 L1.2,12.3 L13.8,12.3 L13.8,1.7 L1.2,1.7 Z M1,0.5 L14,0.5 C14.5522847,0.5 15,0.94771525 15,1.5 L15,12.5 C15,13.0522847 14.5522847,13.5 14,13.5 L1,13.5 C0.44771525,13.5 0,13.0522847 0,12.5 L0,1.5 C0,0.94771525 0.44771525,0.5 1,0.5 Z M17.2,8.42325018 L19.8,10.5032502 L19.8,3.49674982 L17.2,5.57674982 L17.2,8.42325018 Z M16,5 L19.375305,2.29975604 C19.8065669,1.95474649 20.4358593,2.02466786 20.7808688,2.4559298 C20.9227192,2.63324285 21,2.85355335 21,3.08062485 L21,10.9193752 C21,11.4716599 20.5522847,11.9193752 20,11.9193752 C19.7729285,11.9193752 19.552618,11.8420944 19.375305,11.700244 L16,9 L16,5 Z"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/video-call</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/video-call" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Group" transform="translate(2.000000, 5.000000)" fill="#000000">            <path d="M1,0.5 L14,0.5 C14.5522847,0.5 15,0.94771525 15,1.5 L15,12.5 C15,13.0522847 14.5522847,13.5 14,13.5 L1,13.5 C0.44771525,13.5 6.76353751e-17,13.0522847 0,12.5 L0,1.5 C-6.76353751e-17,0.94771525 0.44771525,0.5 1,0.5 Z M16,5 L19.375305,2.29975604 C19.8065669,1.95474649 20.4358593,2.02466786 20.7808688,2.4559298 C20.9227192,2.63324285 21,2.85355335 21,3.08062485 L21,10.9193752 C21,11.4716599 20.5522847,11.9193752 20,11.9193752 C19.7729285,11.9193752 19.552618,11.8420944 19.375305,11.700244 L16,9 L16,5 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "voice": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/voice</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/voice" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="Icons/Tint-Color/Black" transform="translate(2.000000, 2.000000)" fill="#000000">            <path d="M10,20 C4.4771525,20 0,15.5228475 0,10 C0,4.4771525 4.4771525,0 10,0 C15.5228475,0 20,4.4771525 20,10 C20,15.5228475 15.5228475,20 10,20 Z M10,18.8 C14.8601058,18.8 18.8,14.8601058 18.8,10 C18.8,5.1398942 14.8601058,1.2 10,1.2 C5.1398942,1.2 1.2,5.1398942 1.2,10 C1.2,14.8601058 5.1398942,18.8 10,18.8 Z M10.3083261,14.8083261 C11.5761218,13.5405304 12.3,11.8290902 12.3,10 C12.3,8.17090981 11.5761218,6.45946962 10.3083261,5.19167389 L11.1568542,4.34314575 C12.6045695,5.790861 13.5,7.790861 13.5,10 C13.5,12.209139 12.6045695,14.209139 11.1568542,15.6568542 L10.3083261,14.8083261 Z M8.32842712,12.8284271 C9.07450989,12.0823444 9.5,11.0763727 9.5,10 C9.5,8.92362725 9.07450989,7.91765564 8.32842712,7.17157288 L9.17695526,6.32304474 C10.1179702,7.26405965 10.7,8.56405965 10.7,10 C10.7,11.4359403 10.1179702,12.7359403 9.17695526,13.6769553 L8.32842712,12.8284271 Z M7.19705627,11.6970563 L5.5,10 L7.19705627,8.30294373 C7.63137085,8.7372583 7.9,9.3372583 7.9,10 C7.9,10.6627417 7.63137085,11.2627417 7.19705627,11.6970563 Z" id="图标颜色"></path>        </g>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/voice</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/voice" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="icon24" fill="#000000">            <path d="M12,22 C6.4771525,22 2,17.5228475 2,12 C2,6.4771525 6.4771525,2 12,2 C17.5228475,2 22,6.4771525 22,12 C22,17.5228475 17.5228475,22 12,22 Z M12.3083261,16.8083261 L13.1568542,17.6568542 C14.6045695,16.209139 15.5,14.209139 15.5,12 C15.5,9.790861 14.6045695,7.790861 13.1568542,6.34314575 L12.3083261,7.19167389 C13.5761218,8.45946962 14.3,10.1709098 14.3,12 C14.3,13.8290902 13.5761218,15.5405304 12.3083261,16.8083261 Z M10.3284271,14.8284271 L11.1769553,15.6769553 C12.1179702,14.7359403 12.7,13.4359403 12.7,12 C12.7,10.5640597 12.1179702,9.26405965 11.1769553,8.32304474 L10.3284271,9.17157288 C11.0745099,9.91765564 11.5,10.9236273 11.5,12 C11.5,13.0763727 11.0745099,14.0823444 10.3284271,14.8284271 Z M9.19705627,13.6970563 C9.63137085,13.2627417 9.9,12.6627417 9.9,12 C9.9,11.3372583 9.63137085,10.7372583 9.19705627,10.3029437 L7.5,12 L9.19705627,13.6970563 Z" id="图标颜色"></path>        </g>    </g></svg>\` },
    "volume-down": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/volume_down</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/volume_down" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <path d="M12,5.41421356 L7.91421356,9.5 L3,9.5 L3,14.5 L7.91421356,14.5 L12,18.5857864 L12,5.41421356 Z M7.5,8.5 L11.2928932,4.70710678 C11.6834175,4.31658249 12.3165825,4.31658249 12.7071068,4.70710678 C12.8946432,4.89464316 13,5.14899707 13,5.41421356 L13,18.5857864 C13,19.1380712 12.5522847,19.5857864 12,19.5857864 C11.7347835,19.5857864 11.4804296,19.4804296 11.2928932,19.2928932 L7.5,15.5 L3,15.5 C2.44771525,15.5 2,15.0522847 2,14.5 L2,9.5 C2,8.94771525 2.44771525,8.5 3,8.5 L7.5,8.5 Z M15.3284271,14.8284271 C16.0745099,14.0823444 16.5,13.0763727 16.5,12 C16.5,10.9236273 16.0745099,9.91765564 15.3284271,9.17157288 L16.1769553,8.32304474 C17.1179702,9.26405965 17.7,10.5640597 17.7,12 C17.7,13.4359403 17.1179702,14.7359403 16.1769553,15.6769553 L15.3284271,14.8284271 Z" id="Combined-Shape" fill="#000000"></path>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/volume-down</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/volume-down" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="音量" transform="translate(2.000000, 4.000000)" fill="#000000">            <path d="M9.3,0.7 C9.7,0.3 10.3,0.3 10.7,0.7 C10.9,0.9 11,1.1 11,1.4 L11,14.6 C11,15.2 10.6,15.6 10,15.6 C9.7,15.6 9.5,15.5 9.3,15.3 L5.5,11.5 L1,11.5 C0.4,11.5 0,11.1 0,10.5 L0,5.5 C0,4.9 0.4,4.5 1,4.5 L5.5,4.5 L9.3,0.7 Z M14.2,11.9 L13.1,10.8 L13.2,10.7 C13.9,10 14.3,9 14.3,8 C14.3,7 13.9,6 13.2,5.3 L13.1,5.2 L14.2,4.1 L14.3,4.2 C15.3,5.2 15.9,6.6 15.9,8 C15.9,9.4 15.3,10.8 14.3,11.8 L14.2,11.9 Z" id="Combined-Shape"></path>        </g>    </g></svg>\` },
    "volume-off": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/volume_off</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/volume_off" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <path d="M8.84852842,7.15147158 L11.2928932,4.70710678 C11.6834175,4.31658249 12.3165825,4.31658249 12.7071068,4.70710678 C12.8946432,4.89464316 13,5.14899707 13,5.41421356 L13,11.3029432 L15.8635112,14.1664544 C16.2743314,13.5304353 16.5,12.7838871 16.5,12 C16.5,10.9236273 16.0745099,9.91765564 15.3284271,9.17157288 L16.1769553,8.32304474 C17.1179702,9.26405965 17.7,10.5640597 17.7,12 C17.7,13.1304063 17.3393038,14.1765674 16.7267259,15.029669 L17.8698713,16.1728144 C18.7875614,14.994606 19.3,13.5389457 19.3,12 C19.3,10.1709098 18.5761218,8.45946962 17.3083261,7.19167389 L18.1568542,6.34314575 C19.6045695,7.790861 20.5,9.790861 20.5,12 C20.5,13.9042389 19.8346829,15.6530847 18.7238215,17.0267646 L21.4249789,19.7279221 L20.5764507,20.5764502 L3.84852868,3.84852817 L4.69705685,3 L8.84852842,7.15147158 Z M12,10.3029432 L12,5.41421356 L9.55563521,7.85857836 L12,10.3029432 Z M7.80294315,9.5 L3,9.5 L3,14.5 L7.91421356,14.5 L12,18.5857864 L12,13.6970568 L13,14.6970568 L13,18.5857864 C13,19.1380712 12.5522847,19.5857864 12,19.5857864 C11.7347835,19.5857864 11.4804296,19.4804296 11.2928932,19.2928932 L7.5,15.5 L3,15.5 C2.44771525,15.5 2,15.0522847 2,14.5 L2,9.5 C2,8.94771525 2.44771525,8.5 3,8.5 L6.80294315,8.5 L7.80294315,9.5 Z" id="Combined-Shape" fill="#000000"></path>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/volume-off</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/volume-off" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <path d="M9.06066017,6.93933983 L11.3,4.7 C11.7,4.3 12.3,4.3 12.7,4.7 C12.9,4.9 13,5.1 13,5.4 L13,10.8786797 L15.8785479,13.7572276 C16.1520151,13.2164906 16.3,12.6082453 16.3,12 C16.3,11 15.9,10 15.2,9.3 L15.1,9.2 L16.2,8.1 L16.3,8.2 C17.3,9.2 17.9,10.6 17.9,12 C17.9,13.0188672 17.5822172,14.0377344 17.0237418,14.9024214 L17.9423347,15.8210143 C18.7302379,14.7028496 19.1,13.4130518 19.1,12 C19.1,10.2 18.4,8.6 17.2,7.3 L17.1,7.2 L18.2,6.1 L18.3,6.2 C19.8,7.7 20.7,9.8 20.7,12 C20.7,13.820007 20.1524917,15.5031369 19.057475,16.9361546 L22.0918831,19.9705627 L21.0312229,21.0312229 L4.06066017,4.06066017 L5.12132034,3 L9.06066017,6.93933983 Z M13,15.1213203 L13,18.6 C13,19.2 12.6,19.6 12,19.6 C11.7,19.6 11.5,19.5 11.3,19.3 L7.5,15.5 L3,15.5 C2.4,15.5 2,15.1 2,14.5 L2,9.5 C2,8.9 2.4,8.5 3,8.5 L6.37867966,8.5 L13,15.1213203 Z" id="Combined-Shape" fill="#000000"></path>    </g></svg>\` },
    "volume-up": { outline: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Outlined/volume_up</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Outlined/volume_up" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <path d="M12,5.41421356 L7.91421356,9.5 L3,9.5 L3,14.5 L7.91421356,14.5 L12,18.5857864 L12,5.41421356 Z M7.5,8.5 L11.2928932,4.70710678 C11.6834175,4.31658249 12.3165825,4.31658249 12.7071068,4.70710678 C12.8946432,4.89464316 13,5.14899707 13,5.41421356 L13,18.5857864 C13,19.1380712 12.5522847,19.5857864 12,19.5857864 C11.7347835,19.5857864 11.4804296,19.4804296 11.2928932,19.2928932 L7.5,15.5 L3,15.5 C2.44771525,15.5 2,15.0522847 2,14.5 L2,9.5 C2,8.94771525 2.44771525,8.5 3,8.5 L7.5,8.5 Z M17.3083261,16.8083261 C18.5761218,15.5405304 19.3,13.8290902 19.3,12 C19.3,10.1709098 18.5761218,8.45946962 17.3083261,7.19167389 L18.1568542,6.34314575 C19.6045695,7.790861 20.5,9.790861 20.5,12 C20.5,14.209139 19.6045695,16.209139 18.1568542,17.6568542 L17.3083261,16.8083261 Z M15.3284271,14.8284271 C16.0745099,14.0823444 16.5,13.0763727 16.5,12 C16.5,10.9236273 16.0745099,9.91765564 15.3284271,9.17157288 L16.1769553,8.32304474 C17.1179702,9.26405965 17.7,10.5640597 17.7,12 C17.7,13.4359403 17.1179702,14.7359403 16.1769553,15.6769553 L15.3284271,14.8284271 Z" id="Combined-Shape" fill="#000000"></path>    </g></svg>\`, filled: \`<?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">        <title>3.Icons/Filled/volume-up</title>    <desc>Created with Sketch.</desc>    <g id="3.Icons/Filled/volume-up" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.9">        <g id="音量" transform="translate(2.000000, 4.000000)" fill="#000000">            <path d="M9.3,0.7 C9.7,0.3 10.3,0.3 10.7,0.7 C10.9,0.9 11,1.1 11,1.4 L11,14.6 C11,15.2 10.6,15.6 10,15.6 C9.7,15.6 9.5,15.5 9.3,15.3 L5.5,11.5 L1,11.5 C0.4,11.5 0,11.1 0,10.5 L0,5.5 C0,4.9 0.4,4.5 1,4.5 L5.5,4.5 L9.3,0.7 Z M16.2,13.9 L15.1,12.8 L15.2,12.7 C16.5,11.4 17.1,9.8 17.1,8 C17.1,6.2 16.4,4.6 15.2,3.3 L15.1,3.2 L16.2,2.1 L16.3,2.2 C17.8,3.7 18.7,5.8 18.7,8 C18.7,10.2 17.9,12.2 16.3,13.8 L16.2,13.9 Z M14.2,11.9 L13.1,10.8 L13.2,10.7 C13.9,10 14.3,9 14.3,8 C14.3,7 13.9,6 13.2,5.3 L13.1,5.2 L14.2,4.1 L14.3,4.2 C15.3,5.2 15.9,6.6 15.9,8 C15.9,9.4 15.3,10.8 14.3,11.8 L14.2,11.9 Z" id="Combined-Shape"></path>        </g>    </g></svg>\` },



    }

`
}
export const createMpIcon= (files: Record<string, string>) => {
    if(files['app.json']){
        Object.keys(needInFile).forEach((key) => {
            if(!files[key]){
                console.log(key, 'key')
                createFileWithContent(key, needInFile[key],false)
            }
        })
    }
    
    
}