'use client';

import { useState, useMemo } from 'react';
import { useFileStore } from '@/stores/fileStore';
import FileIcon from '../FileExplorer/components/fileIcon';
import { Search as SearchIcon } from 'lucide-react';

interface SearchProps {
  onFileSelect: (path: string) => void;
}

interface SearchResult {
  path: string;
  line: number;
  content: string;
  matchStart: number;
  matchEnd: number;
}

export function Search({ onFileSelect }: SearchProps) {
  const { files } = useFileStore();
  const [searchTerm, setSearchTerm] = useState('');
  const [caseSensitive, setCaseSensitive] = useState(false);
  const [useRegex, setUseRegex] = useState(false);

  const searchResults = useMemo(() => {
    if (!searchTerm.trim()) return [];

    const results: SearchResult[] = [];
    
    Object.entries(files).forEach(([path, content]) => {
      const lines = content.split('\n');
      
      lines.forEach((line, lineIndex) => {
        let searchPattern: RegExp;
        
        try {
          if (useRegex) {
            searchPattern = new RegExp(searchTerm, caseSensitive ? 'g' : 'gi');
          } else {
            const escapedTerm = searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
            searchPattern = new RegExp(escapedTerm, caseSensitive ? 'g' : 'gi');
          }
          
          let match;
          while ((match = searchPattern.exec(line)) !== null) {
            results.push({
              path,
              line: lineIndex + 1,
              content: line,
              matchStart: match.index,
              matchEnd: match.index + match[0].length,
            });
            
            // Prevent infinite loop for zero-length matches
            if (match.index === searchPattern.lastIndex) {
              searchPattern.lastIndex++;
            }
          }
        } catch (error) {
          // Invalid regex, skip
        }
      });
    });

    return results;
  }, [files, searchTerm, caseSensitive, useRegex]);

  const handleResultClick = (result: SearchResult) => {
    onFileSelect(result.path);
    // Emit event to open file at specific line
    const event = new CustomEvent('openFile', {
      detail: { path: result.path, line: result.line }
    });
    window.dispatchEvent(event);
  };

  const getFileName = (path: string) => {
    return path.split('/').pop() || path;
  };

  const highlightMatch = (content: string, start: number, end: number) => {
    return (
      <>
        {content.substring(0, start)}
        <span className="bg-yellow-200 dark:bg-yellow-800 text-yellow-900 dark:text-yellow-100">
          {content.substring(start, end)}
        </span>
        {content.substring(end)}
      </>
    );
  };

  return (
    <div className="h-full bg-white dark:bg-[#18181a] flex flex-col">
      <div className="p-2 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Search</h3>
        
        <div className="space-y-2">
          <div className="relative">
            <SearchIcon size={14} className="absolute left-2 top-2 text-gray-400" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search in files..."
              className="w-full pl-7 pr-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-purple-500"
            />
          </div>
          
          <div className="flex gap-2 text-xs">
            <label className="flex items-center gap-1 cursor-pointer">
              <input
                type="checkbox"
                checked={caseSensitive}
                onChange={(e) => setCaseSensitive(e.target.checked)}
                className="w-3 h-3"
              />
              <span className="text-gray-600 dark:text-gray-400">Case sensitive</span>
            </label>
            
            <label className="flex items-center gap-1 cursor-pointer">
              <input
                type="checkbox"
                checked={useRegex}
                onChange={(e) => setUseRegex(e.target.checked)}
                className="w-3 h-3"
              />
              <span className="text-gray-600 dark:text-gray-400">Regex</span>
            </label>
          </div>
        </div>
      </div>
      
      <div className="flex-1 overflow-y-auto">
        {searchTerm && (
          <div className="p-2 text-xs text-gray-600 dark:text-gray-400 border-b border-gray-200 dark:border-gray-700">
            {searchResults.length} results in {new Set(searchResults.map(r => r.path)).size} files
          </div>
        )}
        
        {searchResults.map((result, index) => (
          <div
            key={`${result.path}-${result.line}-${index}`}
            onClick={() => handleResultClick(result)}
            className="px-2 py-1 hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer border-b border-gray-100 dark:border-gray-800"
          >
            <div className="flex items-center gap-2 mb-1">
              <FileIcon fileName={getFileName(result.path)} />
              <span className="text-sm font-medium text-gray-900 dark:text-white truncate">
                {getFileName(result.path)}
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400">
                :{result.line}
              </span>
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400 ml-6 truncate">
              {highlightMatch(result.content.trim(), result.matchStart, result.matchEnd)}
            </div>
          </div>
        ))}
        
        {searchTerm && searchResults.length === 0 && (
          <div className="p-4 text-center text-gray-500 dark:text-gray-400">
            <p className="text-sm">No results found</p>
          </div>
        )}
        
        {!searchTerm && (
          <div className="p-4 text-center text-gray-500 dark:text-gray-400">
            <p className="text-sm">Enter a search term to find text in files</p>
          </div>
        )}
      </div>
    </div>
  );
}
