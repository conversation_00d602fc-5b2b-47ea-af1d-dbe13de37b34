"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/AiChat/chat/index.tsx":
/*!**********************************************!*\
  !*** ./src/components/AiChat/chat/index.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseChat: function() { return /* binding */ BaseChat; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ai_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ai/react */ \"(app-pages-browser)/./node_modules/.pnpm/ai@4.3.17_react@18.3.1_zod@3.24.1/node_modules/ai/react/dist/index.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _stores_chatSlice__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/stores/chatSlice */ \"(app-pages-browser)/./src/stores/chatSlice.ts\");\n/* harmony import */ var _stores_fileStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/stores/fileStore */ \"(app-pages-browser)/./src/stores/fileStore.ts\");\n/* harmony import */ var _utils_indexDB__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/indexDB */ \"(app-pages-browser)/./src/utils/indexDB.ts\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/.pnpm/uuid@11.0.5/node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _components_MessageItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/MessageItem */ \"(app-pages-browser)/./src/components/AiChat/chat/components/MessageItem.tsx\");\n/* harmony import */ var _components_ChatInput__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/ChatInput */ \"(app-pages-browser)/./src/components/AiChat/chat/components/ChatInput/index.tsx\");\n/* harmony import */ var _components_Tips__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./components/Tips */ \"(app-pages-browser)/./src/components/AiChat/chat/components/Tips.tsx\");\n/* harmony import */ var _stores_userSlice__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/stores/userSlice */ \"(app-pages-browser)/./src/stores/userSlice.ts\");\n/* harmony import */ var _UserModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../UserModal */ \"(app-pages-browser)/./src/components/UserModal/index.tsx\");\n/* harmony import */ var _stores_chatModeSlice__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/stores/chatModeSlice */ \"(app-pages-browser)/./src/stores/chatModeSlice.ts\");\n/* __next_internal_client_entry_do_not_use__ BaseChat auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst BaseChat = (param)=>{\n    let { uuid: propUuid } = param;\n    _s();\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { otherConfig, uploadedImages, clearImages, addImages, removeImage } = (0,_stores_chatSlice__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    const [checkCount, setCheckCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [baseModal, setBaseModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        value: \"anthropic/claude-3.5-sonnet\",\n        label: \"Claude 3.5 Sonnet\",\n        useImage: true,\n        from: \"default\",\n        quota: 2,\n        functionCall: true\n    });\n    const { files } = (0,_stores_fileStore__WEBPACK_IMPORTED_MODULE_4__.useFileStore)();\n    const { user, token } = (0,_stores_userSlice__WEBPACK_IMPORTED_MODULE_9__[\"default\"])();\n    const { openModal } = (0,_UserModal__WEBPACK_IMPORTED_MODULE_10__.useLimitModalStore)();\n    const { mode } = (0,_stores_chatModeSlice__WEBPACK_IMPORTED_MODULE_11__[\"default\"])();\n    const [messages, setMessagesa] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const parseTimeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(Date.now());\n    const refUuidMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const chatUuid = propUuid || (0,uuid__WEBPACK_IMPORTED_MODULE_12__[\"default\"])();\n    const baseChatUrl =  true ? window.location.origin : 0;\n    const clearErrors = ()=>setErrors([]);\n    const scrollToBottom = ()=>{\n        setTimeout(()=>{\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }, 100);\n    };\n    // Mock functions for web environment\n    const parseMessages = (messages)=>{\n        // This would parse messages and update file system in desktop app\n        // For web, we'll just log for now\n        console.log(\"Parsing messages:\", messages);\n    };\n    const updateFileSystemNow = ()=>{\n        // This would update the file system in desktop app\n        // For web, this is a no-op\n        return Promise.resolve();\n    };\n    const createMpIcon = (files)=>{\n    // This would create mini program icons in desktop app\n    // For web, this is a no-op\n    };\n    const checkExecList = ()=>{\n        // This would check execution list in desktop app\n        return [];\n    };\n    const checkFinish = ()=>{\n        // This would check if execution is finished in desktop app\n        return true;\n    };\n    const { messages: realMessages, input, handleInputChange, isLoading, setMessages, append, setInput, stop, reload } = (0,ai_react__WEBPACK_IMPORTED_MODULE_13__.useChat)({\n        api: \"\".concat(baseChatUrl, \"/api/chat\"),\n        headers: {\n            ...token && {\n                Authorization: \"Bearer \".concat(token)\n            }\n        },\n        body: {\n            model: baseModal.value,\n            mode: mode,\n            otherConfig: {\n                ...otherConfig,\n                extra: {\n                    ...otherConfig.extra,\n                    isBackEnd: otherConfig.isBackEnd,\n                    backendLanguage: otherConfig.backendLanguage\n                }\n            }\n        },\n        onFinish: async (message)=>{\n            try {\n                var _find_content, _find;\n                const initMessage = messages.filter((m)=>m.role === \"system\");\n                await _utils_indexDB__WEBPACK_IMPORTED_MODULE_5__.db.insert(chatUuid, {\n                    messages: [\n                        ...messages,\n                        ...initMessage,\n                        message\n                    ],\n                    title: ((_find = [\n                        ...initMessage,\n                        ...messages\n                    ].find((m)=>m.role === \"user\" && !m.content.includes(\"<boltArtifact\"))) === null || _find === void 0 ? void 0 : (_find_content = _find.content) === null || _find_content === void 0 ? void 0 : _find_content.slice(0, 50)) || \"New Chat\"\n                });\n            } catch (error) {\n                console.error(\"Failed to save chat history:\", error);\n            }\n            setCheckCount((checkCount)=>checkCount + 1);\n        },\n        onError: (error)=>{\n            var _error_errors_, _error_errors;\n            const msg = (error === null || error === void 0 ? void 0 : (_error_errors = error.errors) === null || _error_errors === void 0 ? void 0 : (_error_errors_ = _error_errors[0]) === null || _error_errors_ === void 0 ? void 0 : _error_errors_.responseBody) || String(error);\n            console.log(\"error\", error, msg);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(msg);\n            if (String(error).includes(\"Quota not enough\")) {\n                openModal(\"limit\");\n            }\n            if (String(error).includes(\"Authentication required\")) {\n                openModal(\"login\");\n            }\n        }\n    });\n    const filterMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return messages.filter((message)=>{\n            if (message.role === \"system\") return false;\n            return true;\n        });\n    }, [\n        messages\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (Date.now() - parseTimeRef.current > 200 && isLoading) {\n            setMessagesa(realMessages);\n            parseTimeRef.current = Date.now();\n            const needParseMessages = messages.filter((m)=>!refUuidMessages.current.includes(m.id));\n            parseMessages(needParseMessages);\n            scrollToBottom();\n        }\n        if (errors.length > 0 && isLoading) {\n            clearErrors();\n        }\n        if (!isLoading) {\n            setMessagesa(realMessages);\n            createMpIcon(files);\n        }\n    }, [\n        realMessages,\n        isLoading\n    ]);\n    const handleFileSelect = async (e)=>{\n        const files = Array.from(e.target.files || []);\n        if (files.length === 0) return;\n        setIsUploading(true);\n        try {\n            const newImages = files.map((file)=>({\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(),\n                    file,\n                    url: URL.createObjectURL(file),\n                    localUrl: URL.createObjectURL(file),\n                    status: \"done\"\n                }));\n            addImages(newImages);\n        } catch (error) {\n            console.error(\"File upload failed:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to upload files\");\n        } finally{\n            setIsUploading(false);\n        }\n    };\n    const handleSubmitWithFiles = async (e, text)=>{\n        e === null || e === void 0 ? void 0 : e.preventDefault();\n        if (!input.trim() && !(text === null || text === void 0 ? void 0 : text.trim()) || isLoading) return;\n        try {\n            const currentAttachments = uploadedImages.map((img)=>({\n                    id: img.id,\n                    name: img.id,\n                    type: img.file.type,\n                    localUrl: img.localUrl,\n                    contentType: img.file.type,\n                    url: img.url\n                }));\n            clearImages();\n            append({\n                role: \"user\",\n                content: text || input\n            }, {\n                experimental_attachments: currentAttachments\n            });\n            setInput(\"\");\n            setTimeout(()=>{\n                scrollToBottom();\n            }, 100);\n        } catch (error) {\n            console.error(\"Upload failed:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to upload files\");\n        }\n    };\n    const handleKeySubmit = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmitWithFiles(e);\n        }\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        const files = Array.from(e.dataTransfer.files);\n        if (files.length > 0) {\n            const fileInput = document.createElement(\"input\");\n            fileInput.type = \"file\";\n            fileInput.multiple = true;\n            fileInput.files = e.dataTransfer.files;\n            handleFileSelect({\n                target: fileInput\n            });\n        }\n    };\n    const handleScroll = ()=>{\n    // Handle scroll events if needed\n    };\n    const showJsx = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 overflow-y-auto px-1 py-2 message-container [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]\",\n            onScroll: handleScroll,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Tips__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    append: append,\n                    setInput: setInput,\n                    handleFileSelect: handleFileSelect\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                    lineNumber: 271,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-[640px] w-full mx-auto space-y-3\",\n                    children: [\n                        filterMessages.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MessageItem__WEBPACK_IMPORTED_MODULE_6__.MessageItem, {\n                                handleRetry: ()=>{\n                                    reload();\n                                },\n                                message: message,\n                                isEndMessage: filterMessages[filterMessages.length - 1].id === message.id,\n                                isLoading: isLoading,\n                                onUpdateMessage: (messageId, content)=>{\n                                    var _content_;\n                                    append({\n                                        role: \"user\",\n                                        content: \" \".concat(content === null || content === void 0 ? void 0 : (_content_ = content[0]) === null || _content_ === void 0 ? void 0 : _content_.text)\n                                    });\n                                }\n                            }, \"\".concat(message.id, \"-\").concat(index), false, {\n                                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 25\n                            }, undefined)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: messagesEndRef\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n            lineNumber: 267,\n            columnNumber: 13\n        }, undefined);\n    }, [\n        filterMessages,\n        isLoading,\n        append,\n        setInput,\n        handleFileSelect,\n        reload\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-full flex-col dark:bg-[#18181a] max-w-full\",\n        onDragOver: handleDragOver,\n        onDrop: handleDrop,\n        children: [\n            showJsx,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatInput__WEBPACK_IMPORTED_MODULE_7__.ChatInput, {\n                input: input,\n                setMessages: setMessages,\n                append: append,\n                messages: messages,\n                stopRuning: stop,\n                setInput: setInput,\n                isLoading: isLoading,\n                isUploading: isUploading,\n                uploadedImages: uploadedImages,\n                baseModal: baseModal,\n                handleInputChange: handleInputChange,\n                handleKeySubmit: handleKeySubmit,\n                handleSubmitWithFiles: handleSubmitWithFiles,\n                handleFileSelect: handleFileSelect,\n                removeImage: removeImage,\n                addImages: addImages,\n                setIsUploading: setIsUploading,\n                setBaseModal: setBaseModal\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                lineNumber: 309,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n        lineNumber: 303,\n        columnNumber: 9\n    }, undefined);\n};\n_s(BaseChat, \"XjpiMRuQUoOxvZGXxaMsIOhU7GA=\", false, function() {\n    return [\n        _stores_chatSlice__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        _stores_fileStore__WEBPACK_IMPORTED_MODULE_4__.useFileStore,\n        _stores_userSlice__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        _UserModal__WEBPACK_IMPORTED_MODULE_10__.useLimitModalStore,\n        _stores_chatModeSlice__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        ai_react__WEBPACK_IMPORTED_MODULE_13__.useChat\n    ];\n});\n_c = BaseChat;\nvar _c;\n$RefreshReg$(_c, \"BaseChat\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AiChat/chat/index.tsx\n"));

/***/ })

});