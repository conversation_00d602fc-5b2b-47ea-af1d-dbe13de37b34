"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/AiChat/chat/index.tsx":
/*!**********************************************!*\
  !*** ./src/components/AiChat/chat/index.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseChat: function() { return /* binding */ BaseChat; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ai_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ai/react */ \"(app-pages-browser)/./node_modules/.pnpm/ai@4.3.17_react@18.3.1_zod@3.24.1/node_modules/ai/react/dist/index.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _stores_chatSlice__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/stores/chatSlice */ \"(app-pages-browser)/./src/stores/chatSlice.ts\");\n/* harmony import */ var _stores_fileStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/stores/fileStore */ \"(app-pages-browser)/./src/stores/fileStore.ts\");\n/* harmony import */ var _utils_indexDB__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/indexDB */ \"(app-pages-browser)/./src/utils/indexDB.ts\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/.pnpm/uuid@11.0.5/node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _components_MessageItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/MessageItem */ \"(app-pages-browser)/./src/components/AiChat/chat/components/MessageItem.tsx\");\n/* harmony import */ var _components_ChatInput__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/ChatInput */ \"(app-pages-browser)/./src/components/AiChat/chat/components/ChatInput/index.tsx\");\n/* harmony import */ var _components_Tips__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./components/Tips */ \"(app-pages-browser)/./src/components/AiChat/chat/components/Tips.tsx\");\n/* harmony import */ var _utils_messagepParseJson__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/messagepParseJson */ \"(app-pages-browser)/./src/utils/messagepParseJson.ts\");\n/* harmony import */ var _utils_messageParserNew__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/messageParserNew */ \"(app-pages-browser)/./src/utils/messageParserNew.ts\");\n/* harmony import */ var _stores_userSlice__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/stores/userSlice */ \"(app-pages-browser)/./src/stores/userSlice.ts\");\n/* harmony import */ var _UserModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../UserModal */ \"(app-pages-browser)/./src/components/UserModal/index.tsx\");\n/* harmony import */ var _stores_chatModeSlice__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/stores/chatModeSlice */ \"(app-pages-browser)/./src/stores/chatModeSlice.ts\");\n/* __next_internal_client_entry_do_not_use__ BaseChat auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst BaseChat = (param)=>{\n    let { uuid: propUuid } = param;\n    _s();\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { otherConfig, uploadedImages, clearImages, addImages, removeImage } = (0,_stores_chatSlice__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    const [checkCount, setCheckCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [baseModal, setBaseModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        value: \"anthropic/claude-3.5-sonnet\",\n        label: \"Claude 3.5 Sonnet\",\n        useImage: true,\n        from: \"default\",\n        quota: 2,\n        functionCall: true\n    });\n    const { files } = (0,_stores_fileStore__WEBPACK_IMPORTED_MODULE_4__.useFileStore)();\n    const { user, token } = (0,_stores_userSlice__WEBPACK_IMPORTED_MODULE_11__[\"default\"])();\n    const { openModal } = (0,_UserModal__WEBPACK_IMPORTED_MODULE_12__.useLimitModalStore)();\n    const { mode } = (0,_stores_chatModeSlice__WEBPACK_IMPORTED_MODULE_13__[\"default\"])();\n    const [messages, setMessagesa] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const parseTimeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(Date.now());\n    const refUuidMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const chatUuid = propUuid || (0,uuid__WEBPACK_IMPORTED_MODULE_14__[\"default\"])();\n    const baseChatUrl =  true ? window.location.origin : 0;\n    const clearErrors = ()=>setErrors([]);\n    const scrollToBottom = ()=>{\n        setTimeout(()=>{\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }, 100);\n    };\n    // Parse messages and create files\n    const parseMessagesAndCreateFiles = async (messages)=>{\n        try {\n            await (0,_utils_messageParserNew__WEBPACK_IMPORTED_MODULE_10__.parseMessages)(messages);\n        } catch (error) {\n            console.error(\"Error parsing messages:\", error);\n        }\n    };\n    const updateFileSystemNow = ()=>{\n        // This would update the file system in desktop app\n        // For web, this is a no-op\n        return Promise.resolve();\n    };\n    const createMpIcon = (files)=>{\n    // This would create mini program icons in desktop app\n    // For web, this is a no-op\n    };\n    const checkExecList = ()=>{\n        // This would check execution list in desktop app\n        return [];\n    };\n    const checkFinish = ()=>{\n        // This would check if execution is finished in desktop app\n        return true;\n    };\n    const { messages: realMessages, input, handleInputChange, isLoading, setMessages, append, setInput, stop, reload } = (0,ai_react__WEBPACK_IMPORTED_MODULE_15__.useChat)({\n        api: \"\".concat(baseChatUrl, \"/api/chat\"),\n        headers: {\n            ...token && {\n                Authorization: \"Bearer \".concat(token)\n            }\n        },\n        body: {\n            model: baseModal.value,\n            mode: mode,\n            otherConfig: {\n                ...otherConfig,\n                extra: {\n                    ...otherConfig.extra,\n                    isBackEnd: otherConfig.isBackEnd,\n                    backendLanguage: otherConfig.backendLanguage\n                }\n            }\n        },\n        onFinish: async (message)=>{\n            try {\n                var _find_content, _find;\n                // Parse message for files (like we-dev-client does)\n                if (message && message.content) {\n                    console.log(\"\\uD83C\\uDFAF onFinish: Processing message for files\");\n                    const { files: messageFiles } = (0,_utils_messagepParseJson__WEBPACK_IMPORTED_MODULE_9__.parseMessage)(message.content);\n                    console.log(\"\\uD83D\\uDCC1 Found \".concat(Object.keys(messageFiles).length, \" files:\"), Object.keys(messageFiles));\n                    for(let filePath in messageFiles){\n                        console.log(\"\\uD83D\\uDCC4 Creating file: \".concat(filePath));\n                        await updateContent(filePath, messageFiles[filePath], false, true);\n                    }\n                }\n                // Also parse with the streaming parser for any missed files\n                const needParseMessages = [\n                    ...messages,\n                    message\n                ].filter((m)=>!refUuidMessages.current.includes(m.id));\n                refUuidMessages.current = [\n                    ...refUuidMessages.current,\n                    ...needParseMessages.map((m)=>m.id)\n                ];\n                if (needParseMessages.length > 0) {\n                    console.log(\"\\uD83D\\uDD04 Also parsing with streaming parser for \".concat(needParseMessages.length, \" messages\"));\n                    parseMessagesAndCreateFiles(needParseMessages);\n                }\n                const initMessage = messages.filter((m)=>m.role === \"system\");\n                await _utils_indexDB__WEBPACK_IMPORTED_MODULE_5__.db.insert(chatUuid, {\n                    messages: [\n                        ...messages,\n                        ...initMessage,\n                        message\n                    ],\n                    title: ((_find = [\n                        ...initMessage,\n                        ...messages\n                    ].find((m)=>m.role === \"user\" && !m.content.includes(\"<boltArtifact\"))) === null || _find === void 0 ? void 0 : (_find_content = _find.content) === null || _find_content === void 0 ? void 0 : _find_content.slice(0, 50)) || \"New Chat\"\n                });\n            } catch (error) {\n                console.error(\"Failed to save chat history:\", error);\n            }\n            setCheckCount((checkCount)=>checkCount + 1);\n        },\n        onError: (error)=>{\n            var _error_errors_, _error_errors;\n            const msg = (error === null || error === void 0 ? void 0 : (_error_errors = error.errors) === null || _error_errors === void 0 ? void 0 : (_error_errors_ = _error_errors[0]) === null || _error_errors_ === void 0 ? void 0 : _error_errors_.responseBody) || String(error);\n            console.log(\"error\", error, msg);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(msg);\n            if (String(error).includes(\"Quota not enough\")) {\n                openModal(\"limit\");\n            }\n            if (String(error).includes(\"Authentication required\")) {\n                openModal(\"login\");\n            }\n        }\n    });\n    const filterMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return messages.filter((message)=>{\n            if (message.role === \"system\") return false;\n            return true;\n        });\n    }, [\n        messages\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only update messages during streaming, but don't parse yet\n        if (Date.now() - parseTimeRef.current > 200 && isLoading) {\n            setMessagesa(realMessages);\n            parseTimeRef.current = Date.now();\n            scrollToBottom();\n        }\n        if (errors.length > 0 && isLoading) {\n            clearErrors();\n        }\n        // Only parse messages when streaming is complete\n        if (!isLoading) {\n            setMessagesa(realMessages);\n            createMpIcon(files);\n            // Parse messages when loading is complete\n            const needParseMessages = messages.filter((m)=>!refUuidMessages.current.includes(m.id) && m.role === \"assistant\" && m.content && typeof m.content === \"string\" && m.content.trim().length > 0);\n            if (needParseMessages.length > 0) {\n                console.log(\"\\uD83D\\uDCE8 Processing \".concat(needParseMessages.length, \" new messages (loading complete):\"), needParseMessages.map((m)=>{\n                    var _m_content;\n                    return {\n                        id: m.id,\n                        role: m.role,\n                        contentLength: ((_m_content = m.content) === null || _m_content === void 0 ? void 0 : _m_content.length) || 0,\n                        hasContent: !!m.content\n                    };\n                }));\n                parseMessagesAndCreateFiles(needParseMessages);\n                // Update tracked message IDs\n                refUuidMessages.current = [\n                    ...refUuidMessages.current,\n                    ...needParseMessages.map((m)=>m.id)\n                ];\n            }\n        }\n    }, [\n        realMessages,\n        isLoading\n    ]);\n    const handleFileSelect = async (e)=>{\n        const files = Array.from(e.target.files || []);\n        if (files.length === 0) return;\n        setIsUploading(true);\n        try {\n            const newImages = files.map((file)=>({\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(),\n                    file,\n                    url: URL.createObjectURL(file),\n                    localUrl: URL.createObjectURL(file),\n                    status: \"done\"\n                }));\n            addImages(newImages);\n        } catch (error) {\n            console.error(\"File upload failed:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to upload files\");\n        } finally{\n            setIsUploading(false);\n        }\n    };\n    const handleSubmitWithFiles = async (e, text)=>{\n        e === null || e === void 0 ? void 0 : e.preventDefault();\n        if (!input.trim() && !(text === null || text === void 0 ? void 0 : text.trim()) || isLoading) return;\n        try {\n            const currentAttachments = uploadedImages.map((img)=>({\n                    id: img.id,\n                    name: img.id,\n                    type: img.file.type,\n                    localUrl: img.localUrl,\n                    contentType: img.file.type,\n                    url: img.url\n                }));\n            clearImages();\n            append({\n                role: \"user\",\n                content: text || input\n            }, {\n                experimental_attachments: currentAttachments\n            });\n            setInput(\"\");\n            setTimeout(()=>{\n                scrollToBottom();\n            }, 100);\n        } catch (error) {\n            console.error(\"Upload failed:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to upload files\");\n        }\n    };\n    const handleKeySubmit = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmitWithFiles(e);\n        }\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        const files = Array.from(e.dataTransfer.files);\n        if (files.length > 0) {\n            const fileInput = document.createElement(\"input\");\n            fileInput.type = \"file\";\n            fileInput.multiple = true;\n            fileInput.files = e.dataTransfer.files;\n            handleFileSelect({\n                target: fileInput\n            });\n        }\n    };\n    const handleScroll = ()=>{\n    // Handle scroll events if needed\n    };\n    const showJsx = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 overflow-y-auto px-1 py-2 message-container [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]\",\n            onScroll: handleScroll,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Tips__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    append: append,\n                    setInput: setInput,\n                    handleFileSelect: handleFileSelect\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-[640px] w-full mx-auto space-y-3\",\n                    children: [\n                        filterMessages.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MessageItem__WEBPACK_IMPORTED_MODULE_6__.MessageItem, {\n                                handleRetry: ()=>{\n                                    reload();\n                                },\n                                message: message,\n                                isEndMessage: filterMessages[filterMessages.length - 1].id === message.id,\n                                isLoading: isLoading,\n                                onUpdateMessage: (messageId, content)=>{\n                                    var _content_;\n                                    append({\n                                        role: \"user\",\n                                        content: \" \".concat(content === null || content === void 0 ? void 0 : (_content_ = content[0]) === null || _content_ === void 0 ? void 0 : _content_.text)\n                                    });\n                                }\n                            }, \"\".concat(message.id, \"-\").concat(index), false, {\n                                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 25\n                            }, undefined)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: messagesEndRef\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n            lineNumber: 313,\n            columnNumber: 13\n        }, undefined);\n    }, [\n        filterMessages,\n        isLoading,\n        append,\n        setInput,\n        handleFileSelect,\n        reload\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-full flex-col dark:bg-[#18181a] max-w-full\",\n        onDragOver: handleDragOver,\n        onDrop: handleDrop,\n        children: [\n            showJsx,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatInput__WEBPACK_IMPORTED_MODULE_7__.ChatInput, {\n                input: input,\n                setMessages: setMessages,\n                append: append,\n                messages: messages,\n                stopRuning: stop,\n                setInput: setInput,\n                isLoading: isLoading,\n                isUploading: isUploading,\n                uploadedImages: uploadedImages,\n                baseModal: baseModal,\n                handleInputChange: handleInputChange,\n                handleKeySubmit: handleKeySubmit,\n                handleSubmitWithFiles: handleSubmitWithFiles,\n                handleFileSelect: handleFileSelect,\n                removeImage: removeImage,\n                addImages: addImages,\n                setIsUploading: setIsUploading,\n                setBaseModal: setBaseModal\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                lineNumber: 355,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n        lineNumber: 349,\n        columnNumber: 9\n    }, undefined);\n};\n_s(BaseChat, \"XjpiMRuQUoOxvZGXxaMsIOhU7GA=\", false, function() {\n    return [\n        _stores_chatSlice__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        _stores_fileStore__WEBPACK_IMPORTED_MODULE_4__.useFileStore,\n        _stores_userSlice__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        _UserModal__WEBPACK_IMPORTED_MODULE_12__.useLimitModalStore,\n        _stores_chatModeSlice__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        ai_react__WEBPACK_IMPORTED_MODULE_15__.useChat\n    ];\n});\n_c = BaseChat;\nvar _c;\n$RefreshReg$(_c, \"BaseChat\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AiChat/chat/index.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/messagepParseJson.ts":
/*!****************************************!*\
  !*** ./src/utils/messagepParseJson.ts ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseMessage: function() { return /* binding */ parseMessage; }\n/* harmony export */ });\n// Pre-compiled regular expressions\nconst ARTIFACT_REGEX = /<boltArtifact[^>]*>([\\s\\S]*?)<\\/boltArtifact>/;\nconst BOLT_ACTION_REGEX = /<boltAction type=\"file\" filePath=\"([^\"]+)\">([\\s\\S]*?)<\\/boltAction>/g;\nfunction parseMessage(content) {\n    // Quick return if content doesn't contain keywords\n    if (!content.includes(\"<boltArtifact\")) {\n        return {\n            content\n        };\n    }\n    try {\n        // Extract boltArtifact content\n        const match = content.match(ARTIFACT_REGEX);\n        if (!match) {\n            return {\n                content\n            };\n        }\n        const artifactContent = match[1].trim();\n        const files = {};\n        // Use string replacement instead of regex matching to extract file content\n        let boltMatch;\n        let startIndex = 0;\n        // Reset regex lastIndex\n        BOLT_ACTION_REGEX.lastIndex = 0;\n        while((boltMatch = BOLT_ACTION_REGEX.exec(artifactContent)) !== null){\n            const [_, filePath, fileContent] = boltMatch;\n            // Use Object.assign instead of spread operator\n            if (fileContent) {\n                files[filePath] = fileContent.trim();\n            }\n            startIndex = BOLT_ACTION_REGEX.lastIndex;\n        }\n        // Use template string instead of string concatenation\n        const fileKeys = Object.keys(files);\n        const newContent = content.replace(ARTIFACT_REGEX, \"Modified directory\".concat(JSON.stringify(fileKeys)));\n        return {\n            content: newContent.trim(),\n            files\n        };\n    } catch (error) {\n        console.error(\"Error parsing message:\", error);\n        return {\n            content\n        };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy91dGlscy9tZXNzYWdlcFBhcnNlSnNvbi50cyIsIm1hcHBpbmdzIjoiOzs7O0FBS0EsbUNBQW1DO0FBQ25DLE1BQU1BLGlCQUFpQjtBQUN2QixNQUFNQyxvQkFDSjtBQUVLLFNBQVNDLGFBQWFDLE9BQWU7SUFDMUMsbURBQW1EO0lBQ25ELElBQUksQ0FBQ0EsUUFBUUMsUUFBUSxDQUFDLGtCQUFrQjtRQUN0QyxPQUFPO1lBQUVEO1FBQVE7SUFDbkI7SUFFQSxJQUFJO1FBQ0YsK0JBQStCO1FBQy9CLE1BQU1FLFFBQVFGLFFBQVFFLEtBQUssQ0FBQ0w7UUFDNUIsSUFBSSxDQUFDSyxPQUFPO1lBQ1YsT0FBTztnQkFBRUY7WUFBUTtRQUNuQjtRQUVBLE1BQU1HLGtCQUFrQkQsS0FBSyxDQUFDLEVBQUUsQ0FBQ0UsSUFBSTtRQUNyQyxNQUFNQyxRQUFnQyxDQUFDO1FBRXZDLDJFQUEyRTtRQUMzRSxJQUFJQztRQUNKLElBQUlDLGFBQWE7UUFFakIsd0JBQXdCO1FBQ3hCVCxrQkFBa0JVLFNBQVMsR0FBRztRQUU5QixNQUFPLENBQUNGLFlBQVlSLGtCQUFrQlcsSUFBSSxDQUFDTixnQkFBZSxNQUFPLEtBQU07WUFDckUsTUFBTSxDQUFDTyxHQUFHQyxVQUFVQyxZQUFZLEdBQUdOO1lBRW5DLCtDQUErQztZQUMvQyxJQUFJTSxhQUFhO2dCQUNmUCxLQUFLLENBQUNNLFNBQVMsR0FBR0MsWUFBWVIsSUFBSTtZQUNwQztZQUVBRyxhQUFhVCxrQkFBa0JVLFNBQVM7UUFDMUM7UUFFQSxzREFBc0Q7UUFDdEQsTUFBTUssV0FBV0MsT0FBT0MsSUFBSSxDQUFDVjtRQUM3QixNQUFNVyxhQUFhaEIsUUFBUWlCLE9BQU8sQ0FDaENwQixnQkFDQSxxQkFBOEMsT0FBekJxQixLQUFLQyxTQUFTLENBQUNOO1FBR3RDLE9BQU87WUFDTGIsU0FBU2dCLFdBQVdaLElBQUk7WUFDeEJDO1FBQ0Y7SUFDRixFQUFFLE9BQU9lLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLDBCQUEwQkE7UUFDeEMsT0FBTztZQUFFcEI7UUFBUTtJQUNuQjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy91dGlscy9tZXNzYWdlcFBhcnNlSnNvbi50cz83MjBiIl0sInNvdXJjZXNDb250ZW50IjpbImludGVyZmFjZSBQYXJzZWRNZXNzYWdlIHtcbiAgY29udGVudDogc3RyaW5nO1xuICBmaWxlcz86IFJlY29yZDxzdHJpbmcsIHN0cmluZz47XG59XG5cbi8vIFByZS1jb21waWxlZCByZWd1bGFyIGV4cHJlc3Npb25zXG5jb25zdCBBUlRJRkFDVF9SRUdFWCA9IC88Ym9sdEFydGlmYWN0W14+XSo+KFtcXHNcXFNdKj8pPFxcL2JvbHRBcnRpZmFjdD4vO1xuY29uc3QgQk9MVF9BQ1RJT05fUkVHRVggPVxuICAvPGJvbHRBY3Rpb24gdHlwZT1cImZpbGVcIiBmaWxlUGF0aD1cIihbXlwiXSspXCI+KFtcXHNcXFNdKj8pPFxcL2JvbHRBY3Rpb24+L2c7XG5cbmV4cG9ydCBmdW5jdGlvbiBwYXJzZU1lc3NhZ2UoY29udGVudDogc3RyaW5nKTogUGFyc2VkTWVzc2FnZSB7XG4gIC8vIFF1aWNrIHJldHVybiBpZiBjb250ZW50IGRvZXNuJ3QgY29udGFpbiBrZXl3b3Jkc1xuICBpZiAoIWNvbnRlbnQuaW5jbHVkZXMoXCI8Ym9sdEFydGlmYWN0XCIpKSB7XG4gICAgcmV0dXJuIHsgY29udGVudCB9O1xuICB9XG5cbiAgdHJ5IHtcbiAgICAvLyBFeHRyYWN0IGJvbHRBcnRpZmFjdCBjb250ZW50XG4gICAgY29uc3QgbWF0Y2ggPSBjb250ZW50Lm1hdGNoKEFSVElGQUNUX1JFR0VYKTtcbiAgICBpZiAoIW1hdGNoKSB7XG4gICAgICByZXR1cm4geyBjb250ZW50IH07XG4gICAgfVxuXG4gICAgY29uc3QgYXJ0aWZhY3RDb250ZW50ID0gbWF0Y2hbMV0udHJpbSgpO1xuICAgIGNvbnN0IGZpbGVzOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+ID0ge307XG5cbiAgICAvLyBVc2Ugc3RyaW5nIHJlcGxhY2VtZW50IGluc3RlYWQgb2YgcmVnZXggbWF0Y2hpbmcgdG8gZXh0cmFjdCBmaWxlIGNvbnRlbnRcbiAgICBsZXQgYm9sdE1hdGNoO1xuICAgIGxldCBzdGFydEluZGV4ID0gMDtcblxuICAgIC8vIFJlc2V0IHJlZ2V4IGxhc3RJbmRleFxuICAgIEJPTFRfQUNUSU9OX1JFR0VYLmxhc3RJbmRleCA9IDA7XG5cbiAgICB3aGlsZSAoKGJvbHRNYXRjaCA9IEJPTFRfQUNUSU9OX1JFR0VYLmV4ZWMoYXJ0aWZhY3RDb250ZW50KSkgIT09IG51bGwpIHtcbiAgICAgIGNvbnN0IFtfLCBmaWxlUGF0aCwgZmlsZUNvbnRlbnRdID0gYm9sdE1hdGNoO1xuXG4gICAgICAvLyBVc2UgT2JqZWN0LmFzc2lnbiBpbnN0ZWFkIG9mIHNwcmVhZCBvcGVyYXRvclxuICAgICAgaWYgKGZpbGVDb250ZW50KSB7XG4gICAgICAgIGZpbGVzW2ZpbGVQYXRoXSA9IGZpbGVDb250ZW50LnRyaW0oKTtcbiAgICAgIH1cblxuICAgICAgc3RhcnRJbmRleCA9IEJPTFRfQUNUSU9OX1JFR0VYLmxhc3RJbmRleDtcbiAgICB9XG5cbiAgICAvLyBVc2UgdGVtcGxhdGUgc3RyaW5nIGluc3RlYWQgb2Ygc3RyaW5nIGNvbmNhdGVuYXRpb25cbiAgICBjb25zdCBmaWxlS2V5cyA9IE9iamVjdC5rZXlzKGZpbGVzKTtcbiAgICBjb25zdCBuZXdDb250ZW50ID0gY29udGVudC5yZXBsYWNlKFxuICAgICAgQVJUSUZBQ1RfUkVHRVgsXG4gICAgICBgTW9kaWZpZWQgZGlyZWN0b3J5JHtKU09OLnN0cmluZ2lmeShmaWxlS2V5cyl9YFxuICAgICk7XG5cbiAgICByZXR1cm4ge1xuICAgICAgY29udGVudDogbmV3Q29udGVudC50cmltKCksXG4gICAgICBmaWxlcyxcbiAgICB9O1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBwYXJzaW5nIG1lc3NhZ2U6XCIsIGVycm9yKTtcbiAgICByZXR1cm4geyBjb250ZW50IH07XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJBUlRJRkFDVF9SRUdFWCIsIkJPTFRfQUNUSU9OX1JFR0VYIiwicGFyc2VNZXNzYWdlIiwiY29udGVudCIsImluY2x1ZGVzIiwibWF0Y2giLCJhcnRpZmFjdENvbnRlbnQiLCJ0cmltIiwiZmlsZXMiLCJib2x0TWF0Y2giLCJzdGFydEluZGV4IiwibGFzdEluZGV4IiwiZXhlYyIsIl8iLCJmaWxlUGF0aCIsImZpbGVDb250ZW50IiwiZmlsZUtleXMiLCJPYmplY3QiLCJrZXlzIiwibmV3Q29udGVudCIsInJlcGxhY2UiLCJKU09OIiwic3RyaW5naWZ5IiwiZXJyb3IiLCJjb25zb2xlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/messagepParseJson.ts\n"));

/***/ })

});