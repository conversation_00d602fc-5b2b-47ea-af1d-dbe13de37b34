'use client';

// API configuration for the web environment
export const API_BASE = typeof window !== 'undefined' ? window.location.origin : '';

// Model types available in the system
export enum ModelTypes {
  Claude35sonnet = "anthropic/claude-3.5-sonnet",
  gpt4oMini = "openai/gpt-4o-mini",
  DeepseekR1 = "deepseek/deepseek-r1",
  DeepseekV3 = "deepseek/deepseek-chat",
}

// Fetch model configurations from the API
export async function fetchModelOptions() {
  try {
    const response = await fetch(`${API_BASE}/api/model`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      throw new Error('Failed to fetch model options');
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error fetching model options:', error);
    // Return default options if API fails
    return [
      {
        label: "Claude 3.5 Sonnet",
        value: ModelTypes.Claude35sonnet,
        useImage: true,
        description: "Claude 3.5 Sonnet via OpenRouter",
        icon: null,
        provider: "anthropic",
        functionCall: true,
      }
    ];
  }
}

// Enhanced prompt API call
export async function enhancePrompt(text: string) {
  try {
    const response = await fetch(`${API_BASE}/api/enhancedPrompt`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ text }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to enhance prompt');
    }
    
    const data = await response.json();
    return data.text || text;
  } catch (error) {
    console.error('Error enhancing prompt:', error);
    return text; // Return original text if enhancement fails
  }
}

// Deploy project API call
export async function deployProject(file: File) {
  try {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await fetch(`${API_BASE}/api/deploy`, {
      method: 'POST',
      body: formData,
    });
    
    if (!response.ok) {
      throw new Error('Failed to deploy project');
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error deploying project:', error);
    throw error;
  }
}
