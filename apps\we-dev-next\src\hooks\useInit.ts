'use client';

import useThemeStore from "@/stores/themeSlice";
import useUserStore from "@/stores/userSlice";
import { useEffect } from "react";

const useInit = (): { isDarkMode: boolean } => {
    const { isDarkMode, setTheme } = useThemeStore();
    const { fetchUser } = useUserStore();

    useEffect(() => {
        // Fetch user info if token exists
        const fetchUserInfo = async () => {
            if (typeof window !== 'undefined') {
                let userInToken = '';
                try {
                    const userStorage = localStorage.getItem("user-storage");
                    if (userStorage) {
                        const parsed = JSON.parse(userStorage);
                        userInToken = parsed?.state?.token;
                    }
                } catch (e) {
                    // Handle parsing error
                }
                
                const token = localStorage.getItem("token") || userInToken;
                if (token) {
                    fetchUser();
                }
            }
        };
        
        fetchUserInfo();

        // Initialize language settings
        if (typeof window !== 'undefined') {
            const settingsConfig = JSON.parse(
                localStorage.getItem("settingsConfig") || "{}"
            );
            
            if (!settingsConfig.language) {
                // Get browser language settings
                const browserLang = navigator.language.toLowerCase();
                // If Chinese environment, set to Chinese, otherwise set to English
                const defaultLang = browserLang.startsWith("zh") ? "zh" : "en";
                // TODO: Implement i18n language change
                // i18n.changeLanguage(defaultLang);
            }
        }

        // Copy functionality
        const callback = (event: ClipboardEvent) => {
            try {
                const selection = window.getSelection();
                if (selection) {
                    navigator.clipboard
                        .writeText(selection.toString().trim())
                        .then(() => {
                            // Copy successful
                        });
                }
                event.preventDefault();
            } catch (e) {
                // Handle error
            }
        };
        
        document.addEventListener("copy", callback);
        return () => document.removeEventListener("copy", callback);
    }, [fetchUser]);

    useEffect(() => {
        if (typeof window !== 'undefined') {
            if (isDarkMode) {
                document.documentElement.classList.add("dark");
            } else {
                document.documentElement.classList.remove("dark");
            }

            const mql = window.matchMedia("(prefers-color-scheme: dark)");
            const savedTheme = localStorage.getItem("theme") || "system";
            
            if (savedTheme !== "system") {
                setTheme(savedTheme === "dark");
            } else {
                setTheme(mql.matches);
            }

            const handleStorageChange = () => {
                if (savedTheme === "system") {
                    setTheme(mql.matches);
                }
            };
            
            mql.addEventListener("change", handleStorageChange);
            return () => {
                mql.removeEventListener("change", handleStorageChange);
            };
        }
    }, [isDarkMode, setTheme]);

    return { isDarkMode };
};

export default useInit;
