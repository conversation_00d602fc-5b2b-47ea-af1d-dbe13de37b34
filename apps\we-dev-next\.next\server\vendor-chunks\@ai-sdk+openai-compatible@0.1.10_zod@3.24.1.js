"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ai-sdk+openai-compatible@0.1.10_zod@3.24.1";
exports.ids = ["vendor-chunks/@ai-sdk+openai-compatible@0.1.10_zod@3.24.1"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@ai-sdk+openai-compatible@0.1.10_zod@3.24.1/node_modules/@ai-sdk/openai-compatible/dist/index.mjs":
/*!******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ai-sdk+openai-compatible@0.1.10_zod@3.24.1/node_modules/@ai-sdk/openai-compatible/dist/index.mjs ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OpenAICompatibleChatLanguageModel: () => (/* binding */ OpenAICompatibleChatLanguageModel),\n/* harmony export */   OpenAICompatibleCompletionLanguageModel: () => (/* binding */ OpenAICompatibleCompletionLanguageModel),\n/* harmony export */   OpenAICompatibleEmbeddingModel: () => (/* binding */ OpenAICompatibleEmbeddingModel),\n/* harmony export */   createOpenAICompatible: () => (/* binding */ createOpenAICompatible)\n/* harmony export */ });\n/* harmony import */ var _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ai-sdk/provider */ \"(rsc)/./node_modules/.pnpm/@ai-sdk+provider@1.0.7/node_modules/@ai-sdk/provider/dist/index.mjs\");\n/* harmony import */ var _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ai-sdk/provider-utils */ \"(rsc)/./node_modules/.pnpm/@ai-sdk+provider-utils@2.1.8_zod@3.24.1/node_modules/@ai-sdk/provider-utils/dist/index.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/.pnpm/zod@3.24.1/node_modules/zod/lib/index.mjs\");\n// src/openai-compatible-chat-language-model.ts\n\n\n\n\n// src/convert-to-openai-compatible-chat-messages.ts\n\n\nfunction getOpenAIMetadata(message) {\n  var _a, _b;\n  return (_b = (_a = message == null ? void 0 : message.providerMetadata) == null ? void 0 : _a.openaiCompatible) != null ? _b : {};\n}\nfunction convertToOpenAICompatibleChatMessages(prompt) {\n  const messages = [];\n  for (const { role, content, ...message } of prompt) {\n    const metadata = getOpenAIMetadata({ ...message });\n    switch (role) {\n      case \"system\": {\n        messages.push({ role: \"system\", content, ...metadata });\n        break;\n      }\n      case \"user\": {\n        if (content.length === 1 && content[0].type === \"text\") {\n          messages.push({\n            role: \"user\",\n            content: content[0].text,\n            ...getOpenAIMetadata(content[0])\n          });\n          break;\n        }\n        messages.push({\n          role: \"user\",\n          content: content.map((part) => {\n            var _a;\n            const partMetadata = getOpenAIMetadata(part);\n            switch (part.type) {\n              case \"text\": {\n                return { type: \"text\", text: part.text, ...partMetadata };\n              }\n              case \"image\": {\n                return {\n                  type: \"image_url\",\n                  image_url: {\n                    url: part.image instanceof URL ? part.image.toString() : `data:${(_a = part.mimeType) != null ? _a : \"image/jpeg\"};base64,${(0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.convertUint8ArrayToBase64)(part.image)}`\n                  },\n                  ...partMetadata\n                };\n              }\n              case \"file\": {\n                throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n                  functionality: \"File content parts in user messages\"\n                });\n              }\n            }\n          }),\n          ...metadata\n        });\n        break;\n      }\n      case \"assistant\": {\n        let text = \"\";\n        const toolCalls = [];\n        for (const part of content) {\n          const partMetadata = getOpenAIMetadata(part);\n          switch (part.type) {\n            case \"text\": {\n              text += part.text;\n              break;\n            }\n            case \"tool-call\": {\n              toolCalls.push({\n                id: part.toolCallId,\n                type: \"function\",\n                function: {\n                  name: part.toolName,\n                  arguments: JSON.stringify(part.args)\n                },\n                ...partMetadata\n              });\n              break;\n            }\n            default: {\n              const _exhaustiveCheck = part;\n              throw new Error(`Unsupported part: ${_exhaustiveCheck}`);\n            }\n          }\n        }\n        messages.push({\n          role: \"assistant\",\n          content: text,\n          tool_calls: toolCalls.length > 0 ? toolCalls : void 0,\n          ...metadata\n        });\n        break;\n      }\n      case \"tool\": {\n        for (const toolResponse of content) {\n          const toolResponseMetadata = getOpenAIMetadata(toolResponse);\n          messages.push({\n            role: \"tool\",\n            tool_call_id: toolResponse.toolCallId,\n            content: JSON.stringify(toolResponse.result),\n            ...toolResponseMetadata\n          });\n        }\n        break;\n      }\n      default: {\n        const _exhaustiveCheck = role;\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n  return messages;\n}\n\n// src/get-response-metadata.ts\nfunction getResponseMetadata({\n  id,\n  model,\n  created\n}) {\n  return {\n    id: id != null ? id : void 0,\n    modelId: model != null ? model : void 0,\n    timestamp: created != null ? new Date(created * 1e3) : void 0\n  };\n}\n\n// src/map-openai-compatible-finish-reason.ts\nfunction mapOpenAICompatibleFinishReason(finishReason) {\n  switch (finishReason) {\n    case \"stop\":\n      return \"stop\";\n    case \"length\":\n      return \"length\";\n    case \"content_filter\":\n      return \"content-filter\";\n    case \"function_call\":\n    case \"tool_calls\":\n      return \"tool-calls\";\n    default:\n      return \"unknown\";\n  }\n}\n\n// src/openai-compatible-error.ts\n\nvar openaiCompatibleErrorDataSchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n  error: zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    message: zod__WEBPACK_IMPORTED_MODULE_2__.z.string(),\n    // The additional information below is handled loosely to support\n    // OpenAI-compatible providers that have slightly different error\n    // responses:\n    type: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish(),\n    param: zod__WEBPACK_IMPORTED_MODULE_2__.z.any().nullish(),\n    code: zod__WEBPACK_IMPORTED_MODULE_2__.z.union([zod__WEBPACK_IMPORTED_MODULE_2__.z.string(), zod__WEBPACK_IMPORTED_MODULE_2__.z.number()]).nullish()\n  })\n});\nvar defaultOpenAICompatibleErrorStructure = {\n  errorSchema: openaiCompatibleErrorDataSchema,\n  errorToMessage: (data) => data.error.message\n};\n\n// src/openai-compatible-prepare-tools.ts\n\nfunction prepareTools({\n  mode,\n  structuredOutputs\n}) {\n  var _a;\n  const tools = ((_a = mode.tools) == null ? void 0 : _a.length) ? mode.tools : void 0;\n  const toolWarnings = [];\n  if (tools == null) {\n    return { tools: void 0, tool_choice: void 0, toolWarnings };\n  }\n  const toolChoice = mode.toolChoice;\n  const openaiCompatTools = [];\n  for (const tool of tools) {\n    if (tool.type === \"provider-defined\") {\n      toolWarnings.push({ type: \"unsupported-tool\", tool });\n    } else {\n      openaiCompatTools.push({\n        type: \"function\",\n        function: {\n          name: tool.name,\n          description: tool.description,\n          parameters: tool.parameters\n        }\n      });\n    }\n  }\n  if (toolChoice == null) {\n    return { tools: openaiCompatTools, tool_choice: void 0, toolWarnings };\n  }\n  const type = toolChoice.type;\n  switch (type) {\n    case \"auto\":\n    case \"none\":\n    case \"required\":\n      return { tools: openaiCompatTools, tool_choice: type, toolWarnings };\n    case \"tool\":\n      return {\n        tools: openaiCompatTools,\n        tool_choice: {\n          type: \"function\",\n          function: {\n            name: toolChoice.toolName\n          }\n        },\n        toolWarnings\n      };\n    default: {\n      const _exhaustiveCheck = type;\n      throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n        functionality: `Unsupported tool choice type: ${_exhaustiveCheck}`\n      });\n    }\n  }\n}\n\n// src/openai-compatible-chat-language-model.ts\nvar OpenAICompatibleChatLanguageModel = class {\n  // type inferred via constructor\n  constructor(modelId, settings, config) {\n    this.specificationVersion = \"v1\";\n    var _a, _b;\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n    const errorStructure = (_a = config.errorStructure) != null ? _a : defaultOpenAICompatibleErrorStructure;\n    this.chunkSchema = createOpenAICompatibleChatChunkSchema(\n      errorStructure.errorSchema\n    );\n    this.failedResponseHandler = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.createJsonErrorResponseHandler)(errorStructure);\n    this.supportsStructuredOutputs = (_b = config.supportsStructuredOutputs) != null ? _b : false;\n  }\n  get defaultObjectGenerationMode() {\n    return this.config.defaultObjectGenerationMode;\n  }\n  get provider() {\n    return this.config.provider;\n  }\n  get providerOptionsName() {\n    return this.config.provider.split(\".\")[0].trim();\n  }\n  getArgs({\n    mode,\n    prompt,\n    maxTokens,\n    temperature,\n    topP,\n    topK,\n    frequencyPenalty,\n    presencePenalty,\n    providerMetadata,\n    stopSequences,\n    responseFormat,\n    seed\n  }) {\n    var _a, _b;\n    const type = mode.type;\n    const warnings = [];\n    if (topK != null) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"topK\"\n      });\n    }\n    if ((responseFormat == null ? void 0 : responseFormat.type) === \"json\" && responseFormat.schema != null && !this.supportsStructuredOutputs) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"responseFormat\",\n        details: \"JSON response format schema is only supported with structuredOutputs\"\n      });\n    }\n    const baseArgs = {\n      // model id:\n      model: this.modelId,\n      // model specific settings:\n      user: this.settings.user,\n      // standardized settings:\n      max_tokens: maxTokens,\n      temperature,\n      top_p: topP,\n      frequency_penalty: frequencyPenalty,\n      presence_penalty: presencePenalty,\n      response_format: (responseFormat == null ? void 0 : responseFormat.type) === \"json\" ? this.supportsStructuredOutputs === true && responseFormat.schema != null ? {\n        type: \"json_schema\",\n        json_schema: {\n          schema: responseFormat.schema,\n          name: (_a = responseFormat.name) != null ? _a : \"response\",\n          description: responseFormat.description\n        }\n      } : { type: \"json_object\" } : void 0,\n      stop: stopSequences,\n      seed,\n      ...providerMetadata == null ? void 0 : providerMetadata[this.providerOptionsName],\n      // messages:\n      messages: convertToOpenAICompatibleChatMessages(prompt)\n    };\n    switch (type) {\n      case \"regular\": {\n        const { tools, tool_choice, toolWarnings } = prepareTools({\n          mode,\n          structuredOutputs: this.supportsStructuredOutputs\n        });\n        return {\n          args: { ...baseArgs, tools, tool_choice },\n          warnings: [...warnings, ...toolWarnings]\n        };\n      }\n      case \"object-json\": {\n        return {\n          args: {\n            ...baseArgs,\n            response_format: this.supportsStructuredOutputs === true && mode.schema != null ? {\n              type: \"json_schema\",\n              json_schema: {\n                schema: mode.schema,\n                name: (_b = mode.name) != null ? _b : \"response\",\n                description: mode.description\n              }\n            } : { type: \"json_object\" }\n          },\n          warnings\n        };\n      }\n      case \"object-tool\": {\n        return {\n          args: {\n            ...baseArgs,\n            tool_choice: {\n              type: \"function\",\n              function: { name: mode.tool.name }\n            },\n            tools: [\n              {\n                type: \"function\",\n                function: {\n                  name: mode.tool.name,\n                  description: mode.tool.description,\n                  parameters: mode.tool.parameters\n                }\n              }\n            ]\n          },\n          warnings\n        };\n      }\n      default: {\n        const _exhaustiveCheck = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n  async doGenerate(options) {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _i;\n    const { args, warnings } = this.getArgs({ ...options });\n    const body = JSON.stringify(args);\n    const {\n      responseHeaders,\n      value: responseBody,\n      rawValue: parsedBody\n    } = await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.postJsonToApi)({\n      url: this.config.url({\n        path: \"/chat/completions\",\n        modelId: this.modelId\n      }),\n      headers: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.combineHeaders)(this.config.headers(), options.headers),\n      body: args,\n      failedResponseHandler: this.failedResponseHandler,\n      successfulResponseHandler: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.createJsonResponseHandler)(\n        OpenAICompatibleChatResponseSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    const { messages: rawPrompt, ...rawSettings } = args;\n    const choice = responseBody.choices[0];\n    const providerMetadata = (_b = (_a = this.config.metadataExtractor) == null ? void 0 : _a.extractMetadata) == null ? void 0 : _b.call(_a, {\n      parsedBody\n    });\n    return {\n      text: (_c = choice.message.content) != null ? _c : void 0,\n      reasoning: (_d = choice.message.reasoning_content) != null ? _d : void 0,\n      toolCalls: (_e = choice.message.tool_calls) == null ? void 0 : _e.map((toolCall) => {\n        var _a2;\n        return {\n          toolCallType: \"function\",\n          toolCallId: (_a2 = toolCall.id) != null ? _a2 : (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId)(),\n          toolName: toolCall.function.name,\n          args: toolCall.function.arguments\n        };\n      }),\n      finishReason: mapOpenAICompatibleFinishReason(choice.finish_reason),\n      usage: {\n        promptTokens: (_g = (_f = responseBody.usage) == null ? void 0 : _f.prompt_tokens) != null ? _g : NaN,\n        completionTokens: (_i = (_h = responseBody.usage) == null ? void 0 : _h.completion_tokens) != null ? _i : NaN\n      },\n      ...providerMetadata && { providerMetadata },\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      response: getResponseMetadata(responseBody),\n      warnings,\n      request: { body }\n    };\n  }\n  async doStream(options) {\n    var _a;\n    if (this.settings.simulateStreaming) {\n      const result = await this.doGenerate(options);\n      const simulatedStream = new ReadableStream({\n        start(controller) {\n          controller.enqueue({ type: \"response-metadata\", ...result.response });\n          if (result.reasoning) {\n            controller.enqueue({\n              type: \"reasoning\",\n              textDelta: result.reasoning\n            });\n          }\n          if (result.text) {\n            controller.enqueue({\n              type: \"text-delta\",\n              textDelta: result.text\n            });\n          }\n          if (result.toolCalls) {\n            for (const toolCall of result.toolCalls) {\n              controller.enqueue({\n                type: \"tool-call\",\n                ...toolCall\n              });\n            }\n          }\n          controller.enqueue({\n            type: \"finish\",\n            finishReason: result.finishReason,\n            usage: result.usage,\n            logprobs: result.logprobs,\n            providerMetadata: result.providerMetadata\n          });\n          controller.close();\n        }\n      });\n      return {\n        stream: simulatedStream,\n        rawCall: result.rawCall,\n        rawResponse: result.rawResponse,\n        warnings: result.warnings\n      };\n    }\n    const { args, warnings } = this.getArgs({ ...options });\n    const body = JSON.stringify({ ...args, stream: true });\n    const metadataExtractor = (_a = this.config.metadataExtractor) == null ? void 0 : _a.createStreamExtractor();\n    const { responseHeaders, value: response } = await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.postJsonToApi)({\n      url: this.config.url({\n        path: \"/chat/completions\",\n        modelId: this.modelId\n      }),\n      headers: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.combineHeaders)(this.config.headers(), options.headers),\n      body: {\n        ...args,\n        stream: true\n      },\n      failedResponseHandler: this.failedResponseHandler,\n      successfulResponseHandler: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.createEventSourceResponseHandler)(\n        this.chunkSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    const { messages: rawPrompt, ...rawSettings } = args;\n    const toolCalls = [];\n    let finishReason = \"unknown\";\n    let usage = {\n      promptTokens: void 0,\n      completionTokens: void 0\n    };\n    let isFirstChunk = true;\n    return {\n      stream: response.pipeThrough(\n        new TransformStream({\n          // TODO we lost type safety on Chunk, most likely due to the error schema. MUST FIX\n          transform(chunk, controller) {\n            var _a2, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n;\n            if (!chunk.success) {\n              finishReason = \"error\";\n              controller.enqueue({ type: \"error\", error: chunk.error });\n              return;\n            }\n            const value = chunk.value;\n            metadataExtractor == null ? void 0 : metadataExtractor.processChunk(chunk.rawValue);\n            if (\"error\" in value) {\n              finishReason = \"error\";\n              controller.enqueue({ type: \"error\", error: value.error.message });\n              return;\n            }\n            if (isFirstChunk) {\n              isFirstChunk = false;\n              controller.enqueue({\n                type: \"response-metadata\",\n                ...getResponseMetadata(value)\n              });\n            }\n            if (value.usage != null) {\n              usage = {\n                promptTokens: (_a2 = value.usage.prompt_tokens) != null ? _a2 : void 0,\n                completionTokens: (_b = value.usage.completion_tokens) != null ? _b : void 0\n              };\n            }\n            const choice = value.choices[0];\n            if ((choice == null ? void 0 : choice.finish_reason) != null) {\n              finishReason = mapOpenAICompatibleFinishReason(\n                choice.finish_reason\n              );\n            }\n            if ((choice == null ? void 0 : choice.delta) == null) {\n              return;\n            }\n            const delta = choice.delta;\n            if (delta.reasoning_content != null) {\n              controller.enqueue({\n                type: \"reasoning\",\n                textDelta: delta.reasoning_content\n              });\n            }\n            if (delta.content != null) {\n              controller.enqueue({\n                type: \"text-delta\",\n                textDelta: delta.content\n              });\n            }\n            if (delta.tool_calls != null) {\n              for (const toolCallDelta of delta.tool_calls) {\n                const index = toolCallDelta.index;\n                if (toolCalls[index] == null) {\n                  if (toolCallDelta.type !== \"function\") {\n                    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'function' type.`\n                    });\n                  }\n                  if (toolCallDelta.id == null) {\n                    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'id' to be a string.`\n                    });\n                  }\n                  if (((_c = toolCallDelta.function) == null ? void 0 : _c.name) == null) {\n                    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'function.name' to be a string.`\n                    });\n                  }\n                  toolCalls[index] = {\n                    id: toolCallDelta.id,\n                    type: \"function\",\n                    function: {\n                      name: toolCallDelta.function.name,\n                      arguments: (_d = toolCallDelta.function.arguments) != null ? _d : \"\"\n                    },\n                    hasFinished: false\n                  };\n                  const toolCall2 = toolCalls[index];\n                  if (((_e = toolCall2.function) == null ? void 0 : _e.name) != null && ((_f = toolCall2.function) == null ? void 0 : _f.arguments) != null) {\n                    if (toolCall2.function.arguments.length > 0) {\n                      controller.enqueue({\n                        type: \"tool-call-delta\",\n                        toolCallType: \"function\",\n                        toolCallId: toolCall2.id,\n                        toolName: toolCall2.function.name,\n                        argsTextDelta: toolCall2.function.arguments\n                      });\n                    }\n                    if ((0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.isParsableJson)(toolCall2.function.arguments)) {\n                      controller.enqueue({\n                        type: \"tool-call\",\n                        toolCallType: \"function\",\n                        toolCallId: (_g = toolCall2.id) != null ? _g : (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId)(),\n                        toolName: toolCall2.function.name,\n                        args: toolCall2.function.arguments\n                      });\n                      toolCall2.hasFinished = true;\n                    }\n                  }\n                  continue;\n                }\n                const toolCall = toolCalls[index];\n                if (toolCall.hasFinished) {\n                  continue;\n                }\n                if (((_h = toolCallDelta.function) == null ? void 0 : _h.arguments) != null) {\n                  toolCall.function.arguments += (_j = (_i = toolCallDelta.function) == null ? void 0 : _i.arguments) != null ? _j : \"\";\n                }\n                controller.enqueue({\n                  type: \"tool-call-delta\",\n                  toolCallType: \"function\",\n                  toolCallId: toolCall.id,\n                  toolName: toolCall.function.name,\n                  argsTextDelta: (_k = toolCallDelta.function.arguments) != null ? _k : \"\"\n                });\n                if (((_l = toolCall.function) == null ? void 0 : _l.name) != null && ((_m = toolCall.function) == null ? void 0 : _m.arguments) != null && (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.isParsableJson)(toolCall.function.arguments)) {\n                  controller.enqueue({\n                    type: \"tool-call\",\n                    toolCallType: \"function\",\n                    toolCallId: (_n = toolCall.id) != null ? _n : (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId)(),\n                    toolName: toolCall.function.name,\n                    args: toolCall.function.arguments\n                  });\n                  toolCall.hasFinished = true;\n                }\n              }\n            }\n          },\n          flush(controller) {\n            var _a2, _b;\n            const metadata = metadataExtractor == null ? void 0 : metadataExtractor.buildMetadata();\n            controller.enqueue({\n              type: \"finish\",\n              finishReason,\n              usage: {\n                promptTokens: (_a2 = usage.promptTokens) != null ? _a2 : NaN,\n                completionTokens: (_b = usage.completionTokens) != null ? _b : NaN\n              },\n              ...metadata && { providerMetadata: metadata }\n            });\n          }\n        })\n      ),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      warnings,\n      request: { body }\n    };\n  }\n};\nvar OpenAICompatibleChatResponseSchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n  id: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish(),\n  created: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().nullish(),\n  model: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish(),\n  choices: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(\n    zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n      message: zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n        role: zod__WEBPACK_IMPORTED_MODULE_2__.z.literal(\"assistant\").nullish(),\n        content: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish(),\n        reasoning_content: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish(),\n        tool_calls: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(\n          zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n            id: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish(),\n            type: zod__WEBPACK_IMPORTED_MODULE_2__.z.literal(\"function\"),\n            function: zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n              name: zod__WEBPACK_IMPORTED_MODULE_2__.z.string(),\n              arguments: zod__WEBPACK_IMPORTED_MODULE_2__.z.string()\n            })\n          })\n        ).nullish()\n      }),\n      finish_reason: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish()\n    })\n  ),\n  usage: zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    prompt_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().nullish(),\n    completion_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().nullish()\n  }).nullish()\n});\nvar createOpenAICompatibleChatChunkSchema = (errorSchema) => zod__WEBPACK_IMPORTED_MODULE_2__.z.union([\n  zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    id: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish(),\n    created: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().nullish(),\n    model: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish(),\n    choices: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(\n      zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n        delta: zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n          role: zod__WEBPACK_IMPORTED_MODULE_2__.z.enum([\"assistant\"]).nullish(),\n          content: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish(),\n          reasoning_content: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish(),\n          tool_calls: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(\n            zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n              index: zod__WEBPACK_IMPORTED_MODULE_2__.z.number(),\n              id: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish(),\n              type: zod__WEBPACK_IMPORTED_MODULE_2__.z.literal(\"function\").optional(),\n              function: zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n                name: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish(),\n                arguments: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish()\n              })\n            })\n          ).nullish()\n        }).nullish(),\n        finish_reason: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish()\n      })\n    ),\n    usage: zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n      prompt_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().nullish(),\n      completion_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().nullish()\n    }).nullish()\n  }),\n  errorSchema\n]);\n\n// src/openai-compatible-completion-language-model.ts\n\n\n\n\n// src/convert-to-openai-compatible-completion-prompt.ts\n\nfunction convertToOpenAICompatibleCompletionPrompt({\n  prompt,\n  inputFormat,\n  user = \"user\",\n  assistant = \"assistant\"\n}) {\n  if (inputFormat === \"prompt\" && prompt.length === 1 && prompt[0].role === \"user\" && prompt[0].content.length === 1 && prompt[0].content[0].type === \"text\") {\n    return { prompt: prompt[0].content[0].text };\n  }\n  let text = \"\";\n  if (prompt[0].role === \"system\") {\n    text += `${prompt[0].content}\n\n`;\n    prompt = prompt.slice(1);\n  }\n  for (const { role, content } of prompt) {\n    switch (role) {\n      case \"system\": {\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.InvalidPromptError({\n          message: \"Unexpected system message in prompt: ${content}\",\n          prompt\n        });\n      }\n      case \"user\": {\n        const userMessage = content.map((part) => {\n          switch (part.type) {\n            case \"text\": {\n              return part.text;\n            }\n            case \"image\": {\n              throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n                functionality: \"images\"\n              });\n            }\n          }\n        }).join(\"\");\n        text += `${user}:\n${userMessage}\n\n`;\n        break;\n      }\n      case \"assistant\": {\n        const assistantMessage = content.map((part) => {\n          switch (part.type) {\n            case \"text\": {\n              return part.text;\n            }\n            case \"tool-call\": {\n              throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n                functionality: \"tool-call messages\"\n              });\n            }\n          }\n        }).join(\"\");\n        text += `${assistant}:\n${assistantMessage}\n\n`;\n        break;\n      }\n      case \"tool\": {\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n          functionality: \"tool messages\"\n        });\n      }\n      default: {\n        const _exhaustiveCheck = role;\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n  text += `${assistant}:\n`;\n  return {\n    prompt: text,\n    stopSequences: [`\n${user}:`]\n  };\n}\n\n// src/openai-compatible-completion-language-model.ts\nvar OpenAICompatibleCompletionLanguageModel = class {\n  // type inferred via constructor\n  constructor(modelId, settings, config) {\n    this.specificationVersion = \"v1\";\n    this.defaultObjectGenerationMode = void 0;\n    var _a;\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n    const errorStructure = (_a = config.errorStructure) != null ? _a : defaultOpenAICompatibleErrorStructure;\n    this.chunkSchema = createOpenAICompatibleCompletionChunkSchema(\n      errorStructure.errorSchema\n    );\n    this.failedResponseHandler = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.createJsonErrorResponseHandler)(errorStructure);\n  }\n  get provider() {\n    return this.config.provider;\n  }\n  get providerOptionsName() {\n    return this.config.provider.split(\".\")[0].trim();\n  }\n  getArgs({\n    mode,\n    inputFormat,\n    prompt,\n    maxTokens,\n    temperature,\n    topP,\n    topK,\n    frequencyPenalty,\n    presencePenalty,\n    stopSequences: userStopSequences,\n    responseFormat,\n    seed,\n    providerMetadata\n  }) {\n    var _a;\n    const type = mode.type;\n    const warnings = [];\n    if (topK != null) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"topK\"\n      });\n    }\n    if (responseFormat != null && responseFormat.type !== \"text\") {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"responseFormat\",\n        details: \"JSON response format is not supported.\"\n      });\n    }\n    const { prompt: completionPrompt, stopSequences } = convertToOpenAICompatibleCompletionPrompt({ prompt, inputFormat });\n    const stop = [...stopSequences != null ? stopSequences : [], ...userStopSequences != null ? userStopSequences : []];\n    const baseArgs = {\n      // model id:\n      model: this.modelId,\n      // model specific settings:\n      echo: this.settings.echo,\n      logit_bias: this.settings.logitBias,\n      suffix: this.settings.suffix,\n      user: this.settings.user,\n      // standardized settings:\n      max_tokens: maxTokens,\n      temperature,\n      top_p: topP,\n      frequency_penalty: frequencyPenalty,\n      presence_penalty: presencePenalty,\n      seed,\n      ...providerMetadata == null ? void 0 : providerMetadata[this.providerOptionsName],\n      // prompt:\n      prompt: completionPrompt,\n      // stop sequences:\n      stop: stop.length > 0 ? stop : void 0\n    };\n    switch (type) {\n      case \"regular\": {\n        if ((_a = mode.tools) == null ? void 0 : _a.length) {\n          throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n            functionality: \"tools\"\n          });\n        }\n        if (mode.toolChoice) {\n          throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n            functionality: \"toolChoice\"\n          });\n        }\n        return { args: baseArgs, warnings };\n      }\n      case \"object-json\": {\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n          functionality: \"object-json mode\"\n        });\n      }\n      case \"object-tool\": {\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n          functionality: \"object-tool mode\"\n        });\n      }\n      default: {\n        const _exhaustiveCheck = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n  async doGenerate(options) {\n    var _a, _b, _c, _d;\n    const { args, warnings } = this.getArgs(options);\n    const { responseHeaders, value: response } = await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.postJsonToApi)({\n      url: this.config.url({\n        path: \"/completions\",\n        modelId: this.modelId\n      }),\n      headers: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.combineHeaders)(this.config.headers(), options.headers),\n      body: args,\n      failedResponseHandler: this.failedResponseHandler,\n      successfulResponseHandler: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.createJsonResponseHandler)(\n        openaiCompatibleCompletionResponseSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    const { prompt: rawPrompt, ...rawSettings } = args;\n    const choice = response.choices[0];\n    return {\n      text: choice.text,\n      usage: {\n        promptTokens: (_b = (_a = response.usage) == null ? void 0 : _a.prompt_tokens) != null ? _b : NaN,\n        completionTokens: (_d = (_c = response.usage) == null ? void 0 : _c.completion_tokens) != null ? _d : NaN\n      },\n      finishReason: mapOpenAICompatibleFinishReason(choice.finish_reason),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      response: getResponseMetadata(response),\n      warnings,\n      request: { body: JSON.stringify(args) }\n    };\n  }\n  async doStream(options) {\n    const { args, warnings } = this.getArgs(options);\n    const body = {\n      ...args,\n      stream: true\n    };\n    const { responseHeaders, value: response } = await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.postJsonToApi)({\n      url: this.config.url({\n        path: \"/completions\",\n        modelId: this.modelId\n      }),\n      headers: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.combineHeaders)(this.config.headers(), options.headers),\n      body,\n      failedResponseHandler: this.failedResponseHandler,\n      successfulResponseHandler: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.createEventSourceResponseHandler)(\n        this.chunkSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    const { prompt: rawPrompt, ...rawSettings } = args;\n    let finishReason = \"unknown\";\n    let usage = {\n      promptTokens: Number.NaN,\n      completionTokens: Number.NaN\n    };\n    let isFirstChunk = true;\n    return {\n      stream: response.pipeThrough(\n        new TransformStream({\n          transform(chunk, controller) {\n            if (!chunk.success) {\n              finishReason = \"error\";\n              controller.enqueue({ type: \"error\", error: chunk.error });\n              return;\n            }\n            const value = chunk.value;\n            if (\"error\" in value) {\n              finishReason = \"error\";\n              controller.enqueue({ type: \"error\", error: value.error });\n              return;\n            }\n            if (isFirstChunk) {\n              isFirstChunk = false;\n              controller.enqueue({\n                type: \"response-metadata\",\n                ...getResponseMetadata(value)\n              });\n            }\n            if (value.usage != null) {\n              usage = {\n                promptTokens: value.usage.prompt_tokens,\n                completionTokens: value.usage.completion_tokens\n              };\n            }\n            const choice = value.choices[0];\n            if ((choice == null ? void 0 : choice.finish_reason) != null) {\n              finishReason = mapOpenAICompatibleFinishReason(\n                choice.finish_reason\n              );\n            }\n            if ((choice == null ? void 0 : choice.text) != null) {\n              controller.enqueue({\n                type: \"text-delta\",\n                textDelta: choice.text\n              });\n            }\n          },\n          flush(controller) {\n            controller.enqueue({\n              type: \"finish\",\n              finishReason,\n              usage\n            });\n          }\n        })\n      ),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      warnings,\n      request: { body: JSON.stringify(body) }\n    };\n  }\n};\nvar openaiCompatibleCompletionResponseSchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n  id: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish(),\n  created: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().nullish(),\n  model: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish(),\n  choices: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(\n    zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n      text: zod__WEBPACK_IMPORTED_MODULE_2__.z.string(),\n      finish_reason: zod__WEBPACK_IMPORTED_MODULE_2__.z.string()\n    })\n  ),\n  usage: zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    prompt_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.z.number(),\n    completion_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.z.number()\n  }).nullish()\n});\nvar createOpenAICompatibleCompletionChunkSchema = (errorSchema) => zod__WEBPACK_IMPORTED_MODULE_2__.z.union([\n  zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    id: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish(),\n    created: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().nullish(),\n    model: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish(),\n    choices: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(\n      zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n        text: zod__WEBPACK_IMPORTED_MODULE_2__.z.string(),\n        finish_reason: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish(),\n        index: zod__WEBPACK_IMPORTED_MODULE_2__.z.number()\n      })\n    ),\n    usage: zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n      prompt_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.z.number(),\n      completion_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.z.number()\n    }).nullish()\n  }),\n  errorSchema\n]);\n\n// src/openai-compatible-embedding-model.ts\n\n\n\nvar OpenAICompatibleEmbeddingModel = class {\n  constructor(modelId, settings, config) {\n    this.specificationVersion = \"v1\";\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n  get provider() {\n    return this.config.provider;\n  }\n  get maxEmbeddingsPerCall() {\n    var _a;\n    return (_a = this.config.maxEmbeddingsPerCall) != null ? _a : 2048;\n  }\n  get supportsParallelCalls() {\n    var _a;\n    return (_a = this.config.supportsParallelCalls) != null ? _a : true;\n  }\n  async doEmbed({\n    values,\n    headers,\n    abortSignal\n  }) {\n    var _a;\n    if (values.length > this.maxEmbeddingsPerCall) {\n      throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TooManyEmbeddingValuesForCallError({\n        provider: this.provider,\n        modelId: this.modelId,\n        maxEmbeddingsPerCall: this.maxEmbeddingsPerCall,\n        values\n      });\n    }\n    const { responseHeaders, value: response } = await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.postJsonToApi)({\n      url: this.config.url({\n        path: \"/embeddings\",\n        modelId: this.modelId\n      }),\n      headers: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.combineHeaders)(this.config.headers(), headers),\n      body: {\n        model: this.modelId,\n        input: values,\n        encoding_format: \"float\",\n        dimensions: this.settings.dimensions,\n        user: this.settings.user\n      },\n      failedResponseHandler: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.createJsonErrorResponseHandler)(\n        (_a = this.config.errorStructure) != null ? _a : defaultOpenAICompatibleErrorStructure\n      ),\n      successfulResponseHandler: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.createJsonResponseHandler)(\n        openaiTextEmbeddingResponseSchema\n      ),\n      abortSignal,\n      fetch: this.config.fetch\n    });\n    return {\n      embeddings: response.data.map((item) => item.embedding),\n      usage: response.usage ? { tokens: response.usage.prompt_tokens } : void 0,\n      rawResponse: { headers: responseHeaders }\n    };\n  }\n};\nvar openaiTextEmbeddingResponseSchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n  data: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(zod__WEBPACK_IMPORTED_MODULE_2__.z.object({ embedding: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(zod__WEBPACK_IMPORTED_MODULE_2__.z.number()) })),\n  usage: zod__WEBPACK_IMPORTED_MODULE_2__.z.object({ prompt_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.z.number() }).nullish()\n});\n\n// src/openai-compatible-provider.ts\n\nfunction createOpenAICompatible(options) {\n  const baseURL = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.withoutTrailingSlash)(options.baseURL);\n  const providerName = options.name;\n  const getHeaders = () => ({\n    ...options.apiKey && { Authorization: `Bearer ${options.apiKey}` },\n    ...options.headers\n  });\n  const getCommonModelConfig = (modelType) => ({\n    provider: `${providerName}.${modelType}`,\n    url: ({ path }) => {\n      const url = new URL(`${baseURL}${path}`);\n      if (options.queryParams) {\n        url.search = new URLSearchParams(options.queryParams).toString();\n      }\n      return url.toString();\n    },\n    headers: getHeaders,\n    fetch: options.fetch\n  });\n  const createLanguageModel = (modelId, settings = {}) => createChatModel(modelId, settings);\n  const createChatModel = (modelId, settings = {}) => new OpenAICompatibleChatLanguageModel(modelId, settings, {\n    ...getCommonModelConfig(\"chat\"),\n    defaultObjectGenerationMode: \"tool\"\n  });\n  const createCompletionModel = (modelId, settings = {}) => new OpenAICompatibleCompletionLanguageModel(\n    modelId,\n    settings,\n    getCommonModelConfig(\"completion\")\n  );\n  const createEmbeddingModel = (modelId, settings = {}) => new OpenAICompatibleEmbeddingModel(\n    modelId,\n    settings,\n    getCommonModelConfig(\"embedding\")\n  );\n  const provider = (modelId, settings) => createLanguageModel(modelId, settings);\n  provider.languageModel = createLanguageModel;\n  provider.chatModel = createChatModel;\n  provider.completionModel = createCompletionModel;\n  provider.textEmbeddingModel = createEmbeddingModel;\n  return provider;\n}\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@ai-sdk+openai-compatible@0.1.10_zod@3.24.1/node_modules/@ai-sdk/openai-compatible/dist/index.mjs\n");

/***/ })

};
;