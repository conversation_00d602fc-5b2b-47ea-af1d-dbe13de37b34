"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/AiChat/chat/index.tsx":
/*!**********************************************!*\
  !*** ./src/components/AiChat/chat/index.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseChat: function() { return /* binding */ BaseChat; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ai_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ai/react */ \"(app-pages-browser)/./node_modules/.pnpm/ai@4.3.17_react@18.3.1_zod@3.24.1/node_modules/ai/react/dist/index.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _stores_chatSlice__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/stores/chatSlice */ \"(app-pages-browser)/./src/stores/chatSlice.ts\");\n/* harmony import */ var _stores_fileStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/stores/fileStore */ \"(app-pages-browser)/./src/stores/fileStore.ts\");\n/* harmony import */ var _utils_indexDB__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/indexDB */ \"(app-pages-browser)/./src/utils/indexDB.ts\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/.pnpm/uuid@11.0.5/node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _components_MessageItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/MessageItem */ \"(app-pages-browser)/./src/components/AiChat/chat/components/MessageItem.tsx\");\n/* harmony import */ var _components_ChatInput__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/ChatInput */ \"(app-pages-browser)/./src/components/AiChat/chat/components/ChatInput/index.tsx\");\n/* harmony import */ var _components_Tips__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./components/Tips */ \"(app-pages-browser)/./src/components/AiChat/chat/components/Tips.tsx\");\n/* harmony import */ var _utils_messageParser__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/messageParser */ \"(app-pages-browser)/./src/utils/messageParser.ts\");\n/* harmony import */ var _stores_userSlice__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/stores/userSlice */ \"(app-pages-browser)/./src/stores/userSlice.ts\");\n/* harmony import */ var _UserModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../UserModal */ \"(app-pages-browser)/./src/components/UserModal/index.tsx\");\n/* harmony import */ var _stores_chatModeSlice__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/stores/chatModeSlice */ \"(app-pages-browser)/./src/stores/chatModeSlice.ts\");\n/* __next_internal_client_entry_do_not_use__ BaseChat auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst BaseChat = (param)=>{\n    let { uuid: propUuid } = param;\n    _s();\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { otherConfig, uploadedImages, clearImages, addImages, removeImage } = (0,_stores_chatSlice__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    const [checkCount, setCheckCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [baseModal, setBaseModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        value: \"anthropic/claude-3.5-sonnet\",\n        label: \"Claude 3.5 Sonnet\",\n        useImage: true,\n        from: \"default\",\n        quota: 2,\n        functionCall: true\n    });\n    const { files } = (0,_stores_fileStore__WEBPACK_IMPORTED_MODULE_4__.useFileStore)();\n    const { user, token } = (0,_stores_userSlice__WEBPACK_IMPORTED_MODULE_10__[\"default\"])();\n    const { openModal } = (0,_UserModal__WEBPACK_IMPORTED_MODULE_11__.useLimitModalStore)();\n    const { mode } = (0,_stores_chatModeSlice__WEBPACK_IMPORTED_MODULE_12__[\"default\"])();\n    const [messages, setMessagesa] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const parseTimeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(Date.now());\n    const refUuidMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const chatUuid = propUuid || (0,uuid__WEBPACK_IMPORTED_MODULE_13__[\"default\"])();\n    const baseChatUrl =  true ? window.location.origin : 0;\n    const clearErrors = ()=>setErrors([]);\n    const scrollToBottom = ()=>{\n        setTimeout(()=>{\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }, 100);\n    };\n    // Parse messages and create files\n    const parseMessagesAndCreateFiles = async (messages)=>{\n        try {\n            await (0,_utils_messageParser__WEBPACK_IMPORTED_MODULE_9__.parseMessages)(messages);\n        } catch (error) {\n            console.error(\"Error parsing messages:\", error);\n        }\n    };\n    const updateFileSystemNow = ()=>{\n        // This would update the file system in desktop app\n        // For web, this is a no-op\n        return Promise.resolve();\n    };\n    const createMpIcon = (files)=>{\n    // This would create mini program icons in desktop app\n    // For web, this is a no-op\n    };\n    const checkExecList = ()=>{\n        // This would check execution list in desktop app\n        return [];\n    };\n    const checkFinish = ()=>{\n        // This would check if execution is finished in desktop app\n        return true;\n    };\n    const { messages: realMessages, input, handleInputChange, isLoading, setMessages, append, setInput, stop, reload } = (0,ai_react__WEBPACK_IMPORTED_MODULE_14__.useChat)({\n        api: \"\".concat(baseChatUrl, \"/api/chat\"),\n        headers: {\n            ...token && {\n                Authorization: \"Bearer \".concat(token)\n            }\n        },\n        body: {\n            model: baseModal.value,\n            mode: mode,\n            otherConfig: {\n                ...otherConfig,\n                extra: {\n                    ...otherConfig.extra,\n                    isBackEnd: otherConfig.isBackEnd,\n                    backendLanguage: otherConfig.backendLanguage\n                }\n            }\n        },\n        onFinish: async (message)=>{\n            try {\n                var _find_content, _find;\n                const initMessage = messages.filter((m)=>m.role === \"system\");\n                await _utils_indexDB__WEBPACK_IMPORTED_MODULE_5__.db.insert(chatUuid, {\n                    messages: [\n                        ...messages,\n                        ...initMessage,\n                        message\n                    ],\n                    title: ((_find = [\n                        ...initMessage,\n                        ...messages\n                    ].find((m)=>m.role === \"user\" && !m.content.includes(\"<boltArtifact\"))) === null || _find === void 0 ? void 0 : (_find_content = _find.content) === null || _find_content === void 0 ? void 0 : _find_content.slice(0, 50)) || \"New Chat\"\n                });\n            } catch (error) {\n                console.error(\"Failed to save chat history:\", error);\n            }\n            setCheckCount((checkCount)=>checkCount + 1);\n        },\n        onError: (error)=>{\n            var _error_errors_, _error_errors;\n            const msg = (error === null || error === void 0 ? void 0 : (_error_errors = error.errors) === null || _error_errors === void 0 ? void 0 : (_error_errors_ = _error_errors[0]) === null || _error_errors_ === void 0 ? void 0 : _error_errors_.responseBody) || String(error);\n            console.log(\"error\", error, msg);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(msg);\n            if (String(error).includes(\"Quota not enough\")) {\n                openModal(\"limit\");\n            }\n            if (String(error).includes(\"Authentication required\")) {\n                openModal(\"login\");\n            }\n        }\n    });\n    const filterMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return messages.filter((message)=>{\n            if (message.role === \"system\") return false;\n            return true;\n        });\n    }, [\n        messages\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (Date.now() - parseTimeRef.current > 200 && isLoading) {\n            setMessagesa(realMessages);\n            parseTimeRef.current = Date.now();\n            const needParseMessages = messages.filter((m)=>!refUuidMessages.current.includes(m.id));\n            console.log(\"\\uD83D\\uDCE8 Processing \".concat(needParseMessages.length, \" new messages:\"), needParseMessages.map((m)=>{\n                var _m_content;\n                return {\n                    id: m.id,\n                    role: m.role,\n                    contentLength: ((_m_content = m.content) === null || _m_content === void 0 ? void 0 : _m_content.length) || 0\n                };\n            }));\n            parseMessagesAndCreateFiles(needParseMessages);\n            // Update tracked message IDs\n            refUuidMessages.current = [\n                ...refUuidMessages.current,\n                ...needParseMessages.map((m)=>m.id)\n            ];\n            scrollToBottom();\n        }\n        if (errors.length > 0 && isLoading) {\n            clearErrors();\n        }\n        if (!isLoading) {\n            setMessagesa(realMessages);\n            createMpIcon(files);\n            // Parse messages when loading is complete\n            const needParseMessages = messages.filter((m)=>!refUuidMessages.current.includes(m.id));\n            if (needParseMessages.length > 0) {\n                console.log(\"\\uD83D\\uDCE8 Processing \".concat(needParseMessages.length, \" new messages (loading complete):\"), needParseMessages.map((m)=>{\n                    var _m_content;\n                    return {\n                        id: m.id,\n                        role: m.role,\n                        contentLength: ((_m_content = m.content) === null || _m_content === void 0 ? void 0 : _m_content.length) || 0\n                    };\n                }));\n                parseMessagesAndCreateFiles(needParseMessages);\n                // Update tracked message IDs\n                refUuidMessages.current = [\n                    ...refUuidMessages.current,\n                    ...needParseMessages.map((m)=>m.id)\n                ];\n            }\n        }\n    }, [\n        realMessages,\n        isLoading\n    ]);\n    const handleFileSelect = async (e)=>{\n        const files = Array.from(e.target.files || []);\n        if (files.length === 0) return;\n        setIsUploading(true);\n        try {\n            const newImages = files.map((file)=>({\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(),\n                    file,\n                    url: URL.createObjectURL(file),\n                    localUrl: URL.createObjectURL(file),\n                    status: \"done\"\n                }));\n            addImages(newImages);\n        } catch (error) {\n            console.error(\"File upload failed:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to upload files\");\n        } finally{\n            setIsUploading(false);\n        }\n    };\n    const handleSubmitWithFiles = async (e, text)=>{\n        e === null || e === void 0 ? void 0 : e.preventDefault();\n        if (!input.trim() && !(text === null || text === void 0 ? void 0 : text.trim()) || isLoading) return;\n        try {\n            const currentAttachments = uploadedImages.map((img)=>({\n                    id: img.id,\n                    name: img.id,\n                    type: img.file.type,\n                    localUrl: img.localUrl,\n                    contentType: img.file.type,\n                    url: img.url\n                }));\n            clearImages();\n            append({\n                role: \"user\",\n                content: text || input\n            }, {\n                experimental_attachments: currentAttachments\n            });\n            setInput(\"\");\n            setTimeout(()=>{\n                scrollToBottom();\n            }, 100);\n        } catch (error) {\n            console.error(\"Upload failed:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to upload files\");\n        }\n    };\n    const handleKeySubmit = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmitWithFiles(e);\n        }\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        const files = Array.from(e.dataTransfer.files);\n        if (files.length > 0) {\n            const fileInput = document.createElement(\"input\");\n            fileInput.type = \"file\";\n            fileInput.multiple = true;\n            fileInput.files = e.dataTransfer.files;\n            handleFileSelect({\n                target: fileInput\n            });\n        }\n    };\n    const handleScroll = ()=>{\n    // Handle scroll events if needed\n    };\n    const showJsx = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 overflow-y-auto px-1 py-2 message-container [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]\",\n            onScroll: handleScroll,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Tips__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    append: append,\n                    setInput: setInput,\n                    handleFileSelect: handleFileSelect\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                    lineNumber: 293,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-[640px] w-full mx-auto space-y-3\",\n                    children: [\n                        filterMessages.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MessageItem__WEBPACK_IMPORTED_MODULE_6__.MessageItem, {\n                                handleRetry: ()=>{\n                                    reload();\n                                },\n                                message: message,\n                                isEndMessage: filterMessages[filterMessages.length - 1].id === message.id,\n                                isLoading: isLoading,\n                                onUpdateMessage: (messageId, content)=>{\n                                    var _content_;\n                                    append({\n                                        role: \"user\",\n                                        content: \" \".concat(content === null || content === void 0 ? void 0 : (_content_ = content[0]) === null || _content_ === void 0 ? void 0 : _content_.text)\n                                    });\n                                }\n                            }, \"\".concat(message.id, \"-\").concat(index), false, {\n                                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 25\n                            }, undefined)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: messagesEndRef\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                    lineNumber: 298,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n            lineNumber: 289,\n            columnNumber: 13\n        }, undefined);\n    }, [\n        filterMessages,\n        isLoading,\n        append,\n        setInput,\n        handleFileSelect,\n        reload\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-full flex-col dark:bg-[#18181a] max-w-full\",\n        onDragOver: handleDragOver,\n        onDrop: handleDrop,\n        children: [\n            showJsx,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatInput__WEBPACK_IMPORTED_MODULE_7__.ChatInput, {\n                input: input,\n                setMessages: setMessages,\n                append: append,\n                messages: messages,\n                stopRuning: stop,\n                setInput: setInput,\n                isLoading: isLoading,\n                isUploading: isUploading,\n                uploadedImages: uploadedImages,\n                baseModal: baseModal,\n                handleInputChange: handleInputChange,\n                handleKeySubmit: handleKeySubmit,\n                handleSubmitWithFiles: handleSubmitWithFiles,\n                handleFileSelect: handleFileSelect,\n                removeImage: removeImage,\n                addImages: addImages,\n                setIsUploading: setIsUploading,\n                setBaseModal: setBaseModal\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                lineNumber: 331,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n        lineNumber: 325,\n        columnNumber: 9\n    }, undefined);\n};\n_s(BaseChat, \"XjpiMRuQUoOxvZGXxaMsIOhU7GA=\", false, function() {\n    return [\n        _stores_chatSlice__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        _stores_fileStore__WEBPACK_IMPORTED_MODULE_4__.useFileStore,\n        _stores_userSlice__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        _UserModal__WEBPACK_IMPORTED_MODULE_11__.useLimitModalStore,\n        _stores_chatModeSlice__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        ai_react__WEBPACK_IMPORTED_MODULE_14__.useChat\n    ];\n});\n_c = BaseChat;\nvar _c;\n$RefreshReg$(_c, \"BaseChat\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AiChat/chat/index.tsx\n"));

/***/ })

});