'use client';

import { FileText, Search, Terminal } from 'lucide-react';

interface ActivityBarProps {
  activeView: "files" | "search";
  onViewChange: (view: "files" | "search") => void;
  onToggleTerminal: () => void;
  showTerminal: boolean;
}

export function ActivityBar({
  activeView,
  onViewChange,
  onToggleTerminal,
  showTerminal,
}: ActivityBarProps) {
  return (
    <div className="w-12 bg-[#f8f8f8] dark:bg-[#1e1e1e] border-r border-[#e4e4e4] dark:border-[#333] flex flex-col items-center py-2">
      <button
        onClick={() => onViewChange("files")}
        className={`
          w-10 h-10 flex items-center justify-center rounded-lg mb-1 transition-colors
          ${activeView === "files" 
            ? "bg-[#e4e4e4] dark:bg-[#333] text-[#333] dark:text-white" 
            : "text-[#666] dark:text-gray-400 hover:bg-[#e4e4e4] dark:hover:bg-[#333]"
          }
        `}
        title="Explorer"
      >
        <FileText size={20} />
      </button>
      
      <button
        onClick={() => onViewChange("search")}
        className={`
          w-10 h-10 flex items-center justify-center rounded-lg mb-1 transition-colors
          ${activeView === "search" 
            ? "bg-[#e4e4e4] dark:bg-[#333] text-[#333] dark:text-white" 
            : "text-[#666] dark:text-gray-400 hover:bg-[#e4e4e4] dark:hover:bg-[#333]"
          }
        `}
        title="Search"
      >
        <Search size={20} />
      </button>

      <div className="flex-1" />

      <button
        onClick={onToggleTerminal}
        className={`
          w-10 h-10 flex items-center justify-center rounded-lg transition-colors
          ${showTerminal 
            ? "bg-[#e4e4e4] dark:bg-[#333] text-[#333] dark:text-white" 
            : "text-[#666] dark:text-gray-400 hover:bg-[#e4e4e4] dark:hover:bg-[#333]"
          }
        `}
        title="Terminal"
      >
        <Terminal size={20} />
      </button>
    </div>
  );
}
