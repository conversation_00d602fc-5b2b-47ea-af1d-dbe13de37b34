'use client';

import { useEffect, useRef, useState } from 'react';
import { useFileStore } from '@/stores/fileStore';
import { useEditorStore } from '@/stores/editorStore';
import useThemeStore from '@/stores/themeSlice';

interface EditorProps {
  fileName: string;
  initialLine?: number;
}

export function Editor({ fileName, initialLine }: EditorProps) {
  const { getContent, updateContent } = useFileStore();
  const { setDirty } = useEditorStore();
  const { isDarkMode } = useThemeStore();
  const [content, setContent] = useState('');

  useEffect(() => {
    const fileContent = getContent(fileName);
    setContent(fileContent);
  }, [fileName, getContent]);

  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newContent = e.target.value;
    setContent(newContent);
    updateContent(fileName, newContent);
    setDirty(fileName, true);
  };

  const getFileExtension = (fileName: string) => {
    return fileName.split('.').pop()?.toLowerCase() || '';
  };

  const getLanguageFromExtension = (extension: string) => {
    switch (extension) {
      case 'js':
      case 'jsx':
        return 'javascript';
      case 'ts':
      case 'tsx':
        return 'typescript';
      case 'html':
      case 'htm':
        return 'html';
      case 'css':
      case 'scss':
      case 'sass':
        return 'css';
      case 'json':
        return 'json';
      case 'md':
      case 'markdown':
        return 'markdown';
      case 'py':
        return 'python';
      default:
        return 'text';
    }
  };

  const extension = getFileExtension(fileName);
  const language = getLanguageFromExtension(extension);

  return (
    <div className="h-full w-full bg-white dark:bg-[#18181a] flex flex-col">
      <div className="flex items-center justify-between px-4 py-2 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-gray-900 dark:text-white">
            {fileName}
          </span>
          <span className="text-xs text-gray-500 dark:text-gray-400 bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded">
            {language}
          </span>
        </div>
        <div className="text-xs text-gray-500 dark:text-gray-400">
          Lines: {content.split('\n').length} | Characters: {content.length}
        </div>
      </div>

      <textarea
        value={content}
        onChange={handleContentChange}
        className={`
          flex-1 w-full p-4 font-mono text-sm resize-none outline-none
          ${isDarkMode
            ? 'bg-[#18181a] text-gray-100 placeholder-gray-500'
            : 'bg-white text-gray-900 placeholder-gray-400'
          }
        `}
        placeholder={`Start editing ${fileName}...`}
        spellCheck={false}
        style={{
          tabSize: 2,
          lineHeight: '1.5',
        }}
      />
    </div>
  );
}
