"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/AiChat/chat/index.tsx":
/*!**********************************************!*\
  !*** ./src/components/AiChat/chat/index.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseChat: function() { return /* binding */ BaseChat; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ai_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ai/react */ \"(app-pages-browser)/./node_modules/.pnpm/ai@4.3.17_react@18.3.1_zod@3.24.1/node_modules/ai/react/dist/index.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _stores_chatSlice__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/stores/chatSlice */ \"(app-pages-browser)/./src/stores/chatSlice.ts\");\n/* harmony import */ var _stores_fileStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/stores/fileStore */ \"(app-pages-browser)/./src/stores/fileStore.ts\");\n/* harmony import */ var _utils_indexDB__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/indexDB */ \"(app-pages-browser)/./src/utils/indexDB.ts\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/.pnpm/uuid@11.0.5/node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _components_MessageItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/MessageItem */ \"(app-pages-browser)/./src/components/AiChat/chat/components/MessageItem.tsx\");\n/* harmony import */ var _components_ChatInput__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/ChatInput */ \"(app-pages-browser)/./src/components/AiChat/chat/components/ChatInput/index.tsx\");\n/* harmony import */ var _components_Tips__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./components/Tips */ \"(app-pages-browser)/./src/components/AiChat/chat/components/Tips.tsx\");\n/* harmony import */ var _utils_messageParser__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/messageParser */ \"(app-pages-browser)/./src/utils/messageParser.ts\");\n/* harmony import */ var _stores_userSlice__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/stores/userSlice */ \"(app-pages-browser)/./src/stores/userSlice.ts\");\n/* harmony import */ var _UserModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../UserModal */ \"(app-pages-browser)/./src/components/UserModal/index.tsx\");\n/* harmony import */ var _stores_chatModeSlice__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/stores/chatModeSlice */ \"(app-pages-browser)/./src/stores/chatModeSlice.ts\");\n/* __next_internal_client_entry_do_not_use__ BaseChat auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst BaseChat = (param)=>{\n    let { uuid: propUuid } = param;\n    _s();\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { otherConfig, uploadedImages, clearImages, addImages, removeImage } = (0,_stores_chatSlice__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    const [checkCount, setCheckCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [baseModal, setBaseModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        value: \"anthropic/claude-3.5-sonnet\",\n        label: \"Claude 3.5 Sonnet\",\n        useImage: true,\n        from: \"default\",\n        quota: 2,\n        functionCall: true\n    });\n    const { files } = (0,_stores_fileStore__WEBPACK_IMPORTED_MODULE_4__.useFileStore)();\n    const { user, token } = (0,_stores_userSlice__WEBPACK_IMPORTED_MODULE_10__[\"default\"])();\n    const { openModal } = (0,_UserModal__WEBPACK_IMPORTED_MODULE_11__.useLimitModalStore)();\n    const { mode } = (0,_stores_chatModeSlice__WEBPACK_IMPORTED_MODULE_12__[\"default\"])();\n    const [messages, setMessagesa] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const parseTimeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(Date.now());\n    const refUuidMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const chatUuid = propUuid || (0,uuid__WEBPACK_IMPORTED_MODULE_13__[\"default\"])();\n    const baseChatUrl =  true ? window.location.origin : 0;\n    const clearErrors = ()=>setErrors([]);\n    const scrollToBottom = ()=>{\n        setTimeout(()=>{\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }, 100);\n    };\n    // Parse messages and create files\n    const parseMessagesAndCreateFiles = async (messages)=>{\n        try {\n            await (0,_utils_messageParser__WEBPACK_IMPORTED_MODULE_9__.parseMessages)(messages);\n        } catch (error) {\n            console.error(\"Error parsing messages:\", error);\n        }\n    };\n    const updateFileSystemNow = ()=>{\n        // This would update the file system in desktop app\n        // For web, this is a no-op\n        return Promise.resolve();\n    };\n    const createMpIcon = (files)=>{\n    // This would create mini program icons in desktop app\n    // For web, this is a no-op\n    };\n    const checkExecList = ()=>{\n        // This would check execution list in desktop app\n        return [];\n    };\n    const checkFinish = ()=>{\n        // This would check if execution is finished in desktop app\n        return true;\n    };\n    const { messages: realMessages, input, handleInputChange, isLoading, setMessages, append, setInput, stop, reload } = (0,ai_react__WEBPACK_IMPORTED_MODULE_14__.useChat)({\n        api: \"\".concat(baseChatUrl, \"/api/chat\"),\n        headers: {\n            ...token && {\n                Authorization: \"Bearer \".concat(token)\n            }\n        },\n        body: {\n            model: baseModal.value,\n            mode: mode,\n            otherConfig: {\n                ...otherConfig,\n                extra: {\n                    ...otherConfig.extra,\n                    isBackEnd: otherConfig.isBackEnd,\n                    backendLanguage: otherConfig.backendLanguage\n                }\n            }\n        },\n        onFinish: async (message)=>{\n            try {\n                var _find_content, _find;\n                const initMessage = messages.filter((m)=>m.role === \"system\");\n                await _utils_indexDB__WEBPACK_IMPORTED_MODULE_5__.db.insert(chatUuid, {\n                    messages: [\n                        ...messages,\n                        ...initMessage,\n                        message\n                    ],\n                    title: ((_find = [\n                        ...initMessage,\n                        ...messages\n                    ].find((m)=>m.role === \"user\" && !m.content.includes(\"<boltArtifact\"))) === null || _find === void 0 ? void 0 : (_find_content = _find.content) === null || _find_content === void 0 ? void 0 : _find_content.slice(0, 50)) || \"New Chat\"\n                });\n            } catch (error) {\n                console.error(\"Failed to save chat history:\", error);\n            }\n            setCheckCount((checkCount)=>checkCount + 1);\n        },\n        onError: (error)=>{\n            var _error_errors_, _error_errors;\n            const msg = (error === null || error === void 0 ? void 0 : (_error_errors = error.errors) === null || _error_errors === void 0 ? void 0 : (_error_errors_ = _error_errors[0]) === null || _error_errors_ === void 0 ? void 0 : _error_errors_.responseBody) || String(error);\n            console.log(\"error\", error, msg);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(msg);\n            if (String(error).includes(\"Quota not enough\")) {\n                openModal(\"limit\");\n            }\n            if (String(error).includes(\"Authentication required\")) {\n                openModal(\"login\");\n            }\n        }\n    });\n    const filterMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return messages.filter((message)=>{\n            if (message.role === \"system\") return false;\n            return true;\n        });\n    }, [\n        messages\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (Date.now() - parseTimeRef.current > 200 && isLoading) {\n            setMessagesa(realMessages);\n            parseTimeRef.current = Date.now();\n            const needParseMessages = messages.filter((m)=>!refUuidMessages.current.includes(m.id));\n            (0,_utils_messageParser__WEBPACK_IMPORTED_MODULE_9__.parseMessages)(needParseMessages);\n            scrollToBottom();\n        }\n        if (errors.length > 0 && isLoading) {\n            clearErrors();\n        }\n        if (!isLoading) {\n            setMessagesa(realMessages);\n            createMpIcon(files);\n        }\n    }, [\n        realMessages,\n        isLoading\n    ]);\n    const handleFileSelect = async (e)=>{\n        const files = Array.from(e.target.files || []);\n        if (files.length === 0) return;\n        setIsUploading(true);\n        try {\n            const newImages = files.map((file)=>({\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(),\n                    file,\n                    url: URL.createObjectURL(file),\n                    localUrl: URL.createObjectURL(file),\n                    status: \"done\"\n                }));\n            addImages(newImages);\n        } catch (error) {\n            console.error(\"File upload failed:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to upload files\");\n        } finally{\n            setIsUploading(false);\n        }\n    };\n    const handleSubmitWithFiles = async (e, text)=>{\n        e === null || e === void 0 ? void 0 : e.preventDefault();\n        if (!input.trim() && !(text === null || text === void 0 ? void 0 : text.trim()) || isLoading) return;\n        try {\n            const currentAttachments = uploadedImages.map((img)=>({\n                    id: img.id,\n                    name: img.id,\n                    type: img.file.type,\n                    localUrl: img.localUrl,\n                    contentType: img.file.type,\n                    url: img.url\n                }));\n            clearImages();\n            append({\n                role: \"user\",\n                content: text || input\n            }, {\n                experimental_attachments: currentAttachments\n            });\n            setInput(\"\");\n            setTimeout(()=>{\n                scrollToBottom();\n            }, 100);\n        } catch (error) {\n            console.error(\"Upload failed:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to upload files\");\n        }\n    };\n    const handleKeySubmit = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmitWithFiles(e);\n        }\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        const files = Array.from(e.dataTransfer.files);\n        if (files.length > 0) {\n            const fileInput = document.createElement(\"input\");\n            fileInput.type = \"file\";\n            fileInput.multiple = true;\n            fileInput.files = e.dataTransfer.files;\n            handleFileSelect({\n                target: fileInput\n            });\n        }\n    };\n    const handleScroll = ()=>{\n    // Handle scroll events if needed\n    };\n    const showJsx = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 overflow-y-auto px-1 py-2 message-container [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]\",\n            onScroll: handleScroll,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Tips__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    append: append,\n                    setInput: setInput,\n                    handleFileSelect: handleFileSelect\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                    lineNumber: 273,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-[640px] w-full mx-auto space-y-3\",\n                    children: [\n                        filterMessages.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MessageItem__WEBPACK_IMPORTED_MODULE_6__.MessageItem, {\n                                handleRetry: ()=>{\n                                    reload();\n                                },\n                                message: message,\n                                isEndMessage: filterMessages[filterMessages.length - 1].id === message.id,\n                                isLoading: isLoading,\n                                onUpdateMessage: (messageId, content)=>{\n                                    var _content_;\n                                    append({\n                                        role: \"user\",\n                                        content: \" \".concat(content === null || content === void 0 ? void 0 : (_content_ = content[0]) === null || _content_ === void 0 ? void 0 : _content_.text)\n                                    });\n                                }\n                            }, \"\".concat(message.id, \"-\").concat(index), false, {\n                                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 25\n                            }, undefined)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: messagesEndRef\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n            lineNumber: 269,\n            columnNumber: 13\n        }, undefined);\n    }, [\n        filterMessages,\n        isLoading,\n        append,\n        setInput,\n        handleFileSelect,\n        reload\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-full flex-col dark:bg-[#18181a] max-w-full\",\n        onDragOver: handleDragOver,\n        onDrop: handleDrop,\n        children: [\n            showJsx,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatInput__WEBPACK_IMPORTED_MODULE_7__.ChatInput, {\n                input: input,\n                setMessages: setMessages,\n                append: append,\n                messages: messages,\n                stopRuning: stop,\n                setInput: setInput,\n                isLoading: isLoading,\n                isUploading: isUploading,\n                uploadedImages: uploadedImages,\n                baseModal: baseModal,\n                handleInputChange: handleInputChange,\n                handleKeySubmit: handleKeySubmit,\n                handleSubmitWithFiles: handleSubmitWithFiles,\n                handleFileSelect: handleFileSelect,\n                removeImage: removeImage,\n                addImages: addImages,\n                setIsUploading: setIsUploading,\n                setBaseModal: setBaseModal\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                lineNumber: 311,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n        lineNumber: 305,\n        columnNumber: 9\n    }, undefined);\n};\n_s(BaseChat, \"XjpiMRuQUoOxvZGXxaMsIOhU7GA=\", false, function() {\n    return [\n        _stores_chatSlice__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        _stores_fileStore__WEBPACK_IMPORTED_MODULE_4__.useFileStore,\n        _stores_userSlice__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        _UserModal__WEBPACK_IMPORTED_MODULE_11__.useLimitModalStore,\n        _stores_chatModeSlice__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        ai_react__WEBPACK_IMPORTED_MODULE_14__.useChat\n    ];\n});\n_c = BaseChat;\nvar _c;\n$RefreshReg$(_c, \"BaseChat\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AiChat/chat/index.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/messageParser.ts":
/*!************************************!*\
  !*** ./src/utils/messageParser.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StreamingMessageParser: function() { return /* binding */ StreamingMessageParser; },\n/* harmony export */   parseMessageForFiles: function() { return /* binding */ parseMessageForFiles; },\n/* harmony export */   parseMessages: function() { return /* binding */ parseMessages; }\n/* harmony export */ });\n/* harmony import */ var _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/stores/fileStore */ \"(app-pages-browser)/./src/stores/fileStore.ts\");\n/* __next_internal_client_entry_do_not_use__ StreamingMessageParser,parseMessages,parseMessageForFiles auto */ \nclass StreamingMessageParser {\n    parse(messageId, content) {\n        // Parse boltArtifact tags\n        const artifactRegex = /<boltArtifact[^>]*>([\\s\\S]*?)<\\/boltArtifact>/g;\n        let artifactMatch;\n        while((artifactMatch = artifactRegex.exec(content)) !== null){\n            const artifactContent = artifactMatch[1];\n            this.parseActions(artifactContent);\n        }\n    }\n    parseActions(content) {\n        // Parse boltAction tags\n        const actionRegex = /<boltAction\\s+type=\"([^\"]+)\"(?:\\s+filePath=\"([^\"]+)\")?\\s*>([\\s\\S]*?)<\\/boltAction>/g;\n        let actionMatch;\n        while((actionMatch = actionRegex.exec(content)) !== null){\n            const [, type, filePath, actionContent] = actionMatch;\n            switch(type){\n                case \"file\":\n                    if (filePath && this.callbacks.onFileAction) {\n                        this.callbacks.onFileAction({\n                            type: \"file\",\n                            filePath,\n                            content: actionContent.trim()\n                        });\n                    }\n                    break;\n                case \"shell\":\n                    if (this.callbacks.onShellAction) {\n                        this.callbacks.onShellAction({\n                            type: \"shell\",\n                            content: actionContent.trim()\n                        });\n                    }\n                    break;\n                case \"start\":\n                    if (this.callbacks.onStartAction) {\n                        this.callbacks.onStartAction({\n                            type: \"start\",\n                            content: actionContent.trim()\n                        });\n                    }\n                    break;\n            }\n        }\n    }\n    constructor(callbacks = {}){\n        this.callbacks = callbacks;\n    }\n}\n// Create a global message parser instance\nconst messageParser = new StreamingMessageParser({\n    onFileAction: async (action)=>{\n        const { addFile } = _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__.useFileStore.getState();\n        try {\n            await addFile(action.filePath, action.content);\n            console.log(\"Created/updated file: \".concat(action.filePath));\n        } catch (error) {\n            console.error(\"Failed to create file \".concat(action.filePath, \":\"), error);\n        }\n    },\n    onShellAction: (action)=>{\n        console.log(\"Shell command:\", action.content);\n    // TODO: Integrate with terminal store to execute commands\n    },\n    onStartAction: (action)=>{\n        console.log(\"Start command:\", action.content);\n    // TODO: Integrate with terminal store to execute start commands\n    }\n});\nconst parseMessages = async (messages)=>{\n    for (const message of messages){\n        if (message.role === \"assistant\") {\n            messageParser.parse(message.id, message.content);\n        }\n    }\n};\n// Simple function to extract files from message content\nfunction parseMessageForFiles(content) {\n    const files = {};\n    // Parse boltArtifact and boltAction tags\n    const artifactRegex = /<boltArtifact[^>]*>([\\s\\S]*?)<\\/boltArtifact>/g;\n    let artifactMatch;\n    while((artifactMatch = artifactRegex.exec(content)) !== null){\n        const artifactContent = artifactMatch[1];\n        // Extract file actions\n        const fileActionRegex = /<boltAction\\s+type=\"file\"\\s+filePath=\"([^\"]+)\"\\s*>([\\s\\S]*?)<\\/boltAction>/g;\n        let fileMatch;\n        while((fileMatch = fileActionRegex.exec(artifactContent)) !== null){\n            const [, filePath, fileContent] = fileMatch;\n            files[filePath] = fileContent.trim();\n        }\n    }\n    return files;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy91dGlscy9tZXNzYWdlUGFyc2VyLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7K0dBRWtEO0FBOEIzQyxNQUFNQztJQU9YQyxNQUFNQyxTQUFpQixFQUFFQyxPQUFlLEVBQUU7UUFDeEMsMEJBQTBCO1FBQzFCLE1BQU1DLGdCQUFnQjtRQUN0QixJQUFJQztRQUVKLE1BQU8sQ0FBQ0EsZ0JBQWdCRCxjQUFjRSxJQUFJLENBQUNILFFBQU8sTUFBTyxLQUFNO1lBQzdELE1BQU1JLGtCQUFrQkYsYUFBYSxDQUFDLEVBQUU7WUFDeEMsSUFBSSxDQUFDRyxZQUFZLENBQUNEO1FBQ3BCO0lBQ0Y7SUFFUUMsYUFBYUwsT0FBZSxFQUFFO1FBQ3BDLHdCQUF3QjtRQUN4QixNQUFNTSxjQUFjO1FBQ3BCLElBQUlDO1FBRUosTUFBTyxDQUFDQSxjQUFjRCxZQUFZSCxJQUFJLENBQUNILFFBQU8sTUFBTyxLQUFNO1lBQ3pELE1BQU0sR0FBR1EsTUFBTUMsVUFBVUMsY0FBYyxHQUFHSDtZQUUxQyxPQUFRQztnQkFDTixLQUFLO29CQUNILElBQUlDLFlBQVksSUFBSSxDQUFDRSxTQUFTLENBQUNDLFlBQVksRUFBRTt3QkFDM0MsSUFBSSxDQUFDRCxTQUFTLENBQUNDLFlBQVksQ0FBQzs0QkFDMUJKLE1BQU07NEJBQ05DOzRCQUNBVCxTQUFTVSxjQUFjRyxJQUFJO3dCQUM3QjtvQkFDRjtvQkFDQTtnQkFDRixLQUFLO29CQUNILElBQUksSUFBSSxDQUFDRixTQUFTLENBQUNHLGFBQWEsRUFBRTt3QkFDaEMsSUFBSSxDQUFDSCxTQUFTLENBQUNHLGFBQWEsQ0FBQzs0QkFDM0JOLE1BQU07NEJBQ05SLFNBQVNVLGNBQWNHLElBQUk7d0JBQzdCO29CQUNGO29CQUNBO2dCQUNGLEtBQUs7b0JBQ0gsSUFBSSxJQUFJLENBQUNGLFNBQVMsQ0FBQ0ksYUFBYSxFQUFFO3dCQUNoQyxJQUFJLENBQUNKLFNBQVMsQ0FBQ0ksYUFBYSxDQUFDOzRCQUMzQlAsTUFBTTs0QkFDTlIsU0FBU1UsY0FBY0csSUFBSTt3QkFDN0I7b0JBQ0Y7b0JBQ0E7WUFDSjtRQUNGO0lBQ0Y7SUFuREFHLFlBQVlMLFlBQTZCLENBQUMsQ0FBQyxDQUFFO1FBQzNDLElBQUksQ0FBQ0EsU0FBUyxHQUFHQTtJQUNuQjtBQWtERjtBQUVBLDBDQUEwQztBQUMxQyxNQUFNTSxnQkFBZ0IsSUFBSXBCLHVCQUF1QjtJQUMvQ2UsY0FBYyxPQUFPTTtRQUNuQixNQUFNLEVBQUVDLE9BQU8sRUFBRSxHQUFHdkIsMkRBQVlBLENBQUN3QixRQUFRO1FBQ3pDLElBQUk7WUFDRixNQUFNRCxRQUFRRCxPQUFPVCxRQUFRLEVBQUVTLE9BQU9sQixPQUFPO1lBQzdDcUIsUUFBUUMsR0FBRyxDQUFDLHlCQUF5QyxPQUFoQkosT0FBT1QsUUFBUTtRQUN0RCxFQUFFLE9BQU9jLE9BQU87WUFDZEYsUUFBUUUsS0FBSyxDQUFDLHlCQUF5QyxPQUFoQkwsT0FBT1QsUUFBUSxFQUFDLE1BQUljO1FBQzdEO0lBQ0Y7SUFDQVQsZUFBZSxDQUFDSTtRQUNkRyxRQUFRQyxHQUFHLENBQUMsa0JBQWtCSixPQUFPbEIsT0FBTztJQUM1QywwREFBMEQ7SUFDNUQ7SUFDQWUsZUFBZSxDQUFDRztRQUNkRyxRQUFRQyxHQUFHLENBQUMsa0JBQWtCSixPQUFPbEIsT0FBTztJQUM1QyxnRUFBZ0U7SUFDbEU7QUFDRjtBQUVPLE1BQU13QixnQkFBZ0IsT0FBT0M7SUFDbEMsS0FBSyxNQUFNQyxXQUFXRCxTQUFVO1FBQzlCLElBQUlDLFFBQVFDLElBQUksS0FBSyxhQUFhO1lBQ2hDVixjQUFjbkIsS0FBSyxDQUFDNEIsUUFBUUUsRUFBRSxFQUFFRixRQUFRMUIsT0FBTztRQUNqRDtJQUNGO0FBQ0YsRUFBRTtBQUVGLHdEQUF3RDtBQUNqRCxTQUFTNkIscUJBQXFCN0IsT0FBZTtJQUNsRCxNQUFNOEIsUUFBZ0MsQ0FBQztJQUV2Qyx5Q0FBeUM7SUFDekMsTUFBTTdCLGdCQUFnQjtJQUN0QixJQUFJQztJQUVKLE1BQU8sQ0FBQ0EsZ0JBQWdCRCxjQUFjRSxJQUFJLENBQUNILFFBQU8sTUFBTyxLQUFNO1FBQzdELE1BQU1JLGtCQUFrQkYsYUFBYSxDQUFDLEVBQUU7UUFFeEMsdUJBQXVCO1FBQ3ZCLE1BQU02QixrQkFBa0I7UUFDeEIsSUFBSUM7UUFFSixNQUFPLENBQUNBLFlBQVlELGdCQUFnQjVCLElBQUksQ0FBQ0MsZ0JBQWUsTUFBTyxLQUFNO1lBQ25FLE1BQU0sR0FBR0ssVUFBVXdCLFlBQVksR0FBR0Q7WUFDbENGLEtBQUssQ0FBQ3JCLFNBQVMsR0FBR3dCLFlBQVlwQixJQUFJO1FBQ3BDO0lBQ0Y7SUFFQSxPQUFPaUI7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvdXRpbHMvbWVzc2FnZVBhcnNlci50cz9jNjEyIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlRmlsZVN0b3JlIH0gZnJvbSAnQC9zdG9yZXMvZmlsZVN0b3JlJztcbmltcG9ydCB7IE1lc3NhZ2UgfSBmcm9tICdhaS9yZWFjdCc7XG5cbmV4cG9ydCB0eXBlIEFjdGlvblR5cGUgPSAnZmlsZScgfCAnc2hlbGwnIHwgJ3N0YXJ0JztcblxuZXhwb3J0IGludGVyZmFjZSBCYXNlQWN0aW9uIHtcbiAgY29udGVudDogc3RyaW5nO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIEZpbGVBY3Rpb24gZXh0ZW5kcyBCYXNlQWN0aW9uIHtcbiAgdHlwZTogJ2ZpbGUnO1xuICBmaWxlUGF0aDogc3RyaW5nO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFNoZWxsQWN0aW9uIGV4dGVuZHMgQmFzZUFjdGlvbiB7XG4gIHR5cGU6ICdzaGVsbCc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgU3RhcnRBY3Rpb24gZXh0ZW5kcyBCYXNlQWN0aW9uIHtcbiAgdHlwZTogJ3N0YXJ0Jztcbn1cblxuZXhwb3J0IHR5cGUgQm9sdEFjdGlvbiA9IEZpbGVBY3Rpb24gfCBTaGVsbEFjdGlvbiB8IFN0YXJ0QWN0aW9uO1xuXG5pbnRlcmZhY2UgUGFyc2VyQ2FsbGJhY2tzIHtcbiAgb25GaWxlQWN0aW9uPzogKGFjdGlvbjogRmlsZUFjdGlvbikgPT4gdm9pZDtcbiAgb25TaGVsbEFjdGlvbj86IChhY3Rpb246IFNoZWxsQWN0aW9uKSA9PiB2b2lkO1xuICBvblN0YXJ0QWN0aW9uPzogKGFjdGlvbjogU3RhcnRBY3Rpb24pID0+IHZvaWQ7XG59XG5cbmV4cG9ydCBjbGFzcyBTdHJlYW1pbmdNZXNzYWdlUGFyc2VyIHtcbiAgcHJpdmF0ZSBjYWxsYmFja3M6IFBhcnNlckNhbGxiYWNrcztcblxuICBjb25zdHJ1Y3RvcihjYWxsYmFja3M6IFBhcnNlckNhbGxiYWNrcyA9IHt9KSB7XG4gICAgdGhpcy5jYWxsYmFja3MgPSBjYWxsYmFja3M7XG4gIH1cblxuICBwYXJzZShtZXNzYWdlSWQ6IHN0cmluZywgY29udGVudDogc3RyaW5nKSB7XG4gICAgLy8gUGFyc2UgYm9sdEFydGlmYWN0IHRhZ3NcbiAgICBjb25zdCBhcnRpZmFjdFJlZ2V4ID0gLzxib2x0QXJ0aWZhY3RbXj5dKj4oW1xcc1xcU10qPyk8XFwvYm9sdEFydGlmYWN0Pi9nO1xuICAgIGxldCBhcnRpZmFjdE1hdGNoO1xuXG4gICAgd2hpbGUgKChhcnRpZmFjdE1hdGNoID0gYXJ0aWZhY3RSZWdleC5leGVjKGNvbnRlbnQpKSAhPT0gbnVsbCkge1xuICAgICAgY29uc3QgYXJ0aWZhY3RDb250ZW50ID0gYXJ0aWZhY3RNYXRjaFsxXTtcbiAgICAgIHRoaXMucGFyc2VBY3Rpb25zKGFydGlmYWN0Q29udGVudCk7XG4gICAgfVxuICB9XG5cbiAgcHJpdmF0ZSBwYXJzZUFjdGlvbnMoY29udGVudDogc3RyaW5nKSB7XG4gICAgLy8gUGFyc2UgYm9sdEFjdGlvbiB0YWdzXG4gICAgY29uc3QgYWN0aW9uUmVnZXggPSAvPGJvbHRBY3Rpb25cXHMrdHlwZT1cIihbXlwiXSspXCIoPzpcXHMrZmlsZVBhdGg9XCIoW15cIl0rKVwiKT9cXHMqPihbXFxzXFxTXSo/KTxcXC9ib2x0QWN0aW9uPi9nO1xuICAgIGxldCBhY3Rpb25NYXRjaDtcblxuICAgIHdoaWxlICgoYWN0aW9uTWF0Y2ggPSBhY3Rpb25SZWdleC5leGVjKGNvbnRlbnQpKSAhPT0gbnVsbCkge1xuICAgICAgY29uc3QgWywgdHlwZSwgZmlsZVBhdGgsIGFjdGlvbkNvbnRlbnRdID0gYWN0aW9uTWF0Y2g7XG4gICAgICBcbiAgICAgIHN3aXRjaCAodHlwZSBhcyBBY3Rpb25UeXBlKSB7XG4gICAgICAgIGNhc2UgJ2ZpbGUnOlxuICAgICAgICAgIGlmIChmaWxlUGF0aCAmJiB0aGlzLmNhbGxiYWNrcy5vbkZpbGVBY3Rpb24pIHtcbiAgICAgICAgICAgIHRoaXMuY2FsbGJhY2tzLm9uRmlsZUFjdGlvbih7XG4gICAgICAgICAgICAgIHR5cGU6ICdmaWxlJyxcbiAgICAgICAgICAgICAgZmlsZVBhdGgsXG4gICAgICAgICAgICAgIGNvbnRlbnQ6IGFjdGlvbkNvbnRlbnQudHJpbSgpLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgfVxuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlICdzaGVsbCc6XG4gICAgICAgICAgaWYgKHRoaXMuY2FsbGJhY2tzLm9uU2hlbGxBY3Rpb24pIHtcbiAgICAgICAgICAgIHRoaXMuY2FsbGJhY2tzLm9uU2hlbGxBY3Rpb24oe1xuICAgICAgICAgICAgICB0eXBlOiAnc2hlbGwnLFxuICAgICAgICAgICAgICBjb250ZW50OiBhY3Rpb25Db250ZW50LnRyaW0oKSxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgIH1cbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAnc3RhcnQnOlxuICAgICAgICAgIGlmICh0aGlzLmNhbGxiYWNrcy5vblN0YXJ0QWN0aW9uKSB7XG4gICAgICAgICAgICB0aGlzLmNhbGxiYWNrcy5vblN0YXJ0QWN0aW9uKHtcbiAgICAgICAgICAgICAgdHlwZTogJ3N0YXJ0JyxcbiAgICAgICAgICAgICAgY29udGVudDogYWN0aW9uQ29udGVudC50cmltKCksXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICB9XG4gICAgICAgICAgYnJlYWs7XG4gICAgICB9XG4gICAgfVxuICB9XG59XG5cbi8vIENyZWF0ZSBhIGdsb2JhbCBtZXNzYWdlIHBhcnNlciBpbnN0YW5jZVxuY29uc3QgbWVzc2FnZVBhcnNlciA9IG5ldyBTdHJlYW1pbmdNZXNzYWdlUGFyc2VyKHtcbiAgb25GaWxlQWN0aW9uOiBhc3luYyAoYWN0aW9uOiBGaWxlQWN0aW9uKSA9PiB7XG4gICAgY29uc3QgeyBhZGRGaWxlIH0gPSB1c2VGaWxlU3RvcmUuZ2V0U3RhdGUoKTtcbiAgICB0cnkge1xuICAgICAgYXdhaXQgYWRkRmlsZShhY3Rpb24uZmlsZVBhdGgsIGFjdGlvbi5jb250ZW50KTtcbiAgICAgIGNvbnNvbGUubG9nKGBDcmVhdGVkL3VwZGF0ZWQgZmlsZTogJHthY3Rpb24uZmlsZVBhdGh9YCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoYEZhaWxlZCB0byBjcmVhdGUgZmlsZSAke2FjdGlvbi5maWxlUGF0aH06YCwgZXJyb3IpO1xuICAgIH1cbiAgfSxcbiAgb25TaGVsbEFjdGlvbjogKGFjdGlvbjogU2hlbGxBY3Rpb24pID0+IHtcbiAgICBjb25zb2xlLmxvZygnU2hlbGwgY29tbWFuZDonLCBhY3Rpb24uY29udGVudCk7XG4gICAgLy8gVE9ETzogSW50ZWdyYXRlIHdpdGggdGVybWluYWwgc3RvcmUgdG8gZXhlY3V0ZSBjb21tYW5kc1xuICB9LFxuICBvblN0YXJ0QWN0aW9uOiAoYWN0aW9uOiBTdGFydEFjdGlvbikgPT4ge1xuICAgIGNvbnNvbGUubG9nKCdTdGFydCBjb21tYW5kOicsIGFjdGlvbi5jb250ZW50KTtcbiAgICAvLyBUT0RPOiBJbnRlZ3JhdGUgd2l0aCB0ZXJtaW5hbCBzdG9yZSB0byBleGVjdXRlIHN0YXJ0IGNvbW1hbmRzXG4gIH0sXG59KTtcblxuZXhwb3J0IGNvbnN0IHBhcnNlTWVzc2FnZXMgPSBhc3luYyAobWVzc2FnZXM6IE1lc3NhZ2VbXSkgPT4ge1xuICBmb3IgKGNvbnN0IG1lc3NhZ2Ugb2YgbWVzc2FnZXMpIHtcbiAgICBpZiAobWVzc2FnZS5yb2xlID09PSAnYXNzaXN0YW50Jykge1xuICAgICAgbWVzc2FnZVBhcnNlci5wYXJzZShtZXNzYWdlLmlkLCBtZXNzYWdlLmNvbnRlbnQgYXMgc3RyaW5nKTtcbiAgICB9XG4gIH1cbn07XG5cbi8vIFNpbXBsZSBmdW5jdGlvbiB0byBleHRyYWN0IGZpbGVzIGZyb20gbWVzc2FnZSBjb250ZW50XG5leHBvcnQgZnVuY3Rpb24gcGFyc2VNZXNzYWdlRm9yRmlsZXMoY29udGVudDogc3RyaW5nKTogUmVjb3JkPHN0cmluZywgc3RyaW5nPiB7XG4gIGNvbnN0IGZpbGVzOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+ID0ge307XG4gIFxuICAvLyBQYXJzZSBib2x0QXJ0aWZhY3QgYW5kIGJvbHRBY3Rpb24gdGFnc1xuICBjb25zdCBhcnRpZmFjdFJlZ2V4ID0gLzxib2x0QXJ0aWZhY3RbXj5dKj4oW1xcc1xcU10qPyk8XFwvYm9sdEFydGlmYWN0Pi9nO1xuICBsZXQgYXJ0aWZhY3RNYXRjaDtcblxuICB3aGlsZSAoKGFydGlmYWN0TWF0Y2ggPSBhcnRpZmFjdFJlZ2V4LmV4ZWMoY29udGVudCkpICE9PSBudWxsKSB7XG4gICAgY29uc3QgYXJ0aWZhY3RDb250ZW50ID0gYXJ0aWZhY3RNYXRjaFsxXTtcbiAgICBcbiAgICAvLyBFeHRyYWN0IGZpbGUgYWN0aW9uc1xuICAgIGNvbnN0IGZpbGVBY3Rpb25SZWdleCA9IC88Ym9sdEFjdGlvblxccyt0eXBlPVwiZmlsZVwiXFxzK2ZpbGVQYXRoPVwiKFteXCJdKylcIlxccyo+KFtcXHNcXFNdKj8pPFxcL2JvbHRBY3Rpb24+L2c7XG4gICAgbGV0IGZpbGVNYXRjaDtcblxuICAgIHdoaWxlICgoZmlsZU1hdGNoID0gZmlsZUFjdGlvblJlZ2V4LmV4ZWMoYXJ0aWZhY3RDb250ZW50KSkgIT09IG51bGwpIHtcbiAgICAgIGNvbnN0IFssIGZpbGVQYXRoLCBmaWxlQ29udGVudF0gPSBmaWxlTWF0Y2g7XG4gICAgICBmaWxlc1tmaWxlUGF0aF0gPSBmaWxlQ29udGVudC50cmltKCk7XG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIGZpbGVzO1xufVxuIl0sIm5hbWVzIjpbInVzZUZpbGVTdG9yZSIsIlN0cmVhbWluZ01lc3NhZ2VQYXJzZXIiLCJwYXJzZSIsIm1lc3NhZ2VJZCIsImNvbnRlbnQiLCJhcnRpZmFjdFJlZ2V4IiwiYXJ0aWZhY3RNYXRjaCIsImV4ZWMiLCJhcnRpZmFjdENvbnRlbnQiLCJwYXJzZUFjdGlvbnMiLCJhY3Rpb25SZWdleCIsImFjdGlvbk1hdGNoIiwidHlwZSIsImZpbGVQYXRoIiwiYWN0aW9uQ29udGVudCIsImNhbGxiYWNrcyIsIm9uRmlsZUFjdGlvbiIsInRyaW0iLCJvblNoZWxsQWN0aW9uIiwib25TdGFydEFjdGlvbiIsImNvbnN0cnVjdG9yIiwibWVzc2FnZVBhcnNlciIsImFjdGlvbiIsImFkZEZpbGUiLCJnZXRTdGF0ZSIsImNvbnNvbGUiLCJsb2ciLCJlcnJvciIsInBhcnNlTWVzc2FnZXMiLCJtZXNzYWdlcyIsIm1lc3NhZ2UiLCJyb2xlIiwiaWQiLCJwYXJzZU1lc3NhZ2VGb3JGaWxlcyIsImZpbGVzIiwiZmlsZUFjdGlvblJlZ2V4IiwiZmlsZU1hdGNoIiwiZmlsZUNvbnRlbnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/messageParser.ts\n"));

/***/ })

});