"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-util-symbol@2.0.1";
exports.ids = ["vendor-chunks/micromark-util-symbol@2.0.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/codes.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/codes.js ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   codes: () => (/* binding */ codes)\n/* harmony export */ });\n/**\n * Character codes.\n *\n * This module is compiled away!\n *\n * micromark works based on character codes.\n * This module contains constants for the ASCII block and the replacement\n * character.\n * A couple of them are handled in a special way, such as the line endings\n * (CR, LF, and CR+LF, commonly known as end-of-line: EOLs), the tab (horizontal\n * tab) and its expansion based on what column it’s at (virtual space),\n * and the end-of-file (eof) character.\n * As values are preprocessed before handling them, the actual characters LF,\n * CR, HT, and NUL (which is present as the replacement character), are\n * guaranteed to not exist.\n *\n * Unicode basic latin block.\n */\nconst codes = /** @type {const} */ ({\n  carriageReturn: -5,\n  lineFeed: -4,\n  carriageReturnLineFeed: -3,\n  horizontalTab: -2,\n  virtualSpace: -1,\n  eof: null,\n  nul: 0,\n  soh: 1,\n  stx: 2,\n  etx: 3,\n  eot: 4,\n  enq: 5,\n  ack: 6,\n  bel: 7,\n  bs: 8,\n  ht: 9, // `\\t`\n  lf: 10, // `\\n`\n  vt: 11, // `\\v`\n  ff: 12, // `\\f`\n  cr: 13, // `\\r`\n  so: 14,\n  si: 15,\n  dle: 16,\n  dc1: 17,\n  dc2: 18,\n  dc3: 19,\n  dc4: 20,\n  nak: 21,\n  syn: 22,\n  etb: 23,\n  can: 24,\n  em: 25,\n  sub: 26,\n  esc: 27,\n  fs: 28,\n  gs: 29,\n  rs: 30,\n  us: 31,\n  space: 32,\n  exclamationMark: 33, // `!`\n  quotationMark: 34, // `\"`\n  numberSign: 35, // `#`\n  dollarSign: 36, // `$`\n  percentSign: 37, // `%`\n  ampersand: 38, // `&`\n  apostrophe: 39, // `'`\n  leftParenthesis: 40, // `(`\n  rightParenthesis: 41, // `)`\n  asterisk: 42, // `*`\n  plusSign: 43, // `+`\n  comma: 44, // `,`\n  dash: 45, // `-`\n  dot: 46, // `.`\n  slash: 47, // `/`\n  digit0: 48, // `0`\n  digit1: 49, // `1`\n  digit2: 50, // `2`\n  digit3: 51, // `3`\n  digit4: 52, // `4`\n  digit5: 53, // `5`\n  digit6: 54, // `6`\n  digit7: 55, // `7`\n  digit8: 56, // `8`\n  digit9: 57, // `9`\n  colon: 58, // `:`\n  semicolon: 59, // `;`\n  lessThan: 60, // `<`\n  equalsTo: 61, // `=`\n  greaterThan: 62, // `>`\n  questionMark: 63, // `?`\n  atSign: 64, // `@`\n  uppercaseA: 65, // `A`\n  uppercaseB: 66, // `B`\n  uppercaseC: 67, // `C`\n  uppercaseD: 68, // `D`\n  uppercaseE: 69, // `E`\n  uppercaseF: 70, // `F`\n  uppercaseG: 71, // `G`\n  uppercaseH: 72, // `H`\n  uppercaseI: 73, // `I`\n  uppercaseJ: 74, // `J`\n  uppercaseK: 75, // `K`\n  uppercaseL: 76, // `L`\n  uppercaseM: 77, // `M`\n  uppercaseN: 78, // `N`\n  uppercaseO: 79, // `O`\n  uppercaseP: 80, // `P`\n  uppercaseQ: 81, // `Q`\n  uppercaseR: 82, // `R`\n  uppercaseS: 83, // `S`\n  uppercaseT: 84, // `T`\n  uppercaseU: 85, // `U`\n  uppercaseV: 86, // `V`\n  uppercaseW: 87, // `W`\n  uppercaseX: 88, // `X`\n  uppercaseY: 89, // `Y`\n  uppercaseZ: 90, // `Z`\n  leftSquareBracket: 91, // `[`\n  backslash: 92, // `\\`\n  rightSquareBracket: 93, // `]`\n  caret: 94, // `^`\n  underscore: 95, // `_`\n  graveAccent: 96, // `` ` ``\n  lowercaseA: 97, // `a`\n  lowercaseB: 98, // `b`\n  lowercaseC: 99, // `c`\n  lowercaseD: 100, // `d`\n  lowercaseE: 101, // `e`\n  lowercaseF: 102, // `f`\n  lowercaseG: 103, // `g`\n  lowercaseH: 104, // `h`\n  lowercaseI: 105, // `i`\n  lowercaseJ: 106, // `j`\n  lowercaseK: 107, // `k`\n  lowercaseL: 108, // `l`\n  lowercaseM: 109, // `m`\n  lowercaseN: 110, // `n`\n  lowercaseO: 111, // `o`\n  lowercaseP: 112, // `p`\n  lowercaseQ: 113, // `q`\n  lowercaseR: 114, // `r`\n  lowercaseS: 115, // `s`\n  lowercaseT: 116, // `t`\n  lowercaseU: 117, // `u`\n  lowercaseV: 118, // `v`\n  lowercaseW: 119, // `w`\n  lowercaseX: 120, // `x`\n  lowercaseY: 121, // `y`\n  lowercaseZ: 122, // `z`\n  leftCurlyBrace: 123, // `{`\n  verticalBar: 124, // `|`\n  rightCurlyBrace: 125, // `}`\n  tilde: 126, // `~`\n  del: 127,\n  // Unicode Specials block.\n  byteOrderMarker: 65_279,\n  // Unicode Specials block.\n  replacementCharacter: 65_533 // `�`\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbWljcm9tYXJrLXV0aWwtc3ltYm9sQDIuMC4xL25vZGVfbW9kdWxlcy9taWNyb21hcmstdXRpbC1zeW1ib2wvbGliL2NvZGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTyx5QkFBeUIsT0FBTztBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0QjtBQUM1QjtBQUNBLDZCQUE2QjtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHdlLWRldi9uZXh0Ly4vbm9kZV9tb2R1bGVzLy5wbnBtL21pY3JvbWFyay11dGlsLXN5bWJvbEAyLjAuMS9ub2RlX21vZHVsZXMvbWljcm9tYXJrLXV0aWwtc3ltYm9sL2xpYi9jb2Rlcy5qcz8wYzQ0Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQ2hhcmFjdGVyIGNvZGVzLlxuICpcbiAqIFRoaXMgbW9kdWxlIGlzIGNvbXBpbGVkIGF3YXkhXG4gKlxuICogbWljcm9tYXJrIHdvcmtzIGJhc2VkIG9uIGNoYXJhY3RlciBjb2Rlcy5cbiAqIFRoaXMgbW9kdWxlIGNvbnRhaW5zIGNvbnN0YW50cyBmb3IgdGhlIEFTQ0lJIGJsb2NrIGFuZCB0aGUgcmVwbGFjZW1lbnRcbiAqIGNoYXJhY3Rlci5cbiAqIEEgY291cGxlIG9mIHRoZW0gYXJlIGhhbmRsZWQgaW4gYSBzcGVjaWFsIHdheSwgc3VjaCBhcyB0aGUgbGluZSBlbmRpbmdzXG4gKiAoQ1IsIExGLCBhbmQgQ1IrTEYsIGNvbW1vbmx5IGtub3duIGFzIGVuZC1vZi1saW5lOiBFT0xzKSwgdGhlIHRhYiAoaG9yaXpvbnRhbFxuICogdGFiKSBhbmQgaXRzIGV4cGFuc2lvbiBiYXNlZCBvbiB3aGF0IGNvbHVtbiBpdOKAmXMgYXQgKHZpcnR1YWwgc3BhY2UpLFxuICogYW5kIHRoZSBlbmQtb2YtZmlsZSAoZW9mKSBjaGFyYWN0ZXIuXG4gKiBBcyB2YWx1ZXMgYXJlIHByZXByb2Nlc3NlZCBiZWZvcmUgaGFuZGxpbmcgdGhlbSwgdGhlIGFjdHVhbCBjaGFyYWN0ZXJzIExGLFxuICogQ1IsIEhULCBhbmQgTlVMICh3aGljaCBpcyBwcmVzZW50IGFzIHRoZSByZXBsYWNlbWVudCBjaGFyYWN0ZXIpLCBhcmVcbiAqIGd1YXJhbnRlZWQgdG8gbm90IGV4aXN0LlxuICpcbiAqIFVuaWNvZGUgYmFzaWMgbGF0aW4gYmxvY2suXG4gKi9cbmV4cG9ydCBjb25zdCBjb2RlcyA9IC8qKiBAdHlwZSB7Y29uc3R9ICovICh7XG4gIGNhcnJpYWdlUmV0dXJuOiAtNSxcbiAgbGluZUZlZWQ6IC00LFxuICBjYXJyaWFnZVJldHVybkxpbmVGZWVkOiAtMyxcbiAgaG9yaXpvbnRhbFRhYjogLTIsXG4gIHZpcnR1YWxTcGFjZTogLTEsXG4gIGVvZjogbnVsbCxcbiAgbnVsOiAwLFxuICBzb2g6IDEsXG4gIHN0eDogMixcbiAgZXR4OiAzLFxuICBlb3Q6IDQsXG4gIGVucTogNSxcbiAgYWNrOiA2LFxuICBiZWw6IDcsXG4gIGJzOiA4LFxuICBodDogOSwgLy8gYFxcdGBcbiAgbGY6IDEwLCAvLyBgXFxuYFxuICB2dDogMTEsIC8vIGBcXHZgXG4gIGZmOiAxMiwgLy8gYFxcZmBcbiAgY3I6IDEzLCAvLyBgXFxyYFxuICBzbzogMTQsXG4gIHNpOiAxNSxcbiAgZGxlOiAxNixcbiAgZGMxOiAxNyxcbiAgZGMyOiAxOCxcbiAgZGMzOiAxOSxcbiAgZGM0OiAyMCxcbiAgbmFrOiAyMSxcbiAgc3luOiAyMixcbiAgZXRiOiAyMyxcbiAgY2FuOiAyNCxcbiAgZW06IDI1LFxuICBzdWI6IDI2LFxuICBlc2M6IDI3LFxuICBmczogMjgsXG4gIGdzOiAyOSxcbiAgcnM6IDMwLFxuICB1czogMzEsXG4gIHNwYWNlOiAzMixcbiAgZXhjbGFtYXRpb25NYXJrOiAzMywgLy8gYCFgXG4gIHF1b3RhdGlvbk1hcms6IDM0LCAvLyBgXCJgXG4gIG51bWJlclNpZ246IDM1LCAvLyBgI2BcbiAgZG9sbGFyU2lnbjogMzYsIC8vIGAkYFxuICBwZXJjZW50U2lnbjogMzcsIC8vIGAlYFxuICBhbXBlcnNhbmQ6IDM4LCAvLyBgJmBcbiAgYXBvc3Ryb3BoZTogMzksIC8vIGAnYFxuICBsZWZ0UGFyZW50aGVzaXM6IDQwLCAvLyBgKGBcbiAgcmlnaHRQYXJlbnRoZXNpczogNDEsIC8vIGApYFxuICBhc3RlcmlzazogNDIsIC8vIGAqYFxuICBwbHVzU2lnbjogNDMsIC8vIGArYFxuICBjb21tYTogNDQsIC8vIGAsYFxuICBkYXNoOiA0NSwgLy8gYC1gXG4gIGRvdDogNDYsIC8vIGAuYFxuICBzbGFzaDogNDcsIC8vIGAvYFxuICBkaWdpdDA6IDQ4LCAvLyBgMGBcbiAgZGlnaXQxOiA0OSwgLy8gYDFgXG4gIGRpZ2l0MjogNTAsIC8vIGAyYFxuICBkaWdpdDM6IDUxLCAvLyBgM2BcbiAgZGlnaXQ0OiA1MiwgLy8gYDRgXG4gIGRpZ2l0NTogNTMsIC8vIGA1YFxuICBkaWdpdDY6IDU0LCAvLyBgNmBcbiAgZGlnaXQ3OiA1NSwgLy8gYDdgXG4gIGRpZ2l0ODogNTYsIC8vIGA4YFxuICBkaWdpdDk6IDU3LCAvLyBgOWBcbiAgY29sb246IDU4LCAvLyBgOmBcbiAgc2VtaWNvbG9uOiA1OSwgLy8gYDtgXG4gIGxlc3NUaGFuOiA2MCwgLy8gYDxgXG4gIGVxdWFsc1RvOiA2MSwgLy8gYD1gXG4gIGdyZWF0ZXJUaGFuOiA2MiwgLy8gYD5gXG4gIHF1ZXN0aW9uTWFyazogNjMsIC8vIGA/YFxuICBhdFNpZ246IDY0LCAvLyBgQGBcbiAgdXBwZXJjYXNlQTogNjUsIC8vIGBBYFxuICB1cHBlcmNhc2VCOiA2NiwgLy8gYEJgXG4gIHVwcGVyY2FzZUM6IDY3LCAvLyBgQ2BcbiAgdXBwZXJjYXNlRDogNjgsIC8vIGBEYFxuICB1cHBlcmNhc2VFOiA2OSwgLy8gYEVgXG4gIHVwcGVyY2FzZUY6IDcwLCAvLyBgRmBcbiAgdXBwZXJjYXNlRzogNzEsIC8vIGBHYFxuICB1cHBlcmNhc2VIOiA3MiwgLy8gYEhgXG4gIHVwcGVyY2FzZUk6IDczLCAvLyBgSWBcbiAgdXBwZXJjYXNlSjogNzQsIC8vIGBKYFxuICB1cHBlcmNhc2VLOiA3NSwgLy8gYEtgXG4gIHVwcGVyY2FzZUw6IDc2LCAvLyBgTGBcbiAgdXBwZXJjYXNlTTogNzcsIC8vIGBNYFxuICB1cHBlcmNhc2VOOiA3OCwgLy8gYE5gXG4gIHVwcGVyY2FzZU86IDc5LCAvLyBgT2BcbiAgdXBwZXJjYXNlUDogODAsIC8vIGBQYFxuICB1cHBlcmNhc2VROiA4MSwgLy8gYFFgXG4gIHVwcGVyY2FzZVI6IDgyLCAvLyBgUmBcbiAgdXBwZXJjYXNlUzogODMsIC8vIGBTYFxuICB1cHBlcmNhc2VUOiA4NCwgLy8gYFRgXG4gIHVwcGVyY2FzZVU6IDg1LCAvLyBgVWBcbiAgdXBwZXJjYXNlVjogODYsIC8vIGBWYFxuICB1cHBlcmNhc2VXOiA4NywgLy8gYFdgXG4gIHVwcGVyY2FzZVg6IDg4LCAvLyBgWGBcbiAgdXBwZXJjYXNlWTogODksIC8vIGBZYFxuICB1cHBlcmNhc2VaOiA5MCwgLy8gYFpgXG4gIGxlZnRTcXVhcmVCcmFja2V0OiA5MSwgLy8gYFtgXG4gIGJhY2tzbGFzaDogOTIsIC8vIGBcXGBcbiAgcmlnaHRTcXVhcmVCcmFja2V0OiA5MywgLy8gYF1gXG4gIGNhcmV0OiA5NCwgLy8gYF5gXG4gIHVuZGVyc2NvcmU6IDk1LCAvLyBgX2BcbiAgZ3JhdmVBY2NlbnQ6IDk2LCAvLyBgYCBgIGBgXG4gIGxvd2VyY2FzZUE6IDk3LCAvLyBgYWBcbiAgbG93ZXJjYXNlQjogOTgsIC8vIGBiYFxuICBsb3dlcmNhc2VDOiA5OSwgLy8gYGNgXG4gIGxvd2VyY2FzZUQ6IDEwMCwgLy8gYGRgXG4gIGxvd2VyY2FzZUU6IDEwMSwgLy8gYGVgXG4gIGxvd2VyY2FzZUY6IDEwMiwgLy8gYGZgXG4gIGxvd2VyY2FzZUc6IDEwMywgLy8gYGdgXG4gIGxvd2VyY2FzZUg6IDEwNCwgLy8gYGhgXG4gIGxvd2VyY2FzZUk6IDEwNSwgLy8gYGlgXG4gIGxvd2VyY2FzZUo6IDEwNiwgLy8gYGpgXG4gIGxvd2VyY2FzZUs6IDEwNywgLy8gYGtgXG4gIGxvd2VyY2FzZUw6IDEwOCwgLy8gYGxgXG4gIGxvd2VyY2FzZU06IDEwOSwgLy8gYG1gXG4gIGxvd2VyY2FzZU46IDExMCwgLy8gYG5gXG4gIGxvd2VyY2FzZU86IDExMSwgLy8gYG9gXG4gIGxvd2VyY2FzZVA6IDExMiwgLy8gYHBgXG4gIGxvd2VyY2FzZVE6IDExMywgLy8gYHFgXG4gIGxvd2VyY2FzZVI6IDExNCwgLy8gYHJgXG4gIGxvd2VyY2FzZVM6IDExNSwgLy8gYHNgXG4gIGxvd2VyY2FzZVQ6IDExNiwgLy8gYHRgXG4gIGxvd2VyY2FzZVU6IDExNywgLy8gYHVgXG4gIGxvd2VyY2FzZVY6IDExOCwgLy8gYHZgXG4gIGxvd2VyY2FzZVc6IDExOSwgLy8gYHdgXG4gIGxvd2VyY2FzZVg6IDEyMCwgLy8gYHhgXG4gIGxvd2VyY2FzZVk6IDEyMSwgLy8gYHlgXG4gIGxvd2VyY2FzZVo6IDEyMiwgLy8gYHpgXG4gIGxlZnRDdXJseUJyYWNlOiAxMjMsIC8vIGB7YFxuICB2ZXJ0aWNhbEJhcjogMTI0LCAvLyBgfGBcbiAgcmlnaHRDdXJseUJyYWNlOiAxMjUsIC8vIGB9YFxuICB0aWxkZTogMTI2LCAvLyBgfmBcbiAgZGVsOiAxMjcsXG4gIC8vIFVuaWNvZGUgU3BlY2lhbHMgYmxvY2suXG4gIGJ5dGVPcmRlck1hcmtlcjogNjVfMjc5LFxuICAvLyBVbmljb2RlIFNwZWNpYWxzIGJsb2NrLlxuICByZXBsYWNlbWVudENoYXJhY3RlcjogNjVfNTMzIC8vIGDvv71gXG59KVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/codes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/constants.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/constants.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   constants: () => (/* binding */ constants)\n/* harmony export */ });\n/**\n * This module is compiled away!\n *\n * Parsing markdown comes with a couple of constants, such as minimum or maximum\n * sizes of certain sequences.\n * Additionally, there are a couple symbols used inside micromark.\n * These are all defined here, but compiled away by scripts.\n */\nconst constants = /** @type {const} */ ({\n  attentionSideAfter: 2, // Symbol to mark an attention sequence as after content: `a*`\n  attentionSideBefore: 1, // Symbol to mark an attention sequence as before content: `*a`\n  atxHeadingOpeningFenceSizeMax: 6, // 6 number signs is fine, 7 isn’t.\n  autolinkDomainSizeMax: 63, // 63 characters is fine, 64 is too many.\n  autolinkSchemeSizeMax: 32, // 32 characters is fine, 33 is too many.\n  cdataOpeningString: 'CDATA[', // And preceded by `<![`.\n  characterGroupPunctuation: 2, // Symbol used to indicate a character is punctuation\n  characterGroupWhitespace: 1, // Symbol used to indicate a character is whitespace\n  characterReferenceDecimalSizeMax: 7, // `&#9999999;`.\n  characterReferenceHexadecimalSizeMax: 6, // `&#xff9999;`.\n  characterReferenceNamedSizeMax: 31, // `&CounterClockwiseContourIntegral;`.\n  codeFencedSequenceSizeMin: 3, // At least 3 ticks or tildes are needed.\n  contentTypeContent: 'content',\n  contentTypeDocument: 'document',\n  contentTypeFlow: 'flow',\n  contentTypeString: 'string',\n  contentTypeText: 'text',\n  hardBreakPrefixSizeMin: 2, // At least 2 trailing spaces are needed.\n  htmlBasic: 6, // Symbol for `<div`\n  htmlCdata: 5, // Symbol for `<![CDATA[]]>`\n  htmlComment: 2, // Symbol for `<!---->`\n  htmlComplete: 7, // Symbol for `<x>`\n  htmlDeclaration: 4, // Symbol for `<!doctype>`\n  htmlInstruction: 3, // Symbol for `<?php?>`\n  htmlRawSizeMax: 8, // Length of `textarea`.\n  htmlRaw: 1, // Symbol for `<script>`\n  linkResourceDestinationBalanceMax: 32, // See: <https://spec.commonmark.org/0.30/#link-destination>, <https://github.com/remarkjs/react-markdown/issues/658#issuecomment-984345577>\n  linkReferenceSizeMax: 999, // See: <https://spec.commonmark.org/0.30/#link-label>\n  listItemValueSizeMax: 10, // See: <https://spec.commonmark.org/0.30/#ordered-list-marker>\n  numericBaseDecimal: 10,\n  numericBaseHexadecimal: 0x10,\n  tabSize: 4, // Tabs have a hard-coded size of 4, per CommonMark.\n  thematicBreakMarkerCountMin: 3, // At least 3 asterisks, dashes, or underscores are needed.\n  v8MaxSafeChunkSize: 10_000 // V8 (and potentially others) have problems injecting giant arrays into other arrays, hence we operate in chunks.\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/types.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/types.js ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   types: () => (/* binding */ types)\n/* harmony export */ });\n/**\n * This module is compiled away!\n *\n * Here is the list of all types of tokens exposed by micromark, with a short\n * explanation of what they include and where they are found.\n * In picking names, generally, the rule is to be as explicit as possible\n * instead of reusing names.\n * For example, there is a `definitionDestination` and a `resourceDestination`,\n * instead of one shared name.\n */\n\n// Note: when changing the next record, you must also change `TokenTypeMap`\n// in `micromark-util-types/index.d.ts`.\nconst types = /** @type {const} */ ({\n  // Generic type for data, such as in a title, a destination, etc.\n  data: 'data',\n\n  // Generic type for syntactic whitespace (tabs, virtual spaces, spaces).\n  // Such as, between a fenced code fence and an info string.\n  whitespace: 'whitespace',\n\n  // Generic type for line endings (line feed, carriage return, carriage return +\n  // line feed).\n  lineEnding: 'lineEnding',\n\n  // A line ending, but ending a blank line.\n  lineEndingBlank: 'lineEndingBlank',\n\n  // Generic type for whitespace (tabs, virtual spaces, spaces) at the start of a\n  // line.\n  linePrefix: 'linePrefix',\n\n  // Generic type for whitespace (tabs, virtual spaces, spaces) at the end of a\n  // line.\n  lineSuffix: 'lineSuffix',\n\n  // Whole ATX heading:\n  //\n  // ```markdown\n  // #\n  // ## Alpha\n  // ### Bravo ###\n  // ```\n  //\n  // Includes `atxHeadingSequence`, `whitespace`, `atxHeadingText`.\n  atxHeading: 'atxHeading',\n\n  // Sequence of number signs in an ATX heading (`###`).\n  atxHeadingSequence: 'atxHeadingSequence',\n\n  // Content in an ATX heading (`alpha`).\n  // Includes text.\n  atxHeadingText: 'atxHeadingText',\n\n  // Whole autolink (`<https://example.com>` or `<<EMAIL>>`)\n  // Includes `autolinkMarker` and `autolinkProtocol` or `autolinkEmail`.\n  autolink: 'autolink',\n\n  // Email autolink w/o markers (`<EMAIL>`)\n  autolinkEmail: 'autolinkEmail',\n\n  // Marker around an `autolinkProtocol` or `autolinkEmail` (`<` or `>`).\n  autolinkMarker: 'autolinkMarker',\n\n  // Protocol autolink w/o markers (`https://example.com`)\n  autolinkProtocol: 'autolinkProtocol',\n\n  // A whole character escape (`\\-`).\n  // Includes `escapeMarker` and `characterEscapeValue`.\n  characterEscape: 'characterEscape',\n\n  // The escaped character (`-`).\n  characterEscapeValue: 'characterEscapeValue',\n\n  // A whole character reference (`&amp;`, `&#8800;`, or `&#x1D306;`).\n  // Includes `characterReferenceMarker`, an optional\n  // `characterReferenceMarkerNumeric`, in which case an optional\n  // `characterReferenceMarkerHexadecimal`, and a `characterReferenceValue`.\n  characterReference: 'characterReference',\n\n  // The start or end marker (`&` or `;`).\n  characterReferenceMarker: 'characterReferenceMarker',\n\n  // Mark reference as numeric (`#`).\n  characterReferenceMarkerNumeric: 'characterReferenceMarkerNumeric',\n\n  // Mark reference as numeric (`x` or `X`).\n  characterReferenceMarkerHexadecimal: 'characterReferenceMarkerHexadecimal',\n\n  // Value of character reference w/o markers (`amp`, `8800`, or `1D306`).\n  characterReferenceValue: 'characterReferenceValue',\n\n  // Whole fenced code:\n  //\n  // ````markdown\n  // ```js\n  // alert(1)\n  // ```\n  // ````\n  codeFenced: 'codeFenced',\n\n  // A fenced code fence, including whitespace, sequence, info, and meta\n  // (` ```js `).\n  codeFencedFence: 'codeFencedFence',\n\n  // Sequence of grave accent or tilde characters (` ``` `) in a fence.\n  codeFencedFenceSequence: 'codeFencedFenceSequence',\n\n  // Info word (`js`) in a fence.\n  // Includes string.\n  codeFencedFenceInfo: 'codeFencedFenceInfo',\n\n  // Meta words (`highlight=\"1\"`) in a fence.\n  // Includes string.\n  codeFencedFenceMeta: 'codeFencedFenceMeta',\n\n  // A line of code.\n  codeFlowValue: 'codeFlowValue',\n\n  // Whole indented code:\n  //\n  // ```markdown\n  //     alert(1)\n  // ```\n  //\n  // Includes `lineEnding`, `linePrefix`, and `codeFlowValue`.\n  codeIndented: 'codeIndented',\n\n  // A text code (``` `alpha` ```).\n  // Includes `codeTextSequence`, `codeTextData`, `lineEnding`, and can include\n  // `codeTextPadding`.\n  codeText: 'codeText',\n\n  codeTextData: 'codeTextData',\n\n  // A space or line ending right after or before a tick.\n  codeTextPadding: 'codeTextPadding',\n\n  // A text code fence (` `` `).\n  codeTextSequence: 'codeTextSequence',\n\n  // Whole content:\n  //\n  // ```markdown\n  // [a]: b\n  // c\n  // =\n  // d\n  // ```\n  //\n  // Includes `paragraph` and `definition`.\n  content: 'content',\n  // Whole definition:\n  //\n  // ```markdown\n  // [micromark]: https://github.com/micromark/micromark\n  // ```\n  //\n  // Includes `definitionLabel`, `definitionMarker`, `whitespace`,\n  // `definitionDestination`, and optionally `lineEnding` and `definitionTitle`.\n  definition: 'definition',\n\n  // Destination of a definition (`https://github.com/micromark/micromark` or\n  // `<https://github.com/micromark/micromark>`).\n  // Includes `definitionDestinationLiteral` or `definitionDestinationRaw`.\n  definitionDestination: 'definitionDestination',\n\n  // Enclosed destination of a definition\n  // (`<https://github.com/micromark/micromark>`).\n  // Includes `definitionDestinationLiteralMarker` and optionally\n  // `definitionDestinationString`.\n  definitionDestinationLiteral: 'definitionDestinationLiteral',\n\n  // Markers of an enclosed definition destination (`<` or `>`).\n  definitionDestinationLiteralMarker: 'definitionDestinationLiteralMarker',\n\n  // Unenclosed destination of a definition\n  // (`https://github.com/micromark/micromark`).\n  // Includes `definitionDestinationString`.\n  definitionDestinationRaw: 'definitionDestinationRaw',\n\n  // Text in an destination (`https://github.com/micromark/micromark`).\n  // Includes string.\n  definitionDestinationString: 'definitionDestinationString',\n\n  // Label of a definition (`[micromark]`).\n  // Includes `definitionLabelMarker` and `definitionLabelString`.\n  definitionLabel: 'definitionLabel',\n\n  // Markers of a definition label (`[` or `]`).\n  definitionLabelMarker: 'definitionLabelMarker',\n\n  // Value of a definition label (`micromark`).\n  // Includes string.\n  definitionLabelString: 'definitionLabelString',\n\n  // Marker between a label and a destination (`:`).\n  definitionMarker: 'definitionMarker',\n\n  // Title of a definition (`\"x\"`, `'y'`, or `(z)`).\n  // Includes `definitionTitleMarker` and optionally `definitionTitleString`.\n  definitionTitle: 'definitionTitle',\n\n  // Marker around a title of a definition (`\"`, `'`, `(`, or `)`).\n  definitionTitleMarker: 'definitionTitleMarker',\n\n  // Data without markers in a title (`z`).\n  // Includes string.\n  definitionTitleString: 'definitionTitleString',\n\n  // Emphasis (`*alpha*`).\n  // Includes `emphasisSequence` and `emphasisText`.\n  emphasis: 'emphasis',\n\n  // Sequence of emphasis markers (`*` or `_`).\n  emphasisSequence: 'emphasisSequence',\n\n  // Emphasis text (`alpha`).\n  // Includes text.\n  emphasisText: 'emphasisText',\n\n  // The character escape marker (`\\`).\n  escapeMarker: 'escapeMarker',\n\n  // A hard break created with a backslash (`\\\\n`).\n  // Note: does not include the line ending.\n  hardBreakEscape: 'hardBreakEscape',\n\n  // A hard break created with trailing spaces (`  \\n`).\n  // Does not include the line ending.\n  hardBreakTrailing: 'hardBreakTrailing',\n\n  // Flow HTML:\n  //\n  // ```markdown\n  // <div\n  // ```\n  //\n  // Inlcudes `lineEnding`, `htmlFlowData`.\n  htmlFlow: 'htmlFlow',\n\n  htmlFlowData: 'htmlFlowData',\n\n  // HTML in text (the tag in `a <i> b`).\n  // Includes `lineEnding`, `htmlTextData`.\n  htmlText: 'htmlText',\n\n  htmlTextData: 'htmlTextData',\n\n  // Whole image (`![alpha](bravo)`, `![alpha][bravo]`, `![alpha][]`, or\n  // `![alpha]`).\n  // Includes `label` and an optional `resource` or `reference`.\n  image: 'image',\n\n  // Whole link label (`[*alpha*]`).\n  // Includes `labelLink` or `labelImage`, `labelText`, and `labelEnd`.\n  label: 'label',\n\n  // Text in an label (`*alpha*`).\n  // Includes text.\n  labelText: 'labelText',\n\n  // Start a link label (`[`).\n  // Includes a `labelMarker`.\n  labelLink: 'labelLink',\n\n  // Start an image label (`![`).\n  // Includes `labelImageMarker` and `labelMarker`.\n  labelImage: 'labelImage',\n\n  // Marker of a label (`[` or `]`).\n  labelMarker: 'labelMarker',\n\n  // Marker to start an image (`!`).\n  labelImageMarker: 'labelImageMarker',\n\n  // End a label (`]`).\n  // Includes `labelMarker`.\n  labelEnd: 'labelEnd',\n\n  // Whole link (`[alpha](bravo)`, `[alpha][bravo]`, `[alpha][]`, or `[alpha]`).\n  // Includes `label` and an optional `resource` or `reference`.\n  link: 'link',\n\n  // Whole paragraph:\n  //\n  // ```markdown\n  // alpha\n  // bravo.\n  // ```\n  //\n  // Includes text.\n  paragraph: 'paragraph',\n\n  // A reference (`[alpha]` or `[]`).\n  // Includes `referenceMarker` and an optional `referenceString`.\n  reference: 'reference',\n\n  // A reference marker (`[` or `]`).\n  referenceMarker: 'referenceMarker',\n\n  // Reference text (`alpha`).\n  // Includes string.\n  referenceString: 'referenceString',\n\n  // A resource (`(https://example.com \"alpha\")`).\n  // Includes `resourceMarker`, an optional `resourceDestination` with an optional\n  // `whitespace` and `resourceTitle`.\n  resource: 'resource',\n\n  // A resource destination (`https://example.com`).\n  // Includes `resourceDestinationLiteral` or `resourceDestinationRaw`.\n  resourceDestination: 'resourceDestination',\n\n  // A literal resource destination (`<https://example.com>`).\n  // Includes `resourceDestinationLiteralMarker` and optionally\n  // `resourceDestinationString`.\n  resourceDestinationLiteral: 'resourceDestinationLiteral',\n\n  // A resource destination marker (`<` or `>`).\n  resourceDestinationLiteralMarker: 'resourceDestinationLiteralMarker',\n\n  // A raw resource destination (`https://example.com`).\n  // Includes `resourceDestinationString`.\n  resourceDestinationRaw: 'resourceDestinationRaw',\n\n  // Resource destination text (`https://example.com`).\n  // Includes string.\n  resourceDestinationString: 'resourceDestinationString',\n\n  // A resource marker (`(` or `)`).\n  resourceMarker: 'resourceMarker',\n\n  // A resource title (`\"alpha\"`, `'alpha'`, or `(alpha)`).\n  // Includes `resourceTitleMarker` and optionally `resourceTitleString`.\n  resourceTitle: 'resourceTitle',\n\n  // A resource title marker (`\"`, `'`, `(`, or `)`).\n  resourceTitleMarker: 'resourceTitleMarker',\n\n  // Resource destination title (`alpha`).\n  // Includes string.\n  resourceTitleString: 'resourceTitleString',\n\n  // Whole setext heading:\n  //\n  // ```markdown\n  // alpha\n  // bravo\n  // =====\n  // ```\n  //\n  // Includes `setextHeadingText`, `lineEnding`, `linePrefix`, and\n  // `setextHeadingLine`.\n  setextHeading: 'setextHeading',\n\n  // Content in a setext heading (`alpha\\nbravo`).\n  // Includes text.\n  setextHeadingText: 'setextHeadingText',\n\n  // Underline in a setext heading, including whitespace suffix (`==`).\n  // Includes `setextHeadingLineSequence`.\n  setextHeadingLine: 'setextHeadingLine',\n\n  // Sequence of equals or dash characters in underline in a setext heading (`-`).\n  setextHeadingLineSequence: 'setextHeadingLineSequence',\n\n  // Strong (`**alpha**`).\n  // Includes `strongSequence` and `strongText`.\n  strong: 'strong',\n\n  // Sequence of strong markers (`**` or `__`).\n  strongSequence: 'strongSequence',\n\n  // Strong text (`alpha`).\n  // Includes text.\n  strongText: 'strongText',\n\n  // Whole thematic break:\n  //\n  // ```markdown\n  // * * *\n  // ```\n  //\n  // Includes `thematicBreakSequence` and `whitespace`.\n  thematicBreak: 'thematicBreak',\n\n  // A sequence of one or more thematic break markers (`***`).\n  thematicBreakSequence: 'thematicBreakSequence',\n\n  // Whole block quote:\n  //\n  // ```markdown\n  // > a\n  // >\n  // > b\n  // ```\n  //\n  // Includes `blockQuotePrefix` and flow.\n  blockQuote: 'blockQuote',\n  // The `>` or `> ` of a block quote.\n  blockQuotePrefix: 'blockQuotePrefix',\n  // The `>` of a block quote prefix.\n  blockQuoteMarker: 'blockQuoteMarker',\n  // The optional ` ` of a block quote prefix.\n  blockQuotePrefixWhitespace: 'blockQuotePrefixWhitespace',\n\n  // Whole ordered list:\n  //\n  // ```markdown\n  // 1. a\n  //    b\n  // ```\n  //\n  // Includes `listItemPrefix`, flow, and optionally  `listItemIndent` on further\n  // lines.\n  listOrdered: 'listOrdered',\n\n  // Whole unordered list:\n  //\n  // ```markdown\n  // - a\n  //   b\n  // ```\n  //\n  // Includes `listItemPrefix`, flow, and optionally  `listItemIndent` on further\n  // lines.\n  listUnordered: 'listUnordered',\n\n  // The indent of further list item lines.\n  listItemIndent: 'listItemIndent',\n\n  // A marker, as in, `*`, `+`, `-`, `.`, or `)`.\n  listItemMarker: 'listItemMarker',\n\n  // The thing that starts a list item, such as `1. `.\n  // Includes `listItemValue` if ordered, `listItemMarker`, and\n  // `listItemPrefixWhitespace` (unless followed by a line ending).\n  listItemPrefix: 'listItemPrefix',\n\n  // The whitespace after a marker.\n  listItemPrefixWhitespace: 'listItemPrefixWhitespace',\n\n  // The numerical value of an ordered item.\n  listItemValue: 'listItemValue',\n\n  // Internal types used for subtokenizers, compiled away\n  chunkDocument: 'chunkDocument',\n  chunkContent: 'chunkContent',\n  chunkFlow: 'chunkFlow',\n  chunkText: 'chunkText',\n  chunkString: 'chunkString'\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/values.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/values.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   values: () => (/* binding */ values)\n/* harmony export */ });\n/**\n * This module is compiled away!\n *\n * While micromark works based on character codes, this module includes the\n * string versions of ’em.\n * The C0 block, except for LF, CR, HT, and w/ the replacement character added,\n * are available here.\n */\nconst values = /** @type {const} */ ({\n  ht: '\\t',\n  lf: '\\n',\n  cr: '\\r',\n  space: ' ',\n  exclamationMark: '!',\n  quotationMark: '\"',\n  numberSign: '#',\n  dollarSign: '$',\n  percentSign: '%',\n  ampersand: '&',\n  apostrophe: \"'\",\n  leftParenthesis: '(',\n  rightParenthesis: ')',\n  asterisk: '*',\n  plusSign: '+',\n  comma: ',',\n  dash: '-',\n  dot: '.',\n  slash: '/',\n  digit0: '0',\n  digit1: '1',\n  digit2: '2',\n  digit3: '3',\n  digit4: '4',\n  digit5: '5',\n  digit6: '6',\n  digit7: '7',\n  digit8: '8',\n  digit9: '9',\n  colon: ':',\n  semicolon: ';',\n  lessThan: '<',\n  equalsTo: '=',\n  greaterThan: '>',\n  questionMark: '?',\n  atSign: '@',\n  uppercaseA: 'A',\n  uppercaseB: 'B',\n  uppercaseC: 'C',\n  uppercaseD: 'D',\n  uppercaseE: 'E',\n  uppercaseF: 'F',\n  uppercaseG: 'G',\n  uppercaseH: 'H',\n  uppercaseI: 'I',\n  uppercaseJ: 'J',\n  uppercaseK: 'K',\n  uppercaseL: 'L',\n  uppercaseM: 'M',\n  uppercaseN: 'N',\n  uppercaseO: 'O',\n  uppercaseP: 'P',\n  uppercaseQ: 'Q',\n  uppercaseR: 'R',\n  uppercaseS: 'S',\n  uppercaseT: 'T',\n  uppercaseU: 'U',\n  uppercaseV: 'V',\n  uppercaseW: 'W',\n  uppercaseX: 'X',\n  uppercaseY: 'Y',\n  uppercaseZ: 'Z',\n  leftSquareBracket: '[',\n  backslash: '\\\\',\n  rightSquareBracket: ']',\n  caret: '^',\n  underscore: '_',\n  graveAccent: '`',\n  lowercaseA: 'a',\n  lowercaseB: 'b',\n  lowercaseC: 'c',\n  lowercaseD: 'd',\n  lowercaseE: 'e',\n  lowercaseF: 'f',\n  lowercaseG: 'g',\n  lowercaseH: 'h',\n  lowercaseI: 'i',\n  lowercaseJ: 'j',\n  lowercaseK: 'k',\n  lowercaseL: 'l',\n  lowercaseM: 'm',\n  lowercaseN: 'n',\n  lowercaseO: 'o',\n  lowercaseP: 'p',\n  lowercaseQ: 'q',\n  lowercaseR: 'r',\n  lowercaseS: 's',\n  lowercaseT: 't',\n  lowercaseU: 'u',\n  lowercaseV: 'v',\n  lowercaseW: 'w',\n  lowercaseX: 'x',\n  lowercaseY: 'y',\n  lowercaseZ: 'z',\n  leftCurlyBrace: '{',\n  verticalBar: '|',\n  rightCurlyBrace: '}',\n  tilde: '~',\n  replacementCharacter: '�'\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/values.js\n");

/***/ })

};
;