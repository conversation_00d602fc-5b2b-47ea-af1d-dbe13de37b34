"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/AiChat/chat/index.tsx":
/*!**********************************************!*\
  !*** ./src/components/AiChat/chat/index.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseChat: function() { return /* binding */ BaseChat; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ai_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ai/react */ \"(app-pages-browser)/./node_modules/.pnpm/ai@4.3.17_react@18.3.1_zod@3.24.1/node_modules/ai/react/dist/index.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _stores_chatSlice__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/stores/chatSlice */ \"(app-pages-browser)/./src/stores/chatSlice.ts\");\n/* harmony import */ var _stores_fileStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/stores/fileStore */ \"(app-pages-browser)/./src/stores/fileStore.ts\");\n/* harmony import */ var _utils_indexDB__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/indexDB */ \"(app-pages-browser)/./src/utils/indexDB.ts\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/.pnpm/uuid@11.0.5/node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _components_MessageItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/MessageItem */ \"(app-pages-browser)/./src/components/AiChat/chat/components/MessageItem.tsx\");\n/* harmony import */ var _components_ChatInput__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/ChatInput */ \"(app-pages-browser)/./src/components/AiChat/chat/components/ChatInput/index.tsx\");\n/* harmony import */ var _components_Tips__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./components/Tips */ \"(app-pages-browser)/./src/components/AiChat/chat/components/Tips.tsx\");\n/* harmony import */ var _utils_messagepParseJson__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/messagepParseJson */ \"(app-pages-browser)/./src/utils/messagepParseJson.ts\");\n/* harmony import */ var _utils_messageParserNew__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/messageParserNew */ \"(app-pages-browser)/./src/utils/messageParserNew.ts\");\n/* harmony import */ var _stores_userSlice__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/stores/userSlice */ \"(app-pages-browser)/./src/stores/userSlice.ts\");\n/* harmony import */ var _UserModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../UserModal */ \"(app-pages-browser)/./src/components/UserModal/index.tsx\");\n/* harmony import */ var _stores_chatModeSlice__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/stores/chatModeSlice */ \"(app-pages-browser)/./src/stores/chatModeSlice.ts\");\n/* __next_internal_client_entry_do_not_use__ BaseChat auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst BaseChat = (param)=>{\n    let { uuid: propUuid } = param;\n    _s();\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { otherConfig, uploadedImages, clearImages, addImages, removeImage } = (0,_stores_chatSlice__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    const [checkCount, setCheckCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [baseModal, setBaseModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        value: \"anthropic/claude-3.5-sonnet\",\n        label: \"Claude 3.5 Sonnet\",\n        useImage: true,\n        from: \"default\",\n        quota: 2,\n        functionCall: true\n    });\n    const { files, updateContent } = (0,_stores_fileStore__WEBPACK_IMPORTED_MODULE_4__.useFileStore)();\n    const { user, token } = (0,_stores_userSlice__WEBPACK_IMPORTED_MODULE_11__[\"default\"])();\n    const { openModal } = (0,_UserModal__WEBPACK_IMPORTED_MODULE_12__.useLimitModalStore)();\n    const { mode } = (0,_stores_chatModeSlice__WEBPACK_IMPORTED_MODULE_13__[\"default\"])();\n    const [messages, setMessagesa] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const parseTimeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(Date.now());\n    const refUuidMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const chatUuid = propUuid || (0,uuid__WEBPACK_IMPORTED_MODULE_14__[\"default\"])();\n    const baseChatUrl =  true ? window.location.origin : 0;\n    const clearErrors = ()=>setErrors([]);\n    const scrollToBottom = ()=>{\n        setTimeout(()=>{\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }, 100);\n    };\n    // Parse messages and create files\n    const parseMessagesAndCreateFiles = async (messages)=>{\n        try {\n            await (0,_utils_messageParserNew__WEBPACK_IMPORTED_MODULE_10__.parseMessages)(messages);\n        } catch (error) {\n            console.error(\"Error parsing messages:\", error);\n        }\n    };\n    const updateFileSystemNow = ()=>{\n        // This would update the file system in desktop app\n        // For web, this is a no-op\n        return Promise.resolve();\n    };\n    const createMpIcon = (files)=>{\n    // This would create mini program icons in desktop app\n    // For web, this is a no-op\n    };\n    const checkExecList = ()=>{\n        // This would check execution list in desktop app\n        return [];\n    };\n    const checkFinish = ()=>{\n        // This would check if execution is finished in desktop app\n        return true;\n    };\n    const { messages: realMessages, input, handleInputChange, isLoading, setMessages, append, setInput, stop, reload } = (0,ai_react__WEBPACK_IMPORTED_MODULE_15__.useChat)({\n        api: \"\".concat(baseChatUrl, \"/api/chat\"),\n        headers: {\n            ...token && {\n                Authorization: \"Bearer \".concat(token)\n            }\n        },\n        body: {\n            model: baseModal.value,\n            mode: mode,\n            otherConfig: {\n                ...otherConfig,\n                extra: {\n                    ...otherConfig.extra,\n                    isBackEnd: otherConfig.isBackEnd,\n                    backendLanguage: otherConfig.backendLanguage\n                }\n            }\n        },\n        onFinish: async (message)=>{\n            try {\n                var _find_content, _find;\n                // Parse message for files (like we-dev-client does)\n                if (message && message.content) {\n                    console.log(\"\\uD83C\\uDFAF onFinish: Processing message for files\");\n                    const { files: messageFiles } = (0,_utils_messagepParseJson__WEBPACK_IMPORTED_MODULE_9__.parseMessage)(message.content);\n                    console.log(\"\\uD83D\\uDCC1 Found \".concat(Object.keys(messageFiles).length, \" files:\"), Object.keys(messageFiles));\n                    for(let filePath in messageFiles){\n                        console.log(\"\\uD83D\\uDCC4 Creating file: \".concat(filePath));\n                        await updateContent(filePath, messageFiles[filePath], false, true);\n                    }\n                }\n                // Also parse with the streaming parser for any missed files\n                const needParseMessages = [\n                    ...messages,\n                    message\n                ].filter((m)=>!refUuidMessages.current.includes(m.id));\n                refUuidMessages.current = [\n                    ...refUuidMessages.current,\n                    ...needParseMessages.map((m)=>m.id)\n                ];\n                if (needParseMessages.length > 0) {\n                    console.log(\"\\uD83D\\uDD04 Also parsing with streaming parser for \".concat(needParseMessages.length, \" messages\"));\n                    parseMessagesAndCreateFiles(needParseMessages);\n                }\n                const initMessage = messages.filter((m)=>m.role === \"system\");\n                await _utils_indexDB__WEBPACK_IMPORTED_MODULE_5__.db.insert(chatUuid, {\n                    messages: [\n                        ...messages,\n                        ...initMessage,\n                        message\n                    ],\n                    title: ((_find = [\n                        ...initMessage,\n                        ...messages\n                    ].find((m)=>m.role === \"user\" && !m.content.includes(\"<boltArtifact\"))) === null || _find === void 0 ? void 0 : (_find_content = _find.content) === null || _find_content === void 0 ? void 0 : _find_content.slice(0, 50)) || \"New Chat\"\n                });\n            } catch (error) {\n                console.error(\"Failed to save chat history:\", error);\n            }\n            setCheckCount((checkCount)=>checkCount + 1);\n        },\n        onError: (error)=>{\n            var _error_errors_, _error_errors;\n            const msg = (error === null || error === void 0 ? void 0 : (_error_errors = error.errors) === null || _error_errors === void 0 ? void 0 : (_error_errors_ = _error_errors[0]) === null || _error_errors_ === void 0 ? void 0 : _error_errors_.responseBody) || String(error);\n            console.log(\"error\", error, msg);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(msg);\n            if (String(error).includes(\"Quota not enough\")) {\n                openModal(\"limit\");\n            }\n            if (String(error).includes(\"Authentication required\")) {\n                openModal(\"login\");\n            }\n        }\n    });\n    const filterMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return messages.filter((message)=>{\n            if (message.role === \"system\") return false;\n            return true;\n        });\n    }, [\n        messages\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only update messages during streaming, but don't parse yet\n        if (Date.now() - parseTimeRef.current > 200 && isLoading) {\n            setMessagesa(realMessages);\n            parseTimeRef.current = Date.now();\n            scrollToBottom();\n        }\n        if (errors.length > 0 && isLoading) {\n            clearErrors();\n        }\n        // Only parse messages when streaming is complete\n        if (!isLoading) {\n            setMessagesa(realMessages);\n            createMpIcon(files);\n            // Parse messages when loading is complete\n            const needParseMessages = messages.filter((m)=>!refUuidMessages.current.includes(m.id) && m.role === \"assistant\" && m.content && typeof m.content === \"string\" && m.content.trim().length > 0);\n            if (needParseMessages.length > 0) {\n                console.log(\"\\uD83D\\uDCE8 Processing \".concat(needParseMessages.length, \" new messages (loading complete):\"), needParseMessages.map((m)=>{\n                    var _m_content;\n                    return {\n                        id: m.id,\n                        role: m.role,\n                        contentLength: ((_m_content = m.content) === null || _m_content === void 0 ? void 0 : _m_content.length) || 0,\n                        hasContent: !!m.content\n                    };\n                }));\n                parseMessagesAndCreateFiles(needParseMessages);\n                // Update tracked message IDs\n                refUuidMessages.current = [\n                    ...refUuidMessages.current,\n                    ...needParseMessages.map((m)=>m.id)\n                ];\n            }\n        }\n    }, [\n        realMessages,\n        isLoading\n    ]);\n    const handleFileSelect = async (e)=>{\n        const files = Array.from(e.target.files || []);\n        if (files.length === 0) return;\n        setIsUploading(true);\n        try {\n            const newImages = files.map((file)=>({\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(),\n                    file,\n                    url: URL.createObjectURL(file),\n                    localUrl: URL.createObjectURL(file),\n                    status: \"done\"\n                }));\n            addImages(newImages);\n        } catch (error) {\n            console.error(\"File upload failed:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to upload files\");\n        } finally{\n            setIsUploading(false);\n        }\n    };\n    const handleSubmitWithFiles = async (e, text)=>{\n        e === null || e === void 0 ? void 0 : e.preventDefault();\n        if (!input.trim() && !(text === null || text === void 0 ? void 0 : text.trim()) || isLoading) return;\n        try {\n            const currentAttachments = uploadedImages.map((img)=>({\n                    id: img.id,\n                    name: img.id,\n                    type: img.file.type,\n                    localUrl: img.localUrl,\n                    contentType: img.file.type,\n                    url: img.url\n                }));\n            clearImages();\n            append({\n                role: \"user\",\n                content: text || input\n            }, {\n                experimental_attachments: currentAttachments\n            });\n            setInput(\"\");\n            setTimeout(()=>{\n                scrollToBottom();\n            }, 100);\n        } catch (error) {\n            console.error(\"Upload failed:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to upload files\");\n        }\n    };\n    const handleKeySubmit = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmitWithFiles(e);\n        }\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        const files = Array.from(e.dataTransfer.files);\n        if (files.length > 0) {\n            const fileInput = document.createElement(\"input\");\n            fileInput.type = \"file\";\n            fileInput.multiple = true;\n            fileInput.files = e.dataTransfer.files;\n            handleFileSelect({\n                target: fileInput\n            });\n        }\n    };\n    const handleScroll = ()=>{\n    // Handle scroll events if needed\n    };\n    const showJsx = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 overflow-y-auto px-1 py-2 message-container [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]\",\n            onScroll: handleScroll,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Tips__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    append: append,\n                    setInput: setInput,\n                    handleFileSelect: handleFileSelect\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-[640px] w-full mx-auto space-y-3\",\n                    children: [\n                        filterMessages.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MessageItem__WEBPACK_IMPORTED_MODULE_6__.MessageItem, {\n                                handleRetry: ()=>{\n                                    reload();\n                                },\n                                message: message,\n                                isEndMessage: filterMessages[filterMessages.length - 1].id === message.id,\n                                isLoading: isLoading,\n                                onUpdateMessage: (messageId, content)=>{\n                                    var _content_;\n                                    append({\n                                        role: \"user\",\n                                        content: \" \".concat(content === null || content === void 0 ? void 0 : (_content_ = content[0]) === null || _content_ === void 0 ? void 0 : _content_.text)\n                                    });\n                                }\n                            }, \"\".concat(message.id, \"-\").concat(index), false, {\n                                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 25\n                            }, undefined)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: messagesEndRef\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n            lineNumber: 313,\n            columnNumber: 13\n        }, undefined);\n    }, [\n        filterMessages,\n        isLoading,\n        append,\n        setInput,\n        handleFileSelect,\n        reload\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-full flex-col dark:bg-[#18181a] max-w-full\",\n        onDragOver: handleDragOver,\n        onDrop: handleDrop,\n        children: [\n            showJsx,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatInput__WEBPACK_IMPORTED_MODULE_7__.ChatInput, {\n                input: input,\n                setMessages: setMessages,\n                append: append,\n                messages: messages,\n                stopRuning: stop,\n                setInput: setInput,\n                isLoading: isLoading,\n                isUploading: isUploading,\n                uploadedImages: uploadedImages,\n                baseModal: baseModal,\n                handleInputChange: handleInputChange,\n                handleKeySubmit: handleKeySubmit,\n                handleSubmitWithFiles: handleSubmitWithFiles,\n                handleFileSelect: handleFileSelect,\n                removeImage: removeImage,\n                addImages: addImages,\n                setIsUploading: setIsUploading,\n                setBaseModal: setBaseModal\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                lineNumber: 355,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n        lineNumber: 349,\n        columnNumber: 9\n    }, undefined);\n};\n_s(BaseChat, \"CSTRmbkoHFlvd0xfpq7TKuR+img=\", false, function() {\n    return [\n        _stores_chatSlice__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        _stores_fileStore__WEBPACK_IMPORTED_MODULE_4__.useFileStore,\n        _stores_userSlice__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        _UserModal__WEBPACK_IMPORTED_MODULE_12__.useLimitModalStore,\n        _stores_chatModeSlice__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        ai_react__WEBPACK_IMPORTED_MODULE_15__.useChat\n    ];\n});\n_c = BaseChat;\nvar _c;\n$RefreshReg$(_c, \"BaseChat\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AiChat/chat/index.tsx\n"));

/***/ })

});