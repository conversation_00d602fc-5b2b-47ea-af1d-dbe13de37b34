"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ai-sdk+provider-utils@2.0.7_zod@3.24.1";
exports.ids = ["vendor-chunks/@ai-sdk+provider-utils@2.0.7_zod@3.24.1"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@ai-sdk+provider-utils@2.0.7_zod@3.24.1/node_modules/@ai-sdk/provider-utils/dist/index.mjs":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ai-sdk+provider-utils@2.0.7_zod@3.24.1/node_modules/@ai-sdk/provider-utils/dist/index.mjs ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   asValidator: () => (/* binding */ asValidator),\n/* harmony export */   combineHeaders: () => (/* binding */ combineHeaders),\n/* harmony export */   convertAsyncIteratorToReadableStream: () => (/* binding */ convertAsyncIteratorToReadableStream),\n/* harmony export */   convertBase64ToUint8Array: () => (/* binding */ convertBase64ToUint8Array),\n/* harmony export */   convertUint8ArrayToBase64: () => (/* binding */ convertUint8ArrayToBase64),\n/* harmony export */   createEventSourceResponseHandler: () => (/* binding */ createEventSourceResponseHandler),\n/* harmony export */   createIdGenerator: () => (/* binding */ createIdGenerator),\n/* harmony export */   createJsonErrorResponseHandler: () => (/* binding */ createJsonErrorResponseHandler),\n/* harmony export */   createJsonResponseHandler: () => (/* binding */ createJsonResponseHandler),\n/* harmony export */   createJsonStreamResponseHandler: () => (/* binding */ createJsonStreamResponseHandler),\n/* harmony export */   extractResponseHeaders: () => (/* binding */ extractResponseHeaders),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   getErrorMessage: () => (/* binding */ getErrorMessage),\n/* harmony export */   isAbortError: () => (/* binding */ isAbortError),\n/* harmony export */   isParsableJson: () => (/* binding */ isParsableJson),\n/* harmony export */   isValidator: () => (/* binding */ isValidator),\n/* harmony export */   loadApiKey: () => (/* binding */ loadApiKey),\n/* harmony export */   loadOptionalSetting: () => (/* binding */ loadOptionalSetting),\n/* harmony export */   loadSetting: () => (/* binding */ loadSetting),\n/* harmony export */   parseJSON: () => (/* binding */ parseJSON),\n/* harmony export */   postJsonToApi: () => (/* binding */ postJsonToApi),\n/* harmony export */   postToApi: () => (/* binding */ postToApi),\n/* harmony export */   resolve: () => (/* binding */ resolve),\n/* harmony export */   safeParseJSON: () => (/* binding */ safeParseJSON),\n/* harmony export */   safeValidateTypes: () => (/* binding */ safeValidateTypes),\n/* harmony export */   validateTypes: () => (/* binding */ validateTypes),\n/* harmony export */   validator: () => (/* binding */ validator),\n/* harmony export */   validatorSymbol: () => (/* binding */ validatorSymbol),\n/* harmony export */   withoutTrailingSlash: () => (/* binding */ withoutTrailingSlash),\n/* harmony export */   zodValidator: () => (/* binding */ zodValidator)\n/* harmony export */ });\n/* harmony import */ var _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ai-sdk/provider */ \"(rsc)/./node_modules/.pnpm/@ai-sdk+provider@1.0.4/node_modules/@ai-sdk/provider/dist/index.mjs\");\n/* harmony import */ var nanoid_non_secure__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! nanoid/non-secure */ \"(rsc)/./node_modules/.pnpm/nanoid@3.3.8/node_modules/nanoid/non-secure/index.js\");\n/* harmony import */ var secure_json_parse__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! secure-json-parse */ \"(rsc)/./node_modules/.pnpm/secure-json-parse@2.7.0/node_modules/secure-json-parse/index.js\");\n/* harmony import */ var eventsource_parser_stream__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! eventsource-parser/stream */ \"(rsc)/./node_modules/.pnpm/eventsource-parser@3.0.0/node_modules/eventsource-parser/dist/stream.js\");\n// src/combine-headers.ts\nfunction combineHeaders(...headers) {\n  return headers.reduce(\n    (combinedHeaders, currentHeaders) => ({\n      ...combinedHeaders,\n      ...currentHeaders != null ? currentHeaders : {}\n    }),\n    {}\n  );\n}\n\n// src/convert-async-iterator-to-readable-stream.ts\nfunction convertAsyncIteratorToReadableStream(iterator) {\n  return new ReadableStream({\n    /**\n     * Called when the consumer wants to pull more data from the stream.\n     *\n     * @param {ReadableStreamDefaultController<T>} controller - The controller to enqueue data into the stream.\n     * @returns {Promise<void>}\n     */\n    async pull(controller) {\n      try {\n        const { value, done } = await iterator.next();\n        if (done) {\n          controller.close();\n        } else {\n          controller.enqueue(value);\n        }\n      } catch (error) {\n        controller.error(error);\n      }\n    },\n    /**\n     * Called when the consumer cancels the stream.\n     */\n    cancel() {\n    }\n  });\n}\n\n// src/extract-response-headers.ts\nfunction extractResponseHeaders(response) {\n  const headers = {};\n  response.headers.forEach((value, key) => {\n    headers[key] = value;\n  });\n  return headers;\n}\n\n// src/generate-id.ts\n\n\nvar createIdGenerator = ({\n  prefix,\n  size: defaultSize = 16,\n  alphabet = \"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz\",\n  separator = \"-\"\n} = {}) => {\n  const generator = (0,nanoid_non_secure__WEBPACK_IMPORTED_MODULE_0__.customAlphabet)(alphabet, defaultSize);\n  if (prefix == null) {\n    return generator;\n  }\n  if (alphabet.includes(separator)) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.InvalidArgumentError({\n      argument: \"separator\",\n      message: `The separator \"${separator}\" must not be part of the alphabet \"${alphabet}\".`\n    });\n  }\n  return (size) => `${prefix}${separator}${generator(size)}`;\n};\nvar generateId = createIdGenerator();\n\n// src/get-error-message.ts\nfunction getErrorMessage(error) {\n  if (error == null) {\n    return \"unknown error\";\n  }\n  if (typeof error === \"string\") {\n    return error;\n  }\n  if (error instanceof Error) {\n    return error.message;\n  }\n  return JSON.stringify(error);\n}\n\n// src/is-abort-error.ts\nfunction isAbortError(error) {\n  return error instanceof Error && (error.name === \"AbortError\" || error.name === \"TimeoutError\");\n}\n\n// src/load-api-key.ts\n\nfunction loadApiKey({\n  apiKey,\n  environmentVariableName,\n  apiKeyParameterName = \"apiKey\",\n  description\n}) {\n  if (typeof apiKey === \"string\") {\n    return apiKey;\n  }\n  if (apiKey != null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadAPIKeyError({\n      message: `${description} API key must be a string.`\n    });\n  }\n  if (typeof process === \"undefined\") {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadAPIKeyError({\n      message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter. Environment variables is not supported in this environment.`\n    });\n  }\n  apiKey = process.env[environmentVariableName];\n  if (apiKey == null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadAPIKeyError({\n      message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter or the ${environmentVariableName} environment variable.`\n    });\n  }\n  if (typeof apiKey !== \"string\") {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadAPIKeyError({\n      message: `${description} API key must be a string. The value of the ${environmentVariableName} environment variable is not a string.`\n    });\n  }\n  return apiKey;\n}\n\n// src/load-optional-setting.ts\nfunction loadOptionalSetting({\n  settingValue,\n  environmentVariableName\n}) {\n  if (typeof settingValue === \"string\") {\n    return settingValue;\n  }\n  if (settingValue != null || typeof process === \"undefined\") {\n    return void 0;\n  }\n  settingValue = process.env[environmentVariableName];\n  if (settingValue == null || typeof settingValue !== \"string\") {\n    return void 0;\n  }\n  return settingValue;\n}\n\n// src/load-setting.ts\n\nfunction loadSetting({\n  settingValue,\n  environmentVariableName,\n  settingName,\n  description\n}) {\n  if (typeof settingValue === \"string\") {\n    return settingValue;\n  }\n  if (settingValue != null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadSettingError({\n      message: `${description} setting must be a string.`\n    });\n  }\n  if (typeof process === \"undefined\") {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadSettingError({\n      message: `${description} setting is missing. Pass it using the '${settingName}' parameter. Environment variables is not supported in this environment.`\n    });\n  }\n  settingValue = process.env[environmentVariableName];\n  if (settingValue == null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadSettingError({\n      message: `${description} setting is missing. Pass it using the '${settingName}' parameter or the ${environmentVariableName} environment variable.`\n    });\n  }\n  if (typeof settingValue !== \"string\") {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadSettingError({\n      message: `${description} setting must be a string. The value of the ${environmentVariableName} environment variable is not a string.`\n    });\n  }\n  return settingValue;\n}\n\n// src/parse-json.ts\n\n\n\n// src/validate-types.ts\n\n\n// src/validator.ts\nvar validatorSymbol = Symbol.for(\"vercel.ai.validator\");\nfunction validator(validate) {\n  return { [validatorSymbol]: true, validate };\n}\nfunction isValidator(value) {\n  return typeof value === \"object\" && value !== null && validatorSymbol in value && value[validatorSymbol] === true && \"validate\" in value;\n}\nfunction asValidator(value) {\n  return isValidator(value) ? value : zodValidator(value);\n}\nfunction zodValidator(zodSchema) {\n  return validator((value) => {\n    const result = zodSchema.safeParse(value);\n    return result.success ? { success: true, value: result.data } : { success: false, error: result.error };\n  });\n}\n\n// src/validate-types.ts\nfunction validateTypes({\n  value,\n  schema: inputSchema\n}) {\n  const result = safeValidateTypes({ value, schema: inputSchema });\n  if (!result.success) {\n    throw _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TypeValidationError.wrap({ value, cause: result.error });\n  }\n  return result.value;\n}\nfunction safeValidateTypes({\n  value,\n  schema\n}) {\n  const validator2 = asValidator(schema);\n  try {\n    if (validator2.validate == null) {\n      return { success: true, value };\n    }\n    const result = validator2.validate(value);\n    if (result.success) {\n      return result;\n    }\n    return {\n      success: false,\n      error: _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TypeValidationError.wrap({ value, cause: result.error })\n    };\n  } catch (error) {\n    return {\n      success: false,\n      error: _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TypeValidationError.wrap({ value, cause: error })\n    };\n  }\n}\n\n// src/parse-json.ts\nfunction parseJSON({\n  text,\n  schema\n}) {\n  try {\n    const value = secure_json_parse__WEBPACK_IMPORTED_MODULE_2__.parse(text);\n    if (schema == null) {\n      return value;\n    }\n    return validateTypes({ value, schema });\n  } catch (error) {\n    if (_ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.JSONParseError.isInstance(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TypeValidationError.isInstance(error)) {\n      throw error;\n    }\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.JSONParseError({ text, cause: error });\n  }\n}\nfunction safeParseJSON({\n  text,\n  schema\n}) {\n  try {\n    const value = secure_json_parse__WEBPACK_IMPORTED_MODULE_2__.parse(text);\n    if (schema == null) {\n      return {\n        success: true,\n        value\n      };\n    }\n    return safeValidateTypes({ value, schema });\n  } catch (error) {\n    return {\n      success: false,\n      error: _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.JSONParseError.isInstance(error) ? error : new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.JSONParseError({ text, cause: error })\n    };\n  }\n}\nfunction isParsableJson(input) {\n  try {\n    secure_json_parse__WEBPACK_IMPORTED_MODULE_2__.parse(input);\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\n// src/post-to-api.ts\n\n\n// src/remove-undefined-entries.ts\nfunction removeUndefinedEntries(record) {\n  return Object.fromEntries(\n    Object.entries(record).filter(([_key, value]) => value != null)\n  );\n}\n\n// src/post-to-api.ts\nvar getOriginalFetch = () => globalThis.fetch;\nvar postJsonToApi = async ({\n  url,\n  headers,\n  body,\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch\n}) => postToApi({\n  url,\n  headers: {\n    \"Content-Type\": \"application/json\",\n    ...headers\n  },\n  body: {\n    content: JSON.stringify(body),\n    values: body\n  },\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch\n});\nvar postToApi = async ({\n  url,\n  headers = {},\n  body,\n  successfulResponseHandler,\n  failedResponseHandler,\n  abortSignal,\n  fetch = getOriginalFetch()\n}) => {\n  try {\n    const response = await fetch(url, {\n      method: \"POST\",\n      headers: removeUndefinedEntries(headers),\n      body: body.content,\n      signal: abortSignal\n    });\n    const responseHeaders = extractResponseHeaders(response);\n    if (!response.ok) {\n      let errorInformation;\n      try {\n        errorInformation = await failedResponseHandler({\n          response,\n          url,\n          requestBodyValues: body.values\n        });\n      } catch (error) {\n        if (isAbortError(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError.isInstance(error)) {\n          throw error;\n        }\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n          message: \"Failed to process error response\",\n          cause: error,\n          statusCode: response.status,\n          url,\n          responseHeaders,\n          requestBodyValues: body.values\n        });\n      }\n      throw errorInformation.value;\n    }\n    try {\n      return await successfulResponseHandler({\n        response,\n        url,\n        requestBodyValues: body.values\n      });\n    } catch (error) {\n      if (error instanceof Error) {\n        if (isAbortError(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError.isInstance(error)) {\n          throw error;\n        }\n      }\n      throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: \"Failed to process successful response\",\n        cause: error,\n        statusCode: response.status,\n        url,\n        responseHeaders,\n        requestBodyValues: body.values\n      });\n    }\n  } catch (error) {\n    if (isAbortError(error)) {\n      throw error;\n    }\n    if (error instanceof TypeError && error.message === \"fetch failed\") {\n      const cause = error.cause;\n      if (cause != null) {\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n          message: `Cannot connect to API: ${cause.message}`,\n          cause,\n          url,\n          requestBodyValues: body.values,\n          isRetryable: true\n          // retry when network error\n        });\n      }\n    }\n    throw error;\n  }\n};\n\n// src/resolve.ts\nasync function resolve(value) {\n  if (typeof value === \"function\") {\n    value = value();\n  }\n  return Promise.resolve(value);\n}\n\n// src/response-handler.ts\n\n\nvar createJsonErrorResponseHandler = ({\n  errorSchema,\n  errorToMessage,\n  isRetryable\n}) => async ({ response, url, requestBodyValues }) => {\n  const responseBody = await response.text();\n  const responseHeaders = extractResponseHeaders(response);\n  if (responseBody.trim() === \"\") {\n    return {\n      responseHeaders,\n      value: new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: response.statusText,\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n        isRetryable: isRetryable == null ? void 0 : isRetryable(response)\n      })\n    };\n  }\n  try {\n    const parsedError = parseJSON({\n      text: responseBody,\n      schema: errorSchema\n    });\n    return {\n      responseHeaders,\n      value: new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: errorToMessage(parsedError),\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n        data: parsedError,\n        isRetryable: isRetryable == null ? void 0 : isRetryable(response, parsedError)\n      })\n    };\n  } catch (parseError) {\n    return {\n      responseHeaders,\n      value: new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: response.statusText,\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n        isRetryable: isRetryable == null ? void 0 : isRetryable(response)\n      })\n    };\n  }\n};\nvar createEventSourceResponseHandler = (chunkSchema) => async ({ response }) => {\n  const responseHeaders = extractResponseHeaders(response);\n  if (response.body == null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.EmptyResponseBodyError({});\n  }\n  return {\n    responseHeaders,\n    value: response.body.pipeThrough(new TextDecoderStream()).pipeThrough(new eventsource_parser_stream__WEBPACK_IMPORTED_MODULE_3__.EventSourceParserStream()).pipeThrough(\n      new TransformStream({\n        transform({ data }, controller) {\n          if (data === \"[DONE]\") {\n            return;\n          }\n          controller.enqueue(\n            safeParseJSON({\n              text: data,\n              schema: chunkSchema\n            })\n          );\n        }\n      })\n    )\n  };\n};\nvar createJsonStreamResponseHandler = (chunkSchema) => async ({ response }) => {\n  const responseHeaders = extractResponseHeaders(response);\n  if (response.body == null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.EmptyResponseBodyError({});\n  }\n  let buffer = \"\";\n  return {\n    responseHeaders,\n    value: response.body.pipeThrough(new TextDecoderStream()).pipeThrough(\n      new TransformStream({\n        transform(chunkText, controller) {\n          if (chunkText.endsWith(\"\\n\")) {\n            controller.enqueue(\n              safeParseJSON({\n                text: buffer + chunkText,\n                schema: chunkSchema\n              })\n            );\n            buffer = \"\";\n          } else {\n            buffer += chunkText;\n          }\n        }\n      })\n    )\n  };\n};\nvar createJsonResponseHandler = (responseSchema) => async ({ response, url, requestBodyValues }) => {\n  const responseBody = await response.text();\n  const parsedResult = safeParseJSON({\n    text: responseBody,\n    schema: responseSchema\n  });\n  const responseHeaders = extractResponseHeaders(response);\n  if (!parsedResult.success) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n      message: \"Invalid JSON response\",\n      cause: parsedResult.error,\n      statusCode: response.status,\n      responseHeaders,\n      responseBody,\n      url,\n      requestBodyValues\n    });\n  }\n  return {\n    responseHeaders,\n    value: parsedResult.value\n  };\n};\n\n// src/uint8-utils.ts\nvar { btoa, atob } = globalThis;\nfunction convertBase64ToUint8Array(base64String) {\n  const base64Url = base64String.replace(/-/g, \"+\").replace(/_/g, \"/\");\n  const latin1string = atob(base64Url);\n  return Uint8Array.from(latin1string, (byte) => byte.codePointAt(0));\n}\nfunction convertUint8ArrayToBase64(array) {\n  let latin1string = \"\";\n  for (let i = 0; i < array.length; i++) {\n    latin1string += String.fromCodePoint(array[i]);\n  }\n  return btoa(latin1string);\n}\n\n// src/without-trailing-slash.ts\nfunction withoutTrailingSlash(url) {\n  return url == null ? void 0 : url.replace(/\\/$/, \"\");\n}\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@ai-sdk+provider-utils@2.0.7_zod@3.24.1/node_modules/@ai-sdk/provider-utils/dist/index.mjs\n");

/***/ })

};
;