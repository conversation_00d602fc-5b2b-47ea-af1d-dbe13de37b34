"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-resizable-panels@2.1._c7b59720ff81072a693309ea5f1b1633";
exports.ids = ["vendor-chunks/react-resizable-panels@2.1._c7b59720ff81072a693309ea5f1b1633"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/react-resizable-panels@2.1._c7b59720ff81072a693309ea5f1b1633/node_modules/react-resizable-panels/dist/react-resizable-panels.development.node.esm.js":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-resizable-panels@2.1._c7b59720ff81072a693309ea5f1b1633/node_modules/react-resizable-panels/dist/react-resizable-panels.development.node.esm.js ***!
  \*********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DATA_ATTRIBUTES: () => (/* binding */ DATA_ATTRIBUTES),\n/* harmony export */   Panel: () => (/* binding */ Panel),\n/* harmony export */   PanelGroup: () => (/* binding */ PanelGroup),\n/* harmony export */   PanelResizeHandle: () => (/* binding */ PanelResizeHandle),\n/* harmony export */   assert: () => (/* binding */ assert),\n/* harmony export */   disableGlobalCursorStyles: () => (/* binding */ disableGlobalCursorStyles),\n/* harmony export */   enableGlobalCursorStyles: () => (/* binding */ enableGlobalCursorStyles),\n/* harmony export */   getIntersectingRectangle: () => (/* binding */ getIntersectingRectangle),\n/* harmony export */   getPanelElement: () => (/* binding */ getPanelElement),\n/* harmony export */   getPanelElementsForGroup: () => (/* binding */ getPanelElementsForGroup),\n/* harmony export */   getPanelGroupElement: () => (/* binding */ getPanelGroupElement),\n/* harmony export */   getResizeHandleElement: () => (/* binding */ getResizeHandleElement),\n/* harmony export */   getResizeHandleElementIndex: () => (/* binding */ getResizeHandleElementIndex),\n/* harmony export */   getResizeHandleElementsForGroup: () => (/* binding */ getResizeHandleElementsForGroup),\n/* harmony export */   getResizeHandlePanelIds: () => (/* binding */ getResizeHandlePanelIds),\n/* harmony export */   intersects: () => (/* binding */ intersects),\n/* harmony export */   setNonce: () => (/* binding */ setNonce)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\n\n// The \"contextmenu\" event is not supported as a PointerEvent in all browsers yet, so MouseEvent still need to be handled\n\nconst PanelGroupContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);\nPanelGroupContext.displayName = \"PanelGroupContext\";\n\nconst DATA_ATTRIBUTES = {\n  group: \"data-panel-group\",\n  groupDirection: \"data-panel-group-direction\",\n  groupId: \"data-panel-group-id\",\n  panel: \"data-panel\",\n  panelCollapsible: \"data-panel-collapsible\",\n  panelId: \"data-panel-id\",\n  panelSize: \"data-panel-size\",\n  resizeHandle: \"data-resize-handle\",\n  resizeHandleActive: \"data-resize-handle-active\",\n  resizeHandleEnabled: \"data-panel-resize-handle-enabled\",\n  resizeHandleId: \"data-panel-resize-handle-id\",\n  resizeHandleState: \"data-resize-handle-state\"\n};\nconst PRECISION = 10;\n\nconst useId = react__WEBPACK_IMPORTED_MODULE_0__[\"useId\".toString()];\nconst wrappedUseId = typeof useId === \"function\" ? useId : () => null;\nlet counter = 0;\nfunction useUniqueId(idFromParams = null) {\n  const idFromUseId = wrappedUseId();\n  const idRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(idFromParams || idFromUseId || null);\n  if (idRef.current === null) {\n    idRef.current = \"\" + counter++;\n  }\n  return idFromParams !== null && idFromParams !== void 0 ? idFromParams : idRef.current;\n}\n\nfunction PanelWithForwardedRef({\n  children,\n  className: classNameFromProps = \"\",\n  collapsedSize,\n  collapsible,\n  defaultSize,\n  forwardedRef,\n  id: idFromProps,\n  maxSize,\n  minSize,\n  onCollapse,\n  onExpand,\n  onResize,\n  order,\n  style: styleFromProps,\n  tagName: Type = \"div\",\n  ...rest\n}) {\n  const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(PanelGroupContext);\n  if (context === null) {\n    throw Error(`Panel components must be rendered within a PanelGroup container`);\n  }\n  const {\n    collapsePanel,\n    expandPanel,\n    getPanelSize,\n    getPanelStyle,\n    groupId,\n    isPanelCollapsed,\n    reevaluatePanelConstraints,\n    registerPanel,\n    resizePanel,\n    unregisterPanel\n  } = context;\n  const panelId = useUniqueId(idFromProps);\n  const panelDataRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    callbacks: {\n      onCollapse,\n      onExpand,\n      onResize\n    },\n    constraints: {\n      collapsedSize,\n      collapsible,\n      defaultSize,\n      maxSize,\n      minSize\n    },\n    id: panelId,\n    idIsFromProps: idFromProps !== undefined,\n    order\n  });\n  const devWarningsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    didLogMissingDefaultSizeWarning: false\n  });\n\n  // Normally we wouldn't log a warning during render,\n  // but effects don't run on the server, so we can't do it there\n  {\n    if (!devWarningsRef.current.didLogMissingDefaultSizeWarning) {\n      if (defaultSize == null) {\n        devWarningsRef.current.didLogMissingDefaultSizeWarning = true;\n        console.warn(`WARNING: Panel defaultSize prop recommended to avoid layout shift after server rendering`);\n      }\n    }\n  }\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(forwardedRef, () => ({\n    collapse: () => {\n      collapsePanel(panelDataRef.current);\n    },\n    expand: minSize => {\n      expandPanel(panelDataRef.current, minSize);\n    },\n    getId() {\n      return panelId;\n    },\n    getSize() {\n      return getPanelSize(panelDataRef.current);\n    },\n    isCollapsed() {\n      return isPanelCollapsed(panelDataRef.current);\n    },\n    isExpanded() {\n      return !isPanelCollapsed(panelDataRef.current);\n    },\n    resize: size => {\n      resizePanel(panelDataRef.current, size);\n    }\n  }), [collapsePanel, expandPanel, getPanelSize, isPanelCollapsed, panelId, resizePanel]);\n  const style = getPanelStyle(panelDataRef.current, defaultSize);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(Type, {\n    ...rest,\n    children,\n    className: classNameFromProps,\n    id: panelId,\n    style: {\n      ...style,\n      ...styleFromProps\n    },\n    // CSS selectors\n    [DATA_ATTRIBUTES.groupId]: groupId,\n    [DATA_ATTRIBUTES.panel]: \"\",\n    [DATA_ATTRIBUTES.panelCollapsible]: collapsible || undefined,\n    [DATA_ATTRIBUTES.panelId]: panelId,\n    [DATA_ATTRIBUTES.panelSize]: parseFloat(\"\" + style.flexGrow).toFixed(1)\n  });\n}\nconst Panel = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref) => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(PanelWithForwardedRef, {\n  ...props,\n  forwardedRef: ref\n}));\nPanelWithForwardedRef.displayName = \"Panel\";\nPanel.displayName = \"forwardRef(Panel)\";\n\nlet nonce;\nfunction getNonce() {\n  return nonce;\n}\nfunction setNonce(value) {\n  nonce = value;\n}\n\nlet currentCursorStyle = null;\nlet enabled = true;\nlet prevRuleIndex = -1;\nlet styleElement = null;\nfunction disableGlobalCursorStyles() {\n  enabled = false;\n}\nfunction enableGlobalCursorStyles() {\n  enabled = true;\n}\nfunction getCursorStyle(state, constraintFlags) {\n  if (constraintFlags) {\n    const horizontalMin = (constraintFlags & EXCEEDED_HORIZONTAL_MIN) !== 0;\n    const horizontalMax = (constraintFlags & EXCEEDED_HORIZONTAL_MAX) !== 0;\n    const verticalMin = (constraintFlags & EXCEEDED_VERTICAL_MIN) !== 0;\n    const verticalMax = (constraintFlags & EXCEEDED_VERTICAL_MAX) !== 0;\n    if (horizontalMin) {\n      if (verticalMin) {\n        return \"se-resize\";\n      } else if (verticalMax) {\n        return \"ne-resize\";\n      } else {\n        return \"e-resize\";\n      }\n    } else if (horizontalMax) {\n      if (verticalMin) {\n        return \"sw-resize\";\n      } else if (verticalMax) {\n        return \"nw-resize\";\n      } else {\n        return \"w-resize\";\n      }\n    } else if (verticalMin) {\n      return \"s-resize\";\n    } else if (verticalMax) {\n      return \"n-resize\";\n    }\n  }\n  switch (state) {\n    case \"horizontal\":\n      return \"ew-resize\";\n    case \"intersection\":\n      return \"move\";\n    case \"vertical\":\n      return \"ns-resize\";\n  }\n}\nfunction resetGlobalCursorStyle() {\n  if (styleElement !== null) {\n    document.head.removeChild(styleElement);\n    currentCursorStyle = null;\n    styleElement = null;\n    prevRuleIndex = -1;\n  }\n}\nfunction setGlobalCursorStyle(state, constraintFlags) {\n  var _styleElement$sheet$i, _styleElement$sheet2;\n  if (!enabled) {\n    return;\n  }\n  const style = getCursorStyle(state, constraintFlags);\n  if (currentCursorStyle === style) {\n    return;\n  }\n  currentCursorStyle = style;\n  if (styleElement === null) {\n    styleElement = document.createElement(\"style\");\n    const nonce = getNonce();\n    if (nonce) {\n      styleElement.setAttribute(\"nonce\", nonce);\n    }\n    document.head.appendChild(styleElement);\n  }\n  if (prevRuleIndex >= 0) {\n    var _styleElement$sheet;\n    (_styleElement$sheet = styleElement.sheet) === null || _styleElement$sheet === void 0 ? void 0 : _styleElement$sheet.removeRule(prevRuleIndex);\n  }\n  prevRuleIndex = (_styleElement$sheet$i = (_styleElement$sheet2 = styleElement.sheet) === null || _styleElement$sheet2 === void 0 ? void 0 : _styleElement$sheet2.insertRule(`*{cursor: ${style} !important;}`)) !== null && _styleElement$sheet$i !== void 0 ? _styleElement$sheet$i : -1;\n}\n\nfunction isKeyDown(event) {\n  return event.type === \"keydown\";\n}\nfunction isPointerEvent(event) {\n  return event.type.startsWith(\"pointer\");\n}\nfunction isMouseEvent(event) {\n  return event.type.startsWith(\"mouse\");\n}\n\nfunction getResizeEventCoordinates(event) {\n  if (isPointerEvent(event)) {\n    if (event.isPrimary) {\n      return {\n        x: event.clientX,\n        y: event.clientY\n      };\n    }\n  } else if (isMouseEvent(event)) {\n    return {\n      x: event.clientX,\n      y: event.clientY\n    };\n  }\n  return {\n    x: Infinity,\n    y: Infinity\n  };\n}\n\nfunction getInputType() {\n  if (typeof matchMedia === \"function\") {\n    return matchMedia(\"(pointer:coarse)\").matches ? \"coarse\" : \"fine\";\n  }\n}\n\nfunction intersects(rectOne, rectTwo, strict) {\n  if (strict) {\n    return rectOne.x < rectTwo.x + rectTwo.width && rectOne.x + rectOne.width > rectTwo.x && rectOne.y < rectTwo.y + rectTwo.height && rectOne.y + rectOne.height > rectTwo.y;\n  } else {\n    return rectOne.x <= rectTwo.x + rectTwo.width && rectOne.x + rectOne.width >= rectTwo.x && rectOne.y <= rectTwo.y + rectTwo.height && rectOne.y + rectOne.height >= rectTwo.y;\n  }\n}\n\n// Forked from NPM stacking-order@2.0.0\n\n/**\n * Determine which of two nodes appears in front of the other —\n * if `a` is in front, returns 1, otherwise returns -1\n * @param {HTMLElement | SVGElement} a\n * @param {HTMLElement | SVGElement} b\n */\nfunction compare(a, b) {\n  if (a === b) throw new Error(\"Cannot compare node with itself\");\n  const ancestors = {\n    a: get_ancestors(a),\n    b: get_ancestors(b)\n  };\n  let common_ancestor;\n\n  // remove shared ancestors\n  while (ancestors.a.at(-1) === ancestors.b.at(-1)) {\n    a = ancestors.a.pop();\n    b = ancestors.b.pop();\n    common_ancestor = a;\n  }\n  assert(common_ancestor, \"Stacking order can only be calculated for elements with a common ancestor\");\n  const z_indexes = {\n    a: get_z_index(find_stacking_context(ancestors.a)),\n    b: get_z_index(find_stacking_context(ancestors.b))\n  };\n  if (z_indexes.a === z_indexes.b) {\n    const children = common_ancestor.childNodes;\n    const furthest_ancestors = {\n      a: ancestors.a.at(-1),\n      b: ancestors.b.at(-1)\n    };\n    let i = children.length;\n    while (i--) {\n      const child = children[i];\n      if (child === furthest_ancestors.a) return 1;\n      if (child === furthest_ancestors.b) return -1;\n    }\n  }\n  return Math.sign(z_indexes.a - z_indexes.b);\n}\nconst props = /\\b(?:position|zIndex|opacity|transform|webkitTransform|mixBlendMode|filter|webkitFilter|isolation)\\b/;\n\n/** @param {HTMLElement | SVGElement} node */\nfunction is_flex_item(node) {\n  var _get_parent;\n  // @ts-ignore\n  const display = getComputedStyle((_get_parent = get_parent(node)) !== null && _get_parent !== void 0 ? _get_parent : node).display;\n  return display === \"flex\" || display === \"inline-flex\";\n}\n\n/** @param {HTMLElement | SVGElement} node */\nfunction creates_stacking_context(node) {\n  const style = getComputedStyle(node);\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Positioning/Understanding_z_index/The_stacking_context\n  if (style.position === \"fixed\") return true;\n  // Forked to fix upstream bug https://github.com/Rich-Harris/stacking-order/issues/3\n  // if (\n  //   (style.zIndex !== \"auto\" && style.position !== \"static\") ||\n  //   is_flex_item(node)\n  // )\n  if (style.zIndex !== \"auto\" && (style.position !== \"static\" || is_flex_item(node))) return true;\n  if (+style.opacity < 1) return true;\n  if (\"transform\" in style && style.transform !== \"none\") return true;\n  if (\"webkitTransform\" in style && style.webkitTransform !== \"none\") return true;\n  if (\"mixBlendMode\" in style && style.mixBlendMode !== \"normal\") return true;\n  if (\"filter\" in style && style.filter !== \"none\") return true;\n  if (\"webkitFilter\" in style && style.webkitFilter !== \"none\") return true;\n  if (\"isolation\" in style && style.isolation === \"isolate\") return true;\n  if (props.test(style.willChange)) return true;\n  // @ts-expect-error\n  if (style.webkitOverflowScrolling === \"touch\") return true;\n  return false;\n}\n\n/** @param {(HTMLElement| SVGElement)[]} nodes */\nfunction find_stacking_context(nodes) {\n  let i = nodes.length;\n  while (i--) {\n    const node = nodes[i];\n    assert(node, \"Missing node\");\n    if (creates_stacking_context(node)) return node;\n  }\n  return null;\n}\n\n/** @param {HTMLElement | SVGElement} node */\nfunction get_z_index(node) {\n  return node && Number(getComputedStyle(node).zIndex) || 0;\n}\n\n/** @param {HTMLElement} node */\nfunction get_ancestors(node) {\n  const ancestors = [];\n  while (node) {\n    ancestors.push(node);\n    // @ts-ignore\n    node = get_parent(node);\n  }\n  return ancestors; // [ node, ... <body>, <html>, document ]\n}\n\n/** @param {HTMLElement} node */\nfunction get_parent(node) {\n  const {\n    parentNode\n  } = node;\n  if (parentNode && parentNode instanceof ShadowRoot) {\n    return parentNode.host;\n  }\n  return parentNode;\n}\n\nconst EXCEEDED_HORIZONTAL_MIN = 0b0001;\nconst EXCEEDED_HORIZONTAL_MAX = 0b0010;\nconst EXCEEDED_VERTICAL_MIN = 0b0100;\nconst EXCEEDED_VERTICAL_MAX = 0b1000;\nconst isCoarsePointer = getInputType() === \"coarse\";\nlet intersectingHandles = [];\nlet isPointerDown = false;\nlet ownerDocumentCounts = new Map();\nlet panelConstraintFlags = new Map();\nconst registeredResizeHandlers = new Set();\nfunction registerResizeHandle(resizeHandleId, element, direction, hitAreaMargins, setResizeHandlerState) {\n  var _ownerDocumentCounts$;\n  const {\n    ownerDocument\n  } = element;\n  const data = {\n    direction,\n    element,\n    hitAreaMargins,\n    setResizeHandlerState\n  };\n  const count = (_ownerDocumentCounts$ = ownerDocumentCounts.get(ownerDocument)) !== null && _ownerDocumentCounts$ !== void 0 ? _ownerDocumentCounts$ : 0;\n  ownerDocumentCounts.set(ownerDocument, count + 1);\n  registeredResizeHandlers.add(data);\n  updateListeners();\n  return function unregisterResizeHandle() {\n    var _ownerDocumentCounts$2;\n    panelConstraintFlags.delete(resizeHandleId);\n    registeredResizeHandlers.delete(data);\n    const count = (_ownerDocumentCounts$2 = ownerDocumentCounts.get(ownerDocument)) !== null && _ownerDocumentCounts$2 !== void 0 ? _ownerDocumentCounts$2 : 1;\n    ownerDocumentCounts.set(ownerDocument, count - 1);\n    updateListeners();\n    if (count === 1) {\n      ownerDocumentCounts.delete(ownerDocument);\n    }\n\n    // If the resize handle that is currently unmounting is intersecting with the pointer,\n    // update the global pointer to account for the change\n    if (intersectingHandles.includes(data)) {\n      const index = intersectingHandles.indexOf(data);\n      if (index >= 0) {\n        intersectingHandles.splice(index, 1);\n      }\n      updateCursor();\n\n      // Also instruct the handle to stop dragging; this prevents the parent group from being left in an inconsistent state\n      // See github.com/bvaughn/react-resizable-panels/issues/402\n      setResizeHandlerState(\"up\", true, null);\n    }\n  };\n}\nfunction handlePointerDown(event) {\n  const {\n    target\n  } = event;\n  const {\n    x,\n    y\n  } = getResizeEventCoordinates(event);\n  isPointerDown = true;\n  recalculateIntersectingHandles({\n    target,\n    x,\n    y\n  });\n  updateListeners();\n  if (intersectingHandles.length > 0) {\n    updateResizeHandlerStates(\"down\", event);\n    event.preventDefault();\n    if (!isWithinResizeHandle(target)) {\n      event.stopImmediatePropagation();\n    }\n  }\n}\nfunction handlePointerMove(event) {\n  const {\n    x,\n    y\n  } = getResizeEventCoordinates(event);\n\n  // Edge case (see #340)\n  // Detect when the pointer has been released outside an iframe on a different domain\n  if (isPointerDown && event.buttons === 0) {\n    isPointerDown = false;\n    updateResizeHandlerStates(\"up\", event);\n  }\n  if (!isPointerDown) {\n    const {\n      target\n    } = event;\n\n    // Recalculate intersecting handles whenever the pointer moves, except if it has already been pressed\n    // at that point, the handles may not move with the pointer (depending on constraints)\n    // but the same set of active handles should be locked until the pointer is released\n    recalculateIntersectingHandles({\n      target,\n      x,\n      y\n    });\n  }\n  updateResizeHandlerStates(\"move\", event);\n\n  // Update cursor based on return value(s) from active handles\n  updateCursor();\n  if (intersectingHandles.length > 0) {\n    event.preventDefault();\n  }\n}\nfunction handlePointerUp(event) {\n  const {\n    target\n  } = event;\n  const {\n    x,\n    y\n  } = getResizeEventCoordinates(event);\n  panelConstraintFlags.clear();\n  isPointerDown = false;\n  if (intersectingHandles.length > 0) {\n    event.preventDefault();\n    if (!isWithinResizeHandle(target)) {\n      event.stopImmediatePropagation();\n    }\n  }\n  updateResizeHandlerStates(\"up\", event);\n  recalculateIntersectingHandles({\n    target,\n    x,\n    y\n  });\n  updateCursor();\n  updateListeners();\n}\nfunction isWithinResizeHandle(element) {\n  let currentElement = element;\n  while (currentElement) {\n    if (currentElement.hasAttribute(DATA_ATTRIBUTES.resizeHandle)) {\n      return true;\n    }\n    currentElement = currentElement.parentElement;\n  }\n  return false;\n}\nfunction recalculateIntersectingHandles({\n  target,\n  x,\n  y\n}) {\n  intersectingHandles.splice(0);\n  let targetElement = null;\n  if (target instanceof HTMLElement || target instanceof SVGElement) {\n    targetElement = target;\n  }\n  registeredResizeHandlers.forEach(data => {\n    const {\n      element: dragHandleElement,\n      hitAreaMargins\n    } = data;\n    const dragHandleRect = dragHandleElement.getBoundingClientRect();\n    const {\n      bottom,\n      left,\n      right,\n      top\n    } = dragHandleRect;\n    const margin = isCoarsePointer ? hitAreaMargins.coarse : hitAreaMargins.fine;\n    const eventIntersects = x >= left - margin && x <= right + margin && y >= top - margin && y <= bottom + margin;\n    if (eventIntersects) {\n      // TRICKY\n      // We listen for pointers events at the root in order to support hit area margins\n      // (determining when the pointer is close enough to an element to be considered a \"hit\")\n      // Clicking on an element \"above\" a handle (e.g. a modal) should prevent a hit though\n      // so at this point we need to compare stacking order of a potentially intersecting drag handle,\n      // and the element that was actually clicked/touched\n      if (targetElement !== null && document.contains(targetElement) && dragHandleElement !== targetElement && !dragHandleElement.contains(targetElement) && !targetElement.contains(dragHandleElement) &&\n      // Calculating stacking order has a cost, so we should avoid it if possible\n      // That is why we only check potentially intersecting handles,\n      // and why we skip if the event target is within the handle's DOM\n      compare(targetElement, dragHandleElement) > 0) {\n        // If the target is above the drag handle, then we also need to confirm they overlap\n        // If they are beside each other (e.g. a panel and its drag handle) then the handle is still interactive\n        //\n        // It's not enough to compare only the target\n        // The target might be a small element inside of a larger container\n        // (For example, a SPAN or a DIV inside of a larger modal dialog)\n        let currentElement = targetElement;\n        let didIntersect = false;\n        while (currentElement) {\n          if (currentElement.contains(dragHandleElement)) {\n            break;\n          } else if (intersects(currentElement.getBoundingClientRect(), dragHandleRect, true)) {\n            didIntersect = true;\n            break;\n          }\n          currentElement = currentElement.parentElement;\n        }\n        if (didIntersect) {\n          return;\n        }\n      }\n      intersectingHandles.push(data);\n    }\n  });\n}\nfunction reportConstraintsViolation(resizeHandleId, flag) {\n  panelConstraintFlags.set(resizeHandleId, flag);\n}\nfunction updateCursor() {\n  let intersectsHorizontal = false;\n  let intersectsVertical = false;\n  intersectingHandles.forEach(data => {\n    const {\n      direction\n    } = data;\n    if (direction === \"horizontal\") {\n      intersectsHorizontal = true;\n    } else {\n      intersectsVertical = true;\n    }\n  });\n  let constraintFlags = 0;\n  panelConstraintFlags.forEach(flag => {\n    constraintFlags |= flag;\n  });\n  if (intersectsHorizontal && intersectsVertical) {\n    setGlobalCursorStyle(\"intersection\", constraintFlags);\n  } else if (intersectsHorizontal) {\n    setGlobalCursorStyle(\"horizontal\", constraintFlags);\n  } else if (intersectsVertical) {\n    setGlobalCursorStyle(\"vertical\", constraintFlags);\n  } else {\n    resetGlobalCursorStyle();\n  }\n}\nlet listenersAbortController = new AbortController();\nfunction updateListeners() {\n  listenersAbortController.abort();\n  listenersAbortController = new AbortController();\n  const options = {\n    capture: true,\n    signal: listenersAbortController.signal\n  };\n  if (!registeredResizeHandlers.size) {\n    return;\n  }\n  if (isPointerDown) {\n    if (intersectingHandles.length > 0) {\n      ownerDocumentCounts.forEach((count, ownerDocument) => {\n        const {\n          body\n        } = ownerDocument;\n        if (count > 0) {\n          body.addEventListener(\"contextmenu\", handlePointerUp, options);\n          body.addEventListener(\"pointerleave\", handlePointerMove, options);\n          body.addEventListener(\"pointermove\", handlePointerMove, options);\n        }\n      });\n    }\n    window.addEventListener(\"pointerup\", handlePointerUp, options);\n    window.addEventListener(\"pointercancel\", handlePointerUp, options);\n  } else {\n    ownerDocumentCounts.forEach((count, ownerDocument) => {\n      const {\n        body\n      } = ownerDocument;\n      if (count > 0) {\n        body.addEventListener(\"pointerdown\", handlePointerDown, options);\n        body.addEventListener(\"pointermove\", handlePointerMove, options);\n      }\n    });\n  }\n}\nfunction updateResizeHandlerStates(action, event) {\n  registeredResizeHandlers.forEach(data => {\n    const {\n      setResizeHandlerState\n    } = data;\n    const isActive = intersectingHandles.includes(data);\n    setResizeHandlerState(action, isActive, event);\n  });\n}\n\nfunction useForceUpdate() {\n  const [_, setCount] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => setCount(prevCount => prevCount + 1), []);\n}\n\nfunction assert(expectedCondition, message) {\n  if (!expectedCondition) {\n    console.error(message);\n    throw Error(message);\n  }\n}\n\nfunction fuzzyCompareNumbers(actual, expected, fractionDigits = PRECISION) {\n  if (actual.toFixed(fractionDigits) === expected.toFixed(fractionDigits)) {\n    return 0;\n  } else {\n    return actual > expected ? 1 : -1;\n  }\n}\nfunction fuzzyNumbersEqual$1(actual, expected, fractionDigits = PRECISION) {\n  return fuzzyCompareNumbers(actual, expected, fractionDigits) === 0;\n}\n\nfunction fuzzyNumbersEqual(actual, expected, fractionDigits) {\n  return fuzzyCompareNumbers(actual, expected, fractionDigits) === 0;\n}\n\nfunction fuzzyLayoutsEqual(actual, expected, fractionDigits) {\n  if (actual.length !== expected.length) {\n    return false;\n  }\n  for (let index = 0; index < actual.length; index++) {\n    const actualSize = actual[index];\n    const expectedSize = expected[index];\n    if (!fuzzyNumbersEqual(actualSize, expectedSize, fractionDigits)) {\n      return false;\n    }\n  }\n  return true;\n}\n\n// Panel size must be in percentages; pixel values should be pre-converted\nfunction resizePanel({\n  panelConstraints: panelConstraintsArray,\n  panelIndex,\n  size\n}) {\n  const panelConstraints = panelConstraintsArray[panelIndex];\n  assert(panelConstraints != null, `Panel constraints not found for index ${panelIndex}`);\n  let {\n    collapsedSize = 0,\n    collapsible,\n    maxSize = 100,\n    minSize = 0\n  } = panelConstraints;\n  if (fuzzyCompareNumbers(size, minSize) < 0) {\n    if (collapsible) {\n      // Collapsible panels should snap closed or open only once they cross the halfway point between collapsed and min size.\n      const halfwayPoint = (collapsedSize + minSize) / 2;\n      if (fuzzyCompareNumbers(size, halfwayPoint) < 0) {\n        size = collapsedSize;\n      } else {\n        size = minSize;\n      }\n    } else {\n      size = minSize;\n    }\n  }\n  size = Math.min(maxSize, size);\n  size = parseFloat(size.toFixed(PRECISION));\n  return size;\n}\n\n// All units must be in percentages; pixel values should be pre-converted\nfunction adjustLayoutByDelta({\n  delta,\n  initialLayout,\n  panelConstraints: panelConstraintsArray,\n  pivotIndices,\n  prevLayout,\n  trigger\n}) {\n  if (fuzzyNumbersEqual(delta, 0)) {\n    return initialLayout;\n  }\n  const nextLayout = [...initialLayout];\n  const [firstPivotIndex, secondPivotIndex] = pivotIndices;\n  assert(firstPivotIndex != null, \"Invalid first pivot index\");\n  assert(secondPivotIndex != null, \"Invalid second pivot index\");\n  let deltaApplied = 0;\n\n  // const DEBUG = [];\n  // DEBUG.push(`adjustLayoutByDelta()`);\n  // DEBUG.push(`  initialLayout: ${initialLayout.join(\", \")}`);\n  // DEBUG.push(`  prevLayout: ${prevLayout.join(\", \")}`);\n  // DEBUG.push(`  delta: ${delta}`);\n  // DEBUG.push(`  pivotIndices: ${pivotIndices.join(\", \")}`);\n  // DEBUG.push(`  trigger: ${trigger}`);\n  // DEBUG.push(\"\");\n\n  // A resizing panel affects the panels before or after it.\n  //\n  // A negative delta means the panel(s) immediately after the resize handle should grow/expand by decreasing its offset.\n  // Other panels may also need to shrink/contract (and shift) to make room, depending on the min weights.\n  //\n  // A positive delta means the panel(s) immediately before the resize handle should \"expand\".\n  // This is accomplished by shrinking/contracting (and shifting) one or more of the panels after the resize handle.\n\n  {\n    // If this is a resize triggered by a keyboard event, our logic for expanding/collapsing is different.\n    // We no longer check the halfway threshold because this may prevent the panel from expanding at all.\n    if (trigger === \"keyboard\") {\n      {\n        // Check if we should expand a collapsed panel\n        const index = delta < 0 ? secondPivotIndex : firstPivotIndex;\n        const panelConstraints = panelConstraintsArray[index];\n        assert(panelConstraints, `Panel constraints not found for index ${index}`);\n        const {\n          collapsedSize = 0,\n          collapsible,\n          minSize = 0\n        } = panelConstraints;\n\n        // DEBUG.push(`edge case check 1: ${index}`);\n        // DEBUG.push(`  -> collapsible? ${collapsible}`);\n        if (collapsible) {\n          const prevSize = initialLayout[index];\n          assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n          if (fuzzyNumbersEqual(prevSize, collapsedSize)) {\n            const localDelta = minSize - prevSize;\n            // DEBUG.push(`  -> expand delta: ${localDelta}`);\n\n            if (fuzzyCompareNumbers(localDelta, Math.abs(delta)) > 0) {\n              delta = delta < 0 ? 0 - localDelta : localDelta;\n              // DEBUG.push(`  -> delta: ${delta}`);\n            }\n          }\n        }\n      }\n\n      {\n        // Check if we should collapse a panel at its minimum size\n        const index = delta < 0 ? firstPivotIndex : secondPivotIndex;\n        const panelConstraints = panelConstraintsArray[index];\n        assert(panelConstraints, `No panel constraints found for index ${index}`);\n        const {\n          collapsedSize = 0,\n          collapsible,\n          minSize = 0\n        } = panelConstraints;\n\n        // DEBUG.push(`edge case check 2: ${index}`);\n        // DEBUG.push(`  -> collapsible? ${collapsible}`);\n        if (collapsible) {\n          const prevSize = initialLayout[index];\n          assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n          if (fuzzyNumbersEqual(prevSize, minSize)) {\n            const localDelta = prevSize - collapsedSize;\n            // DEBUG.push(`  -> expand delta: ${localDelta}`);\n\n            if (fuzzyCompareNumbers(localDelta, Math.abs(delta)) > 0) {\n              delta = delta < 0 ? 0 - localDelta : localDelta;\n              // DEBUG.push(`  -> delta: ${delta}`);\n            }\n          }\n        }\n      }\n    }\n    // DEBUG.push(\"\");\n  }\n\n  {\n    // Pre-calculate max available delta in the opposite direction of our pivot.\n    // This will be the maximum amount we're allowed to expand/contract the panels in the primary direction.\n    // If this amount is less than the requested delta, adjust the requested delta.\n    // If this amount is greater than the requested delta, that's useful information too–\n    // as an expanding panel might change from collapsed to min size.\n\n    const increment = delta < 0 ? 1 : -1;\n    let index = delta < 0 ? secondPivotIndex : firstPivotIndex;\n    let maxAvailableDelta = 0;\n\n    // DEBUG.push(\"pre calc...\");\n    while (true) {\n      const prevSize = initialLayout[index];\n      assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n      const maxSafeSize = resizePanel({\n        panelConstraints: panelConstraintsArray,\n        panelIndex: index,\n        size: 100\n      });\n      const delta = maxSafeSize - prevSize;\n      // DEBUG.push(`  ${index}: ${prevSize} -> ${maxSafeSize}`);\n\n      maxAvailableDelta += delta;\n      index += increment;\n      if (index < 0 || index >= panelConstraintsArray.length) {\n        break;\n      }\n    }\n\n    // DEBUG.push(`  -> max available delta: ${maxAvailableDelta}`);\n    const minAbsDelta = Math.min(Math.abs(delta), Math.abs(maxAvailableDelta));\n    delta = delta < 0 ? 0 - minAbsDelta : minAbsDelta;\n    // DEBUG.push(`  -> adjusted delta: ${delta}`);\n    // DEBUG.push(\"\");\n  }\n\n  {\n    // Delta added to a panel needs to be subtracted from other panels (within the constraints that those panels allow).\n\n    const pivotIndex = delta < 0 ? firstPivotIndex : secondPivotIndex;\n    let index = pivotIndex;\n    while (index >= 0 && index < panelConstraintsArray.length) {\n      const deltaRemaining = Math.abs(delta) - Math.abs(deltaApplied);\n      const prevSize = initialLayout[index];\n      assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n      const unsafeSize = prevSize - deltaRemaining;\n      const safeSize = resizePanel({\n        panelConstraints: panelConstraintsArray,\n        panelIndex: index,\n        size: unsafeSize\n      });\n      if (!fuzzyNumbersEqual(prevSize, safeSize)) {\n        deltaApplied += prevSize - safeSize;\n        nextLayout[index] = safeSize;\n        if (deltaApplied.toPrecision(3).localeCompare(Math.abs(delta).toPrecision(3), undefined, {\n          numeric: true\n        }) >= 0) {\n          break;\n        }\n      }\n      if (delta < 0) {\n        index--;\n      } else {\n        index++;\n      }\n    }\n  }\n  // DEBUG.push(`after 1: ${nextLayout.join(\", \")}`);\n  // DEBUG.push(`  deltaApplied: ${deltaApplied}`);\n  // DEBUG.push(\"\");\n\n  // If we were unable to resize any of the panels panels, return the previous state.\n  // This will essentially bailout and ignore e.g. drags past a panel's boundaries\n  if (fuzzyLayoutsEqual(prevLayout, nextLayout)) {\n    // DEBUG.push(`bailout to previous layout: ${prevLayout.join(\", \")}`);\n    // console.log(DEBUG.join(\"\\n\"));\n\n    return prevLayout;\n  }\n  {\n    // Now distribute the applied delta to the panels in the other direction\n    const pivotIndex = delta < 0 ? secondPivotIndex : firstPivotIndex;\n    const prevSize = initialLayout[pivotIndex];\n    assert(prevSize != null, `Previous layout not found for panel index ${pivotIndex}`);\n    const unsafeSize = prevSize + deltaApplied;\n    const safeSize = resizePanel({\n      panelConstraints: panelConstraintsArray,\n      panelIndex: pivotIndex,\n      size: unsafeSize\n    });\n\n    // Adjust the pivot panel before, but only by the amount that surrounding panels were able to shrink/contract.\n    nextLayout[pivotIndex] = safeSize;\n\n    // Edge case where expanding or contracting one panel caused another one to change collapsed state\n    if (!fuzzyNumbersEqual(safeSize, unsafeSize)) {\n      let deltaRemaining = unsafeSize - safeSize;\n      const pivotIndex = delta < 0 ? secondPivotIndex : firstPivotIndex;\n      let index = pivotIndex;\n      while (index >= 0 && index < panelConstraintsArray.length) {\n        const prevSize = nextLayout[index];\n        assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n        const unsafeSize = prevSize + deltaRemaining;\n        const safeSize = resizePanel({\n          panelConstraints: panelConstraintsArray,\n          panelIndex: index,\n          size: unsafeSize\n        });\n        if (!fuzzyNumbersEqual(prevSize, safeSize)) {\n          deltaRemaining -= safeSize - prevSize;\n          nextLayout[index] = safeSize;\n        }\n        if (fuzzyNumbersEqual(deltaRemaining, 0)) {\n          break;\n        }\n        if (delta > 0) {\n          index--;\n        } else {\n          index++;\n        }\n      }\n    }\n  }\n  // DEBUG.push(`after 2: ${nextLayout.join(\", \")}`);\n  // DEBUG.push(`  deltaApplied: ${deltaApplied}`);\n  // DEBUG.push(\"\");\n\n  const totalSize = nextLayout.reduce((total, size) => size + total, 0);\n  // DEBUG.push(`total size: ${totalSize}`);\n\n  // If our new layout doesn't add up to 100%, that means the requested delta can't be applied\n  // In that case, fall back to our most recent valid layout\n  if (!fuzzyNumbersEqual(totalSize, 100)) {\n    // DEBUG.push(`bailout to previous layout: ${prevLayout.join(\", \")}`);\n    // console.log(DEBUG.join(\"\\n\"));\n\n    return prevLayout;\n  }\n\n  // console.log(DEBUG.join(\"\\n\"));\n  return nextLayout;\n}\n\nfunction getResizeHandleElementsForGroup(groupId, scope = document) {\n  return Array.from(scope.querySelectorAll(`[${DATA_ATTRIBUTES.resizeHandleId}][data-panel-group-id=\"${groupId}\"]`));\n}\n\nfunction getResizeHandleElementIndex(groupId, id, scope = document) {\n  const handles = getResizeHandleElementsForGroup(groupId, scope);\n  const index = handles.findIndex(handle => handle.getAttribute(DATA_ATTRIBUTES.resizeHandleId) === id);\n  return index !== null && index !== void 0 ? index : null;\n}\n\nfunction determinePivotIndices(groupId, dragHandleId, panelGroupElement) {\n  const index = getResizeHandleElementIndex(groupId, dragHandleId, panelGroupElement);\n  return index != null ? [index, index + 1] : [-1, -1];\n}\n\nfunction getPanelGroupElement(id, rootElement = document) {\n  var _dataset;\n  //If the root element is the PanelGroup\n  if (rootElement instanceof HTMLElement && (rootElement === null || rootElement === void 0 ? void 0 : (_dataset = rootElement.dataset) === null || _dataset === void 0 ? void 0 : _dataset.panelGroupId) == id) {\n    return rootElement;\n  }\n\n  //Else query children\n  const element = rootElement.querySelector(`[data-panel-group][data-panel-group-id=\"${id}\"]`);\n  if (element) {\n    return element;\n  }\n  return null;\n}\n\nfunction getResizeHandleElement(id, scope = document) {\n  const element = scope.querySelector(`[${DATA_ATTRIBUTES.resizeHandleId}=\"${id}\"]`);\n  if (element) {\n    return element;\n  }\n  return null;\n}\n\nfunction getResizeHandlePanelIds(groupId, handleId, panelsArray, scope = document) {\n  var _panelsArray$index$id, _panelsArray$index, _panelsArray$id, _panelsArray;\n  const handle = getResizeHandleElement(handleId, scope);\n  const handles = getResizeHandleElementsForGroup(groupId, scope);\n  const index = handle ? handles.indexOf(handle) : -1;\n  const idBefore = (_panelsArray$index$id = (_panelsArray$index = panelsArray[index]) === null || _panelsArray$index === void 0 ? void 0 : _panelsArray$index.id) !== null && _panelsArray$index$id !== void 0 ? _panelsArray$index$id : null;\n  const idAfter = (_panelsArray$id = (_panelsArray = panelsArray[index + 1]) === null || _panelsArray === void 0 ? void 0 : _panelsArray.id) !== null && _panelsArray$id !== void 0 ? _panelsArray$id : null;\n  return [idBefore, idAfter];\n}\n\n// https://www.w3.org/WAI/ARIA/apg/patterns/windowsplitter/\n\nfunction useWindowSplitterPanelGroupBehavior({\n  committedValuesRef,\n  eagerValuesRef,\n  groupId,\n  layout,\n  panelDataArray,\n  panelGroupElement,\n  setLayout\n}) {\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    didWarnAboutMissingResizeHandle: false\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!panelGroupElement) {\n      return;\n    }\n    const eagerValues = eagerValuesRef.current;\n    assert(eagerValues, `Eager values not found`);\n    const {\n      panelDataArray\n    } = eagerValues;\n    const groupElement = getPanelGroupElement(groupId, panelGroupElement);\n    assert(groupElement != null, `No group found for id \"${groupId}\"`);\n    const handles = getResizeHandleElementsForGroup(groupId, panelGroupElement);\n    assert(handles, `No resize handles found for group id \"${groupId}\"`);\n    const cleanupFunctions = handles.map(handle => {\n      const handleId = handle.getAttribute(DATA_ATTRIBUTES.resizeHandleId);\n      assert(handleId, `Resize handle element has no handle id attribute`);\n      const [idBefore, idAfter] = getResizeHandlePanelIds(groupId, handleId, panelDataArray, panelGroupElement);\n      if (idBefore == null || idAfter == null) {\n        return () => {};\n      }\n      const onKeyDown = event => {\n        if (event.defaultPrevented) {\n          return;\n        }\n        switch (event.key) {\n          case \"Enter\":\n            {\n              event.preventDefault();\n              const index = panelDataArray.findIndex(panelData => panelData.id === idBefore);\n              if (index >= 0) {\n                const panelData = panelDataArray[index];\n                assert(panelData, `No panel data found for index ${index}`);\n                const size = layout[index];\n                const {\n                  collapsedSize = 0,\n                  collapsible,\n                  minSize = 0\n                } = panelData.constraints;\n                if (size != null && collapsible) {\n                  const nextLayout = adjustLayoutByDelta({\n                    delta: fuzzyNumbersEqual(size, collapsedSize) ? minSize - collapsedSize : collapsedSize - size,\n                    initialLayout: layout,\n                    panelConstraints: panelDataArray.map(panelData => panelData.constraints),\n                    pivotIndices: determinePivotIndices(groupId, handleId, panelGroupElement),\n                    prevLayout: layout,\n                    trigger: \"keyboard\"\n                  });\n                  if (layout !== nextLayout) {\n                    setLayout(nextLayout);\n                  }\n                }\n              }\n              break;\n            }\n        }\n      };\n      handle.addEventListener(\"keydown\", onKeyDown);\n      return () => {\n        handle.removeEventListener(\"keydown\", onKeyDown);\n      };\n    });\n    return () => {\n      cleanupFunctions.forEach(cleanupFunction => cleanupFunction());\n    };\n  }, [panelGroupElement, committedValuesRef, eagerValuesRef, groupId, layout, panelDataArray, setLayout]);\n}\n\nfunction areEqual(arrayA, arrayB) {\n  if (arrayA.length !== arrayB.length) {\n    return false;\n  }\n  for (let index = 0; index < arrayA.length; index++) {\n    if (arrayA[index] !== arrayB[index]) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction getResizeEventCursorPosition(direction, event) {\n  const isHorizontal = direction === \"horizontal\";\n  const {\n    x,\n    y\n  } = getResizeEventCoordinates(event);\n  return isHorizontal ? x : y;\n}\n\nfunction calculateDragOffsetPercentage(event, dragHandleId, direction, initialDragState, panelGroupElement) {\n  const isHorizontal = direction === \"horizontal\";\n  const handleElement = getResizeHandleElement(dragHandleId, panelGroupElement);\n  assert(handleElement, `No resize handle element found for id \"${dragHandleId}\"`);\n  const groupId = handleElement.getAttribute(DATA_ATTRIBUTES.groupId);\n  assert(groupId, `Resize handle element has no group id attribute`);\n  let {\n    initialCursorPosition\n  } = initialDragState;\n  const cursorPosition = getResizeEventCursorPosition(direction, event);\n  const groupElement = getPanelGroupElement(groupId, panelGroupElement);\n  assert(groupElement, `No group element found for id \"${groupId}\"`);\n  const groupRect = groupElement.getBoundingClientRect();\n  const groupSizeInPixels = isHorizontal ? groupRect.width : groupRect.height;\n  const offsetPixels = cursorPosition - initialCursorPosition;\n  const offsetPercentage = offsetPixels / groupSizeInPixels * 100;\n  return offsetPercentage;\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/movementX\nfunction calculateDeltaPercentage(event, dragHandleId, direction, initialDragState, keyboardResizeBy, panelGroupElement) {\n  if (isKeyDown(event)) {\n    const isHorizontal = direction === \"horizontal\";\n    let delta = 0;\n    if (event.shiftKey) {\n      delta = 100;\n    } else if (keyboardResizeBy != null) {\n      delta = keyboardResizeBy;\n    } else {\n      delta = 10;\n    }\n    let movement = 0;\n    switch (event.key) {\n      case \"ArrowDown\":\n        movement = isHorizontal ? 0 : delta;\n        break;\n      case \"ArrowLeft\":\n        movement = isHorizontal ? -delta : 0;\n        break;\n      case \"ArrowRight\":\n        movement = isHorizontal ? delta : 0;\n        break;\n      case \"ArrowUp\":\n        movement = isHorizontal ? 0 : -delta;\n        break;\n      case \"End\":\n        movement = 100;\n        break;\n      case \"Home\":\n        movement = -100;\n        break;\n    }\n    return movement;\n  } else {\n    if (initialDragState == null) {\n      return 0;\n    }\n    return calculateDragOffsetPercentage(event, dragHandleId, direction, initialDragState, panelGroupElement);\n  }\n}\n\n// Layout should be pre-converted into percentages\nfunction callPanelCallbacks(panelsArray, layout, panelIdToLastNotifiedSizeMap) {\n  layout.forEach((size, index) => {\n    const panelData = panelsArray[index];\n    assert(panelData, `Panel data not found for index ${index}`);\n    const {\n      callbacks,\n      constraints,\n      id: panelId\n    } = panelData;\n    const {\n      collapsedSize = 0,\n      collapsible\n    } = constraints;\n    const lastNotifiedSize = panelIdToLastNotifiedSizeMap[panelId];\n    if (lastNotifiedSize == null || size !== lastNotifiedSize) {\n      panelIdToLastNotifiedSizeMap[panelId] = size;\n      const {\n        onCollapse,\n        onExpand,\n        onResize\n      } = callbacks;\n      if (onResize) {\n        onResize(size, lastNotifiedSize);\n      }\n      if (collapsible && (onCollapse || onExpand)) {\n        if (onExpand && (lastNotifiedSize == null || fuzzyNumbersEqual$1(lastNotifiedSize, collapsedSize)) && !fuzzyNumbersEqual$1(size, collapsedSize)) {\n          onExpand();\n        }\n        if (onCollapse && (lastNotifiedSize == null || !fuzzyNumbersEqual$1(lastNotifiedSize, collapsedSize)) && fuzzyNumbersEqual$1(size, collapsedSize)) {\n          onCollapse();\n        }\n      }\n    }\n  });\n}\n\nfunction compareLayouts(a, b) {\n  if (a.length !== b.length) {\n    return false;\n  } else {\n    for (let index = 0; index < a.length; index++) {\n      if (a[index] != b[index]) {\n        return false;\n      }\n    }\n  }\n  return true;\n}\n\n// This method returns a number between 1 and 100 representing\n\n// the % of the group's overall space this panel should occupy.\nfunction computePanelFlexBoxStyle({\n  defaultSize,\n  dragState,\n  layout,\n  panelData,\n  panelIndex,\n  precision = 3\n}) {\n  const size = layout[panelIndex];\n  let flexGrow;\n  if (size == null) {\n    // Initial render (before panels have registered themselves)\n    // In order to support server rendering, fall back to default size if provided\n    flexGrow = defaultSize != undefined ? defaultSize.toPrecision(precision) : \"1\";\n  } else if (panelData.length === 1) {\n    // Special case: Single panel group should always fill full width/height\n    flexGrow = \"1\";\n  } else {\n    flexGrow = size.toPrecision(precision);\n  }\n  return {\n    flexBasis: 0,\n    flexGrow,\n    flexShrink: 1,\n    // Without this, Panel sizes may be unintentionally overridden by their content\n    overflow: \"hidden\",\n    // Disable pointer events inside of a panel during resize\n    // This avoid edge cases like nested iframes\n    pointerEvents: dragState !== null ? \"none\" : undefined\n  };\n}\n\nfunction debounce(callback, durationMs = 10) {\n  let timeoutId = null;\n  let callable = (...args) => {\n    if (timeoutId !== null) {\n      clearTimeout(timeoutId);\n    }\n    timeoutId = setTimeout(() => {\n      callback(...args);\n    }, durationMs);\n  };\n  return callable;\n}\n\n// PanelGroup might be rendering in a server-side environment where localStorage is not available\n// or on a browser with cookies/storage disabled.\n// In either case, this function avoids accessing localStorage until needed,\n// and avoids throwing user-visible errors.\nfunction initializeDefaultStorage(storageObject) {\n  try {\n    if (typeof localStorage !== \"undefined\") {\n      // Bypass this check for future calls\n      storageObject.getItem = name => {\n        return localStorage.getItem(name);\n      };\n      storageObject.setItem = (name, value) => {\n        localStorage.setItem(name, value);\n      };\n    } else {\n      throw new Error(\"localStorage not supported in this environment\");\n    }\n  } catch (error) {\n    console.error(error);\n    storageObject.getItem = () => null;\n    storageObject.setItem = () => {};\n  }\n}\n\nfunction getPanelGroupKey(autoSaveId) {\n  return `react-resizable-panels:${autoSaveId}`;\n}\n\n// Note that Panel ids might be user-provided (stable) or useId generated (non-deterministic)\n// so they should not be used as part of the serialization key.\n// Using the min/max size attributes should work well enough as a backup.\n// Pre-sorting by minSize allows remembering layouts even if panels are re-ordered/dragged.\nfunction getPanelKey(panels) {\n  return panels.map(panel => {\n    const {\n      constraints,\n      id,\n      idIsFromProps,\n      order\n    } = panel;\n    if (idIsFromProps) {\n      return id;\n    } else {\n      return order ? `${order}:${JSON.stringify(constraints)}` : JSON.stringify(constraints);\n    }\n  }).sort((a, b) => a.localeCompare(b)).join(\",\");\n}\nfunction loadSerializedPanelGroupState(autoSaveId, storage) {\n  try {\n    const panelGroupKey = getPanelGroupKey(autoSaveId);\n    const serialized = storage.getItem(panelGroupKey);\n    if (serialized) {\n      const parsed = JSON.parse(serialized);\n      if (typeof parsed === \"object\" && parsed != null) {\n        return parsed;\n      }\n    }\n  } catch (error) {}\n  return null;\n}\nfunction savePanelGroupState(autoSaveId, panels, panelSizesBeforeCollapse, sizes, storage) {\n  var _loadSerializedPanelG2;\n  const panelGroupKey = getPanelGroupKey(autoSaveId);\n  const panelKey = getPanelKey(panels);\n  const state = (_loadSerializedPanelG2 = loadSerializedPanelGroupState(autoSaveId, storage)) !== null && _loadSerializedPanelG2 !== void 0 ? _loadSerializedPanelG2 : {};\n  state[panelKey] = {\n    expandToSizes: Object.fromEntries(panelSizesBeforeCollapse.entries()),\n    layout: sizes\n  };\n  try {\n    storage.setItem(panelGroupKey, JSON.stringify(state));\n  } catch (error) {\n    console.error(error);\n  }\n}\n\nfunction validatePanelConstraints({\n  panelConstraints: panelConstraintsArray,\n  panelId,\n  panelIndex\n}) {\n  {\n    const warnings = [];\n    const panelConstraints = panelConstraintsArray[panelIndex];\n    assert(panelConstraints, `No panel constraints found for index ${panelIndex}`);\n    const {\n      collapsedSize = 0,\n      collapsible = false,\n      defaultSize,\n      maxSize = 100,\n      minSize = 0\n    } = panelConstraints;\n    if (minSize > maxSize) {\n      warnings.push(`min size (${minSize}%) should not be greater than max size (${maxSize}%)`);\n    }\n    if (defaultSize != null) {\n      if (defaultSize < 0) {\n        warnings.push(\"default size should not be less than 0\");\n      } else if (defaultSize < minSize && (!collapsible || defaultSize !== collapsedSize)) {\n        warnings.push(\"default size should not be less than min size\");\n      }\n      if (defaultSize > 100) {\n        warnings.push(\"default size should not be greater than 100\");\n      } else if (defaultSize > maxSize) {\n        warnings.push(\"default size should not be greater than max size\");\n      }\n    }\n    if (collapsedSize > minSize) {\n      warnings.push(\"collapsed size should not be greater than min size\");\n    }\n    if (warnings.length > 0) {\n      const name = panelId != null ? `Panel \"${panelId}\"` : \"Panel\";\n      console.warn(`${name} has an invalid configuration:\\n\\n${warnings.join(\"\\n\")}`);\n      return false;\n    }\n  }\n  return true;\n}\n\n// All units must be in percentages; pixel values should be pre-converted\nfunction validatePanelGroupLayout({\n  layout: prevLayout,\n  panelConstraints\n}) {\n  const nextLayout = [...prevLayout];\n  const nextLayoutTotalSize = nextLayout.reduce((accumulated, current) => accumulated + current, 0);\n\n  // Validate layout expectations\n  if (nextLayout.length !== panelConstraints.length) {\n    throw Error(`Invalid ${panelConstraints.length} panel layout: ${nextLayout.map(size => `${size}%`).join(\", \")}`);\n  } else if (!fuzzyNumbersEqual(nextLayoutTotalSize, 100) && nextLayout.length > 0) {\n    // This is not ideal so we should warn about it, but it may be recoverable in some cases\n    // (especially if the amount is small)\n    {\n      console.warn(`WARNING: Invalid layout total size: ${nextLayout.map(size => `${size}%`).join(\", \")}. Layout normalization will be applied.`);\n    }\n    for (let index = 0; index < panelConstraints.length; index++) {\n      const unsafeSize = nextLayout[index];\n      assert(unsafeSize != null, `No layout data found for index ${index}`);\n      const safeSize = 100 / nextLayoutTotalSize * unsafeSize;\n      nextLayout[index] = safeSize;\n    }\n  }\n  let remainingSize = 0;\n\n  // First pass: Validate the proposed layout given each panel's constraints\n  for (let index = 0; index < panelConstraints.length; index++) {\n    const unsafeSize = nextLayout[index];\n    assert(unsafeSize != null, `No layout data found for index ${index}`);\n    const safeSize = resizePanel({\n      panelConstraints,\n      panelIndex: index,\n      size: unsafeSize\n    });\n    if (unsafeSize != safeSize) {\n      remainingSize += unsafeSize - safeSize;\n      nextLayout[index] = safeSize;\n    }\n  }\n\n  // If there is additional, left over space, assign it to any panel(s) that permits it\n  // (It's not worth taking multiple additional passes to evenly distribute)\n  if (!fuzzyNumbersEqual(remainingSize, 0)) {\n    for (let index = 0; index < panelConstraints.length; index++) {\n      const prevSize = nextLayout[index];\n      assert(prevSize != null, `No layout data found for index ${index}`);\n      const unsafeSize = prevSize + remainingSize;\n      const safeSize = resizePanel({\n        panelConstraints,\n        panelIndex: index,\n        size: unsafeSize\n      });\n      if (prevSize !== safeSize) {\n        remainingSize -= safeSize - prevSize;\n        nextLayout[index] = safeSize;\n\n        // Once we've used up the remainder, bail\n        if (fuzzyNumbersEqual(remainingSize, 0)) {\n          break;\n        }\n      }\n    }\n  }\n  return nextLayout;\n}\n\nconst LOCAL_STORAGE_DEBOUNCE_INTERVAL = 100;\nconst defaultStorage = {\n  getItem: name => {\n    initializeDefaultStorage(defaultStorage);\n    return defaultStorage.getItem(name);\n  },\n  setItem: (name, value) => {\n    initializeDefaultStorage(defaultStorage);\n    defaultStorage.setItem(name, value);\n  }\n};\nconst debounceMap = {};\nfunction PanelGroupWithForwardedRef({\n  autoSaveId = null,\n  children,\n  className: classNameFromProps = \"\",\n  direction,\n  forwardedRef,\n  id: idFromProps = null,\n  onLayout = null,\n  keyboardResizeBy = null,\n  storage = defaultStorage,\n  style: styleFromProps,\n  tagName: Type = \"div\",\n  ...rest\n}) {\n  const groupId = useUniqueId(idFromProps);\n  const panelGroupElementRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const [dragState, setDragState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [layout, setLayout] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const forceUpdate = useForceUpdate();\n  const panelIdToLastNotifiedSizeMapRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({});\n  const panelSizeBeforeCollapseRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(new Map());\n  const prevDeltaRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n  const committedValuesRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    autoSaveId,\n    direction,\n    dragState,\n    id: groupId,\n    keyboardResizeBy,\n    onLayout,\n    storage\n  });\n  const eagerValuesRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    layout,\n    panelDataArray: [],\n    panelDataArrayChanged: false\n  });\n  const devWarningsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    didLogIdAndOrderWarning: false,\n    didLogPanelConstraintsWarning: false,\n    prevPanelIds: []\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(forwardedRef, () => ({\n    getId: () => committedValuesRef.current.id,\n    getLayout: () => {\n      const {\n        layout\n      } = eagerValuesRef.current;\n      return layout;\n    },\n    setLayout: unsafeLayout => {\n      const {\n        onLayout\n      } = committedValuesRef.current;\n      const {\n        layout: prevLayout,\n        panelDataArray\n      } = eagerValuesRef.current;\n      const safeLayout = validatePanelGroupLayout({\n        layout: unsafeLayout,\n        panelConstraints: panelDataArray.map(panelData => panelData.constraints)\n      });\n      if (!areEqual(prevLayout, safeLayout)) {\n        setLayout(safeLayout);\n        eagerValuesRef.current.layout = safeLayout;\n        if (onLayout) {\n          onLayout(safeLayout);\n        }\n        callPanelCallbacks(panelDataArray, safeLayout, panelIdToLastNotifiedSizeMapRef.current);\n      }\n    }\n  }), []);\n  useWindowSplitterPanelGroupBehavior({\n    committedValuesRef,\n    eagerValuesRef,\n    groupId,\n    layout,\n    panelDataArray: eagerValuesRef.current.panelDataArray,\n    setLayout,\n    panelGroupElement: panelGroupElementRef.current\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const {\n      panelDataArray\n    } = eagerValuesRef.current;\n\n    // If this panel has been configured to persist sizing information, save sizes to local storage.\n    if (autoSaveId) {\n      if (layout.length === 0 || layout.length !== panelDataArray.length) {\n        return;\n      }\n      let debouncedSave = debounceMap[autoSaveId];\n\n      // Limit the frequency of localStorage updates.\n      if (debouncedSave == null) {\n        debouncedSave = debounce(savePanelGroupState, LOCAL_STORAGE_DEBOUNCE_INTERVAL);\n        debounceMap[autoSaveId] = debouncedSave;\n      }\n\n      // Clone mutable data before passing to the debounced function,\n      // else we run the risk of saving an incorrect combination of mutable and immutable values to state.\n      const clonedPanelDataArray = [...panelDataArray];\n      const clonedPanelSizesBeforeCollapse = new Map(panelSizeBeforeCollapseRef.current);\n      debouncedSave(autoSaveId, clonedPanelDataArray, clonedPanelSizesBeforeCollapse, layout, storage);\n    }\n  }, [autoSaveId, layout, storage]);\n\n  // DEV warnings\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    {\n      const {\n        panelDataArray\n      } = eagerValuesRef.current;\n      const {\n        didLogIdAndOrderWarning,\n        didLogPanelConstraintsWarning,\n        prevPanelIds\n      } = devWarningsRef.current;\n      if (!didLogIdAndOrderWarning) {\n        const panelIds = panelDataArray.map(({\n          id\n        }) => id);\n        devWarningsRef.current.prevPanelIds = panelIds;\n        const panelsHaveChanged = prevPanelIds.length > 0 && !areEqual(prevPanelIds, panelIds);\n        if (panelsHaveChanged) {\n          if (panelDataArray.find(({\n            idIsFromProps,\n            order\n          }) => !idIsFromProps || order == null)) {\n            devWarningsRef.current.didLogIdAndOrderWarning = true;\n            console.warn(`WARNING: Panel id and order props recommended when panels are dynamically rendered`);\n          }\n        }\n      }\n      if (!didLogPanelConstraintsWarning) {\n        const panelConstraints = panelDataArray.map(panelData => panelData.constraints);\n        for (let panelIndex = 0; panelIndex < panelConstraints.length; panelIndex++) {\n          const panelData = panelDataArray[panelIndex];\n          assert(panelData, `Panel data not found for index ${panelIndex}`);\n          const isValid = validatePanelConstraints({\n            panelConstraints,\n            panelId: panelData.id,\n            panelIndex\n          });\n          if (!isValid) {\n            devWarningsRef.current.didLogPanelConstraintsWarning = true;\n            break;\n          }\n        }\n      }\n    }\n  });\n\n  // External APIs are safe to memoize via committed values ref\n  const collapsePanel = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(panelData => {\n    const {\n      onLayout\n    } = committedValuesRef.current;\n    const {\n      layout: prevLayout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    if (panelData.constraints.collapsible) {\n      const panelConstraintsArray = panelDataArray.map(panelData => panelData.constraints);\n      const {\n        collapsedSize = 0,\n        panelSize,\n        pivotIndices\n      } = panelDataHelper(panelDataArray, panelData, prevLayout);\n      assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n      if (!fuzzyNumbersEqual$1(panelSize, collapsedSize)) {\n        // Store size before collapse;\n        // This is the size that gets restored if the expand() API is used.\n        panelSizeBeforeCollapseRef.current.set(panelData.id, panelSize);\n        const isLastPanel = findPanelDataIndex(panelDataArray, panelData) === panelDataArray.length - 1;\n        const delta = isLastPanel ? panelSize - collapsedSize : collapsedSize - panelSize;\n        const nextLayout = adjustLayoutByDelta({\n          delta,\n          initialLayout: prevLayout,\n          panelConstraints: panelConstraintsArray,\n          pivotIndices,\n          prevLayout,\n          trigger: \"imperative-api\"\n        });\n        if (!compareLayouts(prevLayout, nextLayout)) {\n          setLayout(nextLayout);\n          eagerValuesRef.current.layout = nextLayout;\n          if (onLayout) {\n            onLayout(nextLayout);\n          }\n          callPanelCallbacks(panelDataArray, nextLayout, panelIdToLastNotifiedSizeMapRef.current);\n        }\n      }\n    }\n  }, []);\n\n  // External APIs are safe to memoize via committed values ref\n  const expandPanel = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((panelData, minSizeOverride) => {\n    const {\n      onLayout\n    } = committedValuesRef.current;\n    const {\n      layout: prevLayout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    if (panelData.constraints.collapsible) {\n      const panelConstraintsArray = panelDataArray.map(panelData => panelData.constraints);\n      const {\n        collapsedSize = 0,\n        panelSize = 0,\n        minSize: minSizeFromProps = 0,\n        pivotIndices\n      } = panelDataHelper(panelDataArray, panelData, prevLayout);\n      const minSize = minSizeOverride !== null && minSizeOverride !== void 0 ? minSizeOverride : minSizeFromProps;\n      if (fuzzyNumbersEqual$1(panelSize, collapsedSize)) {\n        // Restore this panel to the size it was before it was collapsed, if possible.\n        const prevPanelSize = panelSizeBeforeCollapseRef.current.get(panelData.id);\n        const baseSize = prevPanelSize != null && prevPanelSize >= minSize ? prevPanelSize : minSize;\n        const isLastPanel = findPanelDataIndex(panelDataArray, panelData) === panelDataArray.length - 1;\n        const delta = isLastPanel ? panelSize - baseSize : baseSize - panelSize;\n        const nextLayout = adjustLayoutByDelta({\n          delta,\n          initialLayout: prevLayout,\n          panelConstraints: panelConstraintsArray,\n          pivotIndices,\n          prevLayout,\n          trigger: \"imperative-api\"\n        });\n        if (!compareLayouts(prevLayout, nextLayout)) {\n          setLayout(nextLayout);\n          eagerValuesRef.current.layout = nextLayout;\n          if (onLayout) {\n            onLayout(nextLayout);\n          }\n          callPanelCallbacks(panelDataArray, nextLayout, panelIdToLastNotifiedSizeMapRef.current);\n        }\n      }\n    }\n  }, []);\n\n  // External APIs are safe to memoize via committed values ref\n  const getPanelSize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(panelData => {\n    const {\n      layout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const {\n      panelSize\n    } = panelDataHelper(panelDataArray, panelData, layout);\n    assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n    return panelSize;\n  }, []);\n\n  // This API should never read from committedValuesRef\n  const getPanelStyle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((panelData, defaultSize) => {\n    const {\n      panelDataArray\n    } = eagerValuesRef.current;\n    const panelIndex = findPanelDataIndex(panelDataArray, panelData);\n    return computePanelFlexBoxStyle({\n      defaultSize,\n      dragState,\n      layout,\n      panelData: panelDataArray,\n      panelIndex\n    });\n  }, [dragState, layout]);\n\n  // External APIs are safe to memoize via committed values ref\n  const isPanelCollapsed = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(panelData => {\n    const {\n      layout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const {\n      collapsedSize = 0,\n      collapsible,\n      panelSize\n    } = panelDataHelper(panelDataArray, panelData, layout);\n    assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n    return collapsible === true && fuzzyNumbersEqual$1(panelSize, collapsedSize);\n  }, []);\n\n  // External APIs are safe to memoize via committed values ref\n  const isPanelExpanded = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(panelData => {\n    const {\n      layout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const {\n      collapsedSize = 0,\n      collapsible,\n      panelSize\n    } = panelDataHelper(panelDataArray, panelData, layout);\n    assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n    return !collapsible || fuzzyCompareNumbers(panelSize, collapsedSize) > 0;\n  }, []);\n  const registerPanel = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(panelData => {\n    const {\n      panelDataArray\n    } = eagerValuesRef.current;\n    panelDataArray.push(panelData);\n    panelDataArray.sort((panelA, panelB) => {\n      const orderA = panelA.order;\n      const orderB = panelB.order;\n      if (orderA == null && orderB == null) {\n        return 0;\n      } else if (orderA == null) {\n        return -1;\n      } else if (orderB == null) {\n        return 1;\n      } else {\n        return orderA - orderB;\n      }\n    });\n    eagerValuesRef.current.panelDataArrayChanged = true;\n    forceUpdate();\n  }, [forceUpdate]);\n  const registerResizeHandle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(dragHandleId => {\n    let isRTL = false;\n    const panelGroupElement = panelGroupElementRef.current;\n    if (panelGroupElement) {\n      const style = window.getComputedStyle(panelGroupElement, null);\n      if (style.getPropertyValue(\"direction\") === \"rtl\") {\n        isRTL = true;\n      }\n    }\n    return function resizeHandler(event) {\n      event.preventDefault();\n      const panelGroupElement = panelGroupElementRef.current;\n      if (!panelGroupElement) {\n        return () => null;\n      }\n      const {\n        direction,\n        dragState,\n        id: groupId,\n        keyboardResizeBy,\n        onLayout\n      } = committedValuesRef.current;\n      const {\n        layout: prevLayout,\n        panelDataArray\n      } = eagerValuesRef.current;\n      const {\n        initialLayout\n      } = dragState !== null && dragState !== void 0 ? dragState : {};\n      const pivotIndices = determinePivotIndices(groupId, dragHandleId, panelGroupElement);\n      let delta = calculateDeltaPercentage(event, dragHandleId, direction, dragState, keyboardResizeBy, panelGroupElement);\n      const isHorizontal = direction === \"horizontal\";\n      if (isHorizontal && isRTL) {\n        delta = -delta;\n      }\n      const panelConstraints = panelDataArray.map(panelData => panelData.constraints);\n      const nextLayout = adjustLayoutByDelta({\n        delta,\n        initialLayout: initialLayout !== null && initialLayout !== void 0 ? initialLayout : prevLayout,\n        panelConstraints,\n        pivotIndices,\n        prevLayout,\n        trigger: isKeyDown(event) ? \"keyboard\" : \"mouse-or-touch\"\n      });\n      const layoutChanged = !compareLayouts(prevLayout, nextLayout);\n\n      // Only update the cursor for layout changes triggered by touch/mouse events (not keyboard)\n      // Update the cursor even if the layout hasn't changed (we may need to show an invalid cursor state)\n      if (isPointerEvent(event) || isMouseEvent(event)) {\n        // Watch for multiple subsequent deltas; this might occur for tiny cursor movements.\n        // In this case, Panel sizes might not change–\n        // but updating cursor in this scenario would cause a flicker.\n        if (prevDeltaRef.current != delta) {\n          prevDeltaRef.current = delta;\n          if (!layoutChanged && delta !== 0) {\n            // If the pointer has moved too far to resize the panel any further, note this so we can update the cursor.\n            // This mimics VS Code behavior.\n            if (isHorizontal) {\n              reportConstraintsViolation(dragHandleId, delta < 0 ? EXCEEDED_HORIZONTAL_MIN : EXCEEDED_HORIZONTAL_MAX);\n            } else {\n              reportConstraintsViolation(dragHandleId, delta < 0 ? EXCEEDED_VERTICAL_MIN : EXCEEDED_VERTICAL_MAX);\n            }\n          } else {\n            reportConstraintsViolation(dragHandleId, 0);\n          }\n        }\n      }\n      if (layoutChanged) {\n        setLayout(nextLayout);\n        eagerValuesRef.current.layout = nextLayout;\n        if (onLayout) {\n          onLayout(nextLayout);\n        }\n        callPanelCallbacks(panelDataArray, nextLayout, panelIdToLastNotifiedSizeMapRef.current);\n      }\n    };\n  }, []);\n\n  // External APIs are safe to memoize via committed values ref\n  const resizePanel = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((panelData, unsafePanelSize) => {\n    const {\n      onLayout\n    } = committedValuesRef.current;\n    const {\n      layout: prevLayout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const panelConstraintsArray = panelDataArray.map(panelData => panelData.constraints);\n    const {\n      panelSize,\n      pivotIndices\n    } = panelDataHelper(panelDataArray, panelData, prevLayout);\n    assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n    const isLastPanel = findPanelDataIndex(panelDataArray, panelData) === panelDataArray.length - 1;\n    const delta = isLastPanel ? panelSize - unsafePanelSize : unsafePanelSize - panelSize;\n    const nextLayout = adjustLayoutByDelta({\n      delta,\n      initialLayout: prevLayout,\n      panelConstraints: panelConstraintsArray,\n      pivotIndices,\n      prevLayout,\n      trigger: \"imperative-api\"\n    });\n    if (!compareLayouts(prevLayout, nextLayout)) {\n      setLayout(nextLayout);\n      eagerValuesRef.current.layout = nextLayout;\n      if (onLayout) {\n        onLayout(nextLayout);\n      }\n      callPanelCallbacks(panelDataArray, nextLayout, panelIdToLastNotifiedSizeMapRef.current);\n    }\n  }, []);\n  const reevaluatePanelConstraints = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((panelData, prevConstraints) => {\n    const {\n      layout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const {\n      collapsedSize: prevCollapsedSize = 0,\n      collapsible: prevCollapsible\n    } = prevConstraints;\n    const {\n      collapsedSize: nextCollapsedSize = 0,\n      collapsible: nextCollapsible,\n      maxSize: nextMaxSize = 100,\n      minSize: nextMinSize = 0\n    } = panelData.constraints;\n    const {\n      panelSize: prevPanelSize\n    } = panelDataHelper(panelDataArray, panelData, layout);\n    if (prevPanelSize == null) {\n      // It's possible that the panels in this group have changed since the last render\n      return;\n    }\n    if (prevCollapsible && nextCollapsible && fuzzyNumbersEqual$1(prevPanelSize, prevCollapsedSize)) {\n      if (!fuzzyNumbersEqual$1(prevCollapsedSize, nextCollapsedSize)) {\n        resizePanel(panelData, nextCollapsedSize);\n      }\n    } else if (prevPanelSize < nextMinSize) {\n      resizePanel(panelData, nextMinSize);\n    } else if (prevPanelSize > nextMaxSize) {\n      resizePanel(panelData, nextMaxSize);\n    }\n  }, [resizePanel]);\n\n  // TODO Multiple drag handles can be active at the same time so this API is a bit awkward now\n  const startDragging = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((dragHandleId, event) => {\n    const {\n      direction\n    } = committedValuesRef.current;\n    const {\n      layout\n    } = eagerValuesRef.current;\n    if (!panelGroupElementRef.current) {\n      return;\n    }\n    const handleElement = getResizeHandleElement(dragHandleId, panelGroupElementRef.current);\n    assert(handleElement, `Drag handle element not found for id \"${dragHandleId}\"`);\n    const initialCursorPosition = getResizeEventCursorPosition(direction, event);\n    setDragState({\n      dragHandleId,\n      dragHandleRect: handleElement.getBoundingClientRect(),\n      initialCursorPosition,\n      initialLayout: layout\n    });\n  }, []);\n  const stopDragging = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    setDragState(null);\n  }, []);\n  const unregisterPanel = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(panelData => {\n    const {\n      panelDataArray\n    } = eagerValuesRef.current;\n    const index = findPanelDataIndex(panelDataArray, panelData);\n    if (index >= 0) {\n      panelDataArray.splice(index, 1);\n\n      // TRICKY\n      // When a panel is removed from the group, we should delete the most recent prev-size entry for it.\n      // If we don't do this, then a conditionally rendered panel might not call onResize when it's re-mounted.\n      // Strict effects mode makes this tricky though because all panels will be registered, unregistered, then re-registered on mount.\n      delete panelIdToLastNotifiedSizeMapRef.current[panelData.id];\n      eagerValuesRef.current.panelDataArrayChanged = true;\n      forceUpdate();\n    }\n  }, [forceUpdate]);\n  const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    collapsePanel,\n    direction,\n    dragState,\n    expandPanel,\n    getPanelSize,\n    getPanelStyle,\n    groupId,\n    isPanelCollapsed,\n    isPanelExpanded,\n    reevaluatePanelConstraints,\n    registerPanel,\n    registerResizeHandle,\n    resizePanel,\n    startDragging,\n    stopDragging,\n    unregisterPanel,\n    panelGroupElement: panelGroupElementRef.current\n  }), [collapsePanel, dragState, direction, expandPanel, getPanelSize, getPanelStyle, groupId, isPanelCollapsed, isPanelExpanded, reevaluatePanelConstraints, registerPanel, registerResizeHandle, resizePanel, startDragging, stopDragging, unregisterPanel]);\n  const style = {\n    display: \"flex\",\n    flexDirection: direction === \"horizontal\" ? \"row\" : \"column\",\n    height: \"100%\",\n    overflow: \"hidden\",\n    width: \"100%\"\n  };\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(PanelGroupContext.Provider, {\n    value: context\n  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(Type, {\n    ...rest,\n    children,\n    className: classNameFromProps,\n    id: idFromProps,\n    ref: panelGroupElementRef,\n    style: {\n      ...style,\n      ...styleFromProps\n    },\n    // CSS selectors\n    [DATA_ATTRIBUTES.group]: \"\",\n    [DATA_ATTRIBUTES.groupDirection]: direction,\n    [DATA_ATTRIBUTES.groupId]: groupId\n  }));\n}\nconst PanelGroup = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref) => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(PanelGroupWithForwardedRef, {\n  ...props,\n  forwardedRef: ref\n}));\nPanelGroupWithForwardedRef.displayName = \"PanelGroup\";\nPanelGroup.displayName = \"forwardRef(PanelGroup)\";\nfunction findPanelDataIndex(panelDataArray, panelData) {\n  return panelDataArray.findIndex(prevPanelData => prevPanelData === panelData || prevPanelData.id === panelData.id);\n}\nfunction panelDataHelper(panelDataArray, panelData, layout) {\n  const panelIndex = findPanelDataIndex(panelDataArray, panelData);\n  const isLastPanel = panelIndex === panelDataArray.length - 1;\n  const pivotIndices = isLastPanel ? [panelIndex - 1, panelIndex] : [panelIndex, panelIndex + 1];\n  const panelSize = layout[panelIndex];\n  return {\n    ...panelData.constraints,\n    panelSize,\n    pivotIndices\n  };\n}\n\n// https://www.w3.org/WAI/ARIA/apg/patterns/windowsplitter/\n\nfunction useWindowSplitterResizeHandlerBehavior({\n  disabled,\n  handleId,\n  resizeHandler,\n  panelGroupElement\n}) {\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled || resizeHandler == null || panelGroupElement == null) {\n      return;\n    }\n    const handleElement = getResizeHandleElement(handleId, panelGroupElement);\n    if (handleElement == null) {\n      return;\n    }\n    const onKeyDown = event => {\n      if (event.defaultPrevented) {\n        return;\n      }\n      switch (event.key) {\n        case \"ArrowDown\":\n        case \"ArrowLeft\":\n        case \"ArrowRight\":\n        case \"ArrowUp\":\n        case \"End\":\n        case \"Home\":\n          {\n            event.preventDefault();\n            resizeHandler(event);\n            break;\n          }\n        case \"F6\":\n          {\n            event.preventDefault();\n            const groupId = handleElement.getAttribute(DATA_ATTRIBUTES.groupId);\n            assert(groupId, `No group element found for id \"${groupId}\"`);\n            const handles = getResizeHandleElementsForGroup(groupId, panelGroupElement);\n            const index = getResizeHandleElementIndex(groupId, handleId, panelGroupElement);\n            assert(index !== null, `No resize element found for id \"${handleId}\"`);\n            const nextIndex = event.shiftKey ? index > 0 ? index - 1 : handles.length - 1 : index + 1 < handles.length ? index + 1 : 0;\n            const nextHandle = handles[nextIndex];\n            nextHandle.focus();\n            break;\n          }\n      }\n    };\n    handleElement.addEventListener(\"keydown\", onKeyDown);\n    return () => {\n      handleElement.removeEventListener(\"keydown\", onKeyDown);\n    };\n  }, [panelGroupElement, disabled, handleId, resizeHandler]);\n}\n\nfunction PanelResizeHandle({\n  children = null,\n  className: classNameFromProps = \"\",\n  disabled = false,\n  hitAreaMargins,\n  id: idFromProps,\n  onBlur,\n  onClick,\n  onDragging,\n  onFocus,\n  onPointerDown,\n  onPointerUp,\n  style: styleFromProps = {},\n  tabIndex = 0,\n  tagName: Type = \"div\",\n  ...rest\n}) {\n  var _hitAreaMargins$coars, _hitAreaMargins$fine;\n  const elementRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n\n  // Use a ref to guard against users passing inline props\n  const callbacksRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    onClick,\n    onDragging,\n    onPointerDown,\n    onPointerUp\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    callbacksRef.current.onClick = onClick;\n    callbacksRef.current.onDragging = onDragging;\n    callbacksRef.current.onPointerDown = onPointerDown;\n    callbacksRef.current.onPointerUp = onPointerUp;\n  });\n  const panelGroupContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(PanelGroupContext);\n  if (panelGroupContext === null) {\n    throw Error(`PanelResizeHandle components must be rendered within a PanelGroup container`);\n  }\n  const {\n    direction,\n    groupId,\n    registerResizeHandle: registerResizeHandleWithParentGroup,\n    startDragging,\n    stopDragging,\n    panelGroupElement\n  } = panelGroupContext;\n  const resizeHandleId = useUniqueId(idFromProps);\n  const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"inactive\");\n  const [isFocused, setIsFocused] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [resizeHandler, setResizeHandler] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const committedValuesRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    state\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled) {\n      setResizeHandler(null);\n    } else {\n      const resizeHandler = registerResizeHandleWithParentGroup(resizeHandleId);\n      setResizeHandler(() => resizeHandler);\n    }\n  }, [disabled, resizeHandleId, registerResizeHandleWithParentGroup]);\n\n  // Extract hit area margins before passing them to the effect's dependency array\n  // so that inline object values won't trigger re-renders\n  const coarseHitAreaMargins = (_hitAreaMargins$coars = hitAreaMargins === null || hitAreaMargins === void 0 ? void 0 : hitAreaMargins.coarse) !== null && _hitAreaMargins$coars !== void 0 ? _hitAreaMargins$coars : 15;\n  const fineHitAreaMargins = (_hitAreaMargins$fine = hitAreaMargins === null || hitAreaMargins === void 0 ? void 0 : hitAreaMargins.fine) !== null && _hitAreaMargins$fine !== void 0 ? _hitAreaMargins$fine : 5;\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled || resizeHandler == null) {\n      return;\n    }\n    const element = elementRef.current;\n    assert(element, \"Element ref not attached\");\n    let didMove = false;\n    const setResizeHandlerState = (action, isActive, event) => {\n      if (!isActive) {\n        setState(\"inactive\");\n        return;\n      }\n      switch (action) {\n        case \"down\":\n          {\n            setState(\"drag\");\n            didMove = false;\n            assert(event, 'Expected event to be defined for \"down\" action');\n            startDragging(resizeHandleId, event);\n            const {\n              onDragging,\n              onPointerDown\n            } = callbacksRef.current;\n            onDragging === null || onDragging === void 0 ? void 0 : onDragging(true);\n            onPointerDown === null || onPointerDown === void 0 ? void 0 : onPointerDown();\n            break;\n          }\n        case \"move\":\n          {\n            const {\n              state\n            } = committedValuesRef.current;\n            didMove = true;\n            if (state !== \"drag\") {\n              setState(\"hover\");\n            }\n            assert(event, 'Expected event to be defined for \"move\" action');\n            resizeHandler(event);\n            break;\n          }\n        case \"up\":\n          {\n            setState(\"hover\");\n            stopDragging();\n            const {\n              onClick,\n              onDragging,\n              onPointerUp\n            } = callbacksRef.current;\n            onDragging === null || onDragging === void 0 ? void 0 : onDragging(false);\n            onPointerUp === null || onPointerUp === void 0 ? void 0 : onPointerUp();\n            if (!didMove) {\n              onClick === null || onClick === void 0 ? void 0 : onClick();\n            }\n            break;\n          }\n      }\n    };\n    return registerResizeHandle(resizeHandleId, element, direction, {\n      coarse: coarseHitAreaMargins,\n      fine: fineHitAreaMargins\n    }, setResizeHandlerState);\n  }, [coarseHitAreaMargins, direction, disabled, fineHitAreaMargins, registerResizeHandleWithParentGroup, resizeHandleId, resizeHandler, startDragging, stopDragging]);\n  useWindowSplitterResizeHandlerBehavior({\n    disabled,\n    handleId: resizeHandleId,\n    resizeHandler,\n    panelGroupElement\n  });\n  const style = {\n    touchAction: \"none\",\n    userSelect: \"none\"\n  };\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(Type, {\n    ...rest,\n    children,\n    className: classNameFromProps,\n    id: idFromProps,\n    onBlur: () => {\n      setIsFocused(false);\n      onBlur === null || onBlur === void 0 ? void 0 : onBlur();\n    },\n    onFocus: () => {\n      setIsFocused(true);\n      onFocus === null || onFocus === void 0 ? void 0 : onFocus();\n    },\n    ref: elementRef,\n    role: \"separator\",\n    style: {\n      ...style,\n      ...styleFromProps\n    },\n    tabIndex,\n    // CSS selectors\n    [DATA_ATTRIBUTES.groupDirection]: direction,\n    [DATA_ATTRIBUTES.groupId]: groupId,\n    [DATA_ATTRIBUTES.resizeHandle]: \"\",\n    [DATA_ATTRIBUTES.resizeHandleActive]: state === \"drag\" ? \"pointer\" : isFocused ? \"keyboard\" : undefined,\n    [DATA_ATTRIBUTES.resizeHandleEnabled]: !disabled,\n    [DATA_ATTRIBUTES.resizeHandleId]: resizeHandleId,\n    [DATA_ATTRIBUTES.resizeHandleState]: state\n  });\n}\nPanelResizeHandle.displayName = \"PanelResizeHandle\";\n\nfunction getPanelElement(id, scope = document) {\n  const element = scope.querySelector(`[data-panel-id=\"${id}\"]`);\n  if (element) {\n    return element;\n  }\n  return null;\n}\n\nfunction getPanelElementsForGroup(groupId, scope = document) {\n  return Array.from(scope.querySelectorAll(`[data-panel][data-panel-group-id=\"${groupId}\"]`));\n}\n\nfunction getIntersectingRectangle(rectOne, rectTwo, strict) {\n  if (!intersects(rectOne, rectTwo, strict)) {\n    return {\n      x: 0,\n      y: 0,\n      width: 0,\n      height: 0\n    };\n  }\n  return {\n    x: Math.max(rectOne.x, rectTwo.x),\n    y: Math.max(rectOne.y, rectTwo.y),\n    width: Math.min(rectOne.x + rectOne.width, rectTwo.x + rectTwo.width) - Math.max(rectOne.x, rectTwo.x),\n    height: Math.min(rectOne.y + rectOne.height, rectTwo.y + rectTwo.height) - Math.max(rectOne.y, rectTwo.y)\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-resizable-panels@2.1._c7b59720ff81072a693309ea5f1b1633/node_modules/react-resizable-panels/dist/react-resizable-panels.development.node.esm.js\n");

/***/ })

};
;