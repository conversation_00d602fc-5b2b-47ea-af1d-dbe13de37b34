// Test script to verify message parsing functionality
// Run with: node test-message-parser.js

// Mock the file store for testing
const mockFileStore = {
  files: {},
  addFile: async (path, content) => {
    console.log(`📁 Adding file: ${path}`);
    console.log(`📄 Content preview: ${content.substring(0, 100)}...`);
    mockFileStore.files[path] = content;
    return Promise.resolve();
  },
  getState: () => mockFileStore
};

// Mock the useFileStore hook
const useFileStore = {
  getState: () => mockFileStore
};

// Simple message parser implementation for testing
class TestMessageParser {
  constructor(callbacks = {}) {
    this.callbacks = callbacks;
  }

  parse(messageId, content) {
    console.log(`🔍 Parsing message ${messageId} for artifacts...`);
    console.log(`📄 Message content preview:`, content.substring(0, 200) + '...');

    // Parse boltArtifact tags
    const artifactRegex = /<boltArtifact[^>]*>([\s\S]*?)<\/boltArtifact>/g;
    let artifactMatch;
    let foundArtifacts = 0;

    while ((artifactMatch = artifactRegex.exec(content)) !== null) {
      foundArtifacts++;
      const artifactContent = artifactMatch[1];
      console.log(`📦 Found artifact ${foundArtifacts}`);
      this.parseActions(artifactContent);
    }

    if (foundArtifacts === 0) {
      console.log(`ℹ️ No artifacts found in message ${messageId}`);
    }
  }

  parseActions(content) {
    // Parse boltAction tags
    const actionRegex = /<boltAction\s+([^>]+)>([\s\S]*?)<\/boltAction>/g;
    let actionMatch;
    let foundActions = 0;

    while ((actionMatch = actionRegex.exec(content)) !== null) {
      foundActions++;
      const [, attributes, actionContent] = actionMatch;

      // Parse attributes
      const typeMatch = attributes.match(/type="([^"]+)"/);
      const filePathMatch = attributes.match(/filePath="([^"]+)"/);

      const type = typeMatch ? typeMatch[1] : '';
      const filePath = filePathMatch ? filePathMatch[1] : '';

      console.log(`⚡ Found action ${foundActions}: type=${type}, filePath=${filePath}`);

      if (type === 'file' && filePath && this.callbacks.onFileAction) {
        this.callbacks.onFileAction({
          type: 'file',
          filePath,
          content: actionContent.trim(),
        });
      }
    }

    if (foundActions === 0) {
      console.log(`ℹ️ No actions found in artifact content`);
    }
  }
}

// Create test parser
const testParser = new TestMessageParser({
  onFileAction: async (action) => {
    try {
      await mockFileStore.addFile(action.filePath, action.content);
      console.log(`✅ Created/updated file: ${action.filePath}`);
    } catch (error) {
      console.error(`❌ Failed to create file ${action.filePath}:`, error);
    }
  }
});

// Test cases
console.log('🧪 Starting message parser tests...\n');

// Test 1: Simple HTML file
console.log('Test 1: Simple HTML file');
const testMessage1 = `<boltArtifact id="test-html" title="Test HTML File">
<boltAction type="file" filePath="test.html"><!DOCTYPE html>
<html>
<head><title>Test</title></head>
<body><h1>Hello World</h1></body>
</html></boltAction>
</boltArtifact>`;

testParser.parse('test-1', testMessage1);

console.log('\n---\n');

// Test 2: Multiple files
console.log('Test 2: Multiple files');
const testMessage2 = `<boltArtifact id="multi-files" title="Multiple Files">
<boltAction type="file" filePath="index.js">console.log('Hello from JS');</boltAction>
<boltAction type="file" filePath="style.css">body { margin: 0; }</boltAction>
</boltArtifact>`;

testParser.parse('test-2', testMessage2);

console.log('\n---\n');

// Test 3: Mixed content with text
console.log('Test 3: Mixed content with text');
const testMessage3 = `Here's a simple React component:

<boltArtifact id="react-component" title="React Component">
<boltAction type="file" filePath="App.jsx">import React from 'react';

function App() {
  return <div>Hello React!</div>;
}

export default App;</boltAction>
</boltArtifact>

This component renders a simple greeting.`;

testParser.parse('test-3', testMessage3);

console.log('\n🏁 Test completed!');
console.log('📁 Files created:', Object.keys(mockFileStore.files));
