"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/AiChat/chat/index.tsx":
/*!**********************************************!*\
  !*** ./src/components/AiChat/chat/index.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseChat: function() { return /* binding */ BaseChat; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ai_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ai/react */ \"(app-pages-browser)/./node_modules/.pnpm/ai@4.3.17_react@18.3.1_zod@3.24.1/node_modules/ai/react/dist/index.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _stores_chatSlice__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/stores/chatSlice */ \"(app-pages-browser)/./src/stores/chatSlice.ts\");\n/* harmony import */ var _stores_fileStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/stores/fileStore */ \"(app-pages-browser)/./src/stores/fileStore.ts\");\n/* harmony import */ var _utils_indexDB__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/indexDB */ \"(app-pages-browser)/./src/utils/indexDB.ts\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/.pnpm/uuid@11.0.5/node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _components_MessageItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/MessageItem */ \"(app-pages-browser)/./src/components/AiChat/chat/components/MessageItem.tsx\");\n/* harmony import */ var _components_ChatInput__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/ChatInput */ \"(app-pages-browser)/./src/components/AiChat/chat/components/ChatInput/index.tsx\");\n/* harmony import */ var _components_Tips__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./components/Tips */ \"(app-pages-browser)/./src/components/AiChat/chat/components/Tips.tsx\");\n/* harmony import */ var _stores_userSlice__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/stores/userSlice */ \"(app-pages-browser)/./src/stores/userSlice.ts\");\n/* harmony import */ var _UserModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../UserModal */ \"(app-pages-browser)/./src/components/UserModal/index.tsx\");\n/* harmony import */ var _stores_chatModeSlice__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/stores/chatModeSlice */ \"(app-pages-browser)/./src/stores/chatModeSlice.ts\");\n/* __next_internal_client_entry_do_not_use__ BaseChat auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst BaseChat = (param)=>{\n    let { uuid: propUuid } = param;\n    _s();\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { otherConfig, uploadedImages, clearImages, addImages, removeImage } = (0,_stores_chatSlice__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    const [checkCount, setCheckCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [baseModal, setBaseModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        value: \"anthropic/claude-3.5-sonnet\",\n        label: \"Claude 3.5 Sonnet\",\n        useImage: true,\n        from: \"default\",\n        quota: 2,\n        functionCall: true\n    });\n    const { files } = (0,_stores_fileStore__WEBPACK_IMPORTED_MODULE_4__.useFileStore)();\n    const { user, token } = (0,_stores_userSlice__WEBPACK_IMPORTED_MODULE_9__[\"default\"])();\n    const { openModal } = (0,_UserModal__WEBPACK_IMPORTED_MODULE_10__.useLimitModalStore)();\n    const { mode } = (0,_stores_chatModeSlice__WEBPACK_IMPORTED_MODULE_11__[\"default\"])();\n    const [messages, setMessagesa] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const parseTimeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(Date.now());\n    const refUuidMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const chatUuid = propUuid || (0,uuid__WEBPACK_IMPORTED_MODULE_12__[\"default\"])();\n    const baseChatUrl =  true ? window.location.origin : 0;\n    const clearErrors = ()=>setErrors([]);\n    const scrollToBottom = ()=>{\n        setTimeout(()=>{\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }, 100);\n    };\n    // Mock functions for web environment\n    const parseMessages = (messages)=>{\n        // This would parse messages and update file system in desktop app\n        // For web, we'll just log for now\n        console.log(\"Parsing messages:\", messages);\n    };\n    const updateFileSystemNow = ()=>{\n        // This would update the file system in desktop app\n        // For web, this is a no-op\n        return Promise.resolve();\n    };\n    const createMpIcon = (files)=>{\n    // This would create mini program icons in desktop app\n    // For web, this is a no-op\n    };\n    const checkExecList = ()=>{\n        // This would check execution list in desktop app\n        return [];\n    };\n    const checkFinish = ()=>{\n        // This would check if execution is finished in desktop app\n        return true;\n    };\n    const { messages: realMessages, input, handleInputChange, isLoading, setMessages, append, setInput, stop, reload } = (0,ai_react__WEBPACK_IMPORTED_MODULE_13__.useChat)({\n        api: \"\".concat(baseChatUrl, \"/api/chat\"),\n        headers: {\n            ...token && {\n                Authorization: \"Bearer \".concat(token)\n            }\n        },\n        body: {\n            model: baseModal.value,\n            mode: mode,\n            otherConfig: {\n                ...otherConfig,\n                extra: {\n                    ...otherConfig.extra,\n                    isBackEnd: otherConfig.isBackEnd,\n                    backendLanguage: otherConfig.backendLanguage\n                }\n            }\n        },\n        onFinish: async (message)=>{\n            try {\n                var _find_content, _find;\n                const initMessage = messages.filter((m)=>m.role === \"system\");\n                await _utils_indexDB__WEBPACK_IMPORTED_MODULE_5__.db.insert(chatUuid, {\n                    messages: [\n                        ...messages,\n                        ...initMessage,\n                        message\n                    ],\n                    title: ((_find = [\n                        ...initMessage,\n                        ...messages\n                    ].find((m)=>m.role === \"user\" && !m.content.includes(\"<boltArtifact\"))) === null || _find === void 0 ? void 0 : (_find_content = _find.content) === null || _find_content === void 0 ? void 0 : _find_content.slice(0, 50)) || \"New Chat\"\n                });\n            } catch (error) {\n                console.error(\"Failed to save chat history:\", error);\n            }\n            setCheckCount((checkCount)=>checkCount + 1);\n        },\n        onError: (error)=>{\n            var _error_errors_, _error_errors;\n            const msg = (error === null || error === void 0 ? void 0 : (_error_errors = error.errors) === null || _error_errors === void 0 ? void 0 : (_error_errors_ = _error_errors[0]) === null || _error_errors_ === void 0 ? void 0 : _error_errors_.responseBody) || String(error);\n            console.log(\"error\", error, msg);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(msg);\n            if (String(error).includes(\"Quota not enough\")) {\n                openModal(\"limit\");\n            }\n            if (String(error).includes(\"Authentication required\")) {\n                openModal(\"login\");\n            }\n        }\n    });\n    const filterMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return messages.filter((message)=>{\n            if (message.role === \"system\") return false;\n            return true;\n        });\n    }, [\n        messages\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (Date.now() - parseTimeRef.current > 200 && isLoading) {\n            setMessagesa(realMessages);\n            parseTimeRef.current = Date.now();\n            const needParseMessages = messages.filter((m)=>!refUuidMessages.current.includes(m.id));\n            parseMessages(needParseMessages);\n            scrollToBottom();\n        }\n        if (errors.length > 0 && isLoading) {\n            clearErrors();\n        }\n        if (!isLoading) {\n            setMessagesa(realMessages);\n            createMpIcon(files);\n        }\n    }, [\n        realMessages,\n        isLoading\n    ]);\n    const handleFileSelect = async (e)=>{\n        const files = Array.from(e.target.files || []);\n        if (files.length === 0) return;\n        setIsUploading(true);\n        try {\n            const newImages = files.map((file)=>({\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(),\n                    file,\n                    url: URL.createObjectURL(file),\n                    localUrl: URL.createObjectURL(file),\n                    status: \"done\"\n                }));\n            addImages(newImages);\n        } catch (error) {\n            console.error(\"File upload failed:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to upload files\");\n        } finally{\n            setIsUploading(false);\n        }\n    };\n    const handleSubmitWithFiles = async (e, text)=>{\n        e === null || e === void 0 ? void 0 : e.preventDefault();\n        if (!input.trim() && !(text === null || text === void 0 ? void 0 : text.trim()) || isLoading) return;\n        try {\n            const currentAttachments = uploadedImages.map((img)=>({\n                    id: img.id,\n                    name: img.id,\n                    type: img.file.type,\n                    localUrl: img.localUrl,\n                    contentType: img.file.type,\n                    url: img.url\n                }));\n            clearImages();\n            append({\n                role: \"user\",\n                content: text || input\n            }, {\n                experimental_attachments: currentAttachments\n            });\n            setInput(\"\");\n            setTimeout(()=>{\n                scrollToBottom();\n            }, 100);\n        } catch (error) {\n            console.error(\"Upload failed:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to upload files\");\n        }\n    };\n    const handleKeySubmit = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmitWithFiles(e);\n        }\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        const files = Array.from(e.dataTransfer.files);\n        if (files.length > 0) {\n            const fileInput = document.createElement(\"input\");\n            fileInput.type = \"file\";\n            fileInput.multiple = true;\n            fileInput.files = e.dataTransfer.files;\n            handleFileSelect({\n                target: fileInput\n            });\n        }\n    };\n    const handleScroll = ()=>{\n    // Handle scroll events if needed\n    };\n    const showJsx = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 overflow-y-auto px-1 py-2 message-container [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]\",\n            onScroll: handleScroll,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Tips__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    append: append,\n                    setInput: setInput,\n                    handleFileSelect: handleFileSelect\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-[640px] w-full mx-auto space-y-3\",\n                    children: [\n                        filterMessages.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MessageItem__WEBPACK_IMPORTED_MODULE_6__.MessageItem, {\n                                handleRetry: ()=>{\n                                    reload();\n                                },\n                                message: message,\n                                isEndMessage: filterMessages[filterMessages.length - 1].id === message.id,\n                                isLoading: isLoading,\n                                onUpdateMessage: (messageId, content)=>{\n                                    var _content_;\n                                    append({\n                                        role: \"user\",\n                                        content: \" \".concat(content === null || content === void 0 ? void 0 : (_content_ = content[0]) === null || _content_ === void 0 ? void 0 : _content_.text)\n                                    });\n                                }\n                            }, \"\".concat(message.id, \"-\").concat(index), false, {\n                                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 25\n                            }, undefined)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: messagesEndRef\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                    lineNumber: 275,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n            lineNumber: 266,\n            columnNumber: 13\n        }, undefined);\n    }, [\n        filterMessages,\n        isLoading,\n        append,\n        setInput,\n        handleFileSelect,\n        reload\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-full flex-col dark:bg-[#18181a] max-w-full\",\n        onDragOver: handleDragOver,\n        onDrop: handleDrop,\n        children: [\n            showJsx,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatInput__WEBPACK_IMPORTED_MODULE_7__.ChatInput, {\n                input: input,\n                setMessages: setMessages,\n                append: append,\n                messages: messages,\n                stopRuning: stop,\n                setInput: setInput,\n                isLoading: isLoading,\n                isUploading: isUploading,\n                uploadedImages: uploadedImages,\n                baseModal: baseModal,\n                handleInputChange: handleInputChange,\n                handleKeySubmit: handleKeySubmit,\n                handleSubmitWithFiles: handleSubmitWithFiles,\n                handleFileSelect: handleFileSelect,\n                removeImage: removeImage,\n                addImages: addImages,\n                setIsUploading: setIsUploading,\n                setBaseModal: setBaseModal\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                lineNumber: 308,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n        lineNumber: 302,\n        columnNumber: 9\n    }, undefined);\n};\n_s(BaseChat, \"XjpiMRuQUoOxvZGXxaMsIOhU7GA=\", false, function() {\n    return [\n        _stores_chatSlice__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        _stores_fileStore__WEBPACK_IMPORTED_MODULE_4__.useFileStore,\n        _stores_userSlice__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        _UserModal__WEBPACK_IMPORTED_MODULE_10__.useLimitModalStore,\n        _stores_chatModeSlice__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        ai_react__WEBPACK_IMPORTED_MODULE_13__.useChat\n    ];\n});\n_c = BaseChat;\nvar _c;\n$RefreshReg$(_c, \"BaseChat\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AiChat/chat/index.tsx\n"));

/***/ })

});