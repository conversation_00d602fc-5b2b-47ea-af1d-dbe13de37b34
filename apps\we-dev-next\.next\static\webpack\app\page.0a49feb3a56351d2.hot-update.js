"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_BASE: function() { return /* binding */ API_BASE; },\n/* harmony export */   ModelTypes: function() { return /* binding */ ModelTypes; },\n/* harmony export */   deployProject: function() { return /* binding */ deployProject; },\n/* harmony export */   enhancePrompt: function() { return /* binding */ enhancePrompt; },\n/* harmony export */   fetchModelOptions: function() { return /* binding */ fetchModelOptions; }\n/* harmony export */ });\n/* __next_internal_client_entry_do_not_use__ API_BASE,ModelTypes,fetchModelOptions,enhancePrompt,deployProject auto */ // API configuration for the web environment\nconst API_BASE =  true ? window.location.origin : 0;\nvar ModelTypes;\n(function(ModelTypes) {\n    ModelTypes[\"Claude35sonnet\"] = \"anthropic/claude-3.5-sonnet\";\n    ModelTypes[\"gpt4oMini\"] = \"openai/gpt-4o-mini\";\n    ModelTypes[\"DeepseekR1\"] = \"deepseek/deepseek-r1\";\n    ModelTypes[\"DeepseekV3\"] = \"deepseek/deepseek-chat\";\n})(ModelTypes || (ModelTypes = {}));\n// Fetch model configurations from the API\nasync function fetchModelOptions() {\n    try {\n        const response = await fetch(\"\".concat(API_BASE, \"/api/model\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch model options\");\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching model options:\", error);\n        // Return default options if API fails\n        return [\n            {\n                label: \"Claude 3.5 Sonnet\",\n                value: \"anthropic/claude-3.5-sonnet\",\n                useImage: true,\n                description: \"Claude 3.5 Sonnet via OpenRouter\",\n                icon: null,\n                provider: \"anthropic\",\n                functionCall: true\n            }\n        ];\n    }\n}\n// Enhanced prompt API call\nasync function enhancePrompt(text) {\n    try {\n        const response = await fetch(\"\".concat(API_BASE, \"/api/enhancedPrompt\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                text\n            })\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to enhance prompt\");\n        }\n        const data = await response.json();\n        return data.text || text;\n    } catch (error) {\n        console.error(\"Error enhancing prompt:\", error);\n        return text; // Return original text if enhancement fails\n    }\n}\n// Deploy project API call\nasync function deployProject(file) {\n    try {\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        const response = await fetch(\"\".concat(API_BASE, \"/api/deploy\"), {\n            method: \"POST\",\n            body: formData\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to deploy project\");\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error deploying project:\", error);\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api.ts\n"));

/***/ })

});