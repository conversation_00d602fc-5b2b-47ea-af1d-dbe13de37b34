'use client';

import { create } from 'zustand';

export interface TerminalProcess {
  id: string;
  command: string;
  output: string;
  isRunning: boolean;
  exitCode?: number;
}

interface TerminalState {
  processes: TerminalProcess[];
  activeProcessId: string | null;
  isTerminalVisible: boolean;
  addProcess: (process: TerminalProcess) => void;
  updateProcess: (id: string, updates: Partial<TerminalProcess>) => void;
  removeProcess: (id: string) => void;
  setActiveProcess: (id: string | null) => void;
  toggleTerminal: () => void;
  setTerminalVisible: (visible: boolean) => void;
  clearOutput: (id: string) => void;
}

const useTerminalStore = create<TerminalState>((set, get) => ({
  processes: [],
  activeProcessId: null,
  isTerminalVisible: false,

  addProcess: (process) =>
    set((state) => ({
      processes: [...state.processes, process],
      activeProcessId: process.id,
    })),

  updateProcess: (id, updates) =>
    set((state) => ({
      processes: state.processes.map((p) =>
        p.id === id ? { ...p, ...updates } : p
      ),
    })),

  removeProcess: (id) =>
    set((state) => ({
      processes: state.processes.filter((p) => p.id !== id),
      activeProcessId: state.activeProcessId === id ? null : state.activeProcessId,
    })),

  setActiveProcess: (id) => set({ activeProcessId: id }),

  toggleTerminal: () =>
    set((state) => ({ isTerminalVisible: !state.isTerminalVisible })),

  setTerminalVisible: (visible) => set({ isTerminalVisible: visible }),

  clearOutput: (id) =>
    set((state) => ({
      processes: state.processes.map((p) =>
        p.id === id ? { ...p, output: '' } : p
      ),
    })),
}));

export default useTerminalStore;
