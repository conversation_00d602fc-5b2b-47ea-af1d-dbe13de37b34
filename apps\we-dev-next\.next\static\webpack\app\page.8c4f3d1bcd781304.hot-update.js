"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/AiChat/chat/index.tsx":
/*!**********************************************!*\
  !*** ./src/components/AiChat/chat/index.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseChat: function() { return /* binding */ BaseChat; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ai_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ai/react */ \"(app-pages-browser)/./node_modules/.pnpm/ai@4.3.17_react@18.3.1_zod@3.24.1/node_modules/ai/react/dist/index.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _stores_chatSlice__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/stores/chatSlice */ \"(app-pages-browser)/./src/stores/chatSlice.ts\");\n/* harmony import */ var _stores_fileStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/stores/fileStore */ \"(app-pages-browser)/./src/stores/fileStore.ts\");\n/* harmony import */ var _utils_indexDB__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/indexDB */ \"(app-pages-browser)/./src/utils/indexDB.ts\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/.pnpm/uuid@11.0.5/node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _components_MessageItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/MessageItem */ \"(app-pages-browser)/./src/components/AiChat/chat/components/MessageItem.tsx\");\n/* harmony import */ var _components_ChatInput__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/ChatInput */ \"(app-pages-browser)/./src/components/AiChat/chat/components/ChatInput/index.tsx\");\n/* harmony import */ var _components_Tips__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./components/Tips */ \"(app-pages-browser)/./src/components/AiChat/chat/components/Tips.tsx\");\n/* harmony import */ var _utils_messageParser__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/messageParser */ \"(app-pages-browser)/./src/utils/messageParser.ts\");\n/* harmony import */ var _stores_userSlice__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/stores/userSlice */ \"(app-pages-browser)/./src/stores/userSlice.ts\");\n/* harmony import */ var _UserModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../UserModal */ \"(app-pages-browser)/./src/components/UserModal/index.tsx\");\n/* harmony import */ var _stores_chatModeSlice__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/stores/chatModeSlice */ \"(app-pages-browser)/./src/stores/chatModeSlice.ts\");\n/* __next_internal_client_entry_do_not_use__ BaseChat auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst BaseChat = (param)=>{\n    let { uuid: propUuid } = param;\n    _s();\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { otherConfig, uploadedImages, clearImages, addImages, removeImage } = (0,_stores_chatSlice__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    const [checkCount, setCheckCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [baseModal, setBaseModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        value: \"anthropic/claude-3.5-sonnet\",\n        label: \"Claude 3.5 Sonnet\",\n        useImage: true,\n        from: \"default\",\n        quota: 2,\n        functionCall: true\n    });\n    const { files } = (0,_stores_fileStore__WEBPACK_IMPORTED_MODULE_4__.useFileStore)();\n    const { user, token } = (0,_stores_userSlice__WEBPACK_IMPORTED_MODULE_10__[\"default\"])();\n    const { openModal } = (0,_UserModal__WEBPACK_IMPORTED_MODULE_11__.useLimitModalStore)();\n    const { mode } = (0,_stores_chatModeSlice__WEBPACK_IMPORTED_MODULE_12__[\"default\"])();\n    const [messages, setMessagesa] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const parseTimeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(Date.now());\n    const refUuidMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const chatUuid = propUuid || (0,uuid__WEBPACK_IMPORTED_MODULE_13__[\"default\"])();\n    const baseChatUrl =  true ? window.location.origin : 0;\n    const clearErrors = ()=>setErrors([]);\n    const scrollToBottom = ()=>{\n        setTimeout(()=>{\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }, 100);\n    };\n    // Parse messages and create files\n    const parseMessagesAndCreateFiles = async (messages)=>{\n        try {\n            await (0,_utils_messageParser__WEBPACK_IMPORTED_MODULE_9__.parseMessages)(messages);\n        } catch (error) {\n            console.error(\"Error parsing messages:\", error);\n        }\n    };\n    const updateFileSystemNow = ()=>{\n        // This would update the file system in desktop app\n        // For web, this is a no-op\n        return Promise.resolve();\n    };\n    const createMpIcon = (files)=>{\n    // This would create mini program icons in desktop app\n    // For web, this is a no-op\n    };\n    const checkExecList = ()=>{\n        // This would check execution list in desktop app\n        return [];\n    };\n    const checkFinish = ()=>{\n        // This would check if execution is finished in desktop app\n        return true;\n    };\n    const { messages: realMessages, input, handleInputChange, isLoading, setMessages, append, setInput, stop, reload } = (0,ai_react__WEBPACK_IMPORTED_MODULE_14__.useChat)({\n        api: \"\".concat(baseChatUrl, \"/api/chat\"),\n        headers: {\n            ...token && {\n                Authorization: \"Bearer \".concat(token)\n            }\n        },\n        body: {\n            model: baseModal.value,\n            mode: mode,\n            otherConfig: {\n                ...otherConfig,\n                extra: {\n                    ...otherConfig.extra,\n                    isBackEnd: otherConfig.isBackEnd,\n                    backendLanguage: otherConfig.backendLanguage\n                }\n            }\n        },\n        onFinish: async (message)=>{\n            try {\n                var _find_content, _find;\n                const initMessage = messages.filter((m)=>m.role === \"system\");\n                await _utils_indexDB__WEBPACK_IMPORTED_MODULE_5__.db.insert(chatUuid, {\n                    messages: [\n                        ...messages,\n                        ...initMessage,\n                        message\n                    ],\n                    title: ((_find = [\n                        ...initMessage,\n                        ...messages\n                    ].find((m)=>m.role === \"user\" && !m.content.includes(\"<boltArtifact\"))) === null || _find === void 0 ? void 0 : (_find_content = _find.content) === null || _find_content === void 0 ? void 0 : _find_content.slice(0, 50)) || \"New Chat\"\n                });\n            } catch (error) {\n                console.error(\"Failed to save chat history:\", error);\n            }\n            setCheckCount((checkCount)=>checkCount + 1);\n        },\n        onError: (error)=>{\n            var _error_errors_, _error_errors;\n            const msg = (error === null || error === void 0 ? void 0 : (_error_errors = error.errors) === null || _error_errors === void 0 ? void 0 : (_error_errors_ = _error_errors[0]) === null || _error_errors_ === void 0 ? void 0 : _error_errors_.responseBody) || String(error);\n            console.log(\"error\", error, msg);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(msg);\n            if (String(error).includes(\"Quota not enough\")) {\n                openModal(\"limit\");\n            }\n            if (String(error).includes(\"Authentication required\")) {\n                openModal(\"login\");\n            }\n        }\n    });\n    const filterMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return messages.filter((message)=>{\n            if (message.role === \"system\") return false;\n            return true;\n        });\n    }, [\n        messages\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only update messages during streaming, but don't parse yet\n        if (Date.now() - parseTimeRef.current > 200 && isLoading) {\n            setMessagesa(realMessages);\n            parseTimeRef.current = Date.now();\n            scrollToBottom();\n        }\n        if (errors.length > 0 && isLoading) {\n            clearErrors();\n        }\n        // Only parse messages when streaming is complete\n        if (!isLoading) {\n            setMessagesa(realMessages);\n            createMpIcon(files);\n            // Parse messages when loading is complete\n            const needParseMessages = messages.filter((m)=>!refUuidMessages.current.includes(m.id) && m.role === \"assistant\" && m.content && typeof m.content === \"string\" && m.content.trim().length > 0);\n            if (needParseMessages.length > 0) {\n                console.log(\"\\uD83D\\uDCE8 Processing \".concat(needParseMessages.length, \" new messages (loading complete):\"), needParseMessages.map((m)=>{\n                    var _m_content;\n                    return {\n                        id: m.id,\n                        role: m.role,\n                        contentLength: ((_m_content = m.content) === null || _m_content === void 0 ? void 0 : _m_content.length) || 0,\n                        hasContent: !!m.content\n                    };\n                }));\n                parseMessagesAndCreateFiles(needParseMessages);\n                // Update tracked message IDs\n                refUuidMessages.current = [\n                    ...refUuidMessages.current,\n                    ...needParseMessages.map((m)=>m.id)\n                ];\n            }\n        }\n    }, [\n        realMessages,\n        isLoading\n    ]);\n    const handleFileSelect = async (e)=>{\n        const files = Array.from(e.target.files || []);\n        if (files.length === 0) return;\n        setIsUploading(true);\n        try {\n            const newImages = files.map((file)=>({\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(),\n                    file,\n                    url: URL.createObjectURL(file),\n                    localUrl: URL.createObjectURL(file),\n                    status: \"done\"\n                }));\n            addImages(newImages);\n        } catch (error) {\n            console.error(\"File upload failed:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to upload files\");\n        } finally{\n            setIsUploading(false);\n        }\n    };\n    const handleSubmitWithFiles = async (e, text)=>{\n        e === null || e === void 0 ? void 0 : e.preventDefault();\n        if (!input.trim() && !(text === null || text === void 0 ? void 0 : text.trim()) || isLoading) return;\n        try {\n            const currentAttachments = uploadedImages.map((img)=>({\n                    id: img.id,\n                    name: img.id,\n                    type: img.file.type,\n                    localUrl: img.localUrl,\n                    contentType: img.file.type,\n                    url: img.url\n                }));\n            clearImages();\n            append({\n                role: \"user\",\n                content: text || input\n            }, {\n                experimental_attachments: currentAttachments\n            });\n            setInput(\"\");\n            setTimeout(()=>{\n                scrollToBottom();\n            }, 100);\n        } catch (error) {\n            console.error(\"Upload failed:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to upload files\");\n        }\n    };\n    const handleKeySubmit = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmitWithFiles(e);\n        }\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        const files = Array.from(e.dataTransfer.files);\n        if (files.length > 0) {\n            const fileInput = document.createElement(\"input\");\n            fileInput.type = \"file\";\n            fileInput.multiple = true;\n            fileInput.files = e.dataTransfer.files;\n            handleFileSelect({\n                target: fileInput\n            });\n        }\n    };\n    const handleScroll = ()=>{\n    // Handle scroll events if needed\n    };\n    const showJsx = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 overflow-y-auto px-1 py-2 message-container [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]\",\n            onScroll: handleScroll,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Tips__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    append: append,\n                    setInput: setInput,\n                    handleFileSelect: handleFileSelect\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                    lineNumber: 290,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-[640px] w-full mx-auto space-y-3\",\n                    children: [\n                        filterMessages.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MessageItem__WEBPACK_IMPORTED_MODULE_6__.MessageItem, {\n                                handleRetry: ()=>{\n                                    reload();\n                                },\n                                message: message,\n                                isEndMessage: filterMessages[filterMessages.length - 1].id === message.id,\n                                isLoading: isLoading,\n                                onUpdateMessage: (messageId, content)=>{\n                                    var _content_;\n                                    append({\n                                        role: \"user\",\n                                        content: \" \".concat(content === null || content === void 0 ? void 0 : (_content_ = content[0]) === null || _content_ === void 0 ? void 0 : _content_.text)\n                                    });\n                                }\n                            }, \"\".concat(message.id, \"-\").concat(index), false, {\n                                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 25\n                            }, undefined)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: messagesEndRef\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n            lineNumber: 286,\n            columnNumber: 13\n        }, undefined);\n    }, [\n        filterMessages,\n        isLoading,\n        append,\n        setInput,\n        handleFileSelect,\n        reload\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-full flex-col dark:bg-[#18181a] max-w-full\",\n        onDragOver: handleDragOver,\n        onDrop: handleDrop,\n        children: [\n            showJsx,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatInput__WEBPACK_IMPORTED_MODULE_7__.ChatInput, {\n                input: input,\n                setMessages: setMessages,\n                append: append,\n                messages: messages,\n                stopRuning: stop,\n                setInput: setInput,\n                isLoading: isLoading,\n                isUploading: isUploading,\n                uploadedImages: uploadedImages,\n                baseModal: baseModal,\n                handleInputChange: handleInputChange,\n                handleKeySubmit: handleKeySubmit,\n                handleSubmitWithFiles: handleSubmitWithFiles,\n                handleFileSelect: handleFileSelect,\n                removeImage: removeImage,\n                addImages: addImages,\n                setIsUploading: setIsUploading,\n                setBaseModal: setBaseModal\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                lineNumber: 328,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n        lineNumber: 322,\n        columnNumber: 9\n    }, undefined);\n};\n_s(BaseChat, \"XjpiMRuQUoOxvZGXxaMsIOhU7GA=\", false, function() {\n    return [\n        _stores_chatSlice__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        _stores_fileStore__WEBPACK_IMPORTED_MODULE_4__.useFileStore,\n        _stores_userSlice__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        _UserModal__WEBPACK_IMPORTED_MODULE_11__.useLimitModalStore,\n        _stores_chatModeSlice__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        ai_react__WEBPACK_IMPORTED_MODULE_14__.useChat\n    ];\n});\n_c = BaseChat;\nvar _c;\n$RefreshReg$(_c, \"BaseChat\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AiChat/chat/index.tsx\n"));

/***/ })

});