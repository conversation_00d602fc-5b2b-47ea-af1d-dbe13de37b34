"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-util-subtokenize@2.0.3";
exports.ids = ["vendor-chunks/micromark-util-subtokenize@2.0.3"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/micromark-util-subtokenize@2.0.3/node_modules/micromark-util-subtokenize/dev/index.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-util-subtokenize@2.0.3/node_modules/micromark-util-subtokenize/dev/index.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SpliceBuffer: () => (/* reexport safe */ _lib_splice_buffer_js__WEBPACK_IMPORTED_MODULE_0__.SpliceBuffer),\n/* harmony export */   subtokenize: () => (/* binding */ subtokenize)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/.pnpm/devlop@1.1.0/node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_chunked__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-chunked */ \"(ssr)/./node_modules/.pnpm/micromark-util-chunked@2.0.1/node_modules/micromark-util-chunked/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var _lib_splice_buffer_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/splice-buffer.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-subtokenize@2.0.3/node_modules/micromark-util-subtokenize/dev/lib/splice-buffer.js\");\n/**\n * @import {Chunk, Event, Token} from 'micromark-util-types'\n */\n\n\n\n\n\n\n// Hidden API exposed for testing.\n\n\n/**\n * Tokenize subcontent.\n *\n * @param {Array<Event>} eventsArray\n *   List of events.\n * @returns {boolean}\n *   Whether subtokens were found.\n */\n// eslint-disable-next-line complexity\nfunction subtokenize(eventsArray) {\n  /** @type {Record<string, number>} */\n  const jumps = {}\n  let index = -1\n  /** @type {Event} */\n  let event\n  /** @type {number | undefined} */\n  let lineIndex\n  /** @type {number} */\n  let otherIndex\n  /** @type {Event} */\n  let otherEvent\n  /** @type {Array<Event>} */\n  let parameters\n  /** @type {Array<Event>} */\n  let subevents\n  /** @type {boolean | undefined} */\n  let more\n  const events = new _lib_splice_buffer_js__WEBPACK_IMPORTED_MODULE_0__.SpliceBuffer(eventsArray)\n\n  while (++index < events.length) {\n    while (index in jumps) {\n      index = jumps[index]\n    }\n\n    event = events.get(index)\n\n    // Add a hook for the GFM tasklist extension, which needs to know if text\n    // is in the first content of a list item.\n    if (\n      index &&\n      event[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow &&\n      events.get(index - 1)[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.listItemPrefix\n    ) {\n      (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(event[1]._tokenizer, 'expected `_tokenizer` on subtokens')\n      subevents = event[1]._tokenizer.events\n      otherIndex = 0\n\n      if (\n        otherIndex < subevents.length &&\n        subevents[otherIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEndingBlank\n      ) {\n        otherIndex += 2\n      }\n\n      if (\n        otherIndex < subevents.length &&\n        subevents[otherIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.content\n      ) {\n        while (++otherIndex < subevents.length) {\n          if (subevents[otherIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.content) {\n            break\n          }\n\n          if (subevents[otherIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkText) {\n            subevents[otherIndex][1]._isInFirstContentOfListItem = true\n            otherIndex++\n          }\n        }\n      }\n    }\n\n    // Enter.\n    if (event[0] === 'enter') {\n      if (event[1].contentType) {\n        Object.assign(jumps, subcontent(events, index))\n        index = jumps[index]\n        more = true\n      }\n    }\n    // Exit.\n    else if (event[1]._container) {\n      otherIndex = index\n      lineIndex = undefined\n\n      while (otherIndex--) {\n        otherEvent = events.get(otherIndex)\n\n        if (\n          otherEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding ||\n          otherEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEndingBlank\n        ) {\n          if (otherEvent[0] === 'enter') {\n            if (lineIndex) {\n              events.get(lineIndex)[1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEndingBlank\n            }\n\n            otherEvent[1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding\n            lineIndex = otherIndex\n          }\n        } else {\n          break\n        }\n      }\n\n      if (lineIndex) {\n        // Fix position.\n        event[1].end = {...events.get(lineIndex)[1].start}\n\n        // Switch container exit w/ line endings.\n        parameters = events.slice(lineIndex, index)\n        parameters.unshift(event)\n        events.splice(lineIndex, index - lineIndex + 1, parameters)\n      }\n    }\n  }\n\n  // The changes to the `events` buffer must be copied back into the eventsArray\n  (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_3__.splice)(eventsArray, 0, Number.POSITIVE_INFINITY, events.slice(0))\n  return !more\n}\n\n/**\n * Tokenize embedded tokens.\n *\n * @param {SpliceBuffer<Event>} events\n *   Events.\n * @param {number} eventIndex\n *   Index.\n * @returns {Record<string, number>}\n *   Gaps.\n */\nfunction subcontent(events, eventIndex) {\n  const token = events.get(eventIndex)[1]\n  const context = events.get(eventIndex)[2]\n  let startPosition = eventIndex - 1\n  /** @type {Array<number>} */\n  const startPositions = []\n  ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(token.contentType, 'expected `contentType` on subtokens')\n  const tokenizer =\n    token._tokenizer || context.parser[token.contentType](token.start)\n  const childEvents = tokenizer.events\n  /** @type {Array<[number, number]>} */\n  const jumps = []\n  /** @type {Record<string, number>} */\n  const gaps = {}\n  /** @type {Array<Chunk>} */\n  let stream\n  /** @type {Token | undefined} */\n  let previous\n  let index = -1\n  /** @type {Token | undefined} */\n  let current = token\n  let adjust = 0\n  let start = 0\n  const breaks = [start]\n\n  // Loop forward through the linked tokens to pass them in order to the\n  // subtokenizer.\n  while (current) {\n    // Find the position of the event for this token.\n    while (events.get(++startPosition)[1] !== current) {\n      // Empty.\n    }\n\n    (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(\n      !previous || current.previous === previous,\n      'expected previous to match'\n    )\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(!previous || previous.next === current, 'expected next to match')\n\n    startPositions.push(startPosition)\n\n    if (!current._tokenizer) {\n      stream = context.sliceStream(current)\n\n      if (!current.next) {\n        stream.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.eof)\n      }\n\n      if (previous) {\n        tokenizer.defineSkip(current.start)\n      }\n\n      if (current._isInFirstContentOfListItem) {\n        tokenizer._gfmTasklistFirstContentOfListItem = true\n      }\n\n      tokenizer.write(stream)\n\n      if (current._isInFirstContentOfListItem) {\n        tokenizer._gfmTasklistFirstContentOfListItem = undefined\n      }\n    }\n\n    // Unravel the next token.\n    previous = current\n    current = current.next\n  }\n\n  // Now, loop back through all events (and linked tokens), to figure out which\n  // parts belong where.\n  current = token\n\n  while (++index < childEvents.length) {\n    if (\n      // Find a void token that includes a break.\n      childEvents[index][0] === 'exit' &&\n      childEvents[index - 1][0] === 'enter' &&\n      childEvents[index][1].type === childEvents[index - 1][1].type &&\n      childEvents[index][1].start.line !== childEvents[index][1].end.line\n    ) {\n      (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(current, 'expected a current token')\n      start = index + 1\n      breaks.push(start)\n      // Help GC.\n      current._tokenizer = undefined\n      current.previous = undefined\n      current = current.next\n    }\n  }\n\n  // Help GC.\n  tokenizer.events = []\n\n  // If there’s one more token (which is the cases for lines that end in an\n  // EOF), that’s perfect: the last point we found starts it.\n  // If there isn’t then make sure any remaining content is added to it.\n  if (current) {\n    // Help GC.\n    current._tokenizer = undefined\n    current.previous = undefined\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(!current.next, 'expected no next token')\n  } else {\n    breaks.pop()\n  }\n\n  // Now splice the events from the subtokenizer into the current events,\n  // moving back to front so that splice indices aren’t affected.\n  index = breaks.length\n\n  while (index--) {\n    const slice = childEvents.slice(breaks[index], breaks[index + 1])\n    const start = startPositions.pop()\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(start !== undefined, 'expected a start position when splicing')\n    jumps.push([start, start + slice.length - 1])\n    events.splice(start, 2, slice)\n  }\n\n  jumps.reverse()\n  index = -1\n\n  while (++index < jumps.length) {\n    gaps[adjust + jumps[index][0]] = adjust + jumps[index][1]\n    adjust += jumps[index][1] - jumps[index][0] - 1\n  }\n\n  return gaps\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-util-subtokenize@2.0.3/node_modules/micromark-util-subtokenize/dev/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/micromark-util-subtokenize@2.0.3/node_modules/micromark-util-subtokenize/dev/lib/splice-buffer.js":
/*!******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-util-subtokenize@2.0.3/node_modules/micromark-util-subtokenize/dev/lib/splice-buffer.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SpliceBuffer: () => (/* binding */ SpliceBuffer)\n/* harmony export */ });\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/constants.js\");\n\n\n/**\n * Some of the internal operations of micromark do lots of editing\n * operations on very large arrays. This runs into problems with two\n * properties of most circa-2020 JavaScript interpreters:\n *\n *  - Array-length modifications at the high end of an array (push/pop) are\n *    expected to be common and are implemented in (amortized) time\n *    proportional to the number of elements added or removed, whereas\n *    other operations (shift/unshift and splice) are much less efficient.\n *  - Function arguments are passed on the stack, so adding tens of thousands\n *    of elements to an array with `arr.push(...newElements)` will frequently\n *    cause stack overflows. (see <https://stackoverflow.com/questions/22123769/rangeerror-maximum-call-stack-size-exceeded-why>)\n *\n * SpliceBuffers are an implementation of gap buffers, which are a\n * generalization of the \"queue made of two stacks\" idea. The splice buffer\n * maintains a cursor, and moving the cursor has cost proportional to the\n * distance the cursor moves, but inserting, deleting, or splicing in\n * new information at the cursor is as efficient as the push/pop operation.\n * This allows for an efficient sequence of splices (or pushes, pops, shifts,\n * or unshifts) as long such edits happen at the same part of the array or\n * generally sweep through the array from the beginning to the end.\n *\n * The interface for splice buffers also supports large numbers of inputs by\n * passing a single array argument rather passing multiple arguments on the\n * function call stack.\n *\n * @template T\n *   Item type.\n */\nclass SpliceBuffer {\n  /**\n   * @param {ReadonlyArray<T> | null | undefined} [initial]\n   *   Initial items (optional).\n   * @returns\n   *   Splice buffer.\n   */\n  constructor(initial) {\n    /** @type {Array<T>} */\n    this.left = initial ? [...initial] : []\n    /** @type {Array<T>} */\n    this.right = []\n  }\n\n  /**\n   * Array access;\n   * does not move the cursor.\n   *\n   * @param {number} index\n   *   Index.\n   * @return {T}\n   *   Item.\n   */\n  get(index) {\n    if (index < 0 || index >= this.left.length + this.right.length) {\n      throw new RangeError(\n        'Cannot access index `' +\n          index +\n          '` in a splice buffer of size `' +\n          (this.left.length + this.right.length) +\n          '`'\n      )\n    }\n\n    if (index < this.left.length) return this.left[index]\n    return this.right[this.right.length - index + this.left.length - 1]\n  }\n\n  /**\n   * The length of the splice buffer, one greater than the largest index in the\n   * array.\n   */\n  get length() {\n    return this.left.length + this.right.length\n  }\n\n  /**\n   * Remove and return `list[0]`;\n   * moves the cursor to `0`.\n   *\n   * @returns {T | undefined}\n   *   Item, optional.\n   */\n  shift() {\n    this.setCursor(0)\n    return this.right.pop()\n  }\n\n  /**\n   * Slice the buffer to get an array;\n   * does not move the cursor.\n   *\n   * @param {number} start\n   *   Start.\n   * @param {number | null | undefined} [end]\n   *   End (optional).\n   * @returns {Array<T>}\n   *   Array of items.\n   */\n  slice(start, end) {\n    /** @type {number} */\n    const stop =\n      end === null || end === undefined ? Number.POSITIVE_INFINITY : end\n\n    if (stop < this.left.length) {\n      return this.left.slice(start, stop)\n    }\n\n    if (start > this.left.length) {\n      return this.right\n        .slice(\n          this.right.length - stop + this.left.length,\n          this.right.length - start + this.left.length\n        )\n        .reverse()\n    }\n\n    return this.left\n      .slice(start)\n      .concat(\n        this.right.slice(this.right.length - stop + this.left.length).reverse()\n      )\n  }\n\n  /**\n   * Mimics the behavior of Array.prototype.splice() except for the change of\n   * interface necessary to avoid segfaults when patching in very large arrays.\n   *\n   * This operation moves cursor is moved to `start` and results in the cursor\n   * placed after any inserted items.\n   *\n   * @param {number} start\n   *   Start;\n   *   zero-based index at which to start changing the array;\n   *   negative numbers count backwards from the end of the array and values\n   *   that are out-of bounds are clamped to the appropriate end of the array.\n   * @param {number | null | undefined} [deleteCount=0]\n   *   Delete count (default: `0`);\n   *   maximum number of elements to delete, starting from start.\n   * @param {Array<T> | null | undefined} [items=[]]\n   *   Items to include in place of the deleted items (default: `[]`).\n   * @return {Array<T>}\n   *   Any removed items.\n   */\n  splice(start, deleteCount, items) {\n    /** @type {number} */\n    const count = deleteCount || 0\n\n    this.setCursor(Math.trunc(start))\n    const removed = this.right.splice(\n      this.right.length - count,\n      Number.POSITIVE_INFINITY\n    )\n    if (items) chunkedPush(this.left, items)\n    return removed.reverse()\n  }\n\n  /**\n   * Remove and return the highest-numbered item in the array, so\n   * `list[list.length - 1]`;\n   * Moves the cursor to `length`.\n   *\n   * @returns {T | undefined}\n   *   Item, optional.\n   */\n  pop() {\n    this.setCursor(Number.POSITIVE_INFINITY)\n    return this.left.pop()\n  }\n\n  /**\n   * Inserts a single item to the high-numbered side of the array;\n   * moves the cursor to `length`.\n   *\n   * @param {T} item\n   *   Item.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  push(item) {\n    this.setCursor(Number.POSITIVE_INFINITY)\n    this.left.push(item)\n  }\n\n  /**\n   * Inserts many items to the high-numbered side of the array.\n   * Moves the cursor to `length`.\n   *\n   * @param {Array<T>} items\n   *   Items.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  pushMany(items) {\n    this.setCursor(Number.POSITIVE_INFINITY)\n    chunkedPush(this.left, items)\n  }\n\n  /**\n   * Inserts a single item to the low-numbered side of the array;\n   * Moves the cursor to `0`.\n   *\n   * @param {T} item\n   *   Item.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  unshift(item) {\n    this.setCursor(0)\n    this.right.push(item)\n  }\n\n  /**\n   * Inserts many items to the low-numbered side of the array;\n   * moves the cursor to `0`.\n   *\n   * @param {Array<T>} items\n   *   Items.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  unshiftMany(items) {\n    this.setCursor(0)\n    chunkedPush(this.right, items.reverse())\n  }\n\n  /**\n   * Move the cursor to a specific position in the array. Requires\n   * time proportional to the distance moved.\n   *\n   * If `n < 0`, the cursor will end up at the beginning.\n   * If `n > length`, the cursor will end up at the end.\n   *\n   * @param {number} n\n   *   Position.\n   * @return {undefined}\n   *   Nothing.\n   */\n  setCursor(n) {\n    if (\n      n === this.left.length ||\n      (n > this.left.length && this.right.length === 0) ||\n      (n < 0 && this.left.length === 0)\n    )\n      return\n    if (n < this.left.length) {\n      // Move cursor to the this.left\n      const removed = this.left.splice(n, Number.POSITIVE_INFINITY)\n      chunkedPush(this.right, removed.reverse())\n    } else {\n      // Move cursor to the this.right\n      const removed = this.right.splice(\n        this.left.length + this.right.length - n,\n        Number.POSITIVE_INFINITY\n      )\n      chunkedPush(this.left, removed.reverse())\n    }\n  }\n}\n\n/**\n * Avoid stack overflow by pushing items onto the stack in segments\n *\n * @template T\n *   Item type.\n * @param {Array<T>} list\n *   List to inject into.\n * @param {ReadonlyArray<T>} right\n *   Items to inject.\n * @return {undefined}\n *   Nothing.\n */\nfunction chunkedPush(list, right) {\n  /** @type {number} */\n  let chunkStart = 0\n\n  if (right.length < micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.constants.v8MaxSafeChunkSize) {\n    list.push(...right)\n  } else {\n    while (chunkStart < right.length) {\n      list.push(\n        ...right.slice(chunkStart, chunkStart + micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.constants.v8MaxSafeChunkSize)\n      )\n      chunkStart += micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.constants.v8MaxSafeChunkSize\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-util-subtokenize@2.0.3/node_modules/micromark-util-subtokenize/dev/lib/splice-buffer.js\n");

/***/ })

};
;