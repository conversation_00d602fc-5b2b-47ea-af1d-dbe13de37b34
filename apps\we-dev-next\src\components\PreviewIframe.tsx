'use client';

import { useEffect, useState } from 'react';
import { useFileStore } from '@/stores/fileStore';

interface PreviewIframeProps {
  isMinPrograme: boolean;
  setShowIframe: (show: boolean) => void;
}

export default function PreviewIframe({ isMinPrograme, setShowIframe }: PreviewIframeProps) {
  const { files } = useFileStore();
  const [previewUrl, setPreviewUrl] = useState<string>('');

  useEffect(() => {
    // Generate preview URL from files
    const generatePreview = () => {
      // Find main HTML file
      const htmlFile = files['index.html'] || files['main.html'] || '';
      
      if (htmlFile) {
        // Create a blob URL for the HTML content
        // In a real implementation, this would be more sophisticated
        const blob = new Blob([htmlFile], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        setPreviewUrl(url);
        
        return () => URL.revokeObjectURL(url);
      } else {
        // Generate a simple preview page
        const defaultHtml = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Preview</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 40px;
            background: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #6366f1;
            margin-bottom: 20px;
        }
        .file-list {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin-top: 20px;
        }
        .file-item {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .file-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 We0 Project Preview</h1>
        <p>Your project is being built! Here are the files in your project:</p>
        
        <div class="file-list">
            <h3>Project Files:</h3>
            ${Object.keys(files).map(fileName => 
              `<div class="file-item">📄 ${fileName}</div>`
            ).join('')}
        </div>
        
        ${Object.keys(files).length === 0 ? 
          '<p><em>No files created yet. Start by asking the AI to build something!</em></p>' : 
          '<p>💡 <strong>Tip:</strong> Create an <code>index.html</code> file to see your web page here.</p>'
        }
    </div>
</body>
</html>`;
        
        const blob = new Blob([defaultHtml], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        setPreviewUrl(url);
        
        return () => URL.revokeObjectURL(url);
      }
    };

    const cleanup = generatePreview();
    return cleanup;
  }, [files]);

  return (
    <div className="h-full w-full bg-white dark:bg-gray-900 flex flex-col">
      <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
        <h3 className="text-sm font-medium text-gray-900 dark:text-white">Preview</h3>
        <div className="flex items-center gap-2">
          <button
            onClick={() => window.open(previewUrl, '_blank')}
            className="px-2 py-1 text-xs bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors"
            disabled={!previewUrl}
          >
            Open in New Tab
          </button>
          <button
            onClick={() => setShowIframe(false)}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            ✕
          </button>
        </div>
      </div>
      
      <div className="flex-1 relative">
        {previewUrl ? (
          <iframe
            src={previewUrl}
            className="w-full h-full border-0"
            title="Preview"
            sandbox="allow-scripts allow-same-origin allow-forms"
          />
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-gray-500 dark:text-gray-400">
              <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              </div>
              <p>Loading preview...</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
