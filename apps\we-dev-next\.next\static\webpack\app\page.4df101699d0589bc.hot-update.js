"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/AiChat/chat/components/Tips.tsx":
/*!********************************************************!*\
  !*** ./src/components/AiChat/chat/components/Tips.tsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nconst Tips = (param)=>{\n    let { append, setInput, handleFileSelect } = param;\n    const tips = [\n        {\n            title: \"Build a React App\",\n            description: \"Create a modern React application with components\",\n            prompt: \"Build a React todo app with add, delete, and mark complete functionality using modern React hooks and Tailwind CSS styling. Use boltArtifact format to create the files in the WebIDE.\"\n        },\n        {\n            title: \"Create a Landing Page\",\n            description: \"Design a beautiful landing page\",\n            prompt: \"Create a modern landing page for a SaaS product with hero section, features, pricing, and contact form using HTML, CSS, and JavaScript. Use boltArtifact format to create the files in the WebIDE.\"\n        },\n        {\n            title: \"Build a Dashboard\",\n            description: \"Create an admin dashboard interface\",\n            prompt: \"Build a responsive admin dashboard with sidebar navigation, charts, tables, and cards using React and Tailwind CSS.\"\n        },\n        {\n            title: \"Make a Game\",\n            description: \"Create an interactive game\",\n            prompt: \"Create a simple Snake game using HTML5 Canvas and JavaScript with score tracking and game over functionality.\"\n        }\n    ];\n    const handleTipClick = (prompt)=>{\n        setInput(prompt);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900 dark:text-white mb-2\",\n                        children: \"What would you like to build?\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\components\\\\Tips.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 dark:text-gray-400\",\n                        children: \"Choose a template below or describe your own idea\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\components\\\\Tips.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\components\\\\Tips.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-3 max-w-2xl mx-auto\",\n                children: tips.map((tip, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>handleTipClick(tip.prompt),\n                        className: \"p-4 text-left bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-purple-300 dark:hover:border-purple-600 hover:shadow-md transition-all duration-200 group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold text-gray-900 dark:text-white group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors\",\n                                children: tip.title\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\components\\\\Tips.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 dark:text-gray-400 mt-1\",\n                                children: tip.description\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\components\\\\Tips.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, index, true, {\n                        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\components\\\\Tips.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\components\\\\Tips.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\components\\\\Tips.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Tips;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Tips);\nvar _c;\n$RefreshReg$(_c, \"Tips\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AiChat/chat/components/Tips.tsx\n"));

/***/ })

});