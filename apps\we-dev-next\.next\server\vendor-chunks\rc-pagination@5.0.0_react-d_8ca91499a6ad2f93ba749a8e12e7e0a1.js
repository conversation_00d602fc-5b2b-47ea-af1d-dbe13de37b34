"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rc-pagination@5.0.0_react-d_8ca91499a6ad2f93ba749a8e12e7e0a1";
exports.ids = ["vendor-chunks/rc-pagination@5.0.0_react-d_8ca91499a6ad2f93ba749a8e12e7e0a1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/rc-pagination@5.0.0_react-d_8ca91499a6ad2f93ba749a8e12e7e0a1/node_modules/rc-pagination/es/locale/en_US.js":
/*!***************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/rc-pagination@5.0.0_react-d_8ca91499a6ad2f93ba749a8e12e7e0a1/node_modules/rc-pagination/es/locale/en_US.js ***!
  \***************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nvar locale = {\n  // Options\n  items_per_page: '/ page',\n  jump_to: 'Go to',\n  jump_to_confirm: 'confirm',\n  page: 'Page',\n  // Pagination\n  prev_page: 'Previous Page',\n  next_page: 'Next Page',\n  prev_5: 'Previous 5 Pages',\n  next_5: 'Next 5 Pages',\n  prev_3: 'Previous 3 Pages',\n  next_3: 'Next 3 Pages',\n  page_size: 'Page Size'\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (locale);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmMtcGFnaW5hdGlvbkA1LjAuMF9yZWFjdC1kXzhjYTkxNDk5YTZhZDJmOTNiYTc0OWE4ZTEyZTdlMGExL25vZGVfbW9kdWxlcy9yYy1wYWdpbmF0aW9uL2VzL2xvY2FsZS9lbl9VUy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUVBQWUsTUFBTSIsInNvdXJjZXMiOlsid2VicGFjazovL0B3ZS1kZXYvbmV4dC8uL25vZGVfbW9kdWxlcy8ucG5wbS9yYy1wYWdpbmF0aW9uQDUuMC4wX3JlYWN0LWRfOGNhOTE0OTlhNmFkMmY5M2JhNzQ5YThlMTJlN2UwYTEvbm9kZV9tb2R1bGVzL3JjLXBhZ2luYXRpb24vZXMvbG9jYWxlL2VuX1VTLmpzP2FiODQiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIGxvY2FsZSA9IHtcbiAgLy8gT3B0aW9uc1xuICBpdGVtc19wZXJfcGFnZTogJy8gcGFnZScsXG4gIGp1bXBfdG86ICdHbyB0bycsXG4gIGp1bXBfdG9fY29uZmlybTogJ2NvbmZpcm0nLFxuICBwYWdlOiAnUGFnZScsXG4gIC8vIFBhZ2luYXRpb25cbiAgcHJldl9wYWdlOiAnUHJldmlvdXMgUGFnZScsXG4gIG5leHRfcGFnZTogJ05leHQgUGFnZScsXG4gIHByZXZfNTogJ1ByZXZpb3VzIDUgUGFnZXMnLFxuICBuZXh0XzU6ICdOZXh0IDUgUGFnZXMnLFxuICBwcmV2XzM6ICdQcmV2aW91cyAzIFBhZ2VzJyxcbiAgbmV4dF8zOiAnTmV4dCAzIFBhZ2VzJyxcbiAgcGFnZV9zaXplOiAnUGFnZSBTaXplJ1xufTtcbmV4cG9ydCBkZWZhdWx0IGxvY2FsZTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/rc-pagination@5.0.0_react-d_8ca91499a6ad2f93ba749a8e12e7e0a1/node_modules/rc-pagination/es/locale/en_US.js\n");

/***/ })

};
;