'use client';

import { useFileStore } from '@/stores/fileStore';
import { Message } from 'ai/react';

export type ActionType = 'file' | 'shell' | 'start';

export interface BaseAction {
  content: string;
}

export interface FileAction extends BaseAction {
  type: 'file';
  filePath: string;
}

export interface ShellAction extends BaseAction {
  type: 'shell';
}

export interface StartAction extends BaseAction {
  type: 'start';
}

export type BoltAction = FileAction | ShellAction | StartAction;

export interface BoltArtifactData {
  id: string;
  title: string;
}

export interface BoltActionData {
  type?: ActionType;
  filePath?: string;
  content: string;
}

export interface ParserCallbacks {
  onFileAction?: (action: FileAction) => void;
  onShellAction?: (action: ShellAction) => void;
  onStartAction?: (action: StartAction) => void;
  onActionStream?: (data: any) => void;
  onActionOpen?: (data: any) => void;
  onActionClose?: (data: any) => void;
  onArtifactClose?: (data: any) => void;
}

interface MessageState {
  position: number;
  insideArtifact: boolean;
  insideAction: boolean;
  currentArtifact?: BoltArtifactData;
  currentAction: BoltActionData;
  actionId: number;
  hasInstallExecuted?: boolean;
  isUseStartCommand?: boolean;
}

export class StreamingMessageParser {
  private messages = new Map<string, MessageState>();
  public isUseStartCommand = false;
  
  constructor(private options: { callbacks?: ParserCallbacks } = {}) { }

  parse(messageId: string, input: string) {
    console.log(`🔍 Parsing message ${messageId} with content length: ${input.length}`);
    console.log(`📄 Content preview:`, input.substring(0, 200) + '...');
    
    let state = this.messages.get(messageId);

    if (!state) {
      state = {
        position: 0,
        insideAction: false,
        insideArtifact: false,
        currentAction: { content: '' },
        actionId: 0,
        hasInstallExecuted: false,
      };
      this.messages.set(messageId, state);
    }

    let output = '';
    const regex = {
      artifactOpen: /<boltArtifact[^>]*>/g,
      artifactClose: /<\/boltArtifact>/g,
      actionOpen: /<boltAction[^>]*>/g,
      actionClose: /<\/boltAction>/g
    };

    const allActionData: Record<string, any> = {};

    while (state.position < input.length) {
      if (state.insideArtifact) {
        if (state.insideAction) {
          // 查找动作结束标签
          regex.actionClose.lastIndex = state.position;
          const actionCloseMatch = regex.actionClose.exec(input);
          
          if (actionCloseMatch) {
            const content = input.slice(state.position, actionCloseMatch.index);
            
            // 处理 file 和 shell 类型的 action
            if ('type' in state.currentAction) {
              const actionData = {
                artifactId: state.currentArtifact!.id,
                messageId,
                actionId: String(state.actionId - 1),
                action: {
                  ...state.currentAction,
                  content,
                },
              };

              console.log(`📦 Found complete action:`, actionData);

              // 根据 action 类型调用不同的回调
              if (state.currentAction.type === 'file') {
                allActionData[state.currentAction.filePath!] = actionData;
                
                // Call onActionStream for file creation
                this.options.callbacks?.onActionStream?.(actionData);
              } else if (state.currentAction.type === 'shell' || state.currentAction.type === 'start') {
                // shell 类型只在关闭时处理
                this.options.callbacks?.onActionClose?.(actionData);
              } 
            }

            state.position = actionCloseMatch.index + actionCloseMatch[0].length;
            state.insideAction = false;
          } else {
            // 只对 file 类型进行流式处理
            const remainingContent = input.slice(state.position);
            if ('type' in state.currentAction && state.currentAction.type === 'file' && !allActionData[state.currentAction.filePath!]) {
              allActionData[state.currentAction.filePath!] = {
                artifactId: state.currentArtifact!.id,
                messageId,
                actionId: String(state.actionId - 1),
                action: {
                  ...state.currentAction as FileAction,
                  content: remainingContent,
                  filePath: state.currentAction.filePath,
                },
              };
              
              console.log(`📦 Found streaming action:`, allActionData[state.currentAction.filePath!]);
              
              // Call onActionStream for streaming file content
              this.options.callbacks?.onActionStream?.(allActionData[state.currentAction.filePath!]);
            }
            break;
          }
        } else {
          // 查找下一个动作开始标签或者 artifact 结束标签
          const nextActionMatch = regex.actionOpen.exec(input.slice(state.position));
          const artifactCloseMatch = regex.artifactClose.exec(input.slice(state.position));
          
          if (nextActionMatch && (!artifactCloseMatch || nextActionMatch.index < artifactCloseMatch.index)) {
            const actionTag = nextActionMatch[0];
            state.currentAction = this.parseActionTag(actionTag);
            state.insideAction = true;
            state.position += nextActionMatch.index + nextActionMatch[0].length;
            
            console.log(`🚀 Found action tag:`, actionTag, state.currentAction);
            
            this.options.callbacks?.onActionOpen?.({
              artifactId: state.currentArtifact!.id,
              messageId,
              actionId: String(state.actionId++),
              action: state.currentAction as BoltAction,
            });
          } else if (artifactCloseMatch) {
            state.position += artifactCloseMatch.index + artifactCloseMatch[0].length;
            state.insideArtifact = false;
            this.options.callbacks?.onArtifactClose?.({ 
              messageId, 
              ...state.currentArtifact! 
            });
          } else {
            break;
          }
        }
      } else {
        // 查找 artifact 开始标签
        const artifactMatch = regex.artifactOpen.exec(input.slice(state.position));
        if (artifactMatch) {
          output += input.slice(state.position, state.position + artifactMatch.index);
          const artifactTag = artifactMatch[0];
          
          const artifactTitle = this.extractAttribute(artifactTag, 'title');
          const artifactId = this.extractAttribute(artifactTag, 'id');
          
          state.currentArtifact = {
            id: artifactId || 'default',
            title: artifactTitle || 'Untitled',
          };
          
          console.log(`🎯 Found artifact:`, state.currentArtifact);
          
          state.insideArtifact = true;
          state.position += artifactMatch.index + artifactMatch[0].length;
        } else {
          output += input.slice(state.position);
          break;
        }
      }
    }

    return output;
  }

  private parseActionTag(tag: string): BoltActionData {
    const typeMatch = tag.match(/type="([^"]+)"/);
    const filePathMatch = tag.match(/filePath="([^"]+)"/);
    
    return {
      type: typeMatch?.[1] as ActionType,
      filePath: filePathMatch?.[1],
      content: '',
    };
  }

  private extractAttribute(tag: string, attribute: string): string | null {
    const match = tag.match(new RegExp(`${attribute}="([^"]+)"`));
    return match ? match[1] : null;
  }
}

// Create file with content function (equivalent to we-dev-client's createFileWithContent)
const createFileWithContent = async (filePath: string, content: string, syncFileClose?: boolean) => {
  const { addFile } = useFileStore.getState();
  console.log(`📁 Creating file: ${filePath}`);
  console.log(`📄 Content preview: ${content.substring(0, 100)}...`);
  await addFile(filePath, content, syncFileClose);
  console.log(`✅ File created successfully: ${filePath}`);
  return filePath;
};

// Create a global message parser instance with the working callback
const messageParser = new StreamingMessageParser({
  callbacks: {
    onActionStream: async (data) => {
      console.log(`🔥 onActionStream called with:`, data);
      const action = data.action as FileAction;
      if (action.type === 'file' && action.filePath && action.content) {
        await createFileWithContent(action.filePath, action.content, true);
        console.log(`✅ File created via onActionStream: ${action.filePath}`);
      }
    },
  },
});

export const parseMessages = async (messages: Message[]) => {
  console.log(`🚀 parseMessages called with ${messages.length} messages`);
  for (const message of messages) {
    if (message.role === 'assistant') {
      console.log(`🤖 Parsing assistant message: ${message.id}`);
      console.log(`📝 Message content:`, message.content);
      messageParser.parse(message.id, message.content as string);
    }
  }
};

// Test functions
export const testWorkingFormat = () => {
  const testContent = `I'll create a simple HTML file for you:

<boltArtifact id="simple-html" title="Simple HTML File">
<boltAction type="file" filePath="index.html"><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page</title>
</head>
<body>
    <h1>Hello from AI!</h1>
    <p>This file was created by the AI response parser.</p>
</body>
</html></boltAction>
</boltArtifact>

The HTML file has been created successfully.`;

  console.log('🧪 Testing working format...');
  messageParser.parse('test-working', testContent);
};

export const debugFileCreation = () => {
  console.log('🔧 Testing file creation directly...');
  const { addFile } = useFileStore.getState();

  addFile('debug-test.html', '<h1>Debug Test</h1>')
    .then(() => {
      console.log('✅ Direct file creation successful');
      const files = useFileStore.getState().files;
      console.log('📁 Current files:', Object.keys(files));
    })
    .catch((error) => {
      console.error('❌ Direct file creation failed:', error);
    });
};

// Test function to create a file directly via file store
export const testDirectFileCreation = async () => {
  console.log('🧪 Testing direct file creation...');
  const { addFile } = useFileStore.getState();

  try {
    await addFile('test-direct.html', `<!DOCTYPE html>
<html>
<head><title>Direct Test</title></head>
<body><h1>This file was created directly!</h1></body>
</html>`);

    console.log('✅ Direct file creation successful');
    const files = useFileStore.getState().files;
    console.log('📁 Current files:', Object.keys(files));
    console.log('📄 File content:', files['test-direct.html']);
  } catch (error) {
    console.error('❌ Direct file creation failed:', error);
  }
};

// Make test functions available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).testWorkingFormat = testWorkingFormat;
  (window as any).debugFileCreation = debugFileCreation;
  (window as any).testDirectFileCreation = testDirectFileCreation;
}
