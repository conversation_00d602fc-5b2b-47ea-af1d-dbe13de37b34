"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/utils/messageParser.ts":
/*!************************************!*\
  !*** ./src/utils/messageParser.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StreamingMessageParser: function() { return /* binding */ StreamingMessageParser; },\n/* harmony export */   parseMessageForFiles: function() { return /* binding */ parseMessageForFiles; },\n/* harmony export */   parseMessages: function() { return /* binding */ parseMessages; },\n/* harmony export */   testAIResponse: function() { return /* binding */ testAIResponse; },\n/* harmony export */   testMessageParser: function() { return /* binding */ testMessageParser; }\n/* harmony export */ });\n/* harmony import */ var _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/stores/fileStore */ \"(app-pages-browser)/./src/stores/fileStore.ts\");\n/* __next_internal_client_entry_do_not_use__ StreamingMessageParser,parseMessages,testMessageParser,testAIResponse,parseMessageForFiles auto */ \nclass StreamingMessageParser {\n    parse(messageId, content) {\n        console.log(\"\\uD83D\\uDD0D Parsing message \".concat(messageId, \" for artifacts...\"));\n        console.log(\"\\uD83D\\uDCC4 Message content preview:\", content.substring(0, 200) + \"...\");\n        // Parse boltArtifact tags - handle both formats\n        const artifactRegex = /<boltArtifact[^>]*>([\\s\\S]*?)<\\/boltArtifact>/g;\n        let artifactMatch;\n        let foundArtifacts = 0;\n        while((artifactMatch = artifactRegex.exec(content)) !== null){\n            foundArtifacts++;\n            const fullMatch = artifactMatch[0];\n            const artifactContent = artifactMatch[1];\n            console.log(\"\\uD83D\\uDCE6 Found artifact \".concat(foundArtifacts, \":\"), fullMatch.substring(0, 100) + \"...\");\n            // Check if this is a simplified format (type=\"file\" name=\"...\")\n            const simplifiedMatch = fullMatch.match(/<boltArtifact[^>]*type=\"file\"[^>]*name=\"([^\"]+)\"[^>]*>/);\n            if (simplifiedMatch) {\n                const fileName = simplifiedMatch[1];\n                console.log(\"\\uD83D\\uDCC4 Simplified format detected for file: \".concat(fileName));\n                if (this.callbacks.onFileAction) {\n                    this.callbacks.onFileAction({\n                        type: \"file\",\n                        filePath: fileName,\n                        content: artifactContent.trim()\n                    });\n                }\n            } else {\n                // Standard format with boltAction tags\n                this.parseActions(artifactContent);\n            }\n        }\n        if (foundArtifacts === 0) {\n            console.log(\"ℹ️ No artifacts found in message \".concat(messageId));\n            console.log(\"\\uD83D\\uDD0D Checking for boltArtifact tags in content...\");\n            if (content.includes(\"<boltArtifact\")) {\n                console.log(\"⚠️ Found boltArtifact text but regex didn't match. Content:\", content);\n            } else {\n                console.log(\"❌ No boltArtifact tags found in content at all\");\n            }\n        }\n    }\n    parseActions(content) {\n        // Parse boltAction tags - handle multiple formats\n        const actionRegex = /<boltAction\\s+([^>]+)>([\\s\\S]*?)<\\/boltAction>/g;\n        let actionMatch;\n        let foundActions = 0;\n        while((actionMatch = actionRegex.exec(content)) !== null){\n            foundActions++;\n            const [, attributes, actionContent] = actionMatch;\n            // Parse attributes\n            const typeMatch = attributes.match(/type=\"([^\"]+)\"/);\n            const filePathMatch = attributes.match(/filePath=\"([^\"]+)\"/);\n            const pathMatch = attributes.match(/path=\"([^\"]+)\"/);\n            const type = typeMatch ? typeMatch[1] : \"\";\n            const filePath = filePathMatch ? filePathMatch[1] : pathMatch ? pathMatch[1] : \"\";\n            console.log(\"⚡ Found action \".concat(foundActions, \": type=\").concat(type, \", filePath=\").concat(filePath));\n            // Handle different type variations\n            if (type === \"file\" || type === \"createFile\") {\n                if (filePath && this.callbacks.onFileAction) {\n                    this.callbacks.onFileAction({\n                        type: \"file\",\n                        filePath,\n                        content: actionContent.trim()\n                    });\n                }\n            } else if (type === \"shell\") {\n                if (this.callbacks.onShellAction) {\n                    this.callbacks.onShellAction({\n                        type: \"shell\",\n                        content: actionContent.trim()\n                    });\n                }\n            } else if (type === \"start\") {\n                if (this.callbacks.onStartAction) {\n                    this.callbacks.onStartAction({\n                        type: \"start\",\n                        content: actionContent.trim()\n                    });\n                }\n            }\n        }\n        if (foundActions === 0) {\n            console.log(\"ℹ️ No actions found in artifact content\");\n        }\n    }\n    constructor(callbacks = {}){\n        this.callbacks = callbacks;\n    }\n}\n// Create a global message parser instance\nconst messageParser = new StreamingMessageParser({\n    onFileAction: async (action)=>{\n        const { addFile } = _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__.useFileStore.getState();\n        try {\n            await addFile(action.filePath, action.content);\n            console.log(\"✅ Created/updated file: \".concat(action.filePath));\n            console.log(\"\\uD83D\\uDCC4 Content preview: \".concat(action.content.substring(0, 100), \"...\"));\n        } catch (error) {\n            console.error(\"❌ Failed to create file \".concat(action.filePath, \":\"), error);\n        }\n    },\n    onShellAction: (action)=>{\n        console.log(\"Shell command:\", action.content);\n    // TODO: Integrate with terminal store to execute commands\n    },\n    onStartAction: (action)=>{\n        console.log(\"Start command:\", action.content);\n    // TODO: Integrate with terminal store to execute start commands\n    }\n});\nconst parseMessages = async (messages)=>{\n    console.log(\"\\uD83D\\uDE80 parseMessages called with \".concat(messages.length, \" messages\"));\n    for (const message of messages){\n        if (message.role === \"assistant\") {\n            console.log(\"\\uD83E\\uDD16 Parsing assistant message: \".concat(message.id));\n            console.log(\"\\uD83D\\uDCCB Message object:\", message);\n            console.log(\"\\uD83D\\uDCDD Message content type:\", typeof message.content);\n            console.log(\"\\uD83D\\uDCDD Message content:\", message.content);\n            messageParser.parse(message.id, message.content);\n        }\n    }\n};\n// Test function to verify parsing works\nconst testMessageParser = ()=>{\n    console.log(\"\\uD83E\\uDDEA Testing message parser...\");\n    const testContent1 = 'Here\\'s a simple HTML file with \\'Hello World\\' using the boltArtifact format:\\n\\n<boltArtifact type=\"file\" name=\"test.html\">\\n<!DOCTYPE html>\\n<html lang=\"en\">\\n<head>\\n    <meta charset=\"UTF-8\">\\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\\n    <title>Hello World</title>\\n</head>\\n<body>\\n    <h1>Hello World</h1>\\n</body>\\n</html>\\n</boltArtifact>';\n    const testContent2 = 'Here\\'s a simple HTML file in the boltArtifact format:\\n\\n<boltArtifact>\\n<boltAction type=\"createFile\" path=\"index.html\">\\n<!DOCTYPE html>\\n<html lang=\"en\">\\n<head>\\n    <meta charset=\"UTF-8\">\\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\\n    <title>Hello World</title>\\n</head>\\n<body>\\n    <h1>Hello World</h1>\\n</body>\\n</html>\\n</boltAction>\\n</boltArtifact>';\n    console.log(\"\\uD83E\\uDDEA Testing message parser with format 1...\");\n    messageParser.parse(\"test-message-1\", testContent1);\n    console.log(\"\\uD83E\\uDDEA Testing message parser with format 2...\");\n    messageParser.parse(\"test-message-2\", testContent2);\n};\n// Test function with the exact AI response format\nconst testAIResponse = ()=>{\n    const aiResponse = '<boltArtifact><boltAction type=\"createFile\" path=\"test.html\">Hello World</boltAction></boltArtifact>';\n    console.log(\"\\uD83E\\uDDEA Testing with exact AI response format...\");\n    messageParser.parse(\"ai-response-test\", aiResponse);\n};\n// Make test functions available globally for debugging\nif (true) {\n    window.testMessageParser = testMessageParser;\n    window.testAIResponse = testAIResponse;\n}\n// Simple function to extract files from message content\nfunction parseMessageForFiles(content) {\n    const files = {};\n    // Parse boltArtifact and boltAction tags\n    const artifactRegex = /<boltArtifact[^>]*>([\\s\\S]*?)<\\/boltArtifact>/g;\n    let artifactMatch;\n    while((artifactMatch = artifactRegex.exec(content)) !== null){\n        const artifactContent = artifactMatch[1];\n        // Extract file actions\n        const fileActionRegex = /<boltAction\\s+type=\"file\"\\s+filePath=\"([^\"]+)\"\\s*>([\\s\\S]*?)<\\/boltAction>/g;\n        let fileMatch;\n        while((fileMatch = fileActionRegex.exec(artifactContent)) !== null){\n            const [, filePath, fileContent] = fileMatch;\n            files[filePath] = fileContent.trim();\n        }\n    }\n    return files;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/messageParser.ts\n"));

/***/ })

});