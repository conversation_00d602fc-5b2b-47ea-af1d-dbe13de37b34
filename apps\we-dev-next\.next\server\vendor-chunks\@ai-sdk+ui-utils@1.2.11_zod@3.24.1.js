"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ai-sdk+ui-utils@1.2.11_zod@3.24.1";
exports.ids = ["vendor-chunks/@ai-sdk+ui-utils@1.2.11_zod@3.24.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@ai-sdk+ui-utils@1.2.11_zod@3.24.1/node_modules/@ai-sdk/ui-utils/dist/index.mjs":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ai-sdk+ui-utils@1.2.11_zod@3.24.1/node_modules/@ai-sdk/ui-utils/dist/index.mjs ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   asSchema: () => (/* binding */ asSchema),\n/* harmony export */   callChatApi: () => (/* binding */ callChatApi),\n/* harmony export */   callCompletionApi: () => (/* binding */ callCompletionApi),\n/* harmony export */   extractMaxToolInvocationStep: () => (/* binding */ extractMaxToolInvocationStep),\n/* harmony export */   fillMessageParts: () => (/* binding */ fillMessageParts),\n/* harmony export */   formatAssistantStreamPart: () => (/* binding */ formatAssistantStreamPart),\n/* harmony export */   formatDataStreamPart: () => (/* binding */ formatDataStreamPart),\n/* harmony export */   generateId: () => (/* reexport safe */ _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId),\n/* harmony export */   getMessageParts: () => (/* binding */ getMessageParts),\n/* harmony export */   getTextFromDataUrl: () => (/* binding */ getTextFromDataUrl),\n/* harmony export */   isAssistantMessageWithCompletedToolCalls: () => (/* binding */ isAssistantMessageWithCompletedToolCalls),\n/* harmony export */   isDeepEqualData: () => (/* binding */ isDeepEqualData),\n/* harmony export */   jsonSchema: () => (/* binding */ jsonSchema),\n/* harmony export */   parseAssistantStreamPart: () => (/* binding */ parseAssistantStreamPart),\n/* harmony export */   parseDataStreamPart: () => (/* binding */ parseDataStreamPart),\n/* harmony export */   parsePartialJson: () => (/* binding */ parsePartialJson),\n/* harmony export */   prepareAttachmentsForRequest: () => (/* binding */ prepareAttachmentsForRequest),\n/* harmony export */   processAssistantStream: () => (/* binding */ processAssistantStream),\n/* harmony export */   processDataStream: () => (/* binding */ processDataStream),\n/* harmony export */   processTextStream: () => (/* binding */ processTextStream),\n/* harmony export */   shouldResubmitMessages: () => (/* binding */ shouldResubmitMessages),\n/* harmony export */   updateToolCallResult: () => (/* binding */ updateToolCallResult),\n/* harmony export */   zodSchema: () => (/* binding */ zodSchema)\n/* harmony export */ });\n/* harmony import */ var _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ai-sdk/provider-utils */ \"(ssr)/./node_modules/.pnpm/@ai-sdk+provider-utils@2.2.8_zod@3.24.1/node_modules/@ai-sdk/provider-utils/dist/index.mjs\");\n/* harmony import */ var zod_to_json_schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod-to-json-schema */ \"(ssr)/./node_modules/.pnpm/zod-to-json-schema@3.24.1_zod@3.24.1/node_modules/zod-to-json-schema/dist/esm/index.js\");\n// src/index.ts\n\n\n// src/assistant-stream-parts.ts\nvar textStreamPart = {\n  code: \"0\",\n  name: \"text\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"text\" parts expect a string value.');\n    }\n    return { type: \"text\", value };\n  }\n};\nvar errorStreamPart = {\n  code: \"3\",\n  name: \"error\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"error\" parts expect a string value.');\n    }\n    return { type: \"error\", value };\n  }\n};\nvar assistantMessageStreamPart = {\n  code: \"4\",\n  name: \"assistant_message\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"id\" in value) || !(\"role\" in value) || !(\"content\" in value) || typeof value.id !== \"string\" || typeof value.role !== \"string\" || value.role !== \"assistant\" || !Array.isArray(value.content) || !value.content.every(\n      (item) => item != null && typeof item === \"object\" && \"type\" in item && item.type === \"text\" && \"text\" in item && item.text != null && typeof item.text === \"object\" && \"value\" in item.text && typeof item.text.value === \"string\"\n    )) {\n      throw new Error(\n        '\"assistant_message\" parts expect an object with an \"id\", \"role\", and \"content\" property.'\n      );\n    }\n    return {\n      type: \"assistant_message\",\n      value\n    };\n  }\n};\nvar assistantControlDataStreamPart = {\n  code: \"5\",\n  name: \"assistant_control_data\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"threadId\" in value) || !(\"messageId\" in value) || typeof value.threadId !== \"string\" || typeof value.messageId !== \"string\") {\n      throw new Error(\n        '\"assistant_control_data\" parts expect an object with a \"threadId\" and \"messageId\" property.'\n      );\n    }\n    return {\n      type: \"assistant_control_data\",\n      value: {\n        threadId: value.threadId,\n        messageId: value.messageId\n      }\n    };\n  }\n};\nvar dataMessageStreamPart = {\n  code: \"6\",\n  name: \"data_message\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"role\" in value) || !(\"data\" in value) || typeof value.role !== \"string\" || value.role !== \"data\") {\n      throw new Error(\n        '\"data_message\" parts expect an object with a \"role\" and \"data\" property.'\n      );\n    }\n    return {\n      type: \"data_message\",\n      value\n    };\n  }\n};\nvar assistantStreamParts = [\n  textStreamPart,\n  errorStreamPart,\n  assistantMessageStreamPart,\n  assistantControlDataStreamPart,\n  dataMessageStreamPart\n];\nvar assistantStreamPartsByCode = {\n  [textStreamPart.code]: textStreamPart,\n  [errorStreamPart.code]: errorStreamPart,\n  [assistantMessageStreamPart.code]: assistantMessageStreamPart,\n  [assistantControlDataStreamPart.code]: assistantControlDataStreamPart,\n  [dataMessageStreamPart.code]: dataMessageStreamPart\n};\nvar StreamStringPrefixes = {\n  [textStreamPart.name]: textStreamPart.code,\n  [errorStreamPart.name]: errorStreamPart.code,\n  [assistantMessageStreamPart.name]: assistantMessageStreamPart.code,\n  [assistantControlDataStreamPart.name]: assistantControlDataStreamPart.code,\n  [dataMessageStreamPart.name]: dataMessageStreamPart.code\n};\nvar validCodes = assistantStreamParts.map((part) => part.code);\nvar parseAssistantStreamPart = (line) => {\n  const firstSeparatorIndex = line.indexOf(\":\");\n  if (firstSeparatorIndex === -1) {\n    throw new Error(\"Failed to parse stream string. No separator found.\");\n  }\n  const prefix = line.slice(0, firstSeparatorIndex);\n  if (!validCodes.includes(prefix)) {\n    throw new Error(`Failed to parse stream string. Invalid code ${prefix}.`);\n  }\n  const code = prefix;\n  const textValue = line.slice(firstSeparatorIndex + 1);\n  const jsonValue = JSON.parse(textValue);\n  return assistantStreamPartsByCode[code].parse(jsonValue);\n};\nfunction formatAssistantStreamPart(type, value) {\n  const streamPart = assistantStreamParts.find((part) => part.name === type);\n  if (!streamPart) {\n    throw new Error(`Invalid stream part type: ${type}`);\n  }\n  return `${streamPart.code}:${JSON.stringify(value)}\n`;\n}\n\n// src/process-chat-response.ts\n\n\n// src/duplicated/usage.ts\nfunction calculateLanguageModelUsage({\n  promptTokens,\n  completionTokens\n}) {\n  return {\n    promptTokens,\n    completionTokens,\n    totalTokens: promptTokens + completionTokens\n  };\n}\n\n// src/parse-partial-json.ts\n\n\n// src/fix-json.ts\nfunction fixJson(input) {\n  const stack = [\"ROOT\"];\n  let lastValidIndex = -1;\n  let literalStart = null;\n  function processValueStart(char, i, swapState) {\n    {\n      switch (char) {\n        case '\"': {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_STRING\");\n          break;\n        }\n        case \"f\":\n        case \"t\":\n        case \"n\": {\n          lastValidIndex = i;\n          literalStart = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_LITERAL\");\n          break;\n        }\n        case \"-\": {\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_NUMBER\");\n          break;\n        }\n        case \"0\":\n        case \"1\":\n        case \"2\":\n        case \"3\":\n        case \"4\":\n        case \"5\":\n        case \"6\":\n        case \"7\":\n        case \"8\":\n        case \"9\": {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_NUMBER\");\n          break;\n        }\n        case \"{\": {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_OBJECT_START\");\n          break;\n        }\n        case \"[\": {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_ARRAY_START\");\n          break;\n        }\n      }\n    }\n  }\n  function processAfterObjectValue(char, i) {\n    switch (char) {\n      case \",\": {\n        stack.pop();\n        stack.push(\"INSIDE_OBJECT_AFTER_COMMA\");\n        break;\n      }\n      case \"}\": {\n        lastValidIndex = i;\n        stack.pop();\n        break;\n      }\n    }\n  }\n  function processAfterArrayValue(char, i) {\n    switch (char) {\n      case \",\": {\n        stack.pop();\n        stack.push(\"INSIDE_ARRAY_AFTER_COMMA\");\n        break;\n      }\n      case \"]\": {\n        lastValidIndex = i;\n        stack.pop();\n        break;\n      }\n    }\n  }\n  for (let i = 0; i < input.length; i++) {\n    const char = input[i];\n    const currentState = stack[stack.length - 1];\n    switch (currentState) {\n      case \"ROOT\":\n        processValueStart(char, i, \"FINISH\");\n        break;\n      case \"INSIDE_OBJECT_START\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_KEY\");\n            break;\n          }\n          case \"}\": {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_AFTER_COMMA\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_KEY\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_KEY\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_AFTER_KEY\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_AFTER_KEY\": {\n        switch (char) {\n          case \":\": {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_BEFORE_VALUE\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_BEFORE_VALUE\": {\n        processValueStart(char, i, \"INSIDE_OBJECT_AFTER_VALUE\");\n        break;\n      }\n      case \"INSIDE_OBJECT_AFTER_VALUE\": {\n        processAfterObjectValue(char, i);\n        break;\n      }\n      case \"INSIDE_STRING\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            lastValidIndex = i;\n            break;\n          }\n          case \"\\\\\": {\n            stack.push(\"INSIDE_STRING_ESCAPE\");\n            break;\n          }\n          default: {\n            lastValidIndex = i;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_ARRAY_START\": {\n        switch (char) {\n          case \"]\": {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n          default: {\n            lastValidIndex = i;\n            processValueStart(char, i, \"INSIDE_ARRAY_AFTER_VALUE\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_ARRAY_AFTER_VALUE\": {\n        switch (char) {\n          case \",\": {\n            stack.pop();\n            stack.push(\"INSIDE_ARRAY_AFTER_COMMA\");\n            break;\n          }\n          case \"]\": {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n          default: {\n            lastValidIndex = i;\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_ARRAY_AFTER_COMMA\": {\n        processValueStart(char, i, \"INSIDE_ARRAY_AFTER_VALUE\");\n        break;\n      }\n      case \"INSIDE_STRING_ESCAPE\": {\n        stack.pop();\n        lastValidIndex = i;\n        break;\n      }\n      case \"INSIDE_NUMBER\": {\n        switch (char) {\n          case \"0\":\n          case \"1\":\n          case \"2\":\n          case \"3\":\n          case \"4\":\n          case \"5\":\n          case \"6\":\n          case \"7\":\n          case \"8\":\n          case \"9\": {\n            lastValidIndex = i;\n            break;\n          }\n          case \"e\":\n          case \"E\":\n          case \"-\":\n          case \".\": {\n            break;\n          }\n          case \",\": {\n            stack.pop();\n            if (stack[stack.length - 1] === \"INSIDE_ARRAY_AFTER_VALUE\") {\n              processAfterArrayValue(char, i);\n            }\n            if (stack[stack.length - 1] === \"INSIDE_OBJECT_AFTER_VALUE\") {\n              processAfterObjectValue(char, i);\n            }\n            break;\n          }\n          case \"}\": {\n            stack.pop();\n            if (stack[stack.length - 1] === \"INSIDE_OBJECT_AFTER_VALUE\") {\n              processAfterObjectValue(char, i);\n            }\n            break;\n          }\n          case \"]\": {\n            stack.pop();\n            if (stack[stack.length - 1] === \"INSIDE_ARRAY_AFTER_VALUE\") {\n              processAfterArrayValue(char, i);\n            }\n            break;\n          }\n          default: {\n            stack.pop();\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_LITERAL\": {\n        const partialLiteral = input.substring(literalStart, i + 1);\n        if (!\"false\".startsWith(partialLiteral) && !\"true\".startsWith(partialLiteral) && !\"null\".startsWith(partialLiteral)) {\n          stack.pop();\n          if (stack[stack.length - 1] === \"INSIDE_OBJECT_AFTER_VALUE\") {\n            processAfterObjectValue(char, i);\n          } else if (stack[stack.length - 1] === \"INSIDE_ARRAY_AFTER_VALUE\") {\n            processAfterArrayValue(char, i);\n          }\n        } else {\n          lastValidIndex = i;\n        }\n        break;\n      }\n    }\n  }\n  let result = input.slice(0, lastValidIndex + 1);\n  for (let i = stack.length - 1; i >= 0; i--) {\n    const state = stack[i];\n    switch (state) {\n      case \"INSIDE_STRING\": {\n        result += '\"';\n        break;\n      }\n      case \"INSIDE_OBJECT_KEY\":\n      case \"INSIDE_OBJECT_AFTER_KEY\":\n      case \"INSIDE_OBJECT_AFTER_COMMA\":\n      case \"INSIDE_OBJECT_START\":\n      case \"INSIDE_OBJECT_BEFORE_VALUE\":\n      case \"INSIDE_OBJECT_AFTER_VALUE\": {\n        result += \"}\";\n        break;\n      }\n      case \"INSIDE_ARRAY_START\":\n      case \"INSIDE_ARRAY_AFTER_COMMA\":\n      case \"INSIDE_ARRAY_AFTER_VALUE\": {\n        result += \"]\";\n        break;\n      }\n      case \"INSIDE_LITERAL\": {\n        const partialLiteral = input.substring(literalStart, input.length);\n        if (\"true\".startsWith(partialLiteral)) {\n          result += \"true\".slice(partialLiteral.length);\n        } else if (\"false\".startsWith(partialLiteral)) {\n          result += \"false\".slice(partialLiteral.length);\n        } else if (\"null\".startsWith(partialLiteral)) {\n          result += \"null\".slice(partialLiteral.length);\n        }\n      }\n    }\n  }\n  return result;\n}\n\n// src/parse-partial-json.ts\nfunction parsePartialJson(jsonText) {\n  if (jsonText === void 0) {\n    return { value: void 0, state: \"undefined-input\" };\n  }\n  let result = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.safeParseJSON)({ text: jsonText });\n  if (result.success) {\n    return { value: result.value, state: \"successful-parse\" };\n  }\n  result = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.safeParseJSON)({ text: fixJson(jsonText) });\n  if (result.success) {\n    return { value: result.value, state: \"repaired-parse\" };\n  }\n  return { value: void 0, state: \"failed-parse\" };\n}\n\n// src/data-stream-parts.ts\nvar textStreamPart2 = {\n  code: \"0\",\n  name: \"text\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"text\" parts expect a string value.');\n    }\n    return { type: \"text\", value };\n  }\n};\nvar dataStreamPart = {\n  code: \"2\",\n  name: \"data\",\n  parse: (value) => {\n    if (!Array.isArray(value)) {\n      throw new Error('\"data\" parts expect an array value.');\n    }\n    return { type: \"data\", value };\n  }\n};\nvar errorStreamPart2 = {\n  code: \"3\",\n  name: \"error\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"error\" parts expect a string value.');\n    }\n    return { type: \"error\", value };\n  }\n};\nvar messageAnnotationsStreamPart = {\n  code: \"8\",\n  name: \"message_annotations\",\n  parse: (value) => {\n    if (!Array.isArray(value)) {\n      throw new Error('\"message_annotations\" parts expect an array value.');\n    }\n    return { type: \"message_annotations\", value };\n  }\n};\nvar toolCallStreamPart = {\n  code: \"9\",\n  name: \"tool_call\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"toolName\" in value) || typeof value.toolName !== \"string\" || !(\"args\" in value) || typeof value.args !== \"object\") {\n      throw new Error(\n        '\"tool_call\" parts expect an object with a \"toolCallId\", \"toolName\", and \"args\" property.'\n      );\n    }\n    return {\n      type: \"tool_call\",\n      value\n    };\n  }\n};\nvar toolResultStreamPart = {\n  code: \"a\",\n  name: \"tool_result\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"result\" in value)) {\n      throw new Error(\n        '\"tool_result\" parts expect an object with a \"toolCallId\" and a \"result\" property.'\n      );\n    }\n    return {\n      type: \"tool_result\",\n      value\n    };\n  }\n};\nvar toolCallStreamingStartStreamPart = {\n  code: \"b\",\n  name: \"tool_call_streaming_start\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"toolName\" in value) || typeof value.toolName !== \"string\") {\n      throw new Error(\n        '\"tool_call_streaming_start\" parts expect an object with a \"toolCallId\" and \"toolName\" property.'\n      );\n    }\n    return {\n      type: \"tool_call_streaming_start\",\n      value\n    };\n  }\n};\nvar toolCallDeltaStreamPart = {\n  code: \"c\",\n  name: \"tool_call_delta\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"argsTextDelta\" in value) || typeof value.argsTextDelta !== \"string\") {\n      throw new Error(\n        '\"tool_call_delta\" parts expect an object with a \"toolCallId\" and \"argsTextDelta\" property.'\n      );\n    }\n    return {\n      type: \"tool_call_delta\",\n      value\n    };\n  }\n};\nvar finishMessageStreamPart = {\n  code: \"d\",\n  name: \"finish_message\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"finishReason\" in value) || typeof value.finishReason !== \"string\") {\n      throw new Error(\n        '\"finish_message\" parts expect an object with a \"finishReason\" property.'\n      );\n    }\n    const result = {\n      finishReason: value.finishReason\n    };\n    if (\"usage\" in value && value.usage != null && typeof value.usage === \"object\" && \"promptTokens\" in value.usage && \"completionTokens\" in value.usage) {\n      result.usage = {\n        promptTokens: typeof value.usage.promptTokens === \"number\" ? value.usage.promptTokens : Number.NaN,\n        completionTokens: typeof value.usage.completionTokens === \"number\" ? value.usage.completionTokens : Number.NaN\n      };\n    }\n    return {\n      type: \"finish_message\",\n      value: result\n    };\n  }\n};\nvar finishStepStreamPart = {\n  code: \"e\",\n  name: \"finish_step\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"finishReason\" in value) || typeof value.finishReason !== \"string\") {\n      throw new Error(\n        '\"finish_step\" parts expect an object with a \"finishReason\" property.'\n      );\n    }\n    const result = {\n      finishReason: value.finishReason,\n      isContinued: false\n    };\n    if (\"usage\" in value && value.usage != null && typeof value.usage === \"object\" && \"promptTokens\" in value.usage && \"completionTokens\" in value.usage) {\n      result.usage = {\n        promptTokens: typeof value.usage.promptTokens === \"number\" ? value.usage.promptTokens : Number.NaN,\n        completionTokens: typeof value.usage.completionTokens === \"number\" ? value.usage.completionTokens : Number.NaN\n      };\n    }\n    if (\"isContinued\" in value && typeof value.isContinued === \"boolean\") {\n      result.isContinued = value.isContinued;\n    }\n    return {\n      type: \"finish_step\",\n      value: result\n    };\n  }\n};\nvar startStepStreamPart = {\n  code: \"f\",\n  name: \"start_step\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"messageId\" in value) || typeof value.messageId !== \"string\") {\n      throw new Error(\n        '\"start_step\" parts expect an object with an \"id\" property.'\n      );\n    }\n    return {\n      type: \"start_step\",\n      value: {\n        messageId: value.messageId\n      }\n    };\n  }\n};\nvar reasoningStreamPart = {\n  code: \"g\",\n  name: \"reasoning\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"reasoning\" parts expect a string value.');\n    }\n    return { type: \"reasoning\", value };\n  }\n};\nvar sourcePart = {\n  code: \"h\",\n  name: \"source\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\") {\n      throw new Error('\"source\" parts expect a Source object.');\n    }\n    return {\n      type: \"source\",\n      value\n    };\n  }\n};\nvar redactedReasoningStreamPart = {\n  code: \"i\",\n  name: \"redacted_reasoning\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"data\" in value) || typeof value.data !== \"string\") {\n      throw new Error(\n        '\"redacted_reasoning\" parts expect an object with a \"data\" property.'\n      );\n    }\n    return { type: \"redacted_reasoning\", value: { data: value.data } };\n  }\n};\nvar reasoningSignatureStreamPart = {\n  code: \"j\",\n  name: \"reasoning_signature\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"signature\" in value) || typeof value.signature !== \"string\") {\n      throw new Error(\n        '\"reasoning_signature\" parts expect an object with a \"signature\" property.'\n      );\n    }\n    return {\n      type: \"reasoning_signature\",\n      value: { signature: value.signature }\n    };\n  }\n};\nvar fileStreamPart = {\n  code: \"k\",\n  name: \"file\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"data\" in value) || typeof value.data !== \"string\" || !(\"mimeType\" in value) || typeof value.mimeType !== \"string\") {\n      throw new Error(\n        '\"file\" parts expect an object with a \"data\" and \"mimeType\" property.'\n      );\n    }\n    return { type: \"file\", value };\n  }\n};\nvar dataStreamParts = [\n  textStreamPart2,\n  dataStreamPart,\n  errorStreamPart2,\n  messageAnnotationsStreamPart,\n  toolCallStreamPart,\n  toolResultStreamPart,\n  toolCallStreamingStartStreamPart,\n  toolCallDeltaStreamPart,\n  finishMessageStreamPart,\n  finishStepStreamPart,\n  startStepStreamPart,\n  reasoningStreamPart,\n  sourcePart,\n  redactedReasoningStreamPart,\n  reasoningSignatureStreamPart,\n  fileStreamPart\n];\nvar dataStreamPartsByCode = Object.fromEntries(\n  dataStreamParts.map((part) => [part.code, part])\n);\nvar DataStreamStringPrefixes = Object.fromEntries(\n  dataStreamParts.map((part) => [part.name, part.code])\n);\nvar validCodes2 = dataStreamParts.map((part) => part.code);\nvar parseDataStreamPart = (line) => {\n  const firstSeparatorIndex = line.indexOf(\":\");\n  if (firstSeparatorIndex === -1) {\n    throw new Error(\"Failed to parse stream string. No separator found.\");\n  }\n  const prefix = line.slice(0, firstSeparatorIndex);\n  if (!validCodes2.includes(prefix)) {\n    throw new Error(`Failed to parse stream string. Invalid code ${prefix}.`);\n  }\n  const code = prefix;\n  const textValue = line.slice(firstSeparatorIndex + 1);\n  const jsonValue = JSON.parse(textValue);\n  return dataStreamPartsByCode[code].parse(jsonValue);\n};\nfunction formatDataStreamPart(type, value) {\n  const streamPart = dataStreamParts.find((part) => part.name === type);\n  if (!streamPart) {\n    throw new Error(`Invalid stream part type: ${type}`);\n  }\n  return `${streamPart.code}:${JSON.stringify(value)}\n`;\n}\n\n// src/process-data-stream.ts\nvar NEWLINE = \"\\n\".charCodeAt(0);\nfunction concatChunks(chunks, totalLength) {\n  const concatenatedChunks = new Uint8Array(totalLength);\n  let offset = 0;\n  for (const chunk of chunks) {\n    concatenatedChunks.set(chunk, offset);\n    offset += chunk.length;\n  }\n  chunks.length = 0;\n  return concatenatedChunks;\n}\nasync function processDataStream({\n  stream,\n  onTextPart,\n  onReasoningPart,\n  onReasoningSignaturePart,\n  onRedactedReasoningPart,\n  onSourcePart,\n  onFilePart,\n  onDataPart,\n  onErrorPart,\n  onToolCallStreamingStartPart,\n  onToolCallDeltaPart,\n  onToolCallPart,\n  onToolResultPart,\n  onMessageAnnotationsPart,\n  onFinishMessagePart,\n  onFinishStepPart,\n  onStartStepPart\n}) {\n  const reader = stream.getReader();\n  const decoder = new TextDecoder();\n  const chunks = [];\n  let totalLength = 0;\n  while (true) {\n    const { value } = await reader.read();\n    if (value) {\n      chunks.push(value);\n      totalLength += value.length;\n      if (value[value.length - 1] !== NEWLINE) {\n        continue;\n      }\n    }\n    if (chunks.length === 0) {\n      break;\n    }\n    const concatenatedChunks = concatChunks(chunks, totalLength);\n    totalLength = 0;\n    const streamParts = decoder.decode(concatenatedChunks, { stream: true }).split(\"\\n\").filter((line) => line !== \"\").map(parseDataStreamPart);\n    for (const { type, value: value2 } of streamParts) {\n      switch (type) {\n        case \"text\":\n          await (onTextPart == null ? void 0 : onTextPart(value2));\n          break;\n        case \"reasoning\":\n          await (onReasoningPart == null ? void 0 : onReasoningPart(value2));\n          break;\n        case \"reasoning_signature\":\n          await (onReasoningSignaturePart == null ? void 0 : onReasoningSignaturePart(value2));\n          break;\n        case \"redacted_reasoning\":\n          await (onRedactedReasoningPart == null ? void 0 : onRedactedReasoningPart(value2));\n          break;\n        case \"file\":\n          await (onFilePart == null ? void 0 : onFilePart(value2));\n          break;\n        case \"source\":\n          await (onSourcePart == null ? void 0 : onSourcePart(value2));\n          break;\n        case \"data\":\n          await (onDataPart == null ? void 0 : onDataPart(value2));\n          break;\n        case \"error\":\n          await (onErrorPart == null ? void 0 : onErrorPart(value2));\n          break;\n        case \"message_annotations\":\n          await (onMessageAnnotationsPart == null ? void 0 : onMessageAnnotationsPart(value2));\n          break;\n        case \"tool_call_streaming_start\":\n          await (onToolCallStreamingStartPart == null ? void 0 : onToolCallStreamingStartPart(value2));\n          break;\n        case \"tool_call_delta\":\n          await (onToolCallDeltaPart == null ? void 0 : onToolCallDeltaPart(value2));\n          break;\n        case \"tool_call\":\n          await (onToolCallPart == null ? void 0 : onToolCallPart(value2));\n          break;\n        case \"tool_result\":\n          await (onToolResultPart == null ? void 0 : onToolResultPart(value2));\n          break;\n        case \"finish_message\":\n          await (onFinishMessagePart == null ? void 0 : onFinishMessagePart(value2));\n          break;\n        case \"finish_step\":\n          await (onFinishStepPart == null ? void 0 : onFinishStepPart(value2));\n          break;\n        case \"start_step\":\n          await (onStartStepPart == null ? void 0 : onStartStepPart(value2));\n          break;\n        default: {\n          const exhaustiveCheck = type;\n          throw new Error(`Unknown stream part type: ${exhaustiveCheck}`);\n        }\n      }\n    }\n  }\n}\n\n// src/process-chat-response.ts\nasync function processChatResponse({\n  stream,\n  update,\n  onToolCall,\n  onFinish,\n  generateId: generateId2 = _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId,\n  getCurrentDate = () => /* @__PURE__ */ new Date(),\n  lastMessage\n}) {\n  var _a, _b;\n  const replaceLastMessage = (lastMessage == null ? void 0 : lastMessage.role) === \"assistant\";\n  let step = replaceLastMessage ? 1 + // find max step in existing tool invocations:\n  ((_b = (_a = lastMessage.toolInvocations) == null ? void 0 : _a.reduce((max, toolInvocation) => {\n    var _a2;\n    return Math.max(max, (_a2 = toolInvocation.step) != null ? _a2 : 0);\n  }, 0)) != null ? _b : 0) : 0;\n  const message = replaceLastMessage ? structuredClone(lastMessage) : {\n    id: generateId2(),\n    createdAt: getCurrentDate(),\n    role: \"assistant\",\n    content: \"\",\n    parts: []\n  };\n  let currentTextPart = void 0;\n  let currentReasoningPart = void 0;\n  let currentReasoningTextDetail = void 0;\n  function updateToolInvocationPart(toolCallId, invocation) {\n    const part = message.parts.find(\n      (part2) => part2.type === \"tool-invocation\" && part2.toolInvocation.toolCallId === toolCallId\n    );\n    if (part != null) {\n      part.toolInvocation = invocation;\n    } else {\n      message.parts.push({\n        type: \"tool-invocation\",\n        toolInvocation: invocation\n      });\n    }\n  }\n  const data = [];\n  let messageAnnotations = replaceLastMessage ? lastMessage == null ? void 0 : lastMessage.annotations : void 0;\n  const partialToolCalls = {};\n  let usage = {\n    completionTokens: NaN,\n    promptTokens: NaN,\n    totalTokens: NaN\n  };\n  let finishReason = \"unknown\";\n  function execUpdate() {\n    const copiedData = [...data];\n    if (messageAnnotations == null ? void 0 : messageAnnotations.length) {\n      message.annotations = messageAnnotations;\n    }\n    const copiedMessage = {\n      // deep copy the message to ensure that deep changes (msg attachments) are updated\n      // with SolidJS. SolidJS uses referential integration of sub-objects to detect changes.\n      ...structuredClone(message),\n      // add a revision id to ensure that the message is updated with SWR. SWR uses a\n      // hashing approach by default to detect changes, but it only works for shallow\n      // changes. This is why we need to add a revision id to ensure that the message\n      // is updated with SWR (without it, the changes get stuck in SWR and are not\n      // forwarded to rendering):\n      revisionId: generateId2()\n    };\n    update({\n      message: copiedMessage,\n      data: copiedData,\n      replaceLastMessage\n    });\n  }\n  await processDataStream({\n    stream,\n    onTextPart(value) {\n      if (currentTextPart == null) {\n        currentTextPart = {\n          type: \"text\",\n          text: value\n        };\n        message.parts.push(currentTextPart);\n      } else {\n        currentTextPart.text += value;\n      }\n      message.content += value;\n      execUpdate();\n    },\n    onReasoningPart(value) {\n      var _a2;\n      if (currentReasoningTextDetail == null) {\n        currentReasoningTextDetail = { type: \"text\", text: value };\n        if (currentReasoningPart != null) {\n          currentReasoningPart.details.push(currentReasoningTextDetail);\n        }\n      } else {\n        currentReasoningTextDetail.text += value;\n      }\n      if (currentReasoningPart == null) {\n        currentReasoningPart = {\n          type: \"reasoning\",\n          reasoning: value,\n          details: [currentReasoningTextDetail]\n        };\n        message.parts.push(currentReasoningPart);\n      } else {\n        currentReasoningPart.reasoning += value;\n      }\n      message.reasoning = ((_a2 = message.reasoning) != null ? _a2 : \"\") + value;\n      execUpdate();\n    },\n    onReasoningSignaturePart(value) {\n      if (currentReasoningTextDetail != null) {\n        currentReasoningTextDetail.signature = value.signature;\n      }\n    },\n    onRedactedReasoningPart(value) {\n      if (currentReasoningPart == null) {\n        currentReasoningPart = {\n          type: \"reasoning\",\n          reasoning: \"\",\n          details: []\n        };\n        message.parts.push(currentReasoningPart);\n      }\n      currentReasoningPart.details.push({\n        type: \"redacted\",\n        data: value.data\n      });\n      currentReasoningTextDetail = void 0;\n      execUpdate();\n    },\n    onFilePart(value) {\n      message.parts.push({\n        type: \"file\",\n        mimeType: value.mimeType,\n        data: value.data\n      });\n      execUpdate();\n    },\n    onSourcePart(value) {\n      message.parts.push({\n        type: \"source\",\n        source: value\n      });\n      execUpdate();\n    },\n    onToolCallStreamingStartPart(value) {\n      if (message.toolInvocations == null) {\n        message.toolInvocations = [];\n      }\n      partialToolCalls[value.toolCallId] = {\n        text: \"\",\n        step,\n        toolName: value.toolName,\n        index: message.toolInvocations.length\n      };\n      const invocation = {\n        state: \"partial-call\",\n        step,\n        toolCallId: value.toolCallId,\n        toolName: value.toolName,\n        args: void 0\n      };\n      message.toolInvocations.push(invocation);\n      updateToolInvocationPart(value.toolCallId, invocation);\n      execUpdate();\n    },\n    onToolCallDeltaPart(value) {\n      const partialToolCall = partialToolCalls[value.toolCallId];\n      partialToolCall.text += value.argsTextDelta;\n      const { value: partialArgs } = parsePartialJson(partialToolCall.text);\n      const invocation = {\n        state: \"partial-call\",\n        step: partialToolCall.step,\n        toolCallId: value.toolCallId,\n        toolName: partialToolCall.toolName,\n        args: partialArgs\n      };\n      message.toolInvocations[partialToolCall.index] = invocation;\n      updateToolInvocationPart(value.toolCallId, invocation);\n      execUpdate();\n    },\n    async onToolCallPart(value) {\n      const invocation = {\n        state: \"call\",\n        step,\n        ...value\n      };\n      if (partialToolCalls[value.toolCallId] != null) {\n        message.toolInvocations[partialToolCalls[value.toolCallId].index] = invocation;\n      } else {\n        if (message.toolInvocations == null) {\n          message.toolInvocations = [];\n        }\n        message.toolInvocations.push(invocation);\n      }\n      updateToolInvocationPart(value.toolCallId, invocation);\n      execUpdate();\n      if (onToolCall) {\n        const result = await onToolCall({ toolCall: value });\n        if (result != null) {\n          const invocation2 = {\n            state: \"result\",\n            step,\n            ...value,\n            result\n          };\n          message.toolInvocations[message.toolInvocations.length - 1] = invocation2;\n          updateToolInvocationPart(value.toolCallId, invocation2);\n          execUpdate();\n        }\n      }\n    },\n    onToolResultPart(value) {\n      const toolInvocations = message.toolInvocations;\n      if (toolInvocations == null) {\n        throw new Error(\"tool_result must be preceded by a tool_call\");\n      }\n      const toolInvocationIndex = toolInvocations.findIndex(\n        (invocation2) => invocation2.toolCallId === value.toolCallId\n      );\n      if (toolInvocationIndex === -1) {\n        throw new Error(\n          \"tool_result must be preceded by a tool_call with the same toolCallId\"\n        );\n      }\n      const invocation = {\n        ...toolInvocations[toolInvocationIndex],\n        state: \"result\",\n        ...value\n      };\n      toolInvocations[toolInvocationIndex] = invocation;\n      updateToolInvocationPart(value.toolCallId, invocation);\n      execUpdate();\n    },\n    onDataPart(value) {\n      data.push(...value);\n      execUpdate();\n    },\n    onMessageAnnotationsPart(value) {\n      if (messageAnnotations == null) {\n        messageAnnotations = [...value];\n      } else {\n        messageAnnotations.push(...value);\n      }\n      execUpdate();\n    },\n    onFinishStepPart(value) {\n      step += 1;\n      currentTextPart = value.isContinued ? currentTextPart : void 0;\n      currentReasoningPart = void 0;\n      currentReasoningTextDetail = void 0;\n    },\n    onStartStepPart(value) {\n      if (!replaceLastMessage) {\n        message.id = value.messageId;\n      }\n      message.parts.push({ type: \"step-start\" });\n      execUpdate();\n    },\n    onFinishMessagePart(value) {\n      finishReason = value.finishReason;\n      if (value.usage != null) {\n        usage = calculateLanguageModelUsage(value.usage);\n      }\n    },\n    onErrorPart(error) {\n      throw new Error(error);\n    }\n  });\n  onFinish == null ? void 0 : onFinish({ message, finishReason, usage });\n}\n\n// src/process-chat-text-response.ts\n\n\n// src/process-text-stream.ts\nasync function processTextStream({\n  stream,\n  onTextPart\n}) {\n  const reader = stream.pipeThrough(new TextDecoderStream()).getReader();\n  while (true) {\n    const { done, value } = await reader.read();\n    if (done) {\n      break;\n    }\n    await onTextPart(value);\n  }\n}\n\n// src/process-chat-text-response.ts\nasync function processChatTextResponse({\n  stream,\n  update,\n  onFinish,\n  getCurrentDate = () => /* @__PURE__ */ new Date(),\n  generateId: generateId2 = _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId\n}) {\n  const textPart = { type: \"text\", text: \"\" };\n  const resultMessage = {\n    id: generateId2(),\n    createdAt: getCurrentDate(),\n    role: \"assistant\",\n    content: \"\",\n    parts: [textPart]\n  };\n  await processTextStream({\n    stream,\n    onTextPart: (chunk) => {\n      resultMessage.content += chunk;\n      textPart.text += chunk;\n      update({\n        message: { ...resultMessage },\n        data: [],\n        replaceLastMessage: false\n      });\n    }\n  });\n  onFinish == null ? void 0 : onFinish(resultMessage, {\n    usage: { completionTokens: NaN, promptTokens: NaN, totalTokens: NaN },\n    finishReason: \"unknown\"\n  });\n}\n\n// src/call-chat-api.ts\nvar getOriginalFetch = () => fetch;\nasync function callChatApi({\n  api,\n  body,\n  streamProtocol = \"data\",\n  credentials,\n  headers,\n  abortController,\n  restoreMessagesOnFailure,\n  onResponse,\n  onUpdate,\n  onFinish,\n  onToolCall,\n  generateId: generateId2,\n  fetch: fetch2 = getOriginalFetch(),\n  lastMessage,\n  requestType = \"generate\"\n}) {\n  var _a, _b, _c;\n  const request = requestType === \"resume\" ? fetch2(`${api}?chatId=${body.id}`, {\n    method: \"GET\",\n    headers: {\n      \"Content-Type\": \"application/json\",\n      ...headers\n    },\n    signal: (_a = abortController == null ? void 0 : abortController()) == null ? void 0 : _a.signal,\n    credentials\n  }) : fetch2(api, {\n    method: \"POST\",\n    body: JSON.stringify(body),\n    headers: {\n      \"Content-Type\": \"application/json\",\n      ...headers\n    },\n    signal: (_b = abortController == null ? void 0 : abortController()) == null ? void 0 : _b.signal,\n    credentials\n  });\n  const response = await request.catch((err) => {\n    restoreMessagesOnFailure();\n    throw err;\n  });\n  if (onResponse) {\n    try {\n      await onResponse(response);\n    } catch (err) {\n      throw err;\n    }\n  }\n  if (!response.ok) {\n    restoreMessagesOnFailure();\n    throw new Error(\n      (_c = await response.text()) != null ? _c : \"Failed to fetch the chat response.\"\n    );\n  }\n  if (!response.body) {\n    throw new Error(\"The response body is empty.\");\n  }\n  switch (streamProtocol) {\n    case \"text\": {\n      await processChatTextResponse({\n        stream: response.body,\n        update: onUpdate,\n        onFinish,\n        generateId: generateId2\n      });\n      return;\n    }\n    case \"data\": {\n      await processChatResponse({\n        stream: response.body,\n        update: onUpdate,\n        lastMessage,\n        onToolCall,\n        onFinish({ message, finishReason, usage }) {\n          if (onFinish && message != null) {\n            onFinish(message, { usage, finishReason });\n          }\n        },\n        generateId: generateId2\n      });\n      return;\n    }\n    default: {\n      const exhaustiveCheck = streamProtocol;\n      throw new Error(`Unknown stream protocol: ${exhaustiveCheck}`);\n    }\n  }\n}\n\n// src/call-completion-api.ts\nvar getOriginalFetch2 = () => fetch;\nasync function callCompletionApi({\n  api,\n  prompt,\n  credentials,\n  headers,\n  body,\n  streamProtocol = \"data\",\n  setCompletion,\n  setLoading,\n  setError,\n  setAbortController,\n  onResponse,\n  onFinish,\n  onError,\n  onData,\n  fetch: fetch2 = getOriginalFetch2()\n}) {\n  var _a;\n  try {\n    setLoading(true);\n    setError(void 0);\n    const abortController = new AbortController();\n    setAbortController(abortController);\n    setCompletion(\"\");\n    const response = await fetch2(api, {\n      method: \"POST\",\n      body: JSON.stringify({\n        prompt,\n        ...body\n      }),\n      credentials,\n      headers: {\n        \"Content-Type\": \"application/json\",\n        ...headers\n      },\n      signal: abortController.signal\n    }).catch((err) => {\n      throw err;\n    });\n    if (onResponse) {\n      try {\n        await onResponse(response);\n      } catch (err) {\n        throw err;\n      }\n    }\n    if (!response.ok) {\n      throw new Error(\n        (_a = await response.text()) != null ? _a : \"Failed to fetch the chat response.\"\n      );\n    }\n    if (!response.body) {\n      throw new Error(\"The response body is empty.\");\n    }\n    let result = \"\";\n    switch (streamProtocol) {\n      case \"text\": {\n        await processTextStream({\n          stream: response.body,\n          onTextPart: (chunk) => {\n            result += chunk;\n            setCompletion(result);\n          }\n        });\n        break;\n      }\n      case \"data\": {\n        await processDataStream({\n          stream: response.body,\n          onTextPart(value) {\n            result += value;\n            setCompletion(result);\n          },\n          onDataPart(value) {\n            onData == null ? void 0 : onData(value);\n          },\n          onErrorPart(value) {\n            throw new Error(value);\n          }\n        });\n        break;\n      }\n      default: {\n        const exhaustiveCheck = streamProtocol;\n        throw new Error(`Unknown stream protocol: ${exhaustiveCheck}`);\n      }\n    }\n    if (onFinish) {\n      onFinish(prompt, result);\n    }\n    setAbortController(null);\n    return result;\n  } catch (err) {\n    if (err.name === \"AbortError\") {\n      setAbortController(null);\n      return null;\n    }\n    if (err instanceof Error) {\n      if (onError) {\n        onError(err);\n      }\n    }\n    setError(err);\n  } finally {\n    setLoading(false);\n  }\n}\n\n// src/data-url.ts\nfunction getTextFromDataUrl(dataUrl) {\n  const [header, base64Content] = dataUrl.split(\",\");\n  const mimeType = header.split(\";\")[0].split(\":\")[1];\n  if (mimeType == null || base64Content == null) {\n    throw new Error(\"Invalid data URL format\");\n  }\n  try {\n    return window.atob(base64Content);\n  } catch (error) {\n    throw new Error(`Error decoding data URL`);\n  }\n}\n\n// src/extract-max-tool-invocation-step.ts\nfunction extractMaxToolInvocationStep(toolInvocations) {\n  return toolInvocations == null ? void 0 : toolInvocations.reduce((max, toolInvocation) => {\n    var _a;\n    return Math.max(max, (_a = toolInvocation.step) != null ? _a : 0);\n  }, 0);\n}\n\n// src/get-message-parts.ts\nfunction getMessageParts(message) {\n  var _a;\n  return (_a = message.parts) != null ? _a : [\n    ...message.toolInvocations ? message.toolInvocations.map((toolInvocation) => ({\n      type: \"tool-invocation\",\n      toolInvocation\n    })) : [],\n    ...message.reasoning ? [\n      {\n        type: \"reasoning\",\n        reasoning: message.reasoning,\n        details: [{ type: \"text\", text: message.reasoning }]\n      }\n    ] : [],\n    ...message.content ? [{ type: \"text\", text: message.content }] : []\n  ];\n}\n\n// src/fill-message-parts.ts\nfunction fillMessageParts(messages) {\n  return messages.map((message) => ({\n    ...message,\n    parts: getMessageParts(message)\n  }));\n}\n\n// src/is-deep-equal-data.ts\nfunction isDeepEqualData(obj1, obj2) {\n  if (obj1 === obj2)\n    return true;\n  if (obj1 == null || obj2 == null)\n    return false;\n  if (typeof obj1 !== \"object\" && typeof obj2 !== \"object\")\n    return obj1 === obj2;\n  if (obj1.constructor !== obj2.constructor)\n    return false;\n  if (obj1 instanceof Date && obj2 instanceof Date) {\n    return obj1.getTime() === obj2.getTime();\n  }\n  if (Array.isArray(obj1)) {\n    if (obj1.length !== obj2.length)\n      return false;\n    for (let i = 0; i < obj1.length; i++) {\n      if (!isDeepEqualData(obj1[i], obj2[i]))\n        return false;\n    }\n    return true;\n  }\n  const keys1 = Object.keys(obj1);\n  const keys2 = Object.keys(obj2);\n  if (keys1.length !== keys2.length)\n    return false;\n  for (const key of keys1) {\n    if (!keys2.includes(key))\n      return false;\n    if (!isDeepEqualData(obj1[key], obj2[key]))\n      return false;\n  }\n  return true;\n}\n\n// src/prepare-attachments-for-request.ts\nasync function prepareAttachmentsForRequest(attachmentsFromOptions) {\n  if (!attachmentsFromOptions) {\n    return [];\n  }\n  if (globalThis.FileList && attachmentsFromOptions instanceof globalThis.FileList) {\n    return Promise.all(\n      Array.from(attachmentsFromOptions).map(async (attachment) => {\n        const { name, type } = attachment;\n        const dataUrl = await new Promise((resolve, reject) => {\n          const reader = new FileReader();\n          reader.onload = (readerEvent) => {\n            var _a;\n            resolve((_a = readerEvent.target) == null ? void 0 : _a.result);\n          };\n          reader.onerror = (error) => reject(error);\n          reader.readAsDataURL(attachment);\n        });\n        return {\n          name,\n          contentType: type,\n          url: dataUrl\n        };\n      })\n    );\n  }\n  if (Array.isArray(attachmentsFromOptions)) {\n    return attachmentsFromOptions;\n  }\n  throw new Error(\"Invalid attachments type\");\n}\n\n// src/process-assistant-stream.ts\nvar NEWLINE2 = \"\\n\".charCodeAt(0);\nfunction concatChunks2(chunks, totalLength) {\n  const concatenatedChunks = new Uint8Array(totalLength);\n  let offset = 0;\n  for (const chunk of chunks) {\n    concatenatedChunks.set(chunk, offset);\n    offset += chunk.length;\n  }\n  chunks.length = 0;\n  return concatenatedChunks;\n}\nasync function processAssistantStream({\n  stream,\n  onTextPart,\n  onErrorPart,\n  onAssistantMessagePart,\n  onAssistantControlDataPart,\n  onDataMessagePart\n}) {\n  const reader = stream.getReader();\n  const decoder = new TextDecoder();\n  const chunks = [];\n  let totalLength = 0;\n  while (true) {\n    const { value } = await reader.read();\n    if (value) {\n      chunks.push(value);\n      totalLength += value.length;\n      if (value[value.length - 1] !== NEWLINE2) {\n        continue;\n      }\n    }\n    if (chunks.length === 0) {\n      break;\n    }\n    const concatenatedChunks = concatChunks2(chunks, totalLength);\n    totalLength = 0;\n    const streamParts = decoder.decode(concatenatedChunks, { stream: true }).split(\"\\n\").filter((line) => line !== \"\").map(parseAssistantStreamPart);\n    for (const { type, value: value2 } of streamParts) {\n      switch (type) {\n        case \"text\":\n          await (onTextPart == null ? void 0 : onTextPart(value2));\n          break;\n        case \"error\":\n          await (onErrorPart == null ? void 0 : onErrorPart(value2));\n          break;\n        case \"assistant_message\":\n          await (onAssistantMessagePart == null ? void 0 : onAssistantMessagePart(value2));\n          break;\n        case \"assistant_control_data\":\n          await (onAssistantControlDataPart == null ? void 0 : onAssistantControlDataPart(value2));\n          break;\n        case \"data_message\":\n          await (onDataMessagePart == null ? void 0 : onDataMessagePart(value2));\n          break;\n        default: {\n          const exhaustiveCheck = type;\n          throw new Error(`Unknown stream part type: ${exhaustiveCheck}`);\n        }\n      }\n    }\n  }\n}\n\n// src/schema.ts\n\n\n// src/zod-schema.ts\n\nfunction zodSchema(zodSchema2, options) {\n  var _a;\n  const useReferences = (_a = options == null ? void 0 : options.useReferences) != null ? _a : false;\n  return jsonSchema(\n    (0,zod_to_json_schema__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(zodSchema2, {\n      $refStrategy: useReferences ? \"root\" : \"none\",\n      target: \"jsonSchema7\"\n      // note: openai mode breaks various gemini conversions\n    }),\n    {\n      validate: (value) => {\n        const result = zodSchema2.safeParse(value);\n        return result.success ? { success: true, value: result.data } : { success: false, error: result.error };\n      }\n    }\n  );\n}\n\n// src/schema.ts\nvar schemaSymbol = Symbol.for(\"vercel.ai.schema\");\nfunction jsonSchema(jsonSchema2, {\n  validate\n} = {}) {\n  return {\n    [schemaSymbol]: true,\n    _type: void 0,\n    // should never be used directly\n    [_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.validatorSymbol]: true,\n    jsonSchema: jsonSchema2,\n    validate\n  };\n}\nfunction isSchema(value) {\n  return typeof value === \"object\" && value !== null && schemaSymbol in value && value[schemaSymbol] === true && \"jsonSchema\" in value && \"validate\" in value;\n}\nfunction asSchema(schema) {\n  return isSchema(schema) ? schema : zodSchema(schema);\n}\n\n// src/should-resubmit-messages.ts\nfunction shouldResubmitMessages({\n  originalMaxToolInvocationStep,\n  originalMessageCount,\n  maxSteps,\n  messages\n}) {\n  var _a;\n  const lastMessage = messages[messages.length - 1];\n  return (\n    // check if the feature is enabled:\n    maxSteps > 1 && // ensure there is a last message:\n    lastMessage != null && // ensure we actually have new steps (to prevent infinite loops in case of errors):\n    (messages.length > originalMessageCount || extractMaxToolInvocationStep(lastMessage.toolInvocations) !== originalMaxToolInvocationStep) && // check that next step is possible:\n    isAssistantMessageWithCompletedToolCalls(lastMessage) && // limit the number of automatic steps:\n    ((_a = extractMaxToolInvocationStep(lastMessage.toolInvocations)) != null ? _a : 0) < maxSteps\n  );\n}\nfunction isAssistantMessageWithCompletedToolCalls(message) {\n  if (message.role !== \"assistant\") {\n    return false;\n  }\n  const lastStepStartIndex = message.parts.reduce((lastIndex, part, index) => {\n    return part.type === \"step-start\" ? index : lastIndex;\n  }, -1);\n  const lastStepToolInvocations = message.parts.slice(lastStepStartIndex + 1).filter((part) => part.type === \"tool-invocation\");\n  return lastStepToolInvocations.length > 0 && lastStepToolInvocations.every((part) => \"result\" in part.toolInvocation);\n}\n\n// src/update-tool-call-result.ts\nfunction updateToolCallResult({\n  messages,\n  toolCallId,\n  toolResult: result\n}) {\n  var _a;\n  const lastMessage = messages[messages.length - 1];\n  const invocationPart = lastMessage.parts.find(\n    (part) => part.type === \"tool-invocation\" && part.toolInvocation.toolCallId === toolCallId\n  );\n  if (invocationPart == null) {\n    return;\n  }\n  const toolResult = {\n    ...invocationPart.toolInvocation,\n    state: \"result\",\n    result\n  };\n  invocationPart.toolInvocation = toolResult;\n  lastMessage.toolInvocations = (_a = lastMessage.toolInvocations) == null ? void 0 : _a.map(\n    (toolInvocation) => toolInvocation.toolCallId === toolCallId ? toolResult : toolInvocation\n  );\n}\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@ai-sdk+ui-utils@1.2.11_zod@3.24.1/node_modules/@ai-sdk/ui-utils/dist/index.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@ai-sdk+ui-utils@1.2.11_zod@3.24.1/node_modules/@ai-sdk/ui-utils/dist/index.mjs":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ai-sdk+ui-utils@1.2.11_zod@3.24.1/node_modules/@ai-sdk/ui-utils/dist/index.mjs ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   asSchema: () => (/* binding */ asSchema),\n/* harmony export */   callChatApi: () => (/* binding */ callChatApi),\n/* harmony export */   callCompletionApi: () => (/* binding */ callCompletionApi),\n/* harmony export */   extractMaxToolInvocationStep: () => (/* binding */ extractMaxToolInvocationStep),\n/* harmony export */   fillMessageParts: () => (/* binding */ fillMessageParts),\n/* harmony export */   formatAssistantStreamPart: () => (/* binding */ formatAssistantStreamPart),\n/* harmony export */   formatDataStreamPart: () => (/* binding */ formatDataStreamPart),\n/* harmony export */   generateId: () => (/* reexport safe */ _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId),\n/* harmony export */   getMessageParts: () => (/* binding */ getMessageParts),\n/* harmony export */   getTextFromDataUrl: () => (/* binding */ getTextFromDataUrl),\n/* harmony export */   isAssistantMessageWithCompletedToolCalls: () => (/* binding */ isAssistantMessageWithCompletedToolCalls),\n/* harmony export */   isDeepEqualData: () => (/* binding */ isDeepEqualData),\n/* harmony export */   jsonSchema: () => (/* binding */ jsonSchema),\n/* harmony export */   parseAssistantStreamPart: () => (/* binding */ parseAssistantStreamPart),\n/* harmony export */   parseDataStreamPart: () => (/* binding */ parseDataStreamPart),\n/* harmony export */   parsePartialJson: () => (/* binding */ parsePartialJson),\n/* harmony export */   prepareAttachmentsForRequest: () => (/* binding */ prepareAttachmentsForRequest),\n/* harmony export */   processAssistantStream: () => (/* binding */ processAssistantStream),\n/* harmony export */   processDataStream: () => (/* binding */ processDataStream),\n/* harmony export */   processTextStream: () => (/* binding */ processTextStream),\n/* harmony export */   shouldResubmitMessages: () => (/* binding */ shouldResubmitMessages),\n/* harmony export */   updateToolCallResult: () => (/* binding */ updateToolCallResult),\n/* harmony export */   zodSchema: () => (/* binding */ zodSchema)\n/* harmony export */ });\n/* harmony import */ var _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ai-sdk/provider-utils */ \"(rsc)/./node_modules/.pnpm/@ai-sdk+provider-utils@2.2.8_zod@3.24.1/node_modules/@ai-sdk/provider-utils/dist/index.mjs\");\n/* harmony import */ var zod_to_json_schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod-to-json-schema */ \"(rsc)/./node_modules/.pnpm/zod-to-json-schema@3.24.1_zod@3.24.1/node_modules/zod-to-json-schema/dist/esm/index.js\");\n// src/index.ts\n\n\n// src/assistant-stream-parts.ts\nvar textStreamPart = {\n  code: \"0\",\n  name: \"text\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"text\" parts expect a string value.');\n    }\n    return { type: \"text\", value };\n  }\n};\nvar errorStreamPart = {\n  code: \"3\",\n  name: \"error\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"error\" parts expect a string value.');\n    }\n    return { type: \"error\", value };\n  }\n};\nvar assistantMessageStreamPart = {\n  code: \"4\",\n  name: \"assistant_message\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"id\" in value) || !(\"role\" in value) || !(\"content\" in value) || typeof value.id !== \"string\" || typeof value.role !== \"string\" || value.role !== \"assistant\" || !Array.isArray(value.content) || !value.content.every(\n      (item) => item != null && typeof item === \"object\" && \"type\" in item && item.type === \"text\" && \"text\" in item && item.text != null && typeof item.text === \"object\" && \"value\" in item.text && typeof item.text.value === \"string\"\n    )) {\n      throw new Error(\n        '\"assistant_message\" parts expect an object with an \"id\", \"role\", and \"content\" property.'\n      );\n    }\n    return {\n      type: \"assistant_message\",\n      value\n    };\n  }\n};\nvar assistantControlDataStreamPart = {\n  code: \"5\",\n  name: \"assistant_control_data\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"threadId\" in value) || !(\"messageId\" in value) || typeof value.threadId !== \"string\" || typeof value.messageId !== \"string\") {\n      throw new Error(\n        '\"assistant_control_data\" parts expect an object with a \"threadId\" and \"messageId\" property.'\n      );\n    }\n    return {\n      type: \"assistant_control_data\",\n      value: {\n        threadId: value.threadId,\n        messageId: value.messageId\n      }\n    };\n  }\n};\nvar dataMessageStreamPart = {\n  code: \"6\",\n  name: \"data_message\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"role\" in value) || !(\"data\" in value) || typeof value.role !== \"string\" || value.role !== \"data\") {\n      throw new Error(\n        '\"data_message\" parts expect an object with a \"role\" and \"data\" property.'\n      );\n    }\n    return {\n      type: \"data_message\",\n      value\n    };\n  }\n};\nvar assistantStreamParts = [\n  textStreamPart,\n  errorStreamPart,\n  assistantMessageStreamPart,\n  assistantControlDataStreamPart,\n  dataMessageStreamPart\n];\nvar assistantStreamPartsByCode = {\n  [textStreamPart.code]: textStreamPart,\n  [errorStreamPart.code]: errorStreamPart,\n  [assistantMessageStreamPart.code]: assistantMessageStreamPart,\n  [assistantControlDataStreamPart.code]: assistantControlDataStreamPart,\n  [dataMessageStreamPart.code]: dataMessageStreamPart\n};\nvar StreamStringPrefixes = {\n  [textStreamPart.name]: textStreamPart.code,\n  [errorStreamPart.name]: errorStreamPart.code,\n  [assistantMessageStreamPart.name]: assistantMessageStreamPart.code,\n  [assistantControlDataStreamPart.name]: assistantControlDataStreamPart.code,\n  [dataMessageStreamPart.name]: dataMessageStreamPart.code\n};\nvar validCodes = assistantStreamParts.map((part) => part.code);\nvar parseAssistantStreamPart = (line) => {\n  const firstSeparatorIndex = line.indexOf(\":\");\n  if (firstSeparatorIndex === -1) {\n    throw new Error(\"Failed to parse stream string. No separator found.\");\n  }\n  const prefix = line.slice(0, firstSeparatorIndex);\n  if (!validCodes.includes(prefix)) {\n    throw new Error(`Failed to parse stream string. Invalid code ${prefix}.`);\n  }\n  const code = prefix;\n  const textValue = line.slice(firstSeparatorIndex + 1);\n  const jsonValue = JSON.parse(textValue);\n  return assistantStreamPartsByCode[code].parse(jsonValue);\n};\nfunction formatAssistantStreamPart(type, value) {\n  const streamPart = assistantStreamParts.find((part) => part.name === type);\n  if (!streamPart) {\n    throw new Error(`Invalid stream part type: ${type}`);\n  }\n  return `${streamPart.code}:${JSON.stringify(value)}\n`;\n}\n\n// src/process-chat-response.ts\n\n\n// src/duplicated/usage.ts\nfunction calculateLanguageModelUsage({\n  promptTokens,\n  completionTokens\n}) {\n  return {\n    promptTokens,\n    completionTokens,\n    totalTokens: promptTokens + completionTokens\n  };\n}\n\n// src/parse-partial-json.ts\n\n\n// src/fix-json.ts\nfunction fixJson(input) {\n  const stack = [\"ROOT\"];\n  let lastValidIndex = -1;\n  let literalStart = null;\n  function processValueStart(char, i, swapState) {\n    {\n      switch (char) {\n        case '\"': {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_STRING\");\n          break;\n        }\n        case \"f\":\n        case \"t\":\n        case \"n\": {\n          lastValidIndex = i;\n          literalStart = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_LITERAL\");\n          break;\n        }\n        case \"-\": {\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_NUMBER\");\n          break;\n        }\n        case \"0\":\n        case \"1\":\n        case \"2\":\n        case \"3\":\n        case \"4\":\n        case \"5\":\n        case \"6\":\n        case \"7\":\n        case \"8\":\n        case \"9\": {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_NUMBER\");\n          break;\n        }\n        case \"{\": {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_OBJECT_START\");\n          break;\n        }\n        case \"[\": {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_ARRAY_START\");\n          break;\n        }\n      }\n    }\n  }\n  function processAfterObjectValue(char, i) {\n    switch (char) {\n      case \",\": {\n        stack.pop();\n        stack.push(\"INSIDE_OBJECT_AFTER_COMMA\");\n        break;\n      }\n      case \"}\": {\n        lastValidIndex = i;\n        stack.pop();\n        break;\n      }\n    }\n  }\n  function processAfterArrayValue(char, i) {\n    switch (char) {\n      case \",\": {\n        stack.pop();\n        stack.push(\"INSIDE_ARRAY_AFTER_COMMA\");\n        break;\n      }\n      case \"]\": {\n        lastValidIndex = i;\n        stack.pop();\n        break;\n      }\n    }\n  }\n  for (let i = 0; i < input.length; i++) {\n    const char = input[i];\n    const currentState = stack[stack.length - 1];\n    switch (currentState) {\n      case \"ROOT\":\n        processValueStart(char, i, \"FINISH\");\n        break;\n      case \"INSIDE_OBJECT_START\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_KEY\");\n            break;\n          }\n          case \"}\": {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_AFTER_COMMA\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_KEY\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_KEY\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_AFTER_KEY\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_AFTER_KEY\": {\n        switch (char) {\n          case \":\": {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_BEFORE_VALUE\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_BEFORE_VALUE\": {\n        processValueStart(char, i, \"INSIDE_OBJECT_AFTER_VALUE\");\n        break;\n      }\n      case \"INSIDE_OBJECT_AFTER_VALUE\": {\n        processAfterObjectValue(char, i);\n        break;\n      }\n      case \"INSIDE_STRING\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            lastValidIndex = i;\n            break;\n          }\n          case \"\\\\\": {\n            stack.push(\"INSIDE_STRING_ESCAPE\");\n            break;\n          }\n          default: {\n            lastValidIndex = i;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_ARRAY_START\": {\n        switch (char) {\n          case \"]\": {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n          default: {\n            lastValidIndex = i;\n            processValueStart(char, i, \"INSIDE_ARRAY_AFTER_VALUE\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_ARRAY_AFTER_VALUE\": {\n        switch (char) {\n          case \",\": {\n            stack.pop();\n            stack.push(\"INSIDE_ARRAY_AFTER_COMMA\");\n            break;\n          }\n          case \"]\": {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n          default: {\n            lastValidIndex = i;\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_ARRAY_AFTER_COMMA\": {\n        processValueStart(char, i, \"INSIDE_ARRAY_AFTER_VALUE\");\n        break;\n      }\n      case \"INSIDE_STRING_ESCAPE\": {\n        stack.pop();\n        lastValidIndex = i;\n        break;\n      }\n      case \"INSIDE_NUMBER\": {\n        switch (char) {\n          case \"0\":\n          case \"1\":\n          case \"2\":\n          case \"3\":\n          case \"4\":\n          case \"5\":\n          case \"6\":\n          case \"7\":\n          case \"8\":\n          case \"9\": {\n            lastValidIndex = i;\n            break;\n          }\n          case \"e\":\n          case \"E\":\n          case \"-\":\n          case \".\": {\n            break;\n          }\n          case \",\": {\n            stack.pop();\n            if (stack[stack.length - 1] === \"INSIDE_ARRAY_AFTER_VALUE\") {\n              processAfterArrayValue(char, i);\n            }\n            if (stack[stack.length - 1] === \"INSIDE_OBJECT_AFTER_VALUE\") {\n              processAfterObjectValue(char, i);\n            }\n            break;\n          }\n          case \"}\": {\n            stack.pop();\n            if (stack[stack.length - 1] === \"INSIDE_OBJECT_AFTER_VALUE\") {\n              processAfterObjectValue(char, i);\n            }\n            break;\n          }\n          case \"]\": {\n            stack.pop();\n            if (stack[stack.length - 1] === \"INSIDE_ARRAY_AFTER_VALUE\") {\n              processAfterArrayValue(char, i);\n            }\n            break;\n          }\n          default: {\n            stack.pop();\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_LITERAL\": {\n        const partialLiteral = input.substring(literalStart, i + 1);\n        if (!\"false\".startsWith(partialLiteral) && !\"true\".startsWith(partialLiteral) && !\"null\".startsWith(partialLiteral)) {\n          stack.pop();\n          if (stack[stack.length - 1] === \"INSIDE_OBJECT_AFTER_VALUE\") {\n            processAfterObjectValue(char, i);\n          } else if (stack[stack.length - 1] === \"INSIDE_ARRAY_AFTER_VALUE\") {\n            processAfterArrayValue(char, i);\n          }\n        } else {\n          lastValidIndex = i;\n        }\n        break;\n      }\n    }\n  }\n  let result = input.slice(0, lastValidIndex + 1);\n  for (let i = stack.length - 1; i >= 0; i--) {\n    const state = stack[i];\n    switch (state) {\n      case \"INSIDE_STRING\": {\n        result += '\"';\n        break;\n      }\n      case \"INSIDE_OBJECT_KEY\":\n      case \"INSIDE_OBJECT_AFTER_KEY\":\n      case \"INSIDE_OBJECT_AFTER_COMMA\":\n      case \"INSIDE_OBJECT_START\":\n      case \"INSIDE_OBJECT_BEFORE_VALUE\":\n      case \"INSIDE_OBJECT_AFTER_VALUE\": {\n        result += \"}\";\n        break;\n      }\n      case \"INSIDE_ARRAY_START\":\n      case \"INSIDE_ARRAY_AFTER_COMMA\":\n      case \"INSIDE_ARRAY_AFTER_VALUE\": {\n        result += \"]\";\n        break;\n      }\n      case \"INSIDE_LITERAL\": {\n        const partialLiteral = input.substring(literalStart, input.length);\n        if (\"true\".startsWith(partialLiteral)) {\n          result += \"true\".slice(partialLiteral.length);\n        } else if (\"false\".startsWith(partialLiteral)) {\n          result += \"false\".slice(partialLiteral.length);\n        } else if (\"null\".startsWith(partialLiteral)) {\n          result += \"null\".slice(partialLiteral.length);\n        }\n      }\n    }\n  }\n  return result;\n}\n\n// src/parse-partial-json.ts\nfunction parsePartialJson(jsonText) {\n  if (jsonText === void 0) {\n    return { value: void 0, state: \"undefined-input\" };\n  }\n  let result = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.safeParseJSON)({ text: jsonText });\n  if (result.success) {\n    return { value: result.value, state: \"successful-parse\" };\n  }\n  result = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.safeParseJSON)({ text: fixJson(jsonText) });\n  if (result.success) {\n    return { value: result.value, state: \"repaired-parse\" };\n  }\n  return { value: void 0, state: \"failed-parse\" };\n}\n\n// src/data-stream-parts.ts\nvar textStreamPart2 = {\n  code: \"0\",\n  name: \"text\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"text\" parts expect a string value.');\n    }\n    return { type: \"text\", value };\n  }\n};\nvar dataStreamPart = {\n  code: \"2\",\n  name: \"data\",\n  parse: (value) => {\n    if (!Array.isArray(value)) {\n      throw new Error('\"data\" parts expect an array value.');\n    }\n    return { type: \"data\", value };\n  }\n};\nvar errorStreamPart2 = {\n  code: \"3\",\n  name: \"error\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"error\" parts expect a string value.');\n    }\n    return { type: \"error\", value };\n  }\n};\nvar messageAnnotationsStreamPart = {\n  code: \"8\",\n  name: \"message_annotations\",\n  parse: (value) => {\n    if (!Array.isArray(value)) {\n      throw new Error('\"message_annotations\" parts expect an array value.');\n    }\n    return { type: \"message_annotations\", value };\n  }\n};\nvar toolCallStreamPart = {\n  code: \"9\",\n  name: \"tool_call\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"toolName\" in value) || typeof value.toolName !== \"string\" || !(\"args\" in value) || typeof value.args !== \"object\") {\n      throw new Error(\n        '\"tool_call\" parts expect an object with a \"toolCallId\", \"toolName\", and \"args\" property.'\n      );\n    }\n    return {\n      type: \"tool_call\",\n      value\n    };\n  }\n};\nvar toolResultStreamPart = {\n  code: \"a\",\n  name: \"tool_result\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"result\" in value)) {\n      throw new Error(\n        '\"tool_result\" parts expect an object with a \"toolCallId\" and a \"result\" property.'\n      );\n    }\n    return {\n      type: \"tool_result\",\n      value\n    };\n  }\n};\nvar toolCallStreamingStartStreamPart = {\n  code: \"b\",\n  name: \"tool_call_streaming_start\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"toolName\" in value) || typeof value.toolName !== \"string\") {\n      throw new Error(\n        '\"tool_call_streaming_start\" parts expect an object with a \"toolCallId\" and \"toolName\" property.'\n      );\n    }\n    return {\n      type: \"tool_call_streaming_start\",\n      value\n    };\n  }\n};\nvar toolCallDeltaStreamPart = {\n  code: \"c\",\n  name: \"tool_call_delta\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"argsTextDelta\" in value) || typeof value.argsTextDelta !== \"string\") {\n      throw new Error(\n        '\"tool_call_delta\" parts expect an object with a \"toolCallId\" and \"argsTextDelta\" property.'\n      );\n    }\n    return {\n      type: \"tool_call_delta\",\n      value\n    };\n  }\n};\nvar finishMessageStreamPart = {\n  code: \"d\",\n  name: \"finish_message\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"finishReason\" in value) || typeof value.finishReason !== \"string\") {\n      throw new Error(\n        '\"finish_message\" parts expect an object with a \"finishReason\" property.'\n      );\n    }\n    const result = {\n      finishReason: value.finishReason\n    };\n    if (\"usage\" in value && value.usage != null && typeof value.usage === \"object\" && \"promptTokens\" in value.usage && \"completionTokens\" in value.usage) {\n      result.usage = {\n        promptTokens: typeof value.usage.promptTokens === \"number\" ? value.usage.promptTokens : Number.NaN,\n        completionTokens: typeof value.usage.completionTokens === \"number\" ? value.usage.completionTokens : Number.NaN\n      };\n    }\n    return {\n      type: \"finish_message\",\n      value: result\n    };\n  }\n};\nvar finishStepStreamPart = {\n  code: \"e\",\n  name: \"finish_step\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"finishReason\" in value) || typeof value.finishReason !== \"string\") {\n      throw new Error(\n        '\"finish_step\" parts expect an object with a \"finishReason\" property.'\n      );\n    }\n    const result = {\n      finishReason: value.finishReason,\n      isContinued: false\n    };\n    if (\"usage\" in value && value.usage != null && typeof value.usage === \"object\" && \"promptTokens\" in value.usage && \"completionTokens\" in value.usage) {\n      result.usage = {\n        promptTokens: typeof value.usage.promptTokens === \"number\" ? value.usage.promptTokens : Number.NaN,\n        completionTokens: typeof value.usage.completionTokens === \"number\" ? value.usage.completionTokens : Number.NaN\n      };\n    }\n    if (\"isContinued\" in value && typeof value.isContinued === \"boolean\") {\n      result.isContinued = value.isContinued;\n    }\n    return {\n      type: \"finish_step\",\n      value: result\n    };\n  }\n};\nvar startStepStreamPart = {\n  code: \"f\",\n  name: \"start_step\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"messageId\" in value) || typeof value.messageId !== \"string\") {\n      throw new Error(\n        '\"start_step\" parts expect an object with an \"id\" property.'\n      );\n    }\n    return {\n      type: \"start_step\",\n      value: {\n        messageId: value.messageId\n      }\n    };\n  }\n};\nvar reasoningStreamPart = {\n  code: \"g\",\n  name: \"reasoning\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"reasoning\" parts expect a string value.');\n    }\n    return { type: \"reasoning\", value };\n  }\n};\nvar sourcePart = {\n  code: \"h\",\n  name: \"source\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\") {\n      throw new Error('\"source\" parts expect a Source object.');\n    }\n    return {\n      type: \"source\",\n      value\n    };\n  }\n};\nvar redactedReasoningStreamPart = {\n  code: \"i\",\n  name: \"redacted_reasoning\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"data\" in value) || typeof value.data !== \"string\") {\n      throw new Error(\n        '\"redacted_reasoning\" parts expect an object with a \"data\" property.'\n      );\n    }\n    return { type: \"redacted_reasoning\", value: { data: value.data } };\n  }\n};\nvar reasoningSignatureStreamPart = {\n  code: \"j\",\n  name: \"reasoning_signature\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"signature\" in value) || typeof value.signature !== \"string\") {\n      throw new Error(\n        '\"reasoning_signature\" parts expect an object with a \"signature\" property.'\n      );\n    }\n    return {\n      type: \"reasoning_signature\",\n      value: { signature: value.signature }\n    };\n  }\n};\nvar fileStreamPart = {\n  code: \"k\",\n  name: \"file\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"data\" in value) || typeof value.data !== \"string\" || !(\"mimeType\" in value) || typeof value.mimeType !== \"string\") {\n      throw new Error(\n        '\"file\" parts expect an object with a \"data\" and \"mimeType\" property.'\n      );\n    }\n    return { type: \"file\", value };\n  }\n};\nvar dataStreamParts = [\n  textStreamPart2,\n  dataStreamPart,\n  errorStreamPart2,\n  messageAnnotationsStreamPart,\n  toolCallStreamPart,\n  toolResultStreamPart,\n  toolCallStreamingStartStreamPart,\n  toolCallDeltaStreamPart,\n  finishMessageStreamPart,\n  finishStepStreamPart,\n  startStepStreamPart,\n  reasoningStreamPart,\n  sourcePart,\n  redactedReasoningStreamPart,\n  reasoningSignatureStreamPart,\n  fileStreamPart\n];\nvar dataStreamPartsByCode = Object.fromEntries(\n  dataStreamParts.map((part) => [part.code, part])\n);\nvar DataStreamStringPrefixes = Object.fromEntries(\n  dataStreamParts.map((part) => [part.name, part.code])\n);\nvar validCodes2 = dataStreamParts.map((part) => part.code);\nvar parseDataStreamPart = (line) => {\n  const firstSeparatorIndex = line.indexOf(\":\");\n  if (firstSeparatorIndex === -1) {\n    throw new Error(\"Failed to parse stream string. No separator found.\");\n  }\n  const prefix = line.slice(0, firstSeparatorIndex);\n  if (!validCodes2.includes(prefix)) {\n    throw new Error(`Failed to parse stream string. Invalid code ${prefix}.`);\n  }\n  const code = prefix;\n  const textValue = line.slice(firstSeparatorIndex + 1);\n  const jsonValue = JSON.parse(textValue);\n  return dataStreamPartsByCode[code].parse(jsonValue);\n};\nfunction formatDataStreamPart(type, value) {\n  const streamPart = dataStreamParts.find((part) => part.name === type);\n  if (!streamPart) {\n    throw new Error(`Invalid stream part type: ${type}`);\n  }\n  return `${streamPart.code}:${JSON.stringify(value)}\n`;\n}\n\n// src/process-data-stream.ts\nvar NEWLINE = \"\\n\".charCodeAt(0);\nfunction concatChunks(chunks, totalLength) {\n  const concatenatedChunks = new Uint8Array(totalLength);\n  let offset = 0;\n  for (const chunk of chunks) {\n    concatenatedChunks.set(chunk, offset);\n    offset += chunk.length;\n  }\n  chunks.length = 0;\n  return concatenatedChunks;\n}\nasync function processDataStream({\n  stream,\n  onTextPart,\n  onReasoningPart,\n  onReasoningSignaturePart,\n  onRedactedReasoningPart,\n  onSourcePart,\n  onFilePart,\n  onDataPart,\n  onErrorPart,\n  onToolCallStreamingStartPart,\n  onToolCallDeltaPart,\n  onToolCallPart,\n  onToolResultPart,\n  onMessageAnnotationsPart,\n  onFinishMessagePart,\n  onFinishStepPart,\n  onStartStepPart\n}) {\n  const reader = stream.getReader();\n  const decoder = new TextDecoder();\n  const chunks = [];\n  let totalLength = 0;\n  while (true) {\n    const { value } = await reader.read();\n    if (value) {\n      chunks.push(value);\n      totalLength += value.length;\n      if (value[value.length - 1] !== NEWLINE) {\n        continue;\n      }\n    }\n    if (chunks.length === 0) {\n      break;\n    }\n    const concatenatedChunks = concatChunks(chunks, totalLength);\n    totalLength = 0;\n    const streamParts = decoder.decode(concatenatedChunks, { stream: true }).split(\"\\n\").filter((line) => line !== \"\").map(parseDataStreamPart);\n    for (const { type, value: value2 } of streamParts) {\n      switch (type) {\n        case \"text\":\n          await (onTextPart == null ? void 0 : onTextPart(value2));\n          break;\n        case \"reasoning\":\n          await (onReasoningPart == null ? void 0 : onReasoningPart(value2));\n          break;\n        case \"reasoning_signature\":\n          await (onReasoningSignaturePart == null ? void 0 : onReasoningSignaturePart(value2));\n          break;\n        case \"redacted_reasoning\":\n          await (onRedactedReasoningPart == null ? void 0 : onRedactedReasoningPart(value2));\n          break;\n        case \"file\":\n          await (onFilePart == null ? void 0 : onFilePart(value2));\n          break;\n        case \"source\":\n          await (onSourcePart == null ? void 0 : onSourcePart(value2));\n          break;\n        case \"data\":\n          await (onDataPart == null ? void 0 : onDataPart(value2));\n          break;\n        case \"error\":\n          await (onErrorPart == null ? void 0 : onErrorPart(value2));\n          break;\n        case \"message_annotations\":\n          await (onMessageAnnotationsPart == null ? void 0 : onMessageAnnotationsPart(value2));\n          break;\n        case \"tool_call_streaming_start\":\n          await (onToolCallStreamingStartPart == null ? void 0 : onToolCallStreamingStartPart(value2));\n          break;\n        case \"tool_call_delta\":\n          await (onToolCallDeltaPart == null ? void 0 : onToolCallDeltaPart(value2));\n          break;\n        case \"tool_call\":\n          await (onToolCallPart == null ? void 0 : onToolCallPart(value2));\n          break;\n        case \"tool_result\":\n          await (onToolResultPart == null ? void 0 : onToolResultPart(value2));\n          break;\n        case \"finish_message\":\n          await (onFinishMessagePart == null ? void 0 : onFinishMessagePart(value2));\n          break;\n        case \"finish_step\":\n          await (onFinishStepPart == null ? void 0 : onFinishStepPart(value2));\n          break;\n        case \"start_step\":\n          await (onStartStepPart == null ? void 0 : onStartStepPart(value2));\n          break;\n        default: {\n          const exhaustiveCheck = type;\n          throw new Error(`Unknown stream part type: ${exhaustiveCheck}`);\n        }\n      }\n    }\n  }\n}\n\n// src/process-chat-response.ts\nasync function processChatResponse({\n  stream,\n  update,\n  onToolCall,\n  onFinish,\n  generateId: generateId2 = _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId,\n  getCurrentDate = () => /* @__PURE__ */ new Date(),\n  lastMessage\n}) {\n  var _a, _b;\n  const replaceLastMessage = (lastMessage == null ? void 0 : lastMessage.role) === \"assistant\";\n  let step = replaceLastMessage ? 1 + // find max step in existing tool invocations:\n  ((_b = (_a = lastMessage.toolInvocations) == null ? void 0 : _a.reduce((max, toolInvocation) => {\n    var _a2;\n    return Math.max(max, (_a2 = toolInvocation.step) != null ? _a2 : 0);\n  }, 0)) != null ? _b : 0) : 0;\n  const message = replaceLastMessage ? structuredClone(lastMessage) : {\n    id: generateId2(),\n    createdAt: getCurrentDate(),\n    role: \"assistant\",\n    content: \"\",\n    parts: []\n  };\n  let currentTextPart = void 0;\n  let currentReasoningPart = void 0;\n  let currentReasoningTextDetail = void 0;\n  function updateToolInvocationPart(toolCallId, invocation) {\n    const part = message.parts.find(\n      (part2) => part2.type === \"tool-invocation\" && part2.toolInvocation.toolCallId === toolCallId\n    );\n    if (part != null) {\n      part.toolInvocation = invocation;\n    } else {\n      message.parts.push({\n        type: \"tool-invocation\",\n        toolInvocation: invocation\n      });\n    }\n  }\n  const data = [];\n  let messageAnnotations = replaceLastMessage ? lastMessage == null ? void 0 : lastMessage.annotations : void 0;\n  const partialToolCalls = {};\n  let usage = {\n    completionTokens: NaN,\n    promptTokens: NaN,\n    totalTokens: NaN\n  };\n  let finishReason = \"unknown\";\n  function execUpdate() {\n    const copiedData = [...data];\n    if (messageAnnotations == null ? void 0 : messageAnnotations.length) {\n      message.annotations = messageAnnotations;\n    }\n    const copiedMessage = {\n      // deep copy the message to ensure that deep changes (msg attachments) are updated\n      // with SolidJS. SolidJS uses referential integration of sub-objects to detect changes.\n      ...structuredClone(message),\n      // add a revision id to ensure that the message is updated with SWR. SWR uses a\n      // hashing approach by default to detect changes, but it only works for shallow\n      // changes. This is why we need to add a revision id to ensure that the message\n      // is updated with SWR (without it, the changes get stuck in SWR and are not\n      // forwarded to rendering):\n      revisionId: generateId2()\n    };\n    update({\n      message: copiedMessage,\n      data: copiedData,\n      replaceLastMessage\n    });\n  }\n  await processDataStream({\n    stream,\n    onTextPart(value) {\n      if (currentTextPart == null) {\n        currentTextPart = {\n          type: \"text\",\n          text: value\n        };\n        message.parts.push(currentTextPart);\n      } else {\n        currentTextPart.text += value;\n      }\n      message.content += value;\n      execUpdate();\n    },\n    onReasoningPart(value) {\n      var _a2;\n      if (currentReasoningTextDetail == null) {\n        currentReasoningTextDetail = { type: \"text\", text: value };\n        if (currentReasoningPart != null) {\n          currentReasoningPart.details.push(currentReasoningTextDetail);\n        }\n      } else {\n        currentReasoningTextDetail.text += value;\n      }\n      if (currentReasoningPart == null) {\n        currentReasoningPart = {\n          type: \"reasoning\",\n          reasoning: value,\n          details: [currentReasoningTextDetail]\n        };\n        message.parts.push(currentReasoningPart);\n      } else {\n        currentReasoningPart.reasoning += value;\n      }\n      message.reasoning = ((_a2 = message.reasoning) != null ? _a2 : \"\") + value;\n      execUpdate();\n    },\n    onReasoningSignaturePart(value) {\n      if (currentReasoningTextDetail != null) {\n        currentReasoningTextDetail.signature = value.signature;\n      }\n    },\n    onRedactedReasoningPart(value) {\n      if (currentReasoningPart == null) {\n        currentReasoningPart = {\n          type: \"reasoning\",\n          reasoning: \"\",\n          details: []\n        };\n        message.parts.push(currentReasoningPart);\n      }\n      currentReasoningPart.details.push({\n        type: \"redacted\",\n        data: value.data\n      });\n      currentReasoningTextDetail = void 0;\n      execUpdate();\n    },\n    onFilePart(value) {\n      message.parts.push({\n        type: \"file\",\n        mimeType: value.mimeType,\n        data: value.data\n      });\n      execUpdate();\n    },\n    onSourcePart(value) {\n      message.parts.push({\n        type: \"source\",\n        source: value\n      });\n      execUpdate();\n    },\n    onToolCallStreamingStartPart(value) {\n      if (message.toolInvocations == null) {\n        message.toolInvocations = [];\n      }\n      partialToolCalls[value.toolCallId] = {\n        text: \"\",\n        step,\n        toolName: value.toolName,\n        index: message.toolInvocations.length\n      };\n      const invocation = {\n        state: \"partial-call\",\n        step,\n        toolCallId: value.toolCallId,\n        toolName: value.toolName,\n        args: void 0\n      };\n      message.toolInvocations.push(invocation);\n      updateToolInvocationPart(value.toolCallId, invocation);\n      execUpdate();\n    },\n    onToolCallDeltaPart(value) {\n      const partialToolCall = partialToolCalls[value.toolCallId];\n      partialToolCall.text += value.argsTextDelta;\n      const { value: partialArgs } = parsePartialJson(partialToolCall.text);\n      const invocation = {\n        state: \"partial-call\",\n        step: partialToolCall.step,\n        toolCallId: value.toolCallId,\n        toolName: partialToolCall.toolName,\n        args: partialArgs\n      };\n      message.toolInvocations[partialToolCall.index] = invocation;\n      updateToolInvocationPart(value.toolCallId, invocation);\n      execUpdate();\n    },\n    async onToolCallPart(value) {\n      const invocation = {\n        state: \"call\",\n        step,\n        ...value\n      };\n      if (partialToolCalls[value.toolCallId] != null) {\n        message.toolInvocations[partialToolCalls[value.toolCallId].index] = invocation;\n      } else {\n        if (message.toolInvocations == null) {\n          message.toolInvocations = [];\n        }\n        message.toolInvocations.push(invocation);\n      }\n      updateToolInvocationPart(value.toolCallId, invocation);\n      execUpdate();\n      if (onToolCall) {\n        const result = await onToolCall({ toolCall: value });\n        if (result != null) {\n          const invocation2 = {\n            state: \"result\",\n            step,\n            ...value,\n            result\n          };\n          message.toolInvocations[message.toolInvocations.length - 1] = invocation2;\n          updateToolInvocationPart(value.toolCallId, invocation2);\n          execUpdate();\n        }\n      }\n    },\n    onToolResultPart(value) {\n      const toolInvocations = message.toolInvocations;\n      if (toolInvocations == null) {\n        throw new Error(\"tool_result must be preceded by a tool_call\");\n      }\n      const toolInvocationIndex = toolInvocations.findIndex(\n        (invocation2) => invocation2.toolCallId === value.toolCallId\n      );\n      if (toolInvocationIndex === -1) {\n        throw new Error(\n          \"tool_result must be preceded by a tool_call with the same toolCallId\"\n        );\n      }\n      const invocation = {\n        ...toolInvocations[toolInvocationIndex],\n        state: \"result\",\n        ...value\n      };\n      toolInvocations[toolInvocationIndex] = invocation;\n      updateToolInvocationPart(value.toolCallId, invocation);\n      execUpdate();\n    },\n    onDataPart(value) {\n      data.push(...value);\n      execUpdate();\n    },\n    onMessageAnnotationsPart(value) {\n      if (messageAnnotations == null) {\n        messageAnnotations = [...value];\n      } else {\n        messageAnnotations.push(...value);\n      }\n      execUpdate();\n    },\n    onFinishStepPart(value) {\n      step += 1;\n      currentTextPart = value.isContinued ? currentTextPart : void 0;\n      currentReasoningPart = void 0;\n      currentReasoningTextDetail = void 0;\n    },\n    onStartStepPart(value) {\n      if (!replaceLastMessage) {\n        message.id = value.messageId;\n      }\n      message.parts.push({ type: \"step-start\" });\n      execUpdate();\n    },\n    onFinishMessagePart(value) {\n      finishReason = value.finishReason;\n      if (value.usage != null) {\n        usage = calculateLanguageModelUsage(value.usage);\n      }\n    },\n    onErrorPart(error) {\n      throw new Error(error);\n    }\n  });\n  onFinish == null ? void 0 : onFinish({ message, finishReason, usage });\n}\n\n// src/process-chat-text-response.ts\n\n\n// src/process-text-stream.ts\nasync function processTextStream({\n  stream,\n  onTextPart\n}) {\n  const reader = stream.pipeThrough(new TextDecoderStream()).getReader();\n  while (true) {\n    const { done, value } = await reader.read();\n    if (done) {\n      break;\n    }\n    await onTextPart(value);\n  }\n}\n\n// src/process-chat-text-response.ts\nasync function processChatTextResponse({\n  stream,\n  update,\n  onFinish,\n  getCurrentDate = () => /* @__PURE__ */ new Date(),\n  generateId: generateId2 = _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId\n}) {\n  const textPart = { type: \"text\", text: \"\" };\n  const resultMessage = {\n    id: generateId2(),\n    createdAt: getCurrentDate(),\n    role: \"assistant\",\n    content: \"\",\n    parts: [textPart]\n  };\n  await processTextStream({\n    stream,\n    onTextPart: (chunk) => {\n      resultMessage.content += chunk;\n      textPart.text += chunk;\n      update({\n        message: { ...resultMessage },\n        data: [],\n        replaceLastMessage: false\n      });\n    }\n  });\n  onFinish == null ? void 0 : onFinish(resultMessage, {\n    usage: { completionTokens: NaN, promptTokens: NaN, totalTokens: NaN },\n    finishReason: \"unknown\"\n  });\n}\n\n// src/call-chat-api.ts\nvar getOriginalFetch = () => fetch;\nasync function callChatApi({\n  api,\n  body,\n  streamProtocol = \"data\",\n  credentials,\n  headers,\n  abortController,\n  restoreMessagesOnFailure,\n  onResponse,\n  onUpdate,\n  onFinish,\n  onToolCall,\n  generateId: generateId2,\n  fetch: fetch2 = getOriginalFetch(),\n  lastMessage,\n  requestType = \"generate\"\n}) {\n  var _a, _b, _c;\n  const request = requestType === \"resume\" ? fetch2(`${api}?chatId=${body.id}`, {\n    method: \"GET\",\n    headers: {\n      \"Content-Type\": \"application/json\",\n      ...headers\n    },\n    signal: (_a = abortController == null ? void 0 : abortController()) == null ? void 0 : _a.signal,\n    credentials\n  }) : fetch2(api, {\n    method: \"POST\",\n    body: JSON.stringify(body),\n    headers: {\n      \"Content-Type\": \"application/json\",\n      ...headers\n    },\n    signal: (_b = abortController == null ? void 0 : abortController()) == null ? void 0 : _b.signal,\n    credentials\n  });\n  const response = await request.catch((err) => {\n    restoreMessagesOnFailure();\n    throw err;\n  });\n  if (onResponse) {\n    try {\n      await onResponse(response);\n    } catch (err) {\n      throw err;\n    }\n  }\n  if (!response.ok) {\n    restoreMessagesOnFailure();\n    throw new Error(\n      (_c = await response.text()) != null ? _c : \"Failed to fetch the chat response.\"\n    );\n  }\n  if (!response.body) {\n    throw new Error(\"The response body is empty.\");\n  }\n  switch (streamProtocol) {\n    case \"text\": {\n      await processChatTextResponse({\n        stream: response.body,\n        update: onUpdate,\n        onFinish,\n        generateId: generateId2\n      });\n      return;\n    }\n    case \"data\": {\n      await processChatResponse({\n        stream: response.body,\n        update: onUpdate,\n        lastMessage,\n        onToolCall,\n        onFinish({ message, finishReason, usage }) {\n          if (onFinish && message != null) {\n            onFinish(message, { usage, finishReason });\n          }\n        },\n        generateId: generateId2\n      });\n      return;\n    }\n    default: {\n      const exhaustiveCheck = streamProtocol;\n      throw new Error(`Unknown stream protocol: ${exhaustiveCheck}`);\n    }\n  }\n}\n\n// src/call-completion-api.ts\nvar getOriginalFetch2 = () => fetch;\nasync function callCompletionApi({\n  api,\n  prompt,\n  credentials,\n  headers,\n  body,\n  streamProtocol = \"data\",\n  setCompletion,\n  setLoading,\n  setError,\n  setAbortController,\n  onResponse,\n  onFinish,\n  onError,\n  onData,\n  fetch: fetch2 = getOriginalFetch2()\n}) {\n  var _a;\n  try {\n    setLoading(true);\n    setError(void 0);\n    const abortController = new AbortController();\n    setAbortController(abortController);\n    setCompletion(\"\");\n    const response = await fetch2(api, {\n      method: \"POST\",\n      body: JSON.stringify({\n        prompt,\n        ...body\n      }),\n      credentials,\n      headers: {\n        \"Content-Type\": \"application/json\",\n        ...headers\n      },\n      signal: abortController.signal\n    }).catch((err) => {\n      throw err;\n    });\n    if (onResponse) {\n      try {\n        await onResponse(response);\n      } catch (err) {\n        throw err;\n      }\n    }\n    if (!response.ok) {\n      throw new Error(\n        (_a = await response.text()) != null ? _a : \"Failed to fetch the chat response.\"\n      );\n    }\n    if (!response.body) {\n      throw new Error(\"The response body is empty.\");\n    }\n    let result = \"\";\n    switch (streamProtocol) {\n      case \"text\": {\n        await processTextStream({\n          stream: response.body,\n          onTextPart: (chunk) => {\n            result += chunk;\n            setCompletion(result);\n          }\n        });\n        break;\n      }\n      case \"data\": {\n        await processDataStream({\n          stream: response.body,\n          onTextPart(value) {\n            result += value;\n            setCompletion(result);\n          },\n          onDataPart(value) {\n            onData == null ? void 0 : onData(value);\n          },\n          onErrorPart(value) {\n            throw new Error(value);\n          }\n        });\n        break;\n      }\n      default: {\n        const exhaustiveCheck = streamProtocol;\n        throw new Error(`Unknown stream protocol: ${exhaustiveCheck}`);\n      }\n    }\n    if (onFinish) {\n      onFinish(prompt, result);\n    }\n    setAbortController(null);\n    return result;\n  } catch (err) {\n    if (err.name === \"AbortError\") {\n      setAbortController(null);\n      return null;\n    }\n    if (err instanceof Error) {\n      if (onError) {\n        onError(err);\n      }\n    }\n    setError(err);\n  } finally {\n    setLoading(false);\n  }\n}\n\n// src/data-url.ts\nfunction getTextFromDataUrl(dataUrl) {\n  const [header, base64Content] = dataUrl.split(\",\");\n  const mimeType = header.split(\";\")[0].split(\":\")[1];\n  if (mimeType == null || base64Content == null) {\n    throw new Error(\"Invalid data URL format\");\n  }\n  try {\n    return window.atob(base64Content);\n  } catch (error) {\n    throw new Error(`Error decoding data URL`);\n  }\n}\n\n// src/extract-max-tool-invocation-step.ts\nfunction extractMaxToolInvocationStep(toolInvocations) {\n  return toolInvocations == null ? void 0 : toolInvocations.reduce((max, toolInvocation) => {\n    var _a;\n    return Math.max(max, (_a = toolInvocation.step) != null ? _a : 0);\n  }, 0);\n}\n\n// src/get-message-parts.ts\nfunction getMessageParts(message) {\n  var _a;\n  return (_a = message.parts) != null ? _a : [\n    ...message.toolInvocations ? message.toolInvocations.map((toolInvocation) => ({\n      type: \"tool-invocation\",\n      toolInvocation\n    })) : [],\n    ...message.reasoning ? [\n      {\n        type: \"reasoning\",\n        reasoning: message.reasoning,\n        details: [{ type: \"text\", text: message.reasoning }]\n      }\n    ] : [],\n    ...message.content ? [{ type: \"text\", text: message.content }] : []\n  ];\n}\n\n// src/fill-message-parts.ts\nfunction fillMessageParts(messages) {\n  return messages.map((message) => ({\n    ...message,\n    parts: getMessageParts(message)\n  }));\n}\n\n// src/is-deep-equal-data.ts\nfunction isDeepEqualData(obj1, obj2) {\n  if (obj1 === obj2)\n    return true;\n  if (obj1 == null || obj2 == null)\n    return false;\n  if (typeof obj1 !== \"object\" && typeof obj2 !== \"object\")\n    return obj1 === obj2;\n  if (obj1.constructor !== obj2.constructor)\n    return false;\n  if (obj1 instanceof Date && obj2 instanceof Date) {\n    return obj1.getTime() === obj2.getTime();\n  }\n  if (Array.isArray(obj1)) {\n    if (obj1.length !== obj2.length)\n      return false;\n    for (let i = 0; i < obj1.length; i++) {\n      if (!isDeepEqualData(obj1[i], obj2[i]))\n        return false;\n    }\n    return true;\n  }\n  const keys1 = Object.keys(obj1);\n  const keys2 = Object.keys(obj2);\n  if (keys1.length !== keys2.length)\n    return false;\n  for (const key of keys1) {\n    if (!keys2.includes(key))\n      return false;\n    if (!isDeepEqualData(obj1[key], obj2[key]))\n      return false;\n  }\n  return true;\n}\n\n// src/prepare-attachments-for-request.ts\nasync function prepareAttachmentsForRequest(attachmentsFromOptions) {\n  if (!attachmentsFromOptions) {\n    return [];\n  }\n  if (globalThis.FileList && attachmentsFromOptions instanceof globalThis.FileList) {\n    return Promise.all(\n      Array.from(attachmentsFromOptions).map(async (attachment) => {\n        const { name, type } = attachment;\n        const dataUrl = await new Promise((resolve, reject) => {\n          const reader = new FileReader();\n          reader.onload = (readerEvent) => {\n            var _a;\n            resolve((_a = readerEvent.target) == null ? void 0 : _a.result);\n          };\n          reader.onerror = (error) => reject(error);\n          reader.readAsDataURL(attachment);\n        });\n        return {\n          name,\n          contentType: type,\n          url: dataUrl\n        };\n      })\n    );\n  }\n  if (Array.isArray(attachmentsFromOptions)) {\n    return attachmentsFromOptions;\n  }\n  throw new Error(\"Invalid attachments type\");\n}\n\n// src/process-assistant-stream.ts\nvar NEWLINE2 = \"\\n\".charCodeAt(0);\nfunction concatChunks2(chunks, totalLength) {\n  const concatenatedChunks = new Uint8Array(totalLength);\n  let offset = 0;\n  for (const chunk of chunks) {\n    concatenatedChunks.set(chunk, offset);\n    offset += chunk.length;\n  }\n  chunks.length = 0;\n  return concatenatedChunks;\n}\nasync function processAssistantStream({\n  stream,\n  onTextPart,\n  onErrorPart,\n  onAssistantMessagePart,\n  onAssistantControlDataPart,\n  onDataMessagePart\n}) {\n  const reader = stream.getReader();\n  const decoder = new TextDecoder();\n  const chunks = [];\n  let totalLength = 0;\n  while (true) {\n    const { value } = await reader.read();\n    if (value) {\n      chunks.push(value);\n      totalLength += value.length;\n      if (value[value.length - 1] !== NEWLINE2) {\n        continue;\n      }\n    }\n    if (chunks.length === 0) {\n      break;\n    }\n    const concatenatedChunks = concatChunks2(chunks, totalLength);\n    totalLength = 0;\n    const streamParts = decoder.decode(concatenatedChunks, { stream: true }).split(\"\\n\").filter((line) => line !== \"\").map(parseAssistantStreamPart);\n    for (const { type, value: value2 } of streamParts) {\n      switch (type) {\n        case \"text\":\n          await (onTextPart == null ? void 0 : onTextPart(value2));\n          break;\n        case \"error\":\n          await (onErrorPart == null ? void 0 : onErrorPart(value2));\n          break;\n        case \"assistant_message\":\n          await (onAssistantMessagePart == null ? void 0 : onAssistantMessagePart(value2));\n          break;\n        case \"assistant_control_data\":\n          await (onAssistantControlDataPart == null ? void 0 : onAssistantControlDataPart(value2));\n          break;\n        case \"data_message\":\n          await (onDataMessagePart == null ? void 0 : onDataMessagePart(value2));\n          break;\n        default: {\n          const exhaustiveCheck = type;\n          throw new Error(`Unknown stream part type: ${exhaustiveCheck}`);\n        }\n      }\n    }\n  }\n}\n\n// src/schema.ts\n\n\n// src/zod-schema.ts\n\nfunction zodSchema(zodSchema2, options) {\n  var _a;\n  const useReferences = (_a = options == null ? void 0 : options.useReferences) != null ? _a : false;\n  return jsonSchema(\n    (0,zod_to_json_schema__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(zodSchema2, {\n      $refStrategy: useReferences ? \"root\" : \"none\",\n      target: \"jsonSchema7\"\n      // note: openai mode breaks various gemini conversions\n    }),\n    {\n      validate: (value) => {\n        const result = zodSchema2.safeParse(value);\n        return result.success ? { success: true, value: result.data } : { success: false, error: result.error };\n      }\n    }\n  );\n}\n\n// src/schema.ts\nvar schemaSymbol = Symbol.for(\"vercel.ai.schema\");\nfunction jsonSchema(jsonSchema2, {\n  validate\n} = {}) {\n  return {\n    [schemaSymbol]: true,\n    _type: void 0,\n    // should never be used directly\n    [_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.validatorSymbol]: true,\n    jsonSchema: jsonSchema2,\n    validate\n  };\n}\nfunction isSchema(value) {\n  return typeof value === \"object\" && value !== null && schemaSymbol in value && value[schemaSymbol] === true && \"jsonSchema\" in value && \"validate\" in value;\n}\nfunction asSchema(schema) {\n  return isSchema(schema) ? schema : zodSchema(schema);\n}\n\n// src/should-resubmit-messages.ts\nfunction shouldResubmitMessages({\n  originalMaxToolInvocationStep,\n  originalMessageCount,\n  maxSteps,\n  messages\n}) {\n  var _a;\n  const lastMessage = messages[messages.length - 1];\n  return (\n    // check if the feature is enabled:\n    maxSteps > 1 && // ensure there is a last message:\n    lastMessage != null && // ensure we actually have new steps (to prevent infinite loops in case of errors):\n    (messages.length > originalMessageCount || extractMaxToolInvocationStep(lastMessage.toolInvocations) !== originalMaxToolInvocationStep) && // check that next step is possible:\n    isAssistantMessageWithCompletedToolCalls(lastMessage) && // limit the number of automatic steps:\n    ((_a = extractMaxToolInvocationStep(lastMessage.toolInvocations)) != null ? _a : 0) < maxSteps\n  );\n}\nfunction isAssistantMessageWithCompletedToolCalls(message) {\n  if (message.role !== \"assistant\") {\n    return false;\n  }\n  const lastStepStartIndex = message.parts.reduce((lastIndex, part, index) => {\n    return part.type === \"step-start\" ? index : lastIndex;\n  }, -1);\n  const lastStepToolInvocations = message.parts.slice(lastStepStartIndex + 1).filter((part) => part.type === \"tool-invocation\");\n  return lastStepToolInvocations.length > 0 && lastStepToolInvocations.every((part) => \"result\" in part.toolInvocation);\n}\n\n// src/update-tool-call-result.ts\nfunction updateToolCallResult({\n  messages,\n  toolCallId,\n  toolResult: result\n}) {\n  var _a;\n  const lastMessage = messages[messages.length - 1];\n  const invocationPart = lastMessage.parts.find(\n    (part) => part.type === \"tool-invocation\" && part.toolInvocation.toolCallId === toolCallId\n  );\n  if (invocationPart == null) {\n    return;\n  }\n  const toolResult = {\n    ...invocationPart.toolInvocation,\n    state: \"result\",\n    result\n  };\n  invocationPart.toolInvocation = toolResult;\n  lastMessage.toolInvocations = (_a = lastMessage.toolInvocations) == null ? void 0 : _a.map(\n    (toolInvocation) => toolInvocation.toolCallId === toolCallId ? toolResult : toolInvocation\n  );\n}\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@ai-sdk+ui-utils@1.2.11_zod@3.24.1/node_modules/@ai-sdk/ui-utils/dist/index.mjs\n");

/***/ })

};
;