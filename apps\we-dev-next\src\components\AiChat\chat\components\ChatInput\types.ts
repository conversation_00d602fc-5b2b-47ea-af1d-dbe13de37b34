'use client';

import { Message, CreateMessage, ChatRequestOptions } from 'ai/react';
import { FilePreview, IModelOption } from '@/stores/chatSlice';

export interface ChatInputProps {
  input: string;
  isLoading: boolean;
  stopRuning: () => void;
  append: (message: Message | CreateMessage, chatRequestOptions?: ChatRequestOptions) => void;
  isUploading: boolean;
  uploadedImages: FilePreview[];
  baseModal: IModelOption;
  messages: Message[];
  setMessages: (message: Message[]) => void;
  setBaseModal: (value: IModelOption) => void;
  handleInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  handleKeySubmit: (e: React.KeyboardEvent) => void;
  handleSubmitWithFiles: (e: any, text?: string) => void;
  handleFileSelect: (e: React.ChangeEvent<HTMLInputElement>) => void;
  removeImage: (id: string) => void;
  addImages: (images: FilePreview[]) => void;
  setInput: (text: string) => void;
  setIsUploading: (uploading: boolean) => void;
}
