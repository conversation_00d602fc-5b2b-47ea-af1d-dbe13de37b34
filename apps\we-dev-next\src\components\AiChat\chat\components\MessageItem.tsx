'use client';

import { Message } from 'ai/react';
import { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import useThemeStore from '@/stores/themeSlice';
import useUserStore from '@/stores/userSlice';

interface MessageItemProps {
  message: Message;
  isEndMessage: boolean;
  isLoading: boolean;
  handleRetry: () => void;
  onUpdateMessage: (messageId: string, content: any[]) => void;
}

export function MessageItem({
  message,
  isEndMessage,
  isLoading,
  handleRetry,
  onUpdateMessage,
}: MessageItemProps) {
  const { isDarkMode } = useThemeStore();
  const { user } = useUserStore();
  const [isExpanded, setIsExpanded] = useState(false);

  const isUser = message.role === 'user';
  const isAssistant = message.role === 'assistant';

  const getInitials = (name: string) => {
    return (
      name
        ?.split(" ")
        .map((word) => word[0])
        .join("")
        .toUpperCase()
        .slice(0, 2) || "?"
    );
  };

  const renderContent = () => {
    if (typeof message.content === 'string') {
      return (
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          components={{
            code({ node, inline, className, children, ...props }) {
              const match = /language-(\w+)/.exec(className || '');
              return !inline && match ? (
                <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded-lg overflow-x-auto">
                  <code className={className} {...props}>
                    {String(children).replace(/\n$/, '')}
                  </code>
                </pre>
              ) : (
                <code className="bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded text-sm" {...props}>
                  {children}
                </code>
              );
            },
          }}
          className="prose prose-sm max-w-none dark:prose-invert"
        >
          {message.content}
        </ReactMarkdown>
      );
    }
    return <div>{JSON.stringify(message.content)}</div>;
  };

  return (
    <div className={`flex gap-3 ${isUser ? 'justify-end' : 'justify-start'}`}>
      {!isUser && (
        <div className="flex-shrink-0">
          <div className="w-8 h-8 rounded-full bg-purple-600 dark:bg-purple-500 flex items-center justify-center">
            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
        </div>
      )}

      <div className={`flex-1 max-w-[80%] ${isUser ? 'flex justify-end' : ''}`}>
        <div
          className={`
            rounded-lg px-4 py-3 
            ${isUser 
              ? 'bg-purple-600 text-white ml-auto' 
              : 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100'
            }
          `}
        >
          {renderContent()}
          
          {isAssistant && isEndMessage && isLoading && (
            <div className="flex items-center gap-2 mt-2 text-sm opacity-70">
              <div className="w-2 h-2 bg-current rounded-full animate-pulse"></div>
              <span>Thinking...</span>
            </div>
          )}
          
          {isAssistant && !isLoading && (
            <div className="flex items-center gap-2 mt-2 text-xs opacity-60">
              <button
                onClick={handleRetry}
                className="hover:opacity-80 transition-opacity"
                title="Retry"
              >
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </button>
              <button
                onClick={() => navigator.clipboard.writeText(message.content as string)}
                className="hover:opacity-80 transition-opacity"
                title="Copy"
              >
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
              </button>
            </div>
          )}
        </div>
      </div>

      {isUser && (
        <div className="flex-shrink-0">
          <div
            className={`
              w-8 h-8 rounded-full
              flex items-center justify-center
              text-white text-xs font-medium
              ${user?.avatar ? "" : "bg-purple-500 dark:bg-purple-600"}
            `}
            style={
              user?.avatar
                ? {
                    backgroundImage: `url(${user.avatar})`,
                    backgroundSize: "cover",
                  }
                : undefined
            }
          >
            {!user?.avatar && getInitials(user?.username || "U")}
          </div>
        </div>
      )}
    </div>
  );
}
