'use client';

import React, { useRef, useState, useCallback, useEffect } from "react";
import { FileIcon, MessageSquare, Code2 } from "lucide-react";
import { toast } from "react-hot-toast";
import classNames from "classnames";
import { useFileStore } from "@/stores/fileStore";
import type { ChatInputProps as ChatInputPropsType } from "./types";
import useChatModeStore from "@/stores/chatModeSlice";
import useChatStore from "@/stores/chatSlice";
import useThemeStore from "@/stores/themeSlice";
import { v4 as uuidv4 } from "uuid";
import useUserStore from "@/stores/userSlice";
import { ChatMode } from "@/types/chat";

export const modePlaceholders = {
  [ChatMode.Chat]: "Ask me anything...",
  [ChatMode.Builder]: "Describe the app you want to build...",
};

export const ChatInput: React.FC<ChatInputPropsType> = ({
  input,
  stopRuning,
  isLoading,
  isUploading,
  append,
  uploadedImages,
  setMessages,
  messages,
  handleInputChange,
  handleKeySubmit,
  handleSubmitWithFiles,
  handleFileSelect,
  removeImage,
  addImages,
  setInput,
  setIsUploading,
  baseModal,
  setBaseModal,
}) => {
  const { files, errors, removeError } = useFileStore();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const { user } = useUserStore();
  const { mode: chatMode } = useChatModeStore();
  const { isDarkMode } = useThemeStore();

  const [mentions, setMentions] = useState<any[]>([]);
  const [highlightRange, setHighlightRange] = useState<any>(null);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.nativeEvent.isComposing || e.keyCode === 229) {
      e.preventDefault();
      return;
    }

    if (e.key === "Enter") {
      setHighlightRange(null);
    }

    handleKeySubmit(e);
  };

  const onInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    handleInputChange(e);
  };

  const handleImageUpload = () => {
    fileInputRef.current?.click();
  };

  const handleStop = () => {
    stopRuning();
  };

  return (
    <div className="relative">
      <div className="flex flex-col gap-2 p-4 bg-white dark:bg-[#18181a] border-t border-gray-200 dark:border-[#333]">
        {/* Error Display */}
        {errors.length > 0 && (
          <div className="space-y-2">
            {errors.map((error, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg"
              >
                <span className="text-sm text-red-700 dark:text-red-300">
                  {error.message}
                </span>
                <button
                  onClick={() => removeError(index)}
                  className="text-red-500 hover:text-red-700 dark:hover:text-red-300"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        )}

        {/* Image Preview Grid */}
        {uploadedImages.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {uploadedImages.map((image) => (
              <div key={image.id} className="relative">
                <img
                  src={image.localUrl}
                  alt="Upload preview"
                  className="w-16 h-16 object-cover rounded-lg border border-gray-200 dark:border-gray-600"
                />
                <button
                  onClick={() => removeImage(image.id)}
                  className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600"
                >
                  ×
                </button>
                {image.status === "uploading" && (
                  <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-lg">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Input Area */}
        <div className="relative">
          <div className="flex items-end gap-2">
            <div className="flex-1 relative">
              <div className="relative">
                <textarea
                  ref={textareaRef}
                  value={input}
                  onChange={onInputChange}
                  onKeyDown={handleKeyDown}
                  placeholder={
                    chatMode === ChatMode.Chat
                      ? modePlaceholders[ChatMode.Chat]
                      : modePlaceholders[ChatMode.Builder]
                  }
                  className={classNames(
                    "w-full p-4 bg-transparent text-gray-900 dark:text-gray-100 focus:outline-none resize-none text-sm",
                    "placeholder-gray-500 dark:placeholder-gray-400",
                    "hover:bg-gray-50/50 dark:hover:bg-white/[0.03]",
                    "focus:bg-gray-50/80 dark:focus:bg-white/[0.05]",
                    "transition-colors duration-200",
                    "relative z-10",
                    "border border-gray-200 dark:border-gray-600 rounded-lg",
                    isLoading && "opacity-50"
                  )}
                  rows={3}
                  style={{
                    minHeight: "60px",
                    maxHeight: "200px",
                    caretColor: isDarkMode ? "white" : "black",
                  }}
                  disabled={isLoading}
                />
              </div>

              {/* Upload and Send Buttons */}
              <div className="absolute bottom-2 right-2 flex items-center gap-2">
                {/* Upload Button */}
                <button
                  onClick={handleImageUpload}
                  disabled={isLoading || isUploading}
                  className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Upload images"
                >
                  <FileIcon size={16} />
                </button>

                {/* Send/Stop Button */}
                {isLoading ? (
                  <button
                    onClick={handleStop}
                    className="p-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
                    title="Stop generation"
                  >
                    <div className="w-4 h-4 bg-white rounded-sm"></div>
                  </button>
                ) : (
                  <button
                    onClick={handleSubmitWithFiles}
                    disabled={!input.trim() || isUploading || uploadedImages.some(img => img.status === "uploading")}
                    className="p-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                    title="Send message"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                    </svg>
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>

        <input
          ref={fileInputRef}
          type="file"
          onChange={handleFileSelect}
          className="hidden"
          multiple
          accept="image/*"
        />
      </div>
    </div>
  );
};
