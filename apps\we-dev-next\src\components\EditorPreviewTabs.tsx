'use client';

import PreviewIframe from "./PreviewIframe";
import { useState } from "react";
import { useFileStore } from "@/stores/fileStore";
import WeIde from "./WeIde";
import useTerminalStore from "@/stores/terminalSlice";
import WeAPI from "./WeAPI";

const EditorPreviewTabs: React.FC = () => {
  const { getFiles, projectRoot, oldFiles, files } = useFileStore();
  const [showIframe, setShowIframe] = useState<string>("editor");
  const [frameStyleMap, setFrameStyleMap] = useState<Record<string, string>>({
    editor: "translate-x-0 opacity-100",
    weApi: "translate-x-full opacity-100",
    preview: "translate-x-full opacity-100",
    diff: "translate-x-full opacity-100"
  });

  const isMinPrograme = getFiles().includes("app.json");

  const onToggle = (tab: string) => {
    setShowIframe(tab);

    // Update frame styles for smooth transitions
    const newFrameStyleMap: Record<string, string> = {};
    Object.keys(frameStyleMap).forEach(key => {
      if (key === tab) {
        newFrameStyleMap[key] = "translate-x-0 opacity-100";
      } else {
        newFrameStyleMap[key] = "translate-x-full opacity-100";
      }
    });
    setFrameStyleMap(newFrameStyleMap);
  };

  return (
    <div className="m-1.5 flex-1 relative flex flex-col">
      <div className="flex h-10 gap-0.5 bg-[#f3f3f3] dark:bg-[#1a1a1a] pl-0 pt-1 rounded-t-lg justify-between border-b border-[#e4e4e4] dark:border-[#333]">
        <div className="flex-1 flex">
          <TabButton
            active={showIframe == "editor" || !showIframe}
            onClick={() => {
              onToggle("editor");
            }}
            icon={<EditorIcon />}
            label="Editor"
          />
          <TabButton
            active={showIframe == "preview"}
            onClick={() => {
              onToggle("preview");
            }}
            icon={<PreviewIcon />}
            label="Preview"
          />
          <TabButton
            active={showIframe == "weApi"}
            onClick={() => {
              onToggle("weApi");
            }}
            icon={<APITestIcon />}
            label="API Test"
          />
        </div>
      </div>

      <div className="flex-1 relative overflow-hidden">
        <div
          className={`
          absolute inset-0
          transform transition-all duration-500 ease-in-out
      ${frameStyleMap["editor"]}
        `}
        >
          <WeIde />
        </div>
        <div
          className={`
          absolute inset-0
          transform transition-all duration-500 ease-in-out
      ${frameStyleMap["preview"]}
        `}
        >
          <PreviewIframe
            isMinPrograme={isMinPrograme}
            setShowIframe={(show) => {
              onToggle("preview");
              setShowIframe(show ? "preview" : "");
            }}
          />
        </div>
        <div
          className={`
          absolute inset-0
          transform transition-all duration-500 ease-in-out
          ${frameStyleMap["weApi"]}
        `}
        >
          <WeAPI />
        </div>
      </div>
    </div>
  );
};

interface TabButtonProps {
  active: boolean;
  onClick: () => void;
  icon: React.ReactNode;
  label: string;
}

const TabButton: React.FC<TabButtonProps> = ({ active, onClick, icon, label }) => {
  return (
    <button
      onClick={onClick}
      className={`
        flex items-center gap-2 px-3 py-1.5 text-sm font-medium rounded-t-lg transition-colors
        ${active
          ? 'bg-white dark:bg-[#18181a] text-gray-900 dark:text-white border-t border-l border-r border-[#e4e4e4] dark:border-[#333]'
          : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800'
        }
      `}
    >
      {icon}
      <span>{label}</span>
    </button>
  );
};

// Icon components
const EditorIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
  </svg>
);

const PreviewIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
  </svg>
);

const APITestIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
  </svg>
);

export default EditorPreviewTabs;
