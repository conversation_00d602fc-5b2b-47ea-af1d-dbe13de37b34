'use client';

import { create } from "zustand";

interface LimitModalStore {
  isVisible: boolean;
  type: "login" | "limit";
  openModal: (type: "login" | "limit") => void;
  closeModal: () => void;
}

// Create global state management
export const useLimitModalStore = create<LimitModalStore>((set) => ({
  isVisible: false,
  type: "login",
  openModal: (type) => set({ isVisible: true, type }),
  closeModal: () => set({ isVisible: false }),
}));

interface GlobalLimitModalProps {
  onLogin: () => void;
}

export function GlobalLimitModal({ onLogin }: GlobalLimitModalProps) {
  const { isVisible, type, closeModal } = useLimitModalStore();

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            {type === "login" ? "Login Required" : "Usage Limit Reached"}
          </h3>
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            {type === "login" 
              ? "Please log in to continue using the application."
              : "You have reached your usage limit. Please upgrade your plan."
            }
          </p>
          <div className="flex gap-3 justify-center">
            <button
              onClick={closeModal}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={() => {
                if (type === "login") {
                  onLogin();
                }
                closeModal();
              }}
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
            >
              {type === "login" ? "Login" : "Upgrade"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
