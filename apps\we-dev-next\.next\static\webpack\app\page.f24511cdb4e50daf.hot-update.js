"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/utils/messageParser.ts":
/*!************************************!*\
  !*** ./src/utils/messageParser.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StreamingMessageParser: function() { return /* binding */ StreamingMessageParser; },\n/* harmony export */   parseMessageForFiles: function() { return /* binding */ parseMessageForFiles; },\n/* harmony export */   parseMessages: function() { return /* binding */ parseMessages; },\n/* harmony export */   testMessageParser: function() { return /* binding */ testMessageParser; }\n/* harmony export */ });\n/* harmony import */ var _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/stores/fileStore */ \"(app-pages-browser)/./src/stores/fileStore.ts\");\n/* __next_internal_client_entry_do_not_use__ StreamingMessageParser,parseMessages,testMessageParser,parseMessageForFiles auto */ \nclass StreamingMessageParser {\n    parse(messageId, content) {\n        console.log(\"\\uD83D\\uDD0D Parsing message \".concat(messageId, \" for artifacts...\"));\n        // Parse boltArtifact tags - handle both formats\n        const artifactRegex = /<boltArtifact[^>]*>([\\s\\S]*?)<\\/boltArtifact>/g;\n        let artifactMatch;\n        let foundArtifacts = 0;\n        while((artifactMatch = artifactRegex.exec(content)) !== null){\n            foundArtifacts++;\n            const fullMatch = artifactMatch[0];\n            const artifactContent = artifactMatch[1];\n            console.log(\"\\uD83D\\uDCE6 Found artifact \".concat(foundArtifacts, \":\"), fullMatch.substring(0, 100) + \"...\");\n            // Check if this is a simplified format (type=\"file\" name=\"...\")\n            const simplifiedMatch = fullMatch.match(/<boltArtifact[^>]*type=\"file\"[^>]*name=\"([^\"]+)\"[^>]*>/);\n            if (simplifiedMatch) {\n                const fileName = simplifiedMatch[1];\n                console.log(\"\\uD83D\\uDCC4 Simplified format detected for file: \".concat(fileName));\n                if (this.callbacks.onFileAction) {\n                    this.callbacks.onFileAction({\n                        type: \"file\",\n                        filePath: fileName,\n                        content: artifactContent.trim()\n                    });\n                }\n            } else {\n                // Standard format with boltAction tags\n                this.parseActions(artifactContent);\n            }\n        }\n        if (foundArtifacts === 0) {\n            console.log(\"ℹ️ No artifacts found in message \".concat(messageId));\n        }\n    }\n    parseActions(content) {\n        // Parse boltAction tags\n        const actionRegex = /<boltAction\\s+type=\"([^\"]+)\"(?:\\s+filePath=\"([^\"]+)\")?\\s*>([\\s\\S]*?)<\\/boltAction>/g;\n        let actionMatch;\n        let foundActions = 0;\n        while((actionMatch = actionRegex.exec(content)) !== null){\n            foundActions++;\n            const [, type, filePath, actionContent] = actionMatch;\n            console.log(\"⚡ Found action \".concat(foundActions, \": type=\").concat(type, \", filePath=\").concat(filePath));\n            switch(type){\n                case \"file\":\n                    if (filePath && this.callbacks.onFileAction) {\n                        this.callbacks.onFileAction({\n                            type: \"file\",\n                            filePath,\n                            content: actionContent.trim()\n                        });\n                    }\n                    break;\n                case \"shell\":\n                    if (this.callbacks.onShellAction) {\n                        this.callbacks.onShellAction({\n                            type: \"shell\",\n                            content: actionContent.trim()\n                        });\n                    }\n                    break;\n                case \"start\":\n                    if (this.callbacks.onStartAction) {\n                        this.callbacks.onStartAction({\n                            type: \"start\",\n                            content: actionContent.trim()\n                        });\n                    }\n                    break;\n            }\n        }\n        if (foundActions === 0) {\n            console.log(\"ℹ️ No actions found in artifact content\");\n        }\n    }\n    constructor(callbacks = {}){\n        this.callbacks = callbacks;\n    }\n}\n// Create a global message parser instance\nconst messageParser = new StreamingMessageParser({\n    onFileAction: async (action)=>{\n        const { addFile } = _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__.useFileStore.getState();\n        try {\n            await addFile(action.filePath, action.content);\n            console.log(\"✅ Created/updated file: \".concat(action.filePath));\n            console.log(\"\\uD83D\\uDCC4 Content preview: \".concat(action.content.substring(0, 100), \"...\"));\n        } catch (error) {\n            console.error(\"❌ Failed to create file \".concat(action.filePath, \":\"), error);\n        }\n    },\n    onShellAction: (action)=>{\n        console.log(\"Shell command:\", action.content);\n    // TODO: Integrate with terminal store to execute commands\n    },\n    onStartAction: (action)=>{\n        console.log(\"Start command:\", action.content);\n    // TODO: Integrate with terminal store to execute start commands\n    }\n});\nconst parseMessages = async (messages)=>{\n    console.log(\"\\uD83D\\uDE80 parseMessages called with \".concat(messages.length, \" messages\"));\n    for (const message of messages){\n        if (message.role === \"assistant\") {\n            console.log(\"\\uD83E\\uDD16 Parsing assistant message: \".concat(message.id));\n            messageParser.parse(message.id, message.content);\n        }\n    }\n};\n// Test function to verify parsing works\nconst testMessageParser = ()=>{\n    const testContent = 'Here\\'s a simple HTML file with \\'Hello World\\' using the boltArtifact format:\\n\\n<boltArtifact type=\"file\" name=\"test.html\">\\n<!DOCTYPE html>\\n<html lang=\"en\">\\n<head>\\n    <meta charset=\"UTF-8\">\\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\\n    <title>Hello World</title>\\n</head>\\n<body>\\n    <h1>Hello World</h1>\\n</body>\\n</html>\\n</boltArtifact>';\n    console.log(\"\\uD83E\\uDDEA Testing message parser...\");\n    messageParser.parse(\"test-message\", testContent);\n};\n// Make test function available globally for debugging\nif (true) {\n    window.testMessageParser = testMessageParser;\n}\n// Simple function to extract files from message content\nfunction parseMessageForFiles(content) {\n    const files = {};\n    // Parse boltArtifact and boltAction tags\n    const artifactRegex = /<boltArtifact[^>]*>([\\s\\S]*?)<\\/boltArtifact>/g;\n    let artifactMatch;\n    while((artifactMatch = artifactRegex.exec(content)) !== null){\n        const artifactContent = artifactMatch[1];\n        // Extract file actions\n        const fileActionRegex = /<boltAction\\s+type=\"file\"\\s+filePath=\"([^\"]+)\"\\s*>([\\s\\S]*?)<\\/boltAction>/g;\n        let fileMatch;\n        while((fileMatch = fileActionRegex.exec(artifactContent)) !== null){\n            const [, filePath, fileContent] = fileMatch;\n            files[filePath] = fileContent.trim();\n        }\n    }\n    return files;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/messageParser.ts\n"));

/***/ })

});