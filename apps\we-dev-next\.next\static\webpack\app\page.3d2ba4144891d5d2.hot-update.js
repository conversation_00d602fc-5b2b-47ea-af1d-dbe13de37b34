"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/utils/messageParser.ts":
/*!************************************!*\
  !*** ./src/utils/messageParser.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StreamingMessageParser: function() { return /* binding */ StreamingMessageParser; },\n/* harmony export */   parseMessageForFiles: function() { return /* binding */ parseMessageForFiles; },\n/* harmony export */   parseMessages: function() { return /* binding */ parseMessages; },\n/* harmony export */   testMessageParser: function() { return /* binding */ testMessageParser; }\n/* harmony export */ });\n/* harmony import */ var _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/stores/fileStore */ \"(app-pages-browser)/./src/stores/fileStore.ts\");\n/* __next_internal_client_entry_do_not_use__ StreamingMessageParser,parseMessages,testMessageParser,parseMessageForFiles auto */ \nclass StreamingMessageParser {\n    parse(messageId, content) {\n        console.log(\"\\uD83D\\uDD0D Parsing message \".concat(messageId, \" for artifacts...\"));\n        // Parse boltArtifact tags - handle both formats\n        const artifactRegex = /<boltArtifact[^>]*>([\\s\\S]*?)<\\/boltArtifact>/g;\n        let artifactMatch;\n        let foundArtifacts = 0;\n        while((artifactMatch = artifactRegex.exec(content)) !== null){\n            foundArtifacts++;\n            const fullMatch = artifactMatch[0];\n            const artifactContent = artifactMatch[1];\n            console.log(\"\\uD83D\\uDCE6 Found artifact \".concat(foundArtifacts, \":\"), fullMatch.substring(0, 100) + \"...\");\n            // Check if this is a simplified format (type=\"file\" name=\"...\")\n            const simplifiedMatch = fullMatch.match(/<boltArtifact[^>]*type=\"file\"[^>]*name=\"([^\"]+)\"[^>]*>/);\n            if (simplifiedMatch) {\n                const fileName = simplifiedMatch[1];\n                console.log(\"\\uD83D\\uDCC4 Simplified format detected for file: \".concat(fileName));\n                if (this.callbacks.onFileAction) {\n                    this.callbacks.onFileAction({\n                        type: \"file\",\n                        filePath: fileName,\n                        content: artifactContent.trim()\n                    });\n                }\n            } else {\n                // Standard format with boltAction tags\n                this.parseActions(artifactContent);\n            }\n        }\n        if (foundArtifacts === 0) {\n            console.log(\"ℹ️ No artifacts found in message \".concat(messageId));\n        }\n    }\n    parseActions(content) {\n        // Parse boltAction tags - handle multiple formats\n        const actionRegex = /<boltAction\\s+([^>]+)>([\\s\\S]*?)<\\/boltAction>/g;\n        let actionMatch;\n        let foundActions = 0;\n        while((actionMatch = actionRegex.exec(content)) !== null){\n            foundActions++;\n            const [, attributes, actionContent] = actionMatch;\n            // Parse attributes\n            const typeMatch = attributes.match(/type=\"([^\"]+)\"/);\n            const filePathMatch = attributes.match(/filePath=\"([^\"]+)\"/);\n            const pathMatch = attributes.match(/path=\"([^\"]+)\"/);\n            const type = typeMatch ? typeMatch[1] : \"\";\n            const filePath = filePathMatch ? filePathMatch[1] : pathMatch ? pathMatch[1] : \"\";\n            console.log(\"⚡ Found action \".concat(foundActions, \": type=\").concat(type, \", filePath=\").concat(filePath));\n            // Handle different type variations\n            if (type === \"file\" || type === \"createFile\") {\n                if (filePath && this.callbacks.onFileAction) {\n                    this.callbacks.onFileAction({\n                        type: \"file\",\n                        filePath,\n                        content: actionContent.trim()\n                    });\n                }\n            } else if (type === \"shell\") {\n                if (this.callbacks.onShellAction) {\n                    this.callbacks.onShellAction({\n                        type: \"shell\",\n                        content: actionContent.trim()\n                    });\n                }\n            } else if (type === \"start\") {\n                if (this.callbacks.onStartAction) {\n                    this.callbacks.onStartAction({\n                        type: \"start\",\n                        content: actionContent.trim()\n                    });\n                }\n            }\n        }\n        if (foundActions === 0) {\n            console.log(\"ℹ️ No actions found in artifact content\");\n        }\n    }\n    constructor(callbacks = {}){\n        this.callbacks = callbacks;\n    }\n}\n// Create a global message parser instance\nconst messageParser = new StreamingMessageParser({\n    onFileAction: async (action)=>{\n        const { addFile } = _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__.useFileStore.getState();\n        try {\n            await addFile(action.filePath, action.content);\n            console.log(\"✅ Created/updated file: \".concat(action.filePath));\n            console.log(\"\\uD83D\\uDCC4 Content preview: \".concat(action.content.substring(0, 100), \"...\"));\n        } catch (error) {\n            console.error(\"❌ Failed to create file \".concat(action.filePath, \":\"), error);\n        }\n    },\n    onShellAction: (action)=>{\n        console.log(\"Shell command:\", action.content);\n    // TODO: Integrate with terminal store to execute commands\n    },\n    onStartAction: (action)=>{\n        console.log(\"Start command:\", action.content);\n    // TODO: Integrate with terminal store to execute start commands\n    }\n});\nconst parseMessages = async (messages)=>{\n    console.log(\"\\uD83D\\uDE80 parseMessages called with \".concat(messages.length, \" messages\"));\n    for (const message of messages){\n        if (message.role === \"assistant\") {\n            console.log(\"\\uD83E\\uDD16 Parsing assistant message: \".concat(message.id));\n            messageParser.parse(message.id, message.content);\n        }\n    }\n};\n// Test function to verify parsing works\nconst testMessageParser = ()=>{\n    const testContent = 'Here\\'s a simple HTML file with \\'Hello World\\' using the boltArtifact format:\\n\\n<boltArtifact type=\"file\" name=\"test.html\">\\n<!DOCTYPE html>\\n<html lang=\"en\">\\n<head>\\n    <meta charset=\"UTF-8\">\\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\\n    <title>Hello World</title>\\n</head>\\n<body>\\n    <h1>Hello World</h1>\\n</body>\\n</html>\\n</boltArtifact>';\n    console.log(\"\\uD83E\\uDDEA Testing message parser...\");\n    messageParser.parse(\"test-message\", testContent);\n};\n// Make test function available globally for debugging\nif (true) {\n    window.testMessageParser = testMessageParser;\n}\n// Simple function to extract files from message content\nfunction parseMessageForFiles(content) {\n    const files = {};\n    // Parse boltArtifact and boltAction tags\n    const artifactRegex = /<boltArtifact[^>]*>([\\s\\S]*?)<\\/boltArtifact>/g;\n    let artifactMatch;\n    while((artifactMatch = artifactRegex.exec(content)) !== null){\n        const artifactContent = artifactMatch[1];\n        // Extract file actions\n        const fileActionRegex = /<boltAction\\s+type=\"file\"\\s+filePath=\"([^\"]+)\"\\s*>([\\s\\S]*?)<\\/boltAction>/g;\n        let fileMatch;\n        while((fileMatch = fileActionRegex.exec(artifactContent)) !== null){\n            const [, filePath, fileContent] = fileMatch;\n            files[filePath] = fileContent.trim();\n        }\n    }\n    return files;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/messageParser.ts\n"));

/***/ })

});