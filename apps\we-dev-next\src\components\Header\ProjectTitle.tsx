'use client';

import React, { useState, useRef } from "react";
import useUserStore from "../../stores/userSlice";

export function ProjectTitle() {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout>();
  const { user, isAuthenticated } = useUserStore();
  
  const getInitials = (name: string) => {
    return (
      name
        ?.split(" ")
        .map((word) => word[0])
        .join("")
        .toUpperCase()
        .slice(0, 2) || "?"
    );
  };

  const handleMouseEnter = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsSidebarOpen(true);
  };

  const handleMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setIsSidebarOpen(false);
    }, 300);
  };

  return (
    <div className="flex items-center gap-4">
      <div
        className="flex items-center gap-1.5 px-2 py-1 rounded hover:bg-gray-100 dark:hover:bg-white/10 transition-colors group"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <div
          className={`
          w-6 h-6 rounded-full
          flex items-center justify-center
          text-white text-xs font-medium
          ${user?.avatar ? "" : "bg-purple-500 dark:bg-purple-600"}
        `}
          style={
            user?.avatar
              ? {
                  backgroundImage: `url(${user.avatar})`,
                  backgroundSize: "cover",
                }
              : undefined
          }
        >
          {!user?.avatar && getInitials(user?.username || "?")}
        </div>
        <span className="text-gray-900 dark:text-white text-[14px] font-normal">
          {isAuthenticated ? user?.username : "Guest"}
        </span>
        <svg
          className="w-3 h-3 text-gray-400 dark:text-gray-500 group-hover:text-gray-600 dark:group-hover:text-gray-300 transition-colors"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </div>

      {/* TODO: Add Sidebar component */}
      {isSidebarOpen && (
        <div className="absolute top-12 left-0 w-64 bg-white dark:bg-gray-800 shadow-lg rounded-lg p-4 z-50">
          <p className="text-sm text-gray-600 dark:text-gray-300">
            Sidebar functionality will be implemented here
          </p>
        </div>
      )}
    </div>
  );
}
