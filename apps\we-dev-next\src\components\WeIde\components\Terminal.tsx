'use client';

import { useState, useRef, useEffect } from 'react';
import useTerminalStore from '@/stores/terminalSlice';
import { v4 as uuidv4 } from 'uuid';

export function Terminal() {
  const [input, setInput] = useState('');
  const [history, setHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);
  const terminalRef = useRef<HTMLDivElement>(null);
  
  const { 
    processes, 
    activeProcessId, 
    addProcess, 
    updateProcess, 
    setActiveProcess 
  } = useTerminalStore();

  const activeProcess = processes.find(p => p.id === activeProcessId);

  useEffect(() => {
    // Auto-focus input when terminal is mounted
    inputRef.current?.focus();
  }, []);

  useEffect(() => {
    // Scroll to bottom when output changes
    if (terminalRef.current) {
      terminalRef.current.scrollTop = terminalRef.current.scrollHeight;
    }
  }, [activeProcess?.output]);

  const executeCommand = async (command: string) => {
    if (!command.trim()) return;

    // Add to history
    setHistory(prev => [...prev, command]);
    setHistoryIndex(-1);

    // Create new process
    const processId = uuidv4();
    const newProcess = {
      id: processId,
      command,
      output: `$ ${command}\n`,
      isRunning: true,
    };

    addProcess(newProcess);

    // Simulate command execution
    try {
      let output = `$ ${command}\n`;
      
      // Handle basic commands
      switch (command.toLowerCase().trim()) {
        case 'help':
          output += `Available commands:
  help     - Show this help message
  clear    - Clear terminal output
  ls       - List files (simulated)
  pwd      - Show current directory
  echo     - Echo text
  date     - Show current date
  whoami   - Show current user
  
Note: This is a simulated terminal for demonstration.
In a real implementation, commands would be executed on the server.
`;
          break;
          
        case 'clear':
          updateProcess(processId, { output: '', isRunning: false });
          return;
          
        case 'ls':
          output += `index.html
style.css
script.js
package.json
README.md
`;
          break;
          
        case 'pwd':
          output += '/workspace/project\n';
          break;
          
        case 'date':
          output += new Date().toString() + '\n';
          break;
          
        case 'whoami':
          output += 'developer\n';
          break;
          
        default:
          if (command.startsWith('echo ')) {
            output += command.substring(5) + '\n';
          } else {
            output += `Command not found: ${command}
Type 'help' for available commands.
`;
          }
      }

      // Simulate processing delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      updateProcess(processId, { 
        output, 
        isRunning: false, 
        exitCode: 0 
      });
      
    } catch (error) {
      updateProcess(processId, { 
        output: `$ ${command}\nError: ${error}\n`, 
        isRunning: false, 
        exitCode: 1 
      });
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      executeCommand(input);
      setInput('');
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      if (history.length > 0) {
        const newIndex = historyIndex === -1 ? history.length - 1 : Math.max(0, historyIndex - 1);
        setHistoryIndex(newIndex);
        setInput(history[newIndex]);
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      if (historyIndex !== -1) {
        const newIndex = historyIndex + 1;
        if (newIndex >= history.length) {
          setHistoryIndex(-1);
          setInput('');
        } else {
          setHistoryIndex(newIndex);
          setInput(history[newIndex]);
        }
      }
    }
  };

  return (
    <div className="h-full bg-black text-green-400 font-mono text-sm flex flex-col">
      <div className="flex items-center justify-between px-3 py-1 bg-gray-800 border-b border-gray-700">
        <span className="text-xs text-gray-300">Terminal</span>
        <div className="flex gap-1">
          <div className="w-3 h-3 rounded-full bg-red-500"></div>
          <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
          <div className="w-3 h-3 rounded-full bg-green-500"></div>
        </div>
      </div>
      
      <div 
        ref={terminalRef}
        className="flex-1 overflow-y-auto p-3 space-y-1"
        onClick={() => inputRef.current?.focus()}
      >
        {/* Welcome message */}
        {processes.length === 0 && (
          <div className="text-gray-400">
            <div>Welcome to We0 Terminal</div>
            <div>Type 'help' for available commands.</div>
            <div></div>
          </div>
        )}
        
        {/* Process outputs */}
        {processes.map(process => (
          <div key={process.id}>
            <pre className="whitespace-pre-wrap">{process.output}</pre>
            {process.isRunning && (
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-gray-400">Running...</span>
              </div>
            )}
          </div>
        ))}
        
        {/* Current input line */}
        <div className="flex items-center">
          <span className="text-blue-400 mr-2">$</span>
          <input
            ref={inputRef}
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            className="flex-1 bg-transparent outline-none text-green-400 caret-green-400"
            placeholder="Enter command..."
            disabled={activeProcess?.isRunning}
          />
        </div>
      </div>
    </div>
  );
}
