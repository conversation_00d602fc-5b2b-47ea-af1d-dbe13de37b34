"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/utils/messageParser.ts":
/*!************************************!*\
  !*** ./src/utils/messageParser.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StreamingMessageParser: function() { return /* binding */ StreamingMessageParser; },\n/* harmony export */   debugFileCreation: function() { return /* binding */ debugFileCreation; },\n/* harmony export */   parseMessageForFiles: function() { return /* binding */ parseMessageForFiles; },\n/* harmony export */   parseMessages: function() { return /* binding */ parseMessages; },\n/* harmony export */   testAIResponse: function() { return /* binding */ testAIResponse; },\n/* harmony export */   testMessageParser: function() { return /* binding */ testMessageParser; }\n/* harmony export */ });\n/* harmony import */ var _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/stores/fileStore */ \"(app-pages-browser)/./src/stores/fileStore.ts\");\n/* __next_internal_client_entry_do_not_use__ StreamingMessageParser,parseMessages,testMessageParser,testAIResponse,debugFileCreation,parseMessageForFiles auto */ \nclass StreamingMessageParser {\n    parse(messageId, content) {\n        console.log(\"\\uD83D\\uDD0D Parsing message \".concat(messageId, \" for artifacts...\"));\n        console.log(\"\\uD83D\\uDCC4 Message content preview:\", content.substring(0, 200) + \"...\");\n        // Parse boltArtifact tags - handle both formats\n        const artifactRegex = /<boltArtifact[^>]*>([\\s\\S]*?)<\\/boltArtifact>/g;\n        let artifactMatch;\n        let foundArtifacts = 0;\n        while((artifactMatch = artifactRegex.exec(content)) !== null){\n            foundArtifacts++;\n            const fullMatch = artifactMatch[0];\n            const artifactContent = artifactMatch[1];\n            console.log(\"\\uD83D\\uDCE6 Found artifact \".concat(foundArtifacts, \":\"), fullMatch.substring(0, 100) + \"...\");\n            // Check if this is a simplified format (type=\"file\" name=\"...\")\n            const simplifiedMatch = fullMatch.match(/<boltArtifact[^>]*type=\"file\"[^>]*name=\"([^\"]+)\"[^>]*>/);\n            if (simplifiedMatch) {\n                const fileName = simplifiedMatch[1];\n                console.log(\"\\uD83D\\uDCC4 Simplified format detected for file: \".concat(fileName));\n                if (this.callbacks.onFileAction) {\n                    this.callbacks.onFileAction({\n                        type: \"file\",\n                        filePath: fileName,\n                        content: artifactContent.trim()\n                    });\n                }\n            } else {\n                // Standard format with boltAction tags\n                this.parseActions(artifactContent);\n            }\n        }\n        if (foundArtifacts === 0) {\n            console.log(\"ℹ️ No artifacts found in message \".concat(messageId));\n            console.log(\"\\uD83D\\uDD0D Checking for boltArtifact tags in content...\");\n            if (content.includes(\"<boltArtifact\")) {\n                console.log(\"⚠️ Found boltArtifact text but regex didn't match. Content:\", content);\n            } else {\n                console.log(\"❌ No boltArtifact tags found in content at all\");\n            }\n        }\n    }\n    parseActions(content) {\n        // Parse boltAction tags - handle multiple formats\n        const actionRegex = /<boltAction\\s+([^>]+)>([\\s\\S]*?)<\\/boltAction>/g;\n        let actionMatch;\n        let foundActions = 0;\n        while((actionMatch = actionRegex.exec(content)) !== null){\n            foundActions++;\n            const [, attributes, actionContent] = actionMatch;\n            // Parse attributes\n            const typeMatch = attributes.match(/type=\"([^\"]+)\"/);\n            const filePathMatch = attributes.match(/filePath=\"([^\"]+)\"/);\n            const pathMatch = attributes.match(/path=\"([^\"]+)\"/);\n            const type = typeMatch ? typeMatch[1] : \"\";\n            const filePath = filePathMatch ? filePathMatch[1] : pathMatch ? pathMatch[1] : \"\";\n            console.log(\"⚡ Found action \".concat(foundActions, \": type=\").concat(type, \", filePath=\").concat(filePath));\n            // Handle different type variations\n            if (type === \"file\" || type === \"createFile\") {\n                if (filePath && this.callbacks.onFileAction) {\n                    this.callbacks.onFileAction({\n                        type: \"file\",\n                        filePath,\n                        content: actionContent.trim()\n                    });\n                }\n            } else if (type === \"shell\") {\n                if (this.callbacks.onShellAction) {\n                    this.callbacks.onShellAction({\n                        type: \"shell\",\n                        content: actionContent.trim()\n                    });\n                }\n            } else if (type === \"start\") {\n                if (this.callbacks.onStartAction) {\n                    this.callbacks.onStartAction({\n                        type: \"start\",\n                        content: actionContent.trim()\n                    });\n                }\n            }\n        }\n        if (foundActions === 0) {\n            console.log(\"ℹ️ No actions found in artifact content\");\n        }\n    }\n    constructor(callbacks = {}){\n        this.callbacks = callbacks;\n    }\n}\n// Create a global message parser instance\nconst messageParser = new StreamingMessageParser({\n    onFileAction: async (action)=>{\n        const { addFile } = _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__.useFileStore.getState();\n        try {\n            await addFile(action.filePath, action.content);\n            console.log(\"✅ Created/updated file: \".concat(action.filePath));\n            console.log(\"\\uD83D\\uDCC4 Content preview: \".concat(action.content.substring(0, 100), \"...\"));\n        } catch (error) {\n            console.error(\"❌ Failed to create file \".concat(action.filePath, \":\"), error);\n        }\n    },\n    onShellAction: (action)=>{\n        console.log(\"Shell command:\", action.content);\n    // TODO: Integrate with terminal store to execute commands\n    },\n    onStartAction: (action)=>{\n        console.log(\"Start command:\", action.content);\n    // TODO: Integrate with terminal store to execute start commands\n    }\n});\nconst parseMessages = async (messages)=>{\n    console.log(\"\\uD83D\\uDE80 parseMessages called with \".concat(messages.length, \" messages\"));\n    console.log(\"\\uD83D\\uDCCA Current file store state:\", _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__.useFileStore.getState().files);\n    for (const message of messages){\n        if (message.role === \"assistant\") {\n            console.log(\"\\uD83E\\uDD16 Parsing assistant message: \".concat(message.id));\n            console.log(\"\\uD83D\\uDCCB Message object:\", message);\n            console.log(\"\\uD83D\\uDCDD Message content type:\", typeof message.content);\n            console.log(\"\\uD83D\\uDCDD Message content:\", message.content);\n            // Check if content contains boltArtifact\n            if (message.content && typeof message.content === \"string\" && message.content.includes(\"<boltArtifact\")) {\n                console.log(\"✅ Message contains boltArtifact tags, proceeding with parsing...\");\n            } else {\n                console.log(\"❌ Message does not contain boltArtifact tags\");\n            }\n            messageParser.parse(message.id, message.content);\n            // Check file store state after parsing\n            console.log(\"\\uD83D\\uDCCA File store state after parsing:\", _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__.useFileStore.getState().files);\n        }\n    }\n};\n// Test function to verify parsing works\nconst testMessageParser = ()=>{\n    console.log(\"\\uD83E\\uDDEA Testing message parser...\");\n    const testContent1 = 'Here\\'s a simple HTML file with \\'Hello World\\' using the boltArtifact format:\\n\\n<boltArtifact type=\"file\" name=\"test.html\">\\n<!DOCTYPE html>\\n<html lang=\"en\">\\n<head>\\n    <meta charset=\"UTF-8\">\\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\\n    <title>Hello World</title>\\n</head>\\n<body>\\n    <h1>Hello World</h1>\\n</body>\\n</html>\\n</boltArtifact>';\n    const testContent2 = 'Here\\'s a simple HTML file in the boltArtifact format:\\n\\n<boltArtifact>\\n<boltAction type=\"createFile\" path=\"index.html\">\\n<!DOCTYPE html>\\n<html lang=\"en\">\\n<head>\\n    <meta charset=\"UTF-8\">\\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\\n    <title>Hello World</title>\\n</head>\\n<body>\\n    <h1>Hello World</h1>\\n</body>\\n</html>\\n</boltAction>\\n</boltArtifact>';\n    console.log(\"\\uD83E\\uDDEA Testing message parser with format 1...\");\n    messageParser.parse(\"test-message-1\", testContent1);\n    console.log(\"\\uD83E\\uDDEA Testing message parser with format 2...\");\n    messageParser.parse(\"test-message-2\", testContent2);\n};\n// Test function with the exact AI response format\nconst testAIResponse = ()=>{\n    const aiResponse = '<boltArtifact><boltAction type=\"file\" filePath=\"test.html\"><!DOCTYPE html>\\n<html>\\n<head><title>Test</title></head>\\n<body><h1>Hello World</h1></body>\\n</html></boltAction></boltArtifact>';\n    console.log(\"\\uD83E\\uDDEA Testing with exact AI response format...\");\n    console.log(\"\\uD83D\\uDCC4 Test content:\", aiResponse);\n    messageParser.parse(\"ai-response-test\", aiResponse);\n};\n// Debug function to test file creation\nconst debugFileCreation = ()=>{\n    console.log(\"\\uD83D\\uDD27 Testing file creation directly...\");\n    const { addFile } = _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__.useFileStore.getState();\n    addFile(\"debug-test.html\", \"<h1>Debug Test</h1>\").then(()=>{\n        console.log(\"✅ Direct file creation successful\");\n        const files = _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__.useFileStore.getState().files;\n        console.log(\"\\uD83D\\uDCC1 Current files:\", Object.keys(files));\n    }).catch((error)=>{\n        console.error(\"❌ Direct file creation failed:\", error);\n    });\n};\n// Make test functions available globally for debugging\nif (true) {\n    window.testMessageParser = testMessageParser;\n    window.testAIResponse = testAIResponse;\n    window.debugFileCreation = debugFileCreation;\n}\n// Simple function to extract files from message content\nfunction parseMessageForFiles(content) {\n    const files = {};\n    // Parse boltArtifact and boltAction tags\n    const artifactRegex = /<boltArtifact[^>]*>([\\s\\S]*?)<\\/boltArtifact>/g;\n    let artifactMatch;\n    while((artifactMatch = artifactRegex.exec(content)) !== null){\n        const artifactContent = artifactMatch[1];\n        // Extract file actions\n        const fileActionRegex = /<boltAction\\s+type=\"file\"\\s+filePath=\"([^\"]+)\"\\s*>([\\s\\S]*?)<\\/boltAction>/g;\n        let fileMatch;\n        while((fileMatch = fileActionRegex.exec(artifactContent)) !== null){\n            const [, filePath, fileContent] = fileMatch;\n            files[filePath] = fileContent.trim();\n        }\n    }\n    return files;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/messageParser.ts\n"));

/***/ })

});