"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/AiChat/chat/index.tsx":
/*!**********************************************!*\
  !*** ./src/components/AiChat/chat/index.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseChat: function() { return /* binding */ BaseChat; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ai_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ai/react */ \"(app-pages-browser)/./node_modules/.pnpm/ai@4.3.17_react@18.3.1_zod@3.24.1/node_modules/ai/react/dist/index.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _stores_chatSlice__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/stores/chatSlice */ \"(app-pages-browser)/./src/stores/chatSlice.ts\");\n/* harmony import */ var _stores_fileStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/stores/fileStore */ \"(app-pages-browser)/./src/stores/fileStore.ts\");\n/* harmony import */ var _utils_indexDB__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/indexDB */ \"(app-pages-browser)/./src/utils/indexDB.ts\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/.pnpm/uuid@11.0.5/node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _components_MessageItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/MessageItem */ \"(app-pages-browser)/./src/components/AiChat/chat/components/MessageItem.tsx\");\n/* harmony import */ var _components_ChatInput__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/ChatInput */ \"(app-pages-browser)/./src/components/AiChat/chat/components/ChatInput/index.tsx\");\n/* harmony import */ var _components_Tips__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./components/Tips */ \"(app-pages-browser)/./src/components/AiChat/chat/components/Tips.tsx\");\n/* harmony import */ var _utils_messageParser__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/messageParser */ \"(app-pages-browser)/./src/utils/messageParser.ts\");\n/* harmony import */ var _stores_userSlice__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/stores/userSlice */ \"(app-pages-browser)/./src/stores/userSlice.ts\");\n/* harmony import */ var _UserModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../UserModal */ \"(app-pages-browser)/./src/components/UserModal/index.tsx\");\n/* harmony import */ var _stores_chatModeSlice__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/stores/chatModeSlice */ \"(app-pages-browser)/./src/stores/chatModeSlice.ts\");\n/* __next_internal_client_entry_do_not_use__ BaseChat auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst BaseChat = (param)=>{\n    let { uuid: propUuid } = param;\n    _s();\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { otherConfig, uploadedImages, clearImages, addImages, removeImage } = (0,_stores_chatSlice__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    const [checkCount, setCheckCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [baseModal, setBaseModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        value: \"anthropic/claude-3.5-sonnet\",\n        label: \"Claude 3.5 Sonnet\",\n        useImage: true,\n        from: \"default\",\n        quota: 2,\n        functionCall: true\n    });\n    const { files } = (0,_stores_fileStore__WEBPACK_IMPORTED_MODULE_4__.useFileStore)();\n    const { user, token } = (0,_stores_userSlice__WEBPACK_IMPORTED_MODULE_10__[\"default\"])();\n    const { openModal } = (0,_UserModal__WEBPACK_IMPORTED_MODULE_11__.useLimitModalStore)();\n    const { mode } = (0,_stores_chatModeSlice__WEBPACK_IMPORTED_MODULE_12__[\"default\"])();\n    const [messages, setMessagesa] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const parseTimeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(Date.now());\n    const refUuidMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const chatUuid = propUuid || (0,uuid__WEBPACK_IMPORTED_MODULE_13__[\"default\"])();\n    const baseChatUrl =  true ? window.location.origin : 0;\n    const clearErrors = ()=>setErrors([]);\n    const scrollToBottom = ()=>{\n        setTimeout(()=>{\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }, 100);\n    };\n    // Parse messages and create files\n    const parseMessagesAndCreateFiles = async (messages)=>{\n        try {\n            await (0,_utils_messageParser__WEBPACK_IMPORTED_MODULE_9__.parseMessages)(messages);\n        } catch (error) {\n            console.error(\"Error parsing messages:\", error);\n        }\n    };\n    const updateFileSystemNow = ()=>{\n        // This would update the file system in desktop app\n        // For web, this is a no-op\n        return Promise.resolve();\n    };\n    const createMpIcon = (files)=>{\n    // This would create mini program icons in desktop app\n    // For web, this is a no-op\n    };\n    const checkExecList = ()=>{\n        // This would check execution list in desktop app\n        return [];\n    };\n    const checkFinish = ()=>{\n        // This would check if execution is finished in desktop app\n        return true;\n    };\n    const { messages: realMessages, input, handleInputChange, isLoading, setMessages, append, setInput, stop, reload } = (0,ai_react__WEBPACK_IMPORTED_MODULE_14__.useChat)({\n        api: \"\".concat(baseChatUrl, \"/api/chat\"),\n        headers: {\n            ...token && {\n                Authorization: \"Bearer \".concat(token)\n            }\n        },\n        body: {\n            model: baseModal.value,\n            mode: mode,\n            otherConfig: {\n                ...otherConfig,\n                extra: {\n                    ...otherConfig.extra,\n                    isBackEnd: otherConfig.isBackEnd,\n                    backendLanguage: otherConfig.backendLanguage\n                }\n            }\n        },\n        onFinish: async (message)=>{\n            try {\n                var _find_content, _find;\n                const initMessage = messages.filter((m)=>m.role === \"system\");\n                await _utils_indexDB__WEBPACK_IMPORTED_MODULE_5__.db.insert(chatUuid, {\n                    messages: [\n                        ...messages,\n                        ...initMessage,\n                        message\n                    ],\n                    title: ((_find = [\n                        ...initMessage,\n                        ...messages\n                    ].find((m)=>m.role === \"user\" && !m.content.includes(\"<boltArtifact\"))) === null || _find === void 0 ? void 0 : (_find_content = _find.content) === null || _find_content === void 0 ? void 0 : _find_content.slice(0, 50)) || \"New Chat\"\n                });\n            } catch (error) {\n                console.error(\"Failed to save chat history:\", error);\n            }\n            setCheckCount((checkCount)=>checkCount + 1);\n        },\n        onError: (error)=>{\n            var _error_errors_, _error_errors;\n            const msg = (error === null || error === void 0 ? void 0 : (_error_errors = error.errors) === null || _error_errors === void 0 ? void 0 : (_error_errors_ = _error_errors[0]) === null || _error_errors_ === void 0 ? void 0 : _error_errors_.responseBody) || String(error);\n            console.log(\"error\", error, msg);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(msg);\n            if (String(error).includes(\"Quota not enough\")) {\n                openModal(\"limit\");\n            }\n            if (String(error).includes(\"Authentication required\")) {\n                openModal(\"login\");\n            }\n        }\n    });\n    const filterMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return messages.filter((message)=>{\n            if (message.role === \"system\") return false;\n            return true;\n        });\n    }, [\n        messages\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (Date.now() - parseTimeRef.current > 200 && isLoading) {\n            setMessagesa(realMessages);\n            parseTimeRef.current = Date.now();\n            const needParseMessages = messages.filter((m)=>!refUuidMessages.current.includes(m.id));\n            console.log(\"\\uD83D\\uDCE8 Processing \".concat(needParseMessages.length, \" new messages:\"), needParseMessages.map((m)=>{\n                var _m_content;\n                return {\n                    id: m.id,\n                    role: m.role,\n                    contentLength: ((_m_content = m.content) === null || _m_content === void 0 ? void 0 : _m_content.length) || 0\n                };\n            }));\n            parseMessagesAndCreateFiles(needParseMessages);\n            // Update tracked message IDs\n            refUuidMessages.current = [\n                ...refUuidMessages.current,\n                ...needParseMessages.map((m)=>m.id)\n            ];\n            scrollToBottom();\n        }\n        if (errors.length > 0 && isLoading) {\n            clearErrors();\n        }\n        if (!isLoading) {\n            setMessagesa(realMessages);\n            createMpIcon(files);\n        }\n    }, [\n        realMessages,\n        isLoading\n    ]);\n    const handleFileSelect = async (e)=>{\n        const files = Array.from(e.target.files || []);\n        if (files.length === 0) return;\n        setIsUploading(true);\n        try {\n            const newImages = files.map((file)=>({\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(),\n                    file,\n                    url: URL.createObjectURL(file),\n                    localUrl: URL.createObjectURL(file),\n                    status: \"done\"\n                }));\n            addImages(newImages);\n        } catch (error) {\n            console.error(\"File upload failed:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to upload files\");\n        } finally{\n            setIsUploading(false);\n        }\n    };\n    const handleSubmitWithFiles = async (e, text)=>{\n        e === null || e === void 0 ? void 0 : e.preventDefault();\n        if (!input.trim() && !(text === null || text === void 0 ? void 0 : text.trim()) || isLoading) return;\n        try {\n            const currentAttachments = uploadedImages.map((img)=>({\n                    id: img.id,\n                    name: img.id,\n                    type: img.file.type,\n                    localUrl: img.localUrl,\n                    contentType: img.file.type,\n                    url: img.url\n                }));\n            clearImages();\n            append({\n                role: \"user\",\n                content: text || input\n            }, {\n                experimental_attachments: currentAttachments\n            });\n            setInput(\"\");\n            setTimeout(()=>{\n                scrollToBottom();\n            }, 100);\n        } catch (error) {\n            console.error(\"Upload failed:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to upload files\");\n        }\n    };\n    const handleKeySubmit = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmitWithFiles(e);\n        }\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        const files = Array.from(e.dataTransfer.files);\n        if (files.length > 0) {\n            const fileInput = document.createElement(\"input\");\n            fileInput.type = \"file\";\n            fileInput.multiple = true;\n            fileInput.files = e.dataTransfer.files;\n            handleFileSelect({\n                target: fileInput\n            });\n        }\n    };\n    const handleScroll = ()=>{\n    // Handle scroll events if needed\n    };\n    const showJsx = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 overflow-y-auto px-1 py-2 message-container [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]\",\n            onScroll: handleScroll,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Tips__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    append: append,\n                    setInput: setInput,\n                    handleFileSelect: handleFileSelect\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                    lineNumber: 279,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-[640px] w-full mx-auto space-y-3\",\n                    children: [\n                        filterMessages.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MessageItem__WEBPACK_IMPORTED_MODULE_6__.MessageItem, {\n                                handleRetry: ()=>{\n                                    reload();\n                                },\n                                message: message,\n                                isEndMessage: filterMessages[filterMessages.length - 1].id === message.id,\n                                isLoading: isLoading,\n                                onUpdateMessage: (messageId, content)=>{\n                                    var _content_;\n                                    append({\n                                        role: \"user\",\n                                        content: \" \".concat(content === null || content === void 0 ? void 0 : (_content_ = content[0]) === null || _content_ === void 0 ? void 0 : _content_.text)\n                                    });\n                                }\n                            }, \"\".concat(message.id, \"-\").concat(index), false, {\n                                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 25\n                            }, undefined)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: messagesEndRef\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n            lineNumber: 275,\n            columnNumber: 13\n        }, undefined);\n    }, [\n        filterMessages,\n        isLoading,\n        append,\n        setInput,\n        handleFileSelect,\n        reload\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-full flex-col dark:bg-[#18181a] max-w-full\",\n        onDragOver: handleDragOver,\n        onDrop: handleDrop,\n        children: [\n            showJsx,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatInput__WEBPACK_IMPORTED_MODULE_7__.ChatInput, {\n                input: input,\n                setMessages: setMessages,\n                append: append,\n                messages: messages,\n                stopRuning: stop,\n                setInput: setInput,\n                isLoading: isLoading,\n                isUploading: isUploading,\n                uploadedImages: uploadedImages,\n                baseModal: baseModal,\n                handleInputChange: handleInputChange,\n                handleKeySubmit: handleKeySubmit,\n                handleSubmitWithFiles: handleSubmitWithFiles,\n                handleFileSelect: handleFileSelect,\n                removeImage: removeImage,\n                addImages: addImages,\n                setIsUploading: setIsUploading,\n                setBaseModal: setBaseModal\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                lineNumber: 317,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n        lineNumber: 311,\n        columnNumber: 9\n    }, undefined);\n};\n_s(BaseChat, \"XjpiMRuQUoOxvZGXxaMsIOhU7GA=\", false, function() {\n    return [\n        _stores_chatSlice__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        _stores_fileStore__WEBPACK_IMPORTED_MODULE_4__.useFileStore,\n        _stores_userSlice__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        _UserModal__WEBPACK_IMPORTED_MODULE_11__.useLimitModalStore,\n        _stores_chatModeSlice__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        ai_react__WEBPACK_IMPORTED_MODULE_14__.useChat\n    ];\n});\n_c = BaseChat;\nvar _c;\n$RefreshReg$(_c, \"BaseChat\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AiChat/chat/index.tsx\n"));

/***/ })

});