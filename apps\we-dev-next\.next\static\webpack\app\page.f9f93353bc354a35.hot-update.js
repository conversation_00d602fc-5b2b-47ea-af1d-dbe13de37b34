"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/AiChat/chat/index.tsx":
/*!**********************************************!*\
  !*** ./src/components/AiChat/chat/index.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseChat: function() { return /* binding */ BaseChat; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ai_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ai/react */ \"(app-pages-browser)/./node_modules/.pnpm/ai@4.3.17_react@18.3.1_zod@3.24.1/node_modules/ai/react/dist/index.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _stores_chatSlice__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/stores/chatSlice */ \"(app-pages-browser)/./src/stores/chatSlice.ts\");\n/* harmony import */ var _stores_fileStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/stores/fileStore */ \"(app-pages-browser)/./src/stores/fileStore.ts\");\n/* harmony import */ var _utils_indexDB__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/indexDB */ \"(app-pages-browser)/./src/utils/indexDB.ts\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/.pnpm/uuid@11.0.5/node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _components_MessageItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/MessageItem */ \"(app-pages-browser)/./src/components/AiChat/chat/components/MessageItem.tsx\");\n/* harmony import */ var _components_ChatInput__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/ChatInput */ \"(app-pages-browser)/./src/components/AiChat/chat/components/ChatInput/index.tsx\");\n/* harmony import */ var _components_Tips__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./components/Tips */ \"(app-pages-browser)/./src/components/AiChat/chat/components/Tips.tsx\");\n/* harmony import */ var _utils_messageParserNew__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/messageParserNew */ \"(app-pages-browser)/./src/utils/messageParserNew.ts\");\n/* harmony import */ var _stores_userSlice__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/stores/userSlice */ \"(app-pages-browser)/./src/stores/userSlice.ts\");\n/* harmony import */ var _UserModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../UserModal */ \"(app-pages-browser)/./src/components/UserModal/index.tsx\");\n/* harmony import */ var _stores_chatModeSlice__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/stores/chatModeSlice */ \"(app-pages-browser)/./src/stores/chatModeSlice.ts\");\n/* __next_internal_client_entry_do_not_use__ BaseChat auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst BaseChat = (param)=>{\n    let { uuid: propUuid } = param;\n    _s();\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { otherConfig, uploadedImages, clearImages, addImages, removeImage } = (0,_stores_chatSlice__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    const [checkCount, setCheckCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [baseModal, setBaseModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        value: \"anthropic/claude-3.5-sonnet\",\n        label: \"Claude 3.5 Sonnet\",\n        useImage: true,\n        from: \"default\",\n        quota: 2,\n        functionCall: true\n    });\n    const { files } = (0,_stores_fileStore__WEBPACK_IMPORTED_MODULE_4__.useFileStore)();\n    const { user, token } = (0,_stores_userSlice__WEBPACK_IMPORTED_MODULE_10__[\"default\"])();\n    const { openModal } = (0,_UserModal__WEBPACK_IMPORTED_MODULE_11__.useLimitModalStore)();\n    const { mode } = (0,_stores_chatModeSlice__WEBPACK_IMPORTED_MODULE_12__[\"default\"])();\n    const [messages, setMessagesa] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const parseTimeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(Date.now());\n    const refUuidMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const chatUuid = propUuid || (0,uuid__WEBPACK_IMPORTED_MODULE_13__[\"default\"])();\n    const baseChatUrl =  true ? window.location.origin : 0;\n    const clearErrors = ()=>setErrors([]);\n    const scrollToBottom = ()=>{\n        setTimeout(()=>{\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }, 100);\n    };\n    // Parse messages and create files\n    const parseMessagesAndCreateFiles = async (messages)=>{\n        try {\n            await (0,_utils_messageParserNew__WEBPACK_IMPORTED_MODULE_9__.parseMessages)(messages);\n        } catch (error) {\n            console.error(\"Error parsing messages:\", error);\n        }\n    };\n    const updateFileSystemNow = ()=>{\n        // This would update the file system in desktop app\n        // For web, this is a no-op\n        return Promise.resolve();\n    };\n    const createMpIcon = (files)=>{\n    // This would create mini program icons in desktop app\n    // For web, this is a no-op\n    };\n    const checkExecList = ()=>{\n        // This would check execution list in desktop app\n        return [];\n    };\n    const checkFinish = ()=>{\n        // This would check if execution is finished in desktop app\n        return true;\n    };\n    const { messages: realMessages, input, handleInputChange, isLoading, setMessages, append, setInput, stop, reload } = (0,ai_react__WEBPACK_IMPORTED_MODULE_14__.useChat)({\n        api: \"\".concat(baseChatUrl, \"/api/chat\"),\n        headers: {\n            ...token && {\n                Authorization: \"Bearer \".concat(token)\n            }\n        },\n        body: {\n            model: baseModal.value,\n            mode: mode,\n            otherConfig: {\n                ...otherConfig,\n                extra: {\n                    ...otherConfig.extra,\n                    isBackEnd: otherConfig.isBackEnd,\n                    backendLanguage: otherConfig.backendLanguage\n                }\n            }\n        },\n        onFinish: async (message)=>{\n            try {\n                var _find_content, _find;\n                const initMessage = messages.filter((m)=>m.role === \"system\");\n                await _utils_indexDB__WEBPACK_IMPORTED_MODULE_5__.db.insert(chatUuid, {\n                    messages: [\n                        ...messages,\n                        ...initMessage,\n                        message\n                    ],\n                    title: ((_find = [\n                        ...initMessage,\n                        ...messages\n                    ].find((m)=>m.role === \"user\" && !m.content.includes(\"<boltArtifact\"))) === null || _find === void 0 ? void 0 : (_find_content = _find.content) === null || _find_content === void 0 ? void 0 : _find_content.slice(0, 50)) || \"New Chat\"\n                });\n            } catch (error) {\n                console.error(\"Failed to save chat history:\", error);\n            }\n            setCheckCount((checkCount)=>checkCount + 1);\n        },\n        onError: (error)=>{\n            var _error_errors_, _error_errors;\n            const msg = (error === null || error === void 0 ? void 0 : (_error_errors = error.errors) === null || _error_errors === void 0 ? void 0 : (_error_errors_ = _error_errors[0]) === null || _error_errors_ === void 0 ? void 0 : _error_errors_.responseBody) || String(error);\n            console.log(\"error\", error, msg);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(msg);\n            if (String(error).includes(\"Quota not enough\")) {\n                openModal(\"limit\");\n            }\n            if (String(error).includes(\"Authentication required\")) {\n                openModal(\"login\");\n            }\n        }\n    });\n    const filterMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return messages.filter((message)=>{\n            if (message.role === \"system\") return false;\n            return true;\n        });\n    }, [\n        messages\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only update messages during streaming, but don't parse yet\n        if (Date.now() - parseTimeRef.current > 200 && isLoading) {\n            setMessagesa(realMessages);\n            parseTimeRef.current = Date.now();\n            scrollToBottom();\n        }\n        if (errors.length > 0 && isLoading) {\n            clearErrors();\n        }\n        // Only parse messages when streaming is complete\n        if (!isLoading) {\n            setMessagesa(realMessages);\n            createMpIcon(files);\n            // Parse messages when loading is complete\n            const needParseMessages = messages.filter((m)=>!refUuidMessages.current.includes(m.id) && m.role === \"assistant\" && m.content && typeof m.content === \"string\" && m.content.trim().length > 0);\n            if (needParseMessages.length > 0) {\n                console.log(\"\\uD83D\\uDCE8 Processing \".concat(needParseMessages.length, \" new messages (loading complete):\"), needParseMessages.map((m)=>{\n                    var _m_content;\n                    return {\n                        id: m.id,\n                        role: m.role,\n                        contentLength: ((_m_content = m.content) === null || _m_content === void 0 ? void 0 : _m_content.length) || 0,\n                        hasContent: !!m.content\n                    };\n                }));\n                parseMessagesAndCreateFiles(needParseMessages);\n                // Update tracked message IDs\n                refUuidMessages.current = [\n                    ...refUuidMessages.current,\n                    ...needParseMessages.map((m)=>m.id)\n                ];\n            }\n        }\n    }, [\n        realMessages,\n        isLoading\n    ]);\n    const handleFileSelect = async (e)=>{\n        const files = Array.from(e.target.files || []);\n        if (files.length === 0) return;\n        setIsUploading(true);\n        try {\n            const newImages = files.map((file)=>({\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(),\n                    file,\n                    url: URL.createObjectURL(file),\n                    localUrl: URL.createObjectURL(file),\n                    status: \"done\"\n                }));\n            addImages(newImages);\n        } catch (error) {\n            console.error(\"File upload failed:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to upload files\");\n        } finally{\n            setIsUploading(false);\n        }\n    };\n    const handleSubmitWithFiles = async (e, text)=>{\n        e === null || e === void 0 ? void 0 : e.preventDefault();\n        if (!input.trim() && !(text === null || text === void 0 ? void 0 : text.trim()) || isLoading) return;\n        try {\n            const currentAttachments = uploadedImages.map((img)=>({\n                    id: img.id,\n                    name: img.id,\n                    type: img.file.type,\n                    localUrl: img.localUrl,\n                    contentType: img.file.type,\n                    url: img.url\n                }));\n            clearImages();\n            append({\n                role: \"user\",\n                content: text || input\n            }, {\n                experimental_attachments: currentAttachments\n            });\n            setInput(\"\");\n            setTimeout(()=>{\n                scrollToBottom();\n            }, 100);\n        } catch (error) {\n            console.error(\"Upload failed:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to upload files\");\n        }\n    };\n    const handleKeySubmit = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmitWithFiles(e);\n        }\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        const files = Array.from(e.dataTransfer.files);\n        if (files.length > 0) {\n            const fileInput = document.createElement(\"input\");\n            fileInput.type = \"file\";\n            fileInput.multiple = true;\n            fileInput.files = e.dataTransfer.files;\n            handleFileSelect({\n                target: fileInput\n            });\n        }\n    };\n    const handleScroll = ()=>{\n    // Handle scroll events if needed\n    };\n    const showJsx = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 overflow-y-auto px-1 py-2 message-container [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]\",\n            onScroll: handleScroll,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Tips__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    append: append,\n                    setInput: setInput,\n                    handleFileSelect: handleFileSelect\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                    lineNumber: 290,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-[640px] w-full mx-auto space-y-3\",\n                    children: [\n                        filterMessages.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MessageItem__WEBPACK_IMPORTED_MODULE_6__.MessageItem, {\n                                handleRetry: ()=>{\n                                    reload();\n                                },\n                                message: message,\n                                isEndMessage: filterMessages[filterMessages.length - 1].id === message.id,\n                                isLoading: isLoading,\n                                onUpdateMessage: (messageId, content)=>{\n                                    var _content_;\n                                    append({\n                                        role: \"user\",\n                                        content: \" \".concat(content === null || content === void 0 ? void 0 : (_content_ = content[0]) === null || _content_ === void 0 ? void 0 : _content_.text)\n                                    });\n                                }\n                            }, \"\".concat(message.id, \"-\").concat(index), false, {\n                                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 25\n                            }, undefined)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: messagesEndRef\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n            lineNumber: 286,\n            columnNumber: 13\n        }, undefined);\n    }, [\n        filterMessages,\n        isLoading,\n        append,\n        setInput,\n        handleFileSelect,\n        reload\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-full flex-col dark:bg-[#18181a] max-w-full\",\n        onDragOver: handleDragOver,\n        onDrop: handleDrop,\n        children: [\n            showJsx,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatInput__WEBPACK_IMPORTED_MODULE_7__.ChatInput, {\n                input: input,\n                setMessages: setMessages,\n                append: append,\n                messages: messages,\n                stopRuning: stop,\n                setInput: setInput,\n                isLoading: isLoading,\n                isUploading: isUploading,\n                uploadedImages: uploadedImages,\n                baseModal: baseModal,\n                handleInputChange: handleInputChange,\n                handleKeySubmit: handleKeySubmit,\n                handleSubmitWithFiles: handleSubmitWithFiles,\n                handleFileSelect: handleFileSelect,\n                removeImage: removeImage,\n                addImages: addImages,\n                setIsUploading: setIsUploading,\n                setBaseModal: setBaseModal\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n                lineNumber: 328,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\components\\\\AiChat\\\\chat\\\\index.tsx\",\n        lineNumber: 322,\n        columnNumber: 9\n    }, undefined);\n};\n_s(BaseChat, \"XjpiMRuQUoOxvZGXxaMsIOhU7GA=\", false, function() {\n    return [\n        _stores_chatSlice__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        _stores_fileStore__WEBPACK_IMPORTED_MODULE_4__.useFileStore,\n        _stores_userSlice__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        _UserModal__WEBPACK_IMPORTED_MODULE_11__.useLimitModalStore,\n        _stores_chatModeSlice__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        ai_react__WEBPACK_IMPORTED_MODULE_14__.useChat\n    ];\n});\n_c = BaseChat;\nvar _c;\n$RefreshReg$(_c, \"BaseChat\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AiChat/chat/index.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/messageParserNew.ts":
/*!***************************************!*\
  !*** ./src/utils/messageParserNew.ts ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StreamingMessageParser: function() { return /* binding */ StreamingMessageParser; },\n/* harmony export */   parseMessages: function() { return /* binding */ parseMessages; }\n/* harmony export */ });\n/* harmony import */ var _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/stores/fileStore */ \"(app-pages-browser)/./src/stores/fileStore.ts\");\n/* __next_internal_client_entry_do_not_use__ StreamingMessageParser,parseMessages auto */ \nclass StreamingMessageParser {\n    parse(messageId, input) {\n        console.log(\"\\uD83D\\uDD0D Parsing message \".concat(messageId, \" with content length: \").concat(input.length));\n        console.log(\"\\uD83D\\uDCC4 Content preview:\", input.substring(0, 200) + \"...\");\n        let state = this.messages.get(messageId);\n        if (!state) {\n            state = {\n                position: 0,\n                insideAction: false,\n                insideArtifact: false,\n                currentAction: {\n                    content: \"\"\n                },\n                actionId: 0,\n                hasInstallExecuted: false\n            };\n            this.messages.set(messageId, state);\n        }\n        let output = \"\";\n        const regex = {\n            artifactOpen: /<boltArtifact[^>]*>/g,\n            artifactClose: /<\\/boltArtifact>/g,\n            actionOpen: /<boltAction[^>]*>/g,\n            actionClose: /<\\/boltAction>/g\n        };\n        const allActionData = {};\n        while(state.position < input.length){\n            if (state.insideArtifact) {\n                if (state.insideAction) {\n                    // 查找动作结束标签\n                    regex.actionClose.lastIndex = state.position;\n                    const actionCloseMatch = regex.actionClose.exec(input);\n                    if (actionCloseMatch) {\n                        const content = input.slice(state.position, actionCloseMatch.index);\n                        // 处理 file 和 shell 类型的 action\n                        if (\"type\" in state.currentAction) {\n                            const actionData = {\n                                artifactId: state.currentArtifact.id,\n                                messageId,\n                                actionId: String(state.actionId - 1),\n                                action: {\n                                    ...state.currentAction,\n                                    content\n                                }\n                            };\n                            console.log(\"\\uD83D\\uDCE6 Found complete action:\", actionData);\n                            // 根据 action 类型调用不同的回调\n                            if (state.currentAction.type === \"file\") {\n                                var // Call onActionStream for file creation\n                                _this_options_callbacks_onActionStream, _this_options_callbacks;\n                                allActionData[state.currentAction.filePath] = actionData;\n                                (_this_options_callbacks = this.options.callbacks) === null || _this_options_callbacks === void 0 ? void 0 : (_this_options_callbacks_onActionStream = _this_options_callbacks.onActionStream) === null || _this_options_callbacks_onActionStream === void 0 ? void 0 : _this_options_callbacks_onActionStream.call(_this_options_callbacks, actionData);\n                            } else if (state.currentAction.type === \"shell\" || state.currentAction.type === \"start\") {\n                                var // shell 类型只在关闭时处理\n                                _this_options_callbacks_onActionClose, _this_options_callbacks1;\n                                (_this_options_callbacks1 = this.options.callbacks) === null || _this_options_callbacks1 === void 0 ? void 0 : (_this_options_callbacks_onActionClose = _this_options_callbacks1.onActionClose) === null || _this_options_callbacks_onActionClose === void 0 ? void 0 : _this_options_callbacks_onActionClose.call(_this_options_callbacks1, actionData);\n                            }\n                        }\n                        state.position = actionCloseMatch.index + actionCloseMatch[0].length;\n                        state.insideAction = false;\n                    } else {\n                        // 只对 file 类型进行流式处理\n                        const remainingContent = input.slice(state.position);\n                        if (\"type\" in state.currentAction && state.currentAction.type === \"file\" && !allActionData[state.currentAction.filePath]) {\n                            var // Call onActionStream for streaming file content\n                            _this_options_callbacks_onActionStream1, _this_options_callbacks2;\n                            allActionData[state.currentAction.filePath] = {\n                                artifactId: state.currentArtifact.id,\n                                messageId,\n                                actionId: String(state.actionId - 1),\n                                action: {\n                                    ...state.currentAction,\n                                    content: remainingContent,\n                                    filePath: state.currentAction.filePath\n                                }\n                            };\n                            console.log(\"\\uD83D\\uDCE6 Found streaming action:\", allActionData[state.currentAction.filePath]);\n                            (_this_options_callbacks2 = this.options.callbacks) === null || _this_options_callbacks2 === void 0 ? void 0 : (_this_options_callbacks_onActionStream1 = _this_options_callbacks2.onActionStream) === null || _this_options_callbacks_onActionStream1 === void 0 ? void 0 : _this_options_callbacks_onActionStream1.call(_this_options_callbacks2, allActionData[state.currentAction.filePath]);\n                        }\n                        break;\n                    }\n                } else {\n                    // 查找下一个动作开始标签或者 artifact 结束标签\n                    const nextActionMatch = regex.actionOpen.exec(input.slice(state.position));\n                    const artifactCloseMatch = regex.artifactClose.exec(input.slice(state.position));\n                    if (nextActionMatch && (!artifactCloseMatch || nextActionMatch.index < artifactCloseMatch.index)) {\n                        var _this_options_callbacks_onActionOpen, _this_options_callbacks3;\n                        const actionTag = nextActionMatch[0];\n                        state.currentAction = this.parseActionTag(actionTag);\n                        state.insideAction = true;\n                        state.position += nextActionMatch.index + nextActionMatch[0].length;\n                        console.log(\"\\uD83D\\uDE80 Found action tag:\", actionTag, state.currentAction);\n                        (_this_options_callbacks3 = this.options.callbacks) === null || _this_options_callbacks3 === void 0 ? void 0 : (_this_options_callbacks_onActionOpen = _this_options_callbacks3.onActionOpen) === null || _this_options_callbacks_onActionOpen === void 0 ? void 0 : _this_options_callbacks_onActionOpen.call(_this_options_callbacks3, {\n                            artifactId: state.currentArtifact.id,\n                            messageId,\n                            actionId: String(state.actionId++),\n                            action: state.currentAction\n                        });\n                    } else if (artifactCloseMatch) {\n                        var _this_options_callbacks_onArtifactClose, _this_options_callbacks4;\n                        state.position += artifactCloseMatch.index + artifactCloseMatch[0].length;\n                        state.insideArtifact = false;\n                        (_this_options_callbacks4 = this.options.callbacks) === null || _this_options_callbacks4 === void 0 ? void 0 : (_this_options_callbacks_onArtifactClose = _this_options_callbacks4.onArtifactClose) === null || _this_options_callbacks_onArtifactClose === void 0 ? void 0 : _this_options_callbacks_onArtifactClose.call(_this_options_callbacks4, {\n                            messageId,\n                            ...state.currentArtifact\n                        });\n                    } else {\n                        break;\n                    }\n                }\n            } else {\n                // 查找 artifact 开始标签\n                const artifactMatch = regex.artifactOpen.exec(input.slice(state.position));\n                if (artifactMatch) {\n                    output += input.slice(state.position, state.position + artifactMatch.index);\n                    const artifactTag = artifactMatch[0];\n                    const artifactTitle = this.extractAttribute(artifactTag, \"title\");\n                    const artifactId = this.extractAttribute(artifactTag, \"id\");\n                    state.currentArtifact = {\n                        id: artifactId || \"default\",\n                        title: artifactTitle || \"Untitled\"\n                    };\n                    console.log(\"\\uD83C\\uDFAF Found artifact:\", state.currentArtifact);\n                    state.insideArtifact = true;\n                    state.position += artifactMatch.index + artifactMatch[0].length;\n                } else {\n                    output += input.slice(state.position);\n                    break;\n                }\n            }\n        }\n        return output;\n    }\n    parseActionTag(tag) {\n        const typeMatch = tag.match(/type=\"([^\"]+)\"/);\n        const filePathMatch = tag.match(/filePath=\"([^\"]+)\"/);\n        return {\n            type: typeMatch === null || typeMatch === void 0 ? void 0 : typeMatch[1],\n            filePath: filePathMatch === null || filePathMatch === void 0 ? void 0 : filePathMatch[1],\n            content: \"\"\n        };\n    }\n    extractAttribute(tag, attribute) {\n        const match = tag.match(new RegExp(\"\".concat(attribute, '=\"([^\"]+)\"')));\n        return match ? match[1] : null;\n    }\n    constructor(options = {}){\n        this.options = options;\n        this.messages = new Map();\n        this.isUseStartCommand = false;\n    }\n}\n// Create file with content function (equivalent to we-dev-client's createFileWithContent)\nconst createFileWithContent = async (filePath, content, syncFileClose)=>{\n    const { addFile } = _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__.useFileStore.getState();\n    console.log(\"\\uD83D\\uDCC1 Creating file: \".concat(filePath));\n    console.log(\"\\uD83D\\uDCC4 Content preview: \".concat(content.substring(0, 100), \"...\"));\n    await addFile(filePath, content, syncFileClose);\n    console.log(\"✅ File created successfully: \".concat(filePath));\n    return filePath;\n};\n// Create a global message parser instance with the working callback\nconst messageParser = new StreamingMessageParser({\n    callbacks: {\n        onActionStream: async (data)=>{\n            console.log(\"\\uD83D\\uDD25 onActionStream called with:\", data);\n            const action = data.action;\n            if (action.type === \"file\" && action.filePath && action.content) {\n                await createFileWithContent(action.filePath, action.content, true);\n                console.log(\"✅ File created via onActionStream: \".concat(action.filePath));\n            }\n        }\n    }\n});\nconst parseMessages = async (messages)=>{\n    console.log(\"\\uD83D\\uDE80 parseMessages called with \".concat(messages.length, \" messages\"));\n    for (const message of messages){\n        if (message.role === \"assistant\") {\n            console.log(\"\\uD83E\\uDD16 Parsing assistant message: \".concat(message.id));\n            console.log(\"\\uD83D\\uDCDD Message content:\", message.content);\n            messageParser.parse(message.id, message.content);\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/messageParserNew.ts\n"));

/***/ })

});