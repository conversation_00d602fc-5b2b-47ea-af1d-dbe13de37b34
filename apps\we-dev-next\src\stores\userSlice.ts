'use client';

import { create } from "zustand"
import { persist } from "zustand/middleware"

export enum TierType {
  FREE = "free",
  PRO = "pro",
  PROMAX = "promax",
}

export interface TierMessage {
  startTime: Date
  tier: TierType
  resetTime: Date
}

export interface User {
  id: string
  username: string
  error?: any
  email: string
  githubId: string
  wechatId: string
  avatar?: string
  userQuota: {
    quota: number
    resetTime: Date
    tierType: TierType
    refillQuota: number
    usedQuota: number
    quotaTotal: number
  }
}

interface UserState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  rememberMe: boolean
  isLoginModalOpen: boolean
  setRememberMe: (remember: boolean) => void
  setUser: (user: User | null) => void
  setToken: (token: string | null) => void
  login: (user: User, token: string) => void
  logout: () => void
  updateUser: (userData: Partial<User>) => void
  openLoginModal: () => void
  closeLoginModal: () => void
  fetchUser: () => Promise<User | undefined>
  isLoading: boolean
}

const useUserStore = create<UserState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      rememberMe: false,
      isLoginModalOpen: false,
      isLoading: false,

      setRememberMe: (remember) => {
        if (typeof window !== 'undefined') {
          localStorage.setItem("rememberMe", remember.toString())
        }
        set({ rememberMe: remember })
      },

      setUser: (user) => {
        if (typeof window !== 'undefined') {
          if (user) {
            localStorage.setItem("user", JSON.stringify(user))
          } else {
            localStorage.removeItem("user")
          }
        }

        set(() => ({
          user,
          isAuthenticated: !!user,
        }))
      },

      setToken: (token) => {
        if (typeof window !== 'undefined') {
          if (token) {
            localStorage.setItem("token", token)
          } else {
            localStorage.removeItem("token")
          }
        }
        set(() => ({ token }))
      },

      fetchUser: async () => {
        set(() => ({ isLoading: true }))
        try {
          if (typeof window !== 'undefined') {
            const token = localStorage.getItem("token")
            if (token) {
              // TODO: Implement API call to get user info
              // const user = await authService.getUserInfo(token)
              // For now, return undefined
              return undefined
            }
          }
        } catch (error) {
          console.error(error)
        } finally {
          set(() => ({ isLoading: false }))
        }
      },

      login: (user, token) => {
        if (typeof window !== 'undefined') {
          localStorage.setItem("user", JSON.stringify(user))
          localStorage.setItem("token", token)
        }

        set(() => ({
          user,
          token,
          isAuthenticated: true,
          isLoginModalOpen: false,
        }))
      },

      logout: () => {
        if (typeof window !== 'undefined') {
          localStorage.removeItem("user")
          localStorage.removeItem("token")
          localStorage.removeItem("rememberMe")
          localStorage.removeItem("user-storage")
          
          document.cookie = "token=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/;"
          if (process.env.NODE_ENV === "production") {
            document.cookie = "token=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; secure=true;"
          }
          fetch('/api/logout').catch(() => {})
        }
        
        set(() => ({
          user: null,
          token: null,
          isAuthenticated: false,
          rememberMe: false,
        }))
      },

      updateUser: (userData) =>
        set((state) => {
          const newUser = state.user ? { ...state.user, ...userData } : null
          if (typeof window !== 'undefined' && newUser) {
            localStorage.setItem("user", JSON.stringify(newUser))
          }
          return { user: newUser }
        }),

      openLoginModal: () =>
        set(() => ({
          isLoginModalOpen: true,
        })),

      closeLoginModal: () =>
        set(() => ({
          isLoginModalOpen: false,
        })),
    }),
    {
      name: "user-storage",
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
        rememberMe: state.rememberMe,
      }),
      version: 1,
      onRehydrateStorage: () => (state) => {
        if (typeof window !== 'undefined') {
          const rememberMe = localStorage.getItem("rememberMe") === "true"
          if (rememberMe) {
            const storedUser = localStorage.getItem("user")
            const storedToken = localStorage.getItem("token")
            if (storedUser && storedToken) {
              state?.setUser(JSON.parse(storedUser))
              state?.setToken(storedToken)
              state?.setRememberMe(true)
            }
          }
        }
      },
    }
  )
)

export default useUserStore
