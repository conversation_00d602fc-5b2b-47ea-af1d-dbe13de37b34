/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[2].use[1]!./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[14].oneOf[2].use[2]!./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Inter","arguments":[{"subsets":["latin"]}],"variableName":"inter"} ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/55c55f0601d81cf3-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/26a46d62cd723877-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/97e0cb1ae144a2a9-s.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/581909926a08bbc8-s.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/df0a9ae256c0569c-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/8e9860b6e62d6359-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/e4af272ccee01ff0-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: '__Inter_Fallback_e8ce0c';src: local("Arial");ascent-override: 90.49%;descent-override: 22.56%;line-gap-override: 0.00%;size-adjust: 107.06%
}.__className_e8ce0c {font-family: '__Inter_e8ce0c', '__Inter_Fallback_e8ce0c';font-style: normal
}

/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/app/globals.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@layer tailwind-base, antd;

@layer tailwind-base {
  *, ::before, ::after{
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x:  ;
    --tw-pan-y:  ;
    --tw-pinch-zoom:  ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position:  ;
    --tw-gradient-via-position:  ;
    --tw-gradient-to-position:  ;
    --tw-ordinal:  ;
    --tw-slashed-zero:  ;
    --tw-numeric-figure:  ;
    --tw-numeric-spacing:  ;
    --tw-numeric-fraction:  ;
    --tw-ring-inset:  ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur:  ;
    --tw-brightness:  ;
    --tw-contrast:  ;
    --tw-grayscale:  ;
    --tw-hue-rotate:  ;
    --tw-invert:  ;
    --tw-saturate:  ;
    --tw-sepia:  ;
    --tw-drop-shadow:  ;
    --tw-backdrop-blur:  ;
    --tw-backdrop-brightness:  ;
    --tw-backdrop-contrast:  ;
    --tw-backdrop-grayscale:  ;
    --tw-backdrop-hue-rotate:  ;
    --tw-backdrop-invert:  ;
    --tw-backdrop-opacity:  ;
    --tw-backdrop-saturate:  ;
    --tw-backdrop-sepia:  ;
    --tw-contain-size:  ;
    --tw-contain-layout:  ;
    --tw-contain-paint:  ;
    --tw-contain-style:  ;
  }
  ::backdrop{
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x:  ;
    --tw-pan-y:  ;
    --tw-pinch-zoom:  ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position:  ;
    --tw-gradient-via-position:  ;
    --tw-gradient-to-position:  ;
    --tw-ordinal:  ;
    --tw-slashed-zero:  ;
    --tw-numeric-figure:  ;
    --tw-numeric-spacing:  ;
    --tw-numeric-fraction:  ;
    --tw-ring-inset:  ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur:  ;
    --tw-brightness:  ;
    --tw-contrast:  ;
    --tw-grayscale:  ;
    --tw-hue-rotate:  ;
    --tw-invert:  ;
    --tw-saturate:  ;
    --tw-sepia:  ;
    --tw-drop-shadow:  ;
    --tw-backdrop-blur:  ;
    --tw-backdrop-brightness:  ;
    --tw-backdrop-contrast:  ;
    --tw-backdrop-grayscale:  ;
    --tw-backdrop-hue-rotate:  ;
    --tw-backdrop-invert:  ;
    --tw-backdrop-opacity:  ;
    --tw-backdrop-saturate:  ;
    --tw-backdrop-sepia:  ;
    --tw-contain-size:  ;
    --tw-contain-layout:  ;
    --tw-contain-paint:  ;
    --tw-contain-style:  ;
  }
  /*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*//*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: var(--font-sans), ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}
  :root{
    --fd-background: 0 0% 96%;
    --fd-foreground: 0 0% 3.9%;
    --fd-muted: 0 0% 96.1%;
    --fd-muted-foreground: 0 0% 45.1%;
    --fd-popover: 0 0% 98%;
    --fd-popover-foreground: 0 0% 15.1%;
    --fd-card: 0 0% 94.7%;
    --fd-card-foreground: 0 0% 3.9%;
    --fd-border: 0 0% 89.8%;
    --fd-primary: 0 0% 9%;
    --fd-primary-foreground: 0 0% 98%;
    --fd-secondary: 0 0% 93.1%;
    --fd-secondary-foreground: 0 0% 9%;
    --fd-accent: 0 0% 90.1%;
    --fd-accent-foreground: 0 0% 9%;
    --fd-ring: 0 0% 63.9%;
    --fd-sidebar-width: 0px;
    --fd-toc-width: 0px;
    --fd-layout-width: 100vw;
    --fd-banner-height: 0px;
    --fd-nav-height: 0px;
    --fd-tocnav-height: 0px;
    --fd-diff-remove-color: rgba(200,10,100,0.12);
    --fd-diff-remove-symbol-color: rgb(230,10,100);
    --fd-diff-add-color: rgba(14,180,100,0.12);
    --fd-diff-add-symbol-color: rgb(10,200,100);
  }
  .dark{
    --fd-background: 0 0% 8.04%;
    --fd-foreground: 0 0% 92%;
    --fd-muted: 0 0% 12.9%;
    --fd-muted-foreground: 0 0% 60.9%;
    --fd-popover: 0 0% 9.8%;
    --fd-popover-foreground: 0 0% 88%;
    --fd-card: 0 0% 9.8%;
    --fd-card-foreground: 0 0% 98%;
    --fd-border: 0 0% 14%;
    --fd-primary: 0 0% 98%;
    --fd-primary-foreground: 0 0% 9%;
    --fd-secondary: 0 0% 12.9%;
    --fd-secondary-foreground: 0 0% 98%;
    --fd-accent: 0 0% 16.9%;
    --fd-accent-foreground: 0 0% 90%;
    --fd-ring: 0 0% 14.9%;
  }
  *{
    border-color: hsl(var(--fd-border) / 1);
  }
  [data-rmiz-modal-overlay='visible']{
    background-color: hsl(var(--fd-background) / 1);
  }
  body{
    background-color: hsl(var(--fd-background) / 1);
    color: hsl(var(--fd-foreground) / 1);
  }
  .shiki code span{
    color: var(--shiki-light);
  }
  .dark .shiki code span{
    color: var(--shiki-dark);
  }
  .fd-codeblock code{
    display: grid;
    font-size: 13px;
  }
  .shiki code .diff.remove{
    background-color: var(--fd-diff-remove-color);
    opacity: 0.7;
  }
  .shiki code .diff::before{
    position: absolute;
    left: 6px;
  }
  .shiki code .diff.remove::before{
    content: '-';
    color: var(--fd-diff-remove-symbol-color);
  }
  .shiki code .diff.add{
    background-color: var(--fd-diff-add-color);
  }
  .shiki code .diff.add::before{
    content: '+';
    color: var(--fd-diff-add-symbol-color);
  }
  .shiki code .diff{
    margin: 0 -16px;
    padding: 0 16px;
    position: relative;
  }
  .shiki .highlighted{
    margin: 0 -16px;
    padding: 0 16px;
    background-color: hsl(var(--fd-primary) / 10%);
  }
  .shiki .highlighted-word{
    padding: 1px 2px;
    margin: -1px -3px;
    border: 1px solid;
    border-color: hsl(var(--fd-primary) / 50%);
    background-color: hsl(var(--fd-primary) / 10%);
    border-radius: 2px;
  }
  :root {
    --background: 0 0% 100%;
    --foreground: 224 71.4% 4.1%;
    --card: 0 0% 100%;
    --card-foreground: 224 71.4% 4.1%;
    --popover: 0 0% 100%;
    --popover-foreground: 224 71.4% 4.1%;
    --primary: 262.1 83.3% 57.8%;
    --primary-foreground: 210 20% 98%;
    --secondary: 220 14.3% 95.9%;
    --secondary-foreground: 220.9 39.3% 11%;
    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;
    --accent: 220 14.3% 95.9%;
    --accent-foreground: 220.9 39.3% 11%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 20% 98%;
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 262.1 83.3% 57.8%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 224 71.4% 4.1%;
    --foreground: 210 20% 98%;
    --card: 224 71.4% 4.1%;
    --card-foreground: 210 20% 98%;
    --popover: 224 71.4% 4.1%;
    --popover-foreground: 210 20% 98%;
    --primary: 263.4 70% 50.4%;
    --primary-foreground: 210 20% 98%;
    --secondary: 215 27.9% 16.9%;
    --secondary-foreground: 210 20% 98%;
    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;
    --accent: 215 27.9% 16.9%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;
    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --ring: 263.4 70% 50.4%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
  *{
    border-color: hsl(var(--border));
  }
  body{
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  *{
    border-color: hsl(var(--border));
  }
  body{
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }
}
.container{
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 1rem;
  padding-left: 1rem;
}
@media (min-width: 1400px){

  .container{
    max-width: 1400px;
  }
}
.prose{
  color: var(--tw-prose-body);
  max-width: none;
  font-size: 1rem;
  line-height: 1.75;
}
.prose :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  font-size: 1.25em;
  line-height: 1.6;
  margin-top: 1.2em;
  margin-bottom: 1.2em;
  color: var(--tw-prose-lead);
}
.prose :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  padding-inline-start: 1rem;
  list-style-type: disc;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}
.prose :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}
.prose :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  padding-inline-start: 0.375em;
}
.prose :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  padding-inline-start: 0;
}
.prose :where(.prose > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}
.prose :where(.prose > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  margin-top: 1.25em;
}
.prose :where(.prose > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  margin-bottom: 1.25em;
}
.prose :where(.prose > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  margin-top: 1.25em;
}
.prose :where(.prose > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  margin-bottom: 1.25em;
}
.prose :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}
.prose :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}
.prose :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  color: var(--tw-prose-headings);
  font-weight: 600;
  margin-top: 1.25em;
}
.prose :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  margin-top: 0.5em;
  padding-inline-start: 1.625em;
}
.prose :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  border-color: var(--tw-prose-hr);
  border-top-width: 1px;
  margin-top: 3em;
  margin-bottom: 3em;
}
.prose :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}
.prose :where(strong):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  color: var(--tw-prose-bold);
  font-weight: 600;
}
.prose :where(a strong):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  color: inherit;
}
.prose :where(blockquote strong):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  color: inherit;
}
.prose :where(thead th strong):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  color: inherit;
}
.prose :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  list-style-type: decimal;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-inline-start: 1.625em;
}
.prose :where(ol[type="A"]):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  list-style-type: upper-alpha;
}
.prose :where(ol[type="a"]):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  list-style-type: lower-alpha;
}
.prose :where(ol[type="A" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  list-style-type: upper-alpha;
}
.prose :where(ol[type="a" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  list-style-type: lower-alpha;
}
.prose :where(ol[type="I"]):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  list-style-type: upper-roman;
}
.prose :where(ol[type="i"]):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  list-style-type: lower-roman;
}
.prose :where(ol[type="I" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  list-style-type: upper-roman;
}
.prose :where(ol[type="i" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  list-style-type: lower-roman;
}
.prose :where(ol[type="1"]):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  list-style-type: decimal;
}
.prose :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker{
  font-weight: 400;
  color: var(--tw-prose-counters);
}
.prose :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker{
  color: var(--tw-prose-bullets);
}
.prose :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  margin-top: 1.6em;
  margin-bottom: 1.6em;
  padding-inline-start: 1em;
  font-weight: 500;
  font-style: italic;
  color: var(--tw-prose-quotes);
  border-inline-start-width: 0.25rem;
  border-inline-start-color: var(--tw-prose-quote-borders);
  quotes: "\201C""\201D""\2018""\2019";
}
.prose :where(blockquote p:first-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::before{
  content: open-quote;
}
.prose :where(blockquote p:last-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::after{
  content: close-quote;
}
.prose :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  color: var(--tw-prose-headings);
  font-weight: 800;
  font-size: 2.25em;
  margin-top: 0;
  margin-bottom: 0.8888889em;
  line-height: 1.1111111;
}
.prose :where(h1 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  font-weight: 900;
  color: inherit;
}
.prose :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  color: var(--tw-prose-headings);
  font-size: 1.5em;
  margin-top: 2em;
  margin-bottom: 1em;
  line-height: 1.3333333;
  font-weight: 600;
}
.prose :where(h2 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  font-weight: 800;
  color: inherit;
}
.prose :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  color: var(--tw-prose-headings);
  font-weight: 600;
  font-size: 1.25em;
  margin-top: 1.6em;
  margin-bottom: 0.6em;
  line-height: 1.6;
}
.prose :where(h3 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  font-weight: 700;
  color: inherit;
}
.prose :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  color: var(--tw-prose-headings);
  font-weight: 600;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  line-height: 1.5;
}
.prose :where(h4 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  font-weight: 700;
  color: inherit;
}
.prose :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  margin-top: 0;
}
.prose :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  margin-top: 0;
}
.prose :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  margin-top: 0;
}
.prose :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  margin-top: 0;
}
.prose :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  margin-top: 2em;
  margin-bottom: 2em;
}
.prose :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  display: block;
  margin-top: 2em;
  margin-bottom: 2em;
}
.prose :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  margin-top: 0;
  margin-bottom: 0;
}
.prose :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  margin-top: 2em;
  margin-bottom: 2em;
}
.prose :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  font-size: 0.875em;
  border-radius: 0.3125rem;
  padding-top: 0.1875em;
  padding-inline-end: 0.375em;
  padding-bottom: 0.1875em;
  padding-inline-start: 0.375em;
  font-weight: 500;
  font-family: inherit;
  color: var(--tw-prose-kbd);
  box-shadow: 0 0 0 1px var(--tw-prose-kbd-shadows),0 3px 0 var(--tw-prose-kbd-shadows);
}
.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  padding: 3px;
  border: solid 1px;
  font-size: 13px;
  border-color: hsl(var(--fd-border) / 1);
  border-radius: 5px;
  font-weight: 400;
  background: hsl(var(--fd-muted) / 1);
  color: var(--tw-prose-code);
}
.prose :where(a code):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  color: inherit;
}
.prose :where(h1 code):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  color: inherit;
}
.prose :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  color: inherit;
  font-size: 0.875em;
}
.prose :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  color: inherit;
  font-size: 0.9em;
}
.prose :where(h4 code):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  color: inherit;
}
.prose :where(blockquote code):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  color: inherit;
}
.prose :where(thead th code):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  color: inherit;
}
.prose :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  font-size: 0.875em;
  line-height: 1.7142857;
  width: 100%;
  table-layout: auto;
  margin-top: 2em;
  margin-bottom: 2em;
  border-collapse: separate;
  border-spacing: 0;
  overflow: hidden;
  border-radius: var(--radius);
  border-width: 1px;
  --tw-bg-opacity: 1;
  background-color: hsl(var(--fd-card) / var(--tw-bg-opacity, 1));
}
.prose :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  color: var(--tw-prose-headings);
  font-weight: 600;
}
.prose :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  margin-top: 2em;
  margin-bottom: 2em;
}
.prose :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  margin-top: 0;
  margin-bottom: 0;
}
.prose :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  color: var(--tw-prose-captions);
  font-size: 0.875em;
  line-height: 1.4285714;
  margin-top: 0.8571429em;
}
.prose :where(a:not([data-card])):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  color: var(--tw-prose-links);
  transition: opacity 0.3s;
  font-weight: 400;
  text-decoration: underline;
  text-underline-offset: 2px;
  text-decoration-color: hsl(var(--fd-primary) / 1);
}
.prose :where(a:not([data-card]):hover):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  opacity: 80%;
}
.prose{
  --tw-prose-body: hsl(var(--fd-foreground) / 90%);
  --tw-prose-headings: hsl(var(--fd-foreground) / 1);
  --tw-prose-lead: hsl(var(--fd-foreground) / 1);
  --tw-prose-links: hsl(var(--fd-foreground) / 1);
  --tw-prose-bold: hsl(var(--fd-foreground) / 1);
  --tw-prose-counters: hsl(var(--fd-muted-foreground) / 1);
  --tw-prose-bullets: hsl(var(--fd-muted-foreground) / 1);
  --tw-prose-hr: hsl(var(--fd-border) / 1);
  --tw-prose-quotes: hsl(var(--fd-foreground) / 1);
  --tw-prose-quote-borders: hsl(var(--fd-border) / 1);
  --tw-prose-captions: hsl(var(--fd-foreground) / 1);
  --tw-prose-code: hsl(var(--fd-foreground) / 1);
  --tw-prose-th-borders: hsl(var(--fd-border) / 1);
  --tw-prose-td-borders: hsl(var(--fd-border) / 1);
  --tw-prose-kbd: hsl(var(--fd-foreground) / 1);
  --tw-prose-kbd-shadows: hsl(var(--fd-primary) / 50%);
}
.prose :where(.prose > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  margin-top: 0;
}
.prose :where(.prose > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  margin-bottom: 0;
}
.prose :where(th):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  text-align: start;
  border-inline-start-width: 1px;
  --tw-bg-opacity: 1;
  background-color: hsl(var(--fd-muted) / var(--tw-bg-opacity, 1));
  padding: 0.625rem;
}
.prose :where(th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  border-inline-start-width: 0px;
}
.prose :where(th:not(tr:last-child *), td:not(tr:last-child *)):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  border-bottom-width: 1px;
}
.prose :where(td):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  text-align: start;
  border-inline-start-width: 1px;
  padding: 0.625rem;
}
.prose :where(td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  border-inline-start-width: 0px;
}
.prose :where(tfoot th, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  border-top-width: 1px;
  border-top-color: var(--tw-prose-th-borders);
}
.prose :where(thead th, thead td):not(:where([class~="not-prose"],[class~="not-prose"] *)){
  border-bottom-width: 1px;
  border-bottom-color: var(--tw-prose-th-borders);
}
.dark #nd-sidebar{
  --muted: 0deg 0% 16%;
  --secondary: 0deg 0% 18%;
  --muted-foreground: 0 0% 72%;
}
.visible{
  visibility: visible;
}
.static{
  position: static;
}
.fixed{
  position: fixed;
}
.absolute{
  position: absolute;
}
.relative{
  position: relative;
}
.sticky{
  position: sticky;
}
.inset-0{
  inset: 0px;
}
.inset-x-0{
  left: 0px;
  right: 0px;
}
.inset-y-0{
  top: 0px;
  bottom: 0px;
}
.-right-1{
  right: -0.25rem;
}
.-top-1{
  top: -0.25rem;
}
.-top-1\.5{
  top: -0.375rem;
}
.bottom-1\.5{
  bottom: 0.375rem;
}
.bottom-2{
  bottom: 0.5rem;
}
.bottom-3{
  bottom: 0.75rem;
}
.end-2{
  inset-inline-end: 0.5rem;
}
.left-0{
  left: 0px;
}
.left-1\/2{
  left: 50%;
}
.left-2{
  left: 0.5rem;
}
.right-2{
  right: 0.5rem;
}
.start-0{
  inset-inline-start: 0px;
}
.top-0{
  top: 0px;
}
.top-1\.5{
  top: 0.375rem;
}
.top-1\/2{
  top: 50%;
}
.top-12{
  top: 3rem;
}
.top-2{
  top: 0.5rem;
}
.top-\[10vh\]{
  top: 10vh;
}
.top-\[var\(--fd-banner-height\)\]{
  top: var(--fd-banner-height);
}
.top-fd-layout-top{
  top: calc(var(--fd-banner-height) + var(--fd-nav-height));
}
.z-10{
  z-index: 10;
}
.z-30{
  z-index: 30;
}
.z-40{
  z-index: 40;
}
.z-50{
  z-index: 50;
}
.z-\[-1\]{
  z-index: -1;
}
.z-\[2\]{
  z-index: 2;
}
.col-start-2{
  grid-column-start: 2;
}
.m-1\.5{
  margin: 0.375rem;
}
.-mx-1\.5{
  margin-left: -0.375rem;
  margin-right: -0.375rem;
}
.-mx-2{
  margin-left: -0.5rem;
  margin-right: -0.5rem;
}
.mx-4{
  margin-left: 1rem;
  margin-right: 1rem;
}
.mx-auto{
  margin-left: auto;
  margin-right: auto;
}
.my-0{
  margin-top: 0px;
  margin-bottom: 0px;
}
.my-4{
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.my-6{
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}
.-mb-1{
  margin-bottom: -0.25rem;
}
.-mb-3{
  margin-bottom: -0.75rem;
}
.-me-1{
  margin-inline-end: -0.25rem;
}
.-me-1\.5{
  margin-inline-end: -0.375rem;
}
.-me-2{
  margin-inline-end: -0.5rem;
}
.-ms-0\.5{
  margin-inline-start: -0.125rem;
}
.-ms-1{
  margin-inline-start: -0.25rem;
}
.-ms-1\.5{
  margin-inline-start: -0.375rem;
}
.-ms-3{
  margin-inline-start: -0.75rem;
}
.mb-1{
  margin-bottom: 0.25rem;
}
.mb-1\.5{
  margin-bottom: 0.375rem;
}
.mb-2{
  margin-bottom: 0.5rem;
}
.mb-4{
  margin-bottom: 1rem;
}
.mb-6{
  margin-bottom: 1.5rem;
}
.mb-8{
  margin-bottom: 2rem;
}
.me-1\.5{
  margin-inline-end: 0.375rem;
}
.me-2{
  margin-inline-end: 0.5rem;
}
.ml-1{
  margin-left: 0.25rem;
}
.ml-1\.5{
  margin-left: 0.375rem;
}
.ml-2{
  margin-left: 0.5rem;
}
.ml-6{
  margin-left: 1.5rem;
}
.ml-\[-1px\]{
  margin-left: -1px;
}
.ml-auto{
  margin-left: auto;
}
.mr-2{
  margin-right: 0.5rem;
}
.ms-1{
  margin-inline-start: 0.25rem;
}
.ms-2{
  margin-inline-start: 0.5rem;
}
.ms-3{
  margin-inline-start: 0.75rem;
}
.ms-auto{
  margin-inline-start: auto;
}
.mt-1{
  margin-top: 0.25rem;
}
.mt-1\.5{
  margin-top: 0.375rem;
}
.mt-2{
  margin-top: 0.5rem;
}
.mt-8{
  margin-top: 2rem;
}
.mt-\[var\(--fd-nav-height\)\]{
  margin-top: var(--fd-nav-height);
}
.mt-\[var\(--fd-top\)\]{
  margin-top: var(--fd-top);
}
.mt-auto{
  margin-top: auto;
}
.box-content{
  box-sizing: content-box;
}
.block{
  display: block;
}
.\!inline{
  display: inline !important;
}
.inline{
  display: inline;
}
.flex{
  display: flex;
}
.inline-flex{
  display: inline-flex;
}
.table{
  display: table;
}
.grid{
  display: grid;
}
.contents{
  display: contents;
}
.hidden{
  display: none;
}
.size-3{
  width: 0.75rem;
  height: 0.75rem;
}
.size-3\.5{
  width: 0.875rem;
  height: 0.875rem;
}
.size-4{
  width: 1rem;
  height: 1rem;
}
.size-5{
  width: 1.25rem;
  height: 1.25rem;
}
.size-7{
  width: 1.75rem;
  height: 1.75rem;
}
.size-full{
  width: 100%;
  height: 100%;
}
.h-1\.5{
  height: 0.375rem;
}
.h-10{
  height: 2.5rem;
}
.h-12{
  height: 3rem;
}
.h-14{
  height: 3.5rem;
}
.h-16{
  height: 4rem;
}
.h-2{
  height: 0.5rem;
}
.h-3{
  height: 0.75rem;
}
.h-4{
  height: 1rem;
}
.h-5{
  height: 1.25rem;
}
.h-6{
  height: 1.5rem;
}
.h-8{
  height: 2rem;
}
.h-\[1px\]{
  height: 1px;
}
.h-\[var\(--fd-height\)\]{
  height: var(--fd-height);
}
.h-\[var\(--fd-toc-height\)\]{
  height: var(--fd-toc-height);
}
.h-\[var\(--radix-navigation-menu-viewport-height\)\]{
  height: var(--radix-navigation-menu-viewport-height);
}
.h-full{
  height: 100%;
}
.h-screen{
  height: 100vh;
}
.max-h-\[400px\]{
  max-height: 400px;
}
.max-h-\[460px\]{
  max-height: 460px;
}
.max-h-\[600px\]{
  max-height: 600px;
}
.max-h-\[calc\(100\%-48px\)\]{
  max-height: calc(100% - 48px);
}
.max-h-\[var\(--radix-popover-content-available-height\)\]{
  max-height: var(--radix-popover-content-available-height);
}
.min-h-0{
  min-height: 0px;
}
.min-h-10{
  min-height: 2.5rem;
}
.min-h-12{
  min-height: 3rem;
}
.w-0{
  width: 0px;
}
.w-1\.5{
  width: 0.375rem;
}
.w-1\/2{
  width: 50%;
}
.w-1\/4{
  width: 25%;
}
.w-10{
  width: 2.5rem;
}
.w-12{
  width: 3rem;
}
.w-16{
  width: 4rem;
}
.w-2{
  width: 0.5rem;
}
.w-3{
  width: 0.75rem;
}
.w-4{
  width: 1rem;
}
.w-5{
  width: 1.25rem;
}
.w-6{
  width: 1.5rem;
}
.w-64{
  width: 16rem;
}
.w-8{
  width: 2rem;
}
.w-\[1px\]{
  width: 1px;
}
.w-\[260px\]{
  width: 260px;
}
.w-\[30\%\]{
  width: 30%;
}
.w-\[45\%\]{
  width: 45%;
}
.w-\[98vw\]{
  width: 98vw;
}
.w-\[var\(--fd-toc-width\)\]{
  width: var(--fd-toc-width);
}
.w-\[var\(--radix-popover-trigger-width\)\]{
  width: var(--radix-popover-trigger-width);
}
.w-fit{
  width: -moz-fit-content;
  width: fit-content;
}
.w-full{
  width: 100%;
}
.w-px{
  width: 1px;
}
.w-screen{
  width: 100vw;
}
.min-w-0{
  min-width: 0px;
}
.min-w-\[220px\]{
  min-width: 220px;
}
.min-w-\[400px\]{
  min-width: 400px;
}
.max-w-2xl{
  max-width: 42rem;
}
.max-w-\[1120px\]{
  max-width: 1120px;
}
.max-w-\[200px\]{
  max-width: 200px;
}
.max-w-\[240px\]{
  max-width: 240px;
}
.max-w-\[400px\]{
  max-width: 400px;
}
.max-w-\[640px\]{
  max-width: 640px;
}
.max-w-\[80\%\]{
  max-width: 80%;
}
.max-w-\[860px\]{
  max-width: 860px;
}
.max-w-\[98vw\]{
  max-width: 98vw;
}
.max-w-fd-container{
  max-width: 1400px;
}
.max-w-full{
  max-width: 100%;
}
.max-w-md{
  max-width: 28rem;
}
.max-w-none{
  max-width: none;
}
.max-w-screen-sm{
  max-width: 640px;
}
.flex-1{
  flex: 1 1 0%;
}
.flex-shrink-0{
  flex-shrink: 0;
}
.shrink-0{
  flex-shrink: 0;
}
.grow{
  flex-grow: 1;
}
.origin-\[top_center\]{
  transform-origin: top center;
}
.origin-left{
  transform-origin: left;
}
.-translate-x-1\/2{
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/2{
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-0{
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-full{
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-rotate-90{
  --tw-rotate: -90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-0{
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform{
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes pulse{

  50%{
    opacity: .5;
  }
}
.animate-pulse{
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes spin{

  to{
    transform: rotate(360deg);
  }
}
.animate-spin{
  animation: spin 1s linear infinite;
}
.cursor-col-resize{
  cursor: col-resize;
}
.cursor-pointer{
  cursor: pointer;
}
.cursor-row-resize{
  cursor: row-resize;
}
.select-none{
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.resize-none{
  resize: none;
}
.scroll-m-20{
  scroll-margin: 5rem;
}
.scroll-m-28{
  scroll-margin: 7rem;
}
.list-none{
  list-style-type: none;
}
.grid-cols-1{
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2{
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.flex-row{
  flex-direction: row;
}
.flex-row-reverse{
  flex-direction: row-reverse;
}
.flex-col{
  flex-direction: column;
}
.flex-wrap{
  flex-wrap: wrap;
}
.items-end{
  align-items: flex-end;
}
.items-center{
  align-items: center;
}
.justify-start{
  justify-content: flex-start;
}
.justify-end{
  justify-content: flex-end;
}
.justify-center{
  justify-content: center;
}
.justify-between{
  justify-content: space-between;
}
.gap-0\.5{
  gap: 0.125rem;
}
.gap-1{
  gap: 0.25rem;
}
.gap-1\.5{
  gap: 0.375rem;
}
.gap-2{
  gap: 0.5rem;
}
.gap-2\.5{
  gap: 0.625rem;
}
.gap-3{
  gap: 0.75rem;
}
.gap-4{
  gap: 1rem;
}
.gap-6{
  gap: 1.5rem;
}
.space-y-1 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.divide-y > :not([hidden]) ~ :not([hidden]){
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}
.divide-fd-border > :not([hidden]) ~ :not([hidden]){
  --tw-divide-opacity: 1;
  border-color: hsl(var(--fd-border) / var(--tw-divide-opacity, 1));
}
.overflow-auto{
  overflow: auto;
}
.overflow-hidden{
  overflow: hidden;
}
.overflow-x-auto{
  overflow-x: auto;
}
.overflow-y-auto{
  overflow-y: auto;
}
.truncate{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.whitespace-nowrap{
  white-space: nowrap;
}
.whitespace-pre-wrap{
  white-space: pre-wrap;
}
.text-nowrap{
  text-wrap: nowrap;
}
.rounded{
  border-radius: 0.25rem;
}
.rounded-\[inherit\]{
  border-radius: inherit;
}
.rounded-full{
  border-radius: 9999px;
}
.rounded-lg{
  border-radius: var(--radius);
}
.rounded-md{
  border-radius: calc(var(--radius) - 2px);
}
.rounded-sm{
  border-radius: calc(var(--radius) - 4px);
}
.rounded-xl{
  border-radius: 0.75rem;
}
.rounded-t-lg{
  border-top-left-radius: var(--radius);
  border-top-right-radius: var(--radius);
}
.border{
  border-width: 1px;
}
.border-0{
  border-width: 0px;
}
.border-2{
  border-width: 2px;
}
.border-b{
  border-bottom-width: 1px;
}
.border-b-2{
  border-bottom-width: 2px;
}
.border-l{
  border-left-width: 1px;
}
.border-r{
  border-right-width: 1px;
}
.border-s{
  border-inline-start-width: 1px;
}
.border-s-0{
  border-inline-start-width: 0px;
}
.border-t{
  border-top-width: 1px;
}
.border-\[\#e4e4e4\]{
  --tw-border-opacity: 1;
  border-color: rgb(228 228 228 / var(--tw-border-opacity, 1));
}
.border-fd-foreground\/10{
  border-color: hsl(var(--fd-foreground) / 0.1);
}
.border-gray-100{
  --tw-border-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));
}
.border-gray-200{
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}
.border-gray-300{
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.border-gray-700{
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
}
.border-purple-600{
  --tw-border-opacity: 1;
  border-color: rgb(147 51 234 / var(--tw-border-opacity, 1));
}
.border-red-200{
  --tw-border-opacity: 1;
  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));
}
.border-transparent{
  border-color: transparent;
}
.border-white{
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.border-t-transparent{
  border-top-color: transparent;
}
.bg-\[\#18181a\]{
  --tw-bg-opacity: 1;
  background-color: rgb(24 24 26 / var(--tw-bg-opacity, 1));
}
.bg-\[\#e4e4e4\]{
  --tw-bg-opacity: 1;
  background-color: rgb(228 228 228 / var(--tw-bg-opacity, 1));
}
.bg-\[\#e6e6e6\]{
  --tw-bg-opacity: 1;
  background-color: rgb(230 230 230 / var(--tw-bg-opacity, 1));
}
.bg-\[\#f3f3f3\]{
  --tw-bg-opacity: 1;
  background-color: rgb(243 243 243 / var(--tw-bg-opacity, 1));
}
.bg-\[\#f8f8f8\]{
  --tw-bg-opacity: 1;
  background-color: rgb(248 248 248 / var(--tw-bg-opacity, 1));
}
.bg-\[\#ffffff\]{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-\[rgba\(255\2c 255\2c 255\)\]{
  --tw-bg-opacity: 1;
  background-color: rgba(255, 255, 255, var(--tw-bg-opacity, 1));
}
.bg-\[var\(--shiki-light-bg\)\]{
  background-color: var(--shiki-light-bg);
}
.bg-black{
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}
.bg-black\/30{
  background-color: rgb(0 0 0 / 0.3);
}
.bg-current{
  background-color: currentColor;
}
.bg-fd-accent{
  --tw-bg-opacity: 1;
  background-color: hsl(var(--fd-accent) / var(--tw-bg-opacity, 1));
}
.bg-fd-background{
  --tw-bg-opacity: 1;
  background-color: hsl(var(--fd-background) / var(--tw-bg-opacity, 1));
}
.bg-fd-background\/80{
  background-color: hsl(var(--fd-background) / 0.8);
}
.bg-fd-border{
  --tw-bg-opacity: 1;
  background-color: hsl(var(--fd-border) / var(--tw-bg-opacity, 1));
}
.bg-fd-card{
  --tw-bg-opacity: 1;
  background-color: hsl(var(--fd-card) / var(--tw-bg-opacity, 1));
}
.bg-fd-foreground\/10{
  background-color: hsl(var(--fd-foreground) / 0.1);
}
.bg-fd-muted{
  --tw-bg-opacity: 1;
  background-color: hsl(var(--fd-muted) / var(--tw-bg-opacity, 1));
}
.bg-fd-popover{
  --tw-bg-opacity: 1;
  background-color: hsl(var(--fd-popover) / var(--tw-bg-opacity, 1));
}
.bg-fd-primary{
  --tw-bg-opacity: 1;
  background-color: hsl(var(--fd-primary) / var(--tw-bg-opacity, 1));
}
.bg-fd-primary\/10{
  background-color: hsl(var(--fd-primary) / 0.1);
}
.bg-fd-secondary{
  --tw-bg-opacity: 1;
  background-color: hsl(var(--fd-secondary) / var(--tw-bg-opacity, 1));
}
.bg-fd-secondary\/50{
  background-color: hsl(var(--fd-secondary) / 0.5);
}
.bg-gray-100{
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.bg-gray-200{
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.bg-gray-50{
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.bg-gray-800{
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}
.bg-green-400{
  --tw-bg-opacity: 1;
  background-color: rgb(74 222 128 / var(--tw-bg-opacity, 1));
}
.bg-green-500{
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}
.bg-purple-500{
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));
}
.bg-purple-600{
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}
.bg-red-50{
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}
.bg-red-500{
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}
.bg-transparent{
  background-color: transparent;
}
.bg-white{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-yellow-200{
  --tw-bg-opacity: 1;
  background-color: rgb(254 240 138 / var(--tw-bg-opacity, 1));
}
.bg-yellow-500{
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}
.bg-opacity-50{
  --tw-bg-opacity: 0.5;
}
.bg-gradient-to-br{
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
.bg-gradient-to-r{
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.from-purple-500{
  --tw-gradient-from: #a855f7 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.to-purple-600{
  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);
}
.bg-clip-text{
  -webkit-background-clip: text;
          background-clip: text;
}
.fill-blue-500{
  fill: #3b82f6;
}
.fill-orange-500{
  fill: #f97316;
}
.fill-red-500{
  fill: #ef4444;
}
.stroke-fd-foreground\/10{
  stroke: hsl(var(--fd-foreground) / 0.1);
}
.object-cover{
  -o-object-fit: cover;
     object-fit: cover;
}
.p-0{
  padding: 0px;
}
.p-0\.5{
  padding: 0.125rem;
}
.p-1{
  padding: 0.25rem;
}
.p-1\.5{
  padding: 0.375rem;
}
.p-2{
  padding: 0.5rem;
}
.p-2\.5{
  padding: 0.625rem;
}
.p-3{
  padding: 0.75rem;
}
.p-4{
  padding: 1rem;
}
.p-6{
  padding: 1.5rem;
}
.p-\[3px\]{
  padding: 3px;
}
.px-1{
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.px-1\.5{
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}
.px-2{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\.5{
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-3{
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4{
  padding-left: 1rem;
  padding-right: 1rem;
}
.py-0\.5{
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}
.py-1{
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5{
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-12{
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.py-2{
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-2\.5{
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.py-3{
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-4{
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.pb-2{
  padding-bottom: 0.5rem;
}
.pb-4{
  padding-bottom: 1rem;
}
.pb-6{
  padding-bottom: 1.5rem;
}
.pe-4{
  padding-inline-end: 1rem;
}
.pe-\[var\(--fd-layout-offset\)\]{
  padding-inline-end: var(--fd-layout-offset);
}
.pl-0{
  padding-left: 0px;
}
.pl-7{
  padding-left: 1.75rem;
}
.pr-2{
  padding-right: 0.5rem;
}
.ps-1\.5{
  padding-inline-start: 0.375rem;
}
.ps-2{
  padding-inline-start: 0.5rem;
}
.ps-3\.5{
  padding-inline-start: 0.875rem;
}
.ps-6{
  padding-inline-start: 1.5rem;
}
.ps-8{
  padding-inline-start: 2rem;
}
.pt-0{
  padding-top: 0px;
}
.pt-1{
  padding-top: 0.25rem;
}
.pt-12{
  padding-top: 3rem;
}
.pt-14{
  padding-top: 3.5rem;
}
.pt-2{
  padding-top: 0.5rem;
}
.pt-4{
  padding-top: 1rem;
}
.pt-8{
  padding-top: 2rem;
}
.text-left{
  text-align: left;
}
.text-center{
  text-align: center;
}
.text-start{
  text-align: start;
}
.text-end{
  text-align: end;
}
.font-mono{
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}
.text-2xl{
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-3xl{
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-5xl{
  font-size: 3rem;
  line-height: 1;
}
.text-\[12px\]{
  font-size: 12px;
}
.text-\[13px\]{
  font-size: 13px;
}
.text-\[14px\]{
  font-size: 14px;
}
.text-\[18px\]{
  font-size: 18px;
}
.text-base{
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-lg{
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm{
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xl{
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs{
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-bold{
  font-weight: 700;
}
.font-medium{
  font-weight: 500;
}
.font-normal{
  font-weight: 400;
}
.font-semibold{
  font-weight: 600;
}
.italic{
  font-style: italic;
}
.text-\[\#333\]{
  --tw-text-opacity: 1;
  color: rgb(51 51 51 / var(--tw-text-opacity, 1));
}
.text-\[\#666\]{
  --tw-text-opacity: 1;
  color: rgb(102 102 102 / var(--tw-text-opacity, 1));
}
.text-blue-400{
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}
.text-blue-500{
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}
.text-blue-600{
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.text-blue-700{
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}
.text-cyan-500{
  --tw-text-opacity: 1;
  color: rgb(6 182 212 / var(--tw-text-opacity, 1));
}
.text-fd-accent-foreground{
  --tw-text-opacity: 1;
  color: hsl(var(--fd-accent-foreground) / var(--tw-text-opacity, 1));
}
.text-fd-card{
  --tw-text-opacity: 1;
  color: hsl(var(--fd-card) / var(--tw-text-opacity, 1));
}
.text-fd-card-foreground{
  --tw-text-opacity: 1;
  color: hsl(var(--fd-card-foreground) / var(--tw-text-opacity, 1));
}
.text-fd-foreground{
  --tw-text-opacity: 1;
  color: hsl(var(--fd-foreground) / var(--tw-text-opacity, 1));
}
.text-fd-muted-foreground{
  --tw-text-opacity: 1;
  color: hsl(var(--fd-muted-foreground) / var(--tw-text-opacity, 1));
}
.text-fd-popover-foreground{
  --tw-text-opacity: 1;
  color: hsl(var(--fd-popover-foreground) / var(--tw-text-opacity, 1));
}
.text-fd-primary{
  --tw-text-opacity: 1;
  color: hsl(var(--fd-primary) / var(--tw-text-opacity, 1));
}
.text-fd-secondary-foreground{
  --tw-text-opacity: 1;
  color: hsl(var(--fd-secondary-foreground) / var(--tw-text-opacity, 1));
}
.text-gray-100{
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity, 1));
}
.text-gray-300{
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}
.text-gray-400{
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.text-gray-500{
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.text-gray-600{
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.text-gray-700{
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.text-gray-900{
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.text-green-400{
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}
.text-green-500{
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}
.text-orange-500{
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity, 1));
}
.text-orange-600{
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity, 1));
}
.text-purple-500{
  --tw-text-opacity: 1;
  color: rgb(168 85 247 / var(--tw-text-opacity, 1));
}
.text-purple-600{
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}
.text-red-500{
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.text-red-600{
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}
.text-red-700{
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}
.text-transparent{
  color: transparent;
}
.text-white{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.text-yellow-500{
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity, 1));
}
.text-yellow-900{
  --tw-text-opacity: 1;
  color: rgb(113 63 18 / var(--tw-text-opacity, 1));
}
.underline{
  text-decoration-line: underline;
}
.placeholder-gray-400::-moz-placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}
.placeholder-gray-400::placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}
.placeholder-gray-500::-moz-placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));
}
.placeholder-gray-500::placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));
}
.caret-green-400{
  caret-color: #4ade80;
}
.accent-foreground{
  accent-color: hsl(var(--foreground));
}
.opacity-0{
  opacity: 0;
}
.opacity-100{
  opacity: 1;
}
.opacity-25{
  opacity: 0.25;
}
.opacity-50{
  opacity: 0.5;
}
.opacity-60{
  opacity: 0.6;
}
.opacity-70{
  opacity: 0.7;
}
.opacity-75{
  opacity: 0.75;
}
.opacity-90{
  opacity: 0.9;
}
.shadow-lg{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm{
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xl{
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.outline-none{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.outline{
  outline-style: solid;
}
.ring{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.filter{
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.backdrop-blur-lg{
  --tw-backdrop-blur: blur(16px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-md{
  --tw-backdrop-blur: blur(12px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-sm{
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.transition{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-\[width\2c height\]{
  transition-property: width,height;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all{
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-opacity{
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform{
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-100{
  transition-duration: 100ms;
}
.duration-200{
  transition-duration: 200ms;
}
.duration-300{
  transition-duration: 300ms;
}
.duration-500{
  transition-duration: 500ms;
}
.ease-in-out{
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-out{
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.steps{
  counter-reset: step;
  border-left-width: 1px;
  margin-left: 1rem;
  padding-left: 1.75rem;
  position: relative;
}
.step:before{
  background-color: hsl(var(--fd-secondary) / 1);
  color: hsl(var(--fd-secondary-foreground) / 1);
  content: counter(step);
  counter-increment: step;
  border-radius: 9999px;
  justify-content: center;
  align-items: center;
  width: 2rem;
  height: 2rem;
  font-size: .875rem;
  line-height: 1.25rem;
  display: flex;
  position: absolute;
  left: -1rem;
}
.prose-no-margin > :first-child{
  margin-top: 0;
}
.prose-no-margin > :last-child{
  margin-bottom: 0;
}
@keyframes enter{

  from{
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}
@keyframes exit{

  to{
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}
.duration-100{
  animation-duration: 100ms;
}
.duration-200{
  animation-duration: 200ms;
}
.duration-300{
  animation-duration: 300ms;
}
.duration-500{
  animation-duration: 500ms;
}
.ease-in-out{
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-out{
  animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.running{
  animation-play-state: running;
}
.\[--fd-nav-height\:3\.5rem\]{
  --fd-nav-height: 3.5rem;
}
.\[--fd-tocnav-height\:36px\]{
  --fd-tocnav-height: 36px;
}
.\[--radix-collapsible-content-height\:0px\]{
  --radix-collapsible-content-height: 0px;
}
.\[-ms-overflow-style\:none\]{
  -ms-overflow-style: none;
}
.\[overflow-wrap\:anywhere\]{
  overflow-wrap: anywhere;
}
.\[scrollbar-width\:none\]{
  scrollbar-width: none;
}

/* Global scrollbar styles */
* {
  /* Firefox */
  scrollbar-width: none;
  /* IE 10+ */
  -ms-overflow-style: none;
}

/* Webkit (Chrome, Safari, Edge) */
*::-webkit-scrollbar {
  display: none;
}

/* Global cursor styles */
input, textarea, [contenteditable="true"] {
  caret-color: #000000 !important;
}

.CodeMirror-cursor {
   border-left: 2px solid #ff0000 !important; /* Set cursor color to red, width to 2px */
}

/* Dark mode cursor styles */
@media (prefers-color-scheme: dark) {
  input, textarea, [contenteditable="true"] {
    caret-color: #ffffff;
  }
}

/* If using Tailwind's dark class to control dark mode */
.dark input,
.dark textarea,
.dark [contenteditable="true"] {
  caret-color: #ffffff;
}

/* Ant Design modal styles */
.ant-modal {
  top: unset;
  padding-bottom: unset;
}

.ant-modal-root .ant-modal-wrap {
  display: flex;
  justify-content: center;
  align-items: center;
}

:root {
  color-scheme: light;
}

html,
body {
  background-color: white;
  overflow-x: hidden;
  height: 100%;
}
.placeholder\:text-fd-muted-foreground::-moz-placeholder{
  --tw-text-opacity: 1;
  color: hsl(var(--fd-muted-foreground) / var(--tw-text-opacity, 1));
}
.placeholder\:text-fd-muted-foreground::placeholder{
  --tw-text-opacity: 1;
  color: hsl(var(--fd-muted-foreground) / var(--tw-text-opacity, 1));
}
.first\:mt-0:first-child{
  margin-top: 0px;
}
.first\:pt-0:first-child{
  padding-top: 0px;
}
.last\:pb-0:last-child{
  padding-bottom: 0px;
}
.empty\:hidden:empty{
  display: none;
}
.hover\:border-purple-300:hover{
  --tw-border-opacity: 1;
  border-color: rgb(216 180 254 / var(--tw-border-opacity, 1));
}
.hover\:bg-\[\#e4e4e4\]:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(228 228 228 / var(--tw-bg-opacity, 1));
}
.hover\:bg-\[\#e8e8e8\]:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(232 232 232 / var(--tw-bg-opacity, 1));
}
.hover\:bg-fd-accent:hover{
  --tw-bg-opacity: 1;
  background-color: hsl(var(--fd-accent) / var(--tw-bg-opacity, 1));
}
.hover\:bg-fd-accent\/50:hover{
  background-color: hsl(var(--fd-accent) / 0.5);
}
.hover\:bg-fd-accent\/80:hover{
  background-color: hsl(var(--fd-accent) / 0.8);
}
.hover\:bg-gray-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-50\/50:hover{
  background-color: rgb(249 250 251 / 0.5);
}
.hover\:bg-purple-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(126 34 206 / var(--tw-bg-opacity, 1));
}
.hover\:bg-red-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}
.hover\:text-fd-accent-foreground:hover{
  --tw-text-opacity: 1;
  color: hsl(var(--fd-accent-foreground) / var(--tw-text-opacity, 1));
}
.hover\:text-fd-accent-foreground\/80:hover{
  color: hsl(var(--fd-accent-foreground) / 0.8);
}
.hover\:text-fd-popover-foreground\/50:hover{
  color: hsl(var(--fd-popover-foreground) / 0.5);
}
.hover\:text-gray-600:hover{
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.hover\:text-gray-700:hover{
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.hover\:text-gray-900:hover{
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.hover\:text-red-700:hover{
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}
.hover\:opacity-80:hover{
  opacity: 0.8;
}
.hover\:shadow-md:hover{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:transition-none:hover{
  transition-property: none;
}
.focus\:bg-gray-50\/80:focus{
  background-color: rgb(249 250 251 / 0.8);
}
.focus\:outline-none:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus\:ring-1:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-2:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-purple-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(168 85 247 / var(--tw-ring-opacity, 1));
}
.focus-visible\:outline-none:focus-visible{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus-visible\:ring-2:focus-visible{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus-visible\:ring-fd-ring:focus-visible{
  --tw-ring-opacity: 1;
  --tw-ring-color: hsl(var(--fd-ring) / var(--tw-ring-opacity, 1));
}
.disabled\:pointer-events-none:disabled{
  pointer-events: none;
}
.disabled\:cursor-not-allowed:disabled{
  cursor: not-allowed;
}
.disabled\:bg-gray-300:disabled{
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}
.disabled\:bg-purple-400:disabled{
  --tw-bg-opacity: 1;
  background-color: rgb(192 132 252 / var(--tw-bg-opacity, 1));
}
.disabled\:opacity-50:disabled{
  opacity: 0.5;
}
.group:hover .group-hover\:text-gray-600{
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\:text-purple-600{
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\:opacity-100{
  opacity: 1;
}
.peer:hover ~ .peer-hover\:opacity-100{
  opacity: 1;
}
.data-\[collapsed\=false\]\:hidden[data-collapsed="false"]{
  display: none;
}
@keyframes fd-enterFromRight{

  from{
    opacity: 0;
    transform: translateX(200px);
  }

  to{
    opacity: 1;
    transform: translateX(0);
  }
}
.data-\[motion\=from-end\]\:animate-fd-enterFromRight[data-motion="from-end"]{
  animation: fd-enterFromRight 250ms ease;
}
@keyframes fd-enterFromLeft{

  from{
    opacity: 0;
    transform: translateX(-200px);
  }

  to{
    opacity: 1;
    transform: translateX(0);
  }
}
.data-\[motion\=from-start\]\:animate-fd-enterFromLeft[data-motion="from-start"]{
  animation: fd-enterFromLeft 250ms ease;
}
@keyframes fd-exitToRight{

  from{
    opacity: 1;
    transform: translateX(0);
  }

  to{
    opacity: 0;
    transform: translateX(200px);
  }
}
.data-\[motion\=to-end\]\:animate-fd-exitToRight[data-motion="to-end"]{
  animation: fd-exitToRight 250ms ease;
}
@keyframes fd-exitToLeft{

  from{
    opacity: 1;
    transform: translateX(0);
  }

  to{
    opacity: 0;
    transform: translateX(-200px);
  }
}
.data-\[motion\=to-start\]\:animate-fd-exitToLeft[data-motion="to-start"]{
  animation: fd-exitToLeft 250ms ease;
}
@keyframes fd-accordion-up{

  from{
    height: var(--radix-accordion-content-height);
  }

  to{
    height: 0;
    opacity: 0.5;
  }
}
.data-\[state\=closed\]\:animate-fd-accordion-up[data-state="closed"]{
  animation: fd-accordion-up 200ms ease-out;
}
@keyframes fd-collapsible-up{

  from{
    height: var(--radix-collapsible-content-height);
  }

  to{
    height: 0;
    opacity: 0;
  }
}
.data-\[state\=closed\]\:animate-fd-collapsible-up[data-state="closed"]{
  animation: fd-collapsible-up 150ms ease-out;
}
@keyframes fd-dialog-out{

  from{
    transform: scale(1) translate(-50%, 0);
  }

  to{
    transform: scale(0.95) translateY(-50%, 0);
    opacity: 0;
  }
}
.data-\[state\=closed\]\:animate-fd-dialog-out[data-state="closed"]{
  animation: fd-dialog-out 300ms cubic-bezier(0.32, 0.72, 0, 1);
}
@keyframes fd-fade-out{

  to{
    opacity: 0;
  }
}
.data-\[state\=closed\]\:animate-fd-fade-out[data-state="closed"]{
  animation: fd-fade-out 300ms ease;
}
@keyframes fd-nav-menu-out{

  from{
    opacity: 1;
    height: var(--radix-navigation-menu-viewport-height);
  }

  to{
    opacity: 0;
    height: 0px;
  }
}
.data-\[state\=closed\]\:animate-fd-nav-menu-out[data-state="closed"]{
  animation: fd-nav-menu-out 200ms ease;
}
@keyframes fd-popover-out{

  from{
    opacity: 1;
    transform: translateY(0);
  }

  to{
    opacity: 0;
    transform: translateY(-4px);
  }
}
.data-\[state\=closed\]\:animate-fd-popover-out[data-state="closed"]{
  animation: fd-popover-out 150ms ease;
}
@keyframes fd-fade-out{

  to{
    opacity: 0;
  }
}
.data-\[state\=hidden\]\:animate-fd-fade-out[data-state="hidden"]{
  animation: fd-fade-out 300ms ease;
}
@keyframes fd-accordion-down{

  from{
    height: 0;
    opacity: 0.5;
  }

  to{
    height: var(--radix-accordion-content-height);
  }
}
.data-\[state\=open\]\:animate-fd-accordion-down[data-state="open"]{
  animation: fd-accordion-down 200ms ease-out;
}
@keyframes fd-collapsible-down{

  from{
    height: 0;
    opacity: 0;
  }

  to{
    height: var(--radix-collapsible-content-height);
  }
}
.data-\[state\=open\]\:animate-fd-collapsible-down[data-state="open"]{
  animation: fd-collapsible-down 150ms ease-out;
}
@keyframes fd-dialog-in{

  from{
    transform: scale(0.95) translate(-50%, 0);
    opacity: 0;
  }

  to{
    transform: scale(1) translate(-50%, 0);
  }
}
.data-\[state\=open\]\:animate-fd-dialog-in[data-state="open"]{
  animation: fd-dialog-in 200ms cubic-bezier(0.32, 0.72, 0, 1);
}
@keyframes fd-fade-in{

  from{
    opacity: 0;
  }

  to{
    opacity: 1;
  }
}
.data-\[state\=open\]\:animate-fd-fade-in[data-state="open"]{
  animation: fd-fade-in 300ms ease;
}
@keyframes fd-nav-menu-in{

  from{
    opacity: 0;
    height: 0px;
  }

  to{
    opacity: 1;
    height: var(--radix-navigation-menu-viewport-height);
  }
}
.data-\[state\=open\]\:animate-fd-nav-menu-in[data-state="open"]{
  animation: fd-nav-menu-in 200ms ease;
}
@keyframes fd-popover-in{

  from{
    opacity: 0;
    transform: scale(0.98) translateY(-4px);
  }

  to{
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}
.data-\[state\=open\]\:animate-fd-popover-in[data-state="open"]{
  animation: fd-popover-in 150ms ease;
}
.data-\[state\=active\]\:border-fd-primary[data-state="active"]{
  --tw-border-opacity: 1;
  border-color: hsl(var(--fd-primary) / var(--tw-border-opacity, 1));
}
.data-\[state\=open\]\:bg-fd-accent\/50[data-state="open"]{
  background-color: hsl(var(--fd-accent) / 0.5);
}
.data-\[active\=true\]\:font-medium[data-active="true"]{
  font-weight: 500;
}
.data-\[active\=true\]\:text-fd-primary[data-active="true"]{
  --tw-text-opacity: 1;
  color: hsl(var(--fd-primary) / var(--tw-text-opacity, 1));
}
.data-\[state\=active\]\:text-fd-primary[data-state="active"]{
  --tw-text-opacity: 1;
  color: hsl(var(--fd-primary) / var(--tw-text-opacity, 1));
}
.group[data-state="closed"] .group-data-\[state\=closed\]\:-rotate-90{
  --tw-rotate: -90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group\/accordion[data-state="open"] .group-data-\[state\=open\]\/accordion\:rotate-90{
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group[data-state="open"] .group-data-\[state\=open\]\:rotate-180{
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.dark\:border-\[\#333333\]:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(51 51 51 / var(--tw-border-opacity, 1));
}
.dark\:border-\[\#333\]:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(51 51 51 / var(--tw-border-opacity, 1));
}
.dark\:border-gray-600:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
}
.dark\:border-gray-700:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
}
.dark\:border-gray-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(31 41 55 / var(--tw-border-opacity, 1));
}
.dark\:border-red-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(153 27 27 / var(--tw-border-opacity, 1));
}
.dark\:bg-\[\#111\]:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(17 17 17 / var(--tw-bg-opacity, 1));
}
.dark\:bg-\[\#18181a\]:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(24 24 26 / var(--tw-bg-opacity, 1));
}
.dark\:bg-\[\#1a1a1a\]:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(26 26 26 / var(--tw-bg-opacity, 1));
}
.dark\:bg-\[\#1e1e1e\]:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(30 30 30 / var(--tw-bg-opacity, 1));
}
.dark\:bg-\[\#333\]:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(51 51 51 / var(--tw-bg-opacity, 1));
}
.dark\:bg-\[var\(--shiki-dark-bg\)\]:is(.dark *){
  background-color: var(--shiki-dark-bg);
}
.dark\:bg-fd-accent:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: hsl(var(--fd-accent) / var(--tw-bg-opacity, 1));
}
.dark\:bg-gray-700:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}
.dark\:bg-gray-800:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}
.dark\:bg-gray-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}
.dark\:bg-purple-500:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));
}
.dark\:bg-purple-600:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}
.dark\:bg-red-900\/20:is(.dark *){
  background-color: rgb(127 29 29 / 0.2);
}
.dark\:bg-transparent:is(.dark *){
  background-color: transparent;
}
.dark\:bg-yellow-800:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(133 77 14 / var(--tw-bg-opacity, 1));
}
.dark\:from-blue-500:is(.dark *){
  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:to-purple-500:is(.dark *){
  --tw-gradient-to: #a855f7 var(--tw-gradient-to-position);
}
.dark\:text-fd-accent-foreground:is(.dark *){
  --tw-text-opacity: 1;
  color: hsl(var(--fd-accent-foreground) / var(--tw-text-opacity, 1));
}
.dark\:text-fd-muted-foreground:is(.dark *){
  --tw-text-opacity: 1;
  color: hsl(var(--fd-muted-foreground) / var(--tw-text-opacity, 1));
}
.dark\:text-gray-100:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity, 1));
}
.dark\:text-gray-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}
.dark\:text-gray-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.dark\:text-gray-500:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.dark\:text-purple-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1));
}
.dark\:text-red-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}
.dark\:text-white:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.dark\:text-yellow-100:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(254 249 195 / var(--tw-text-opacity, 1));
}
.dark\:placeholder-gray-400:is(.dark *)::-moz-placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}
.dark\:placeholder-gray-400:is(.dark *)::placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}
.dark\:hover\:border-purple-600:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(147 51 234 / var(--tw-border-opacity, 1));
}
.dark\:hover\:bg-\[\#333\]:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(51 51 51 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-\[\#404040\]:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(64 64 64 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-gray-600:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-gray-700:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-gray-800:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-purple-600:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-white\/10:hover:is(.dark *){
  background-color: rgb(255 255 255 / 0.1);
}
.dark\:hover\:bg-white\/5:hover:is(.dark *){
  background-color: rgb(255 255 255 / 0.05);
}
.dark\:hover\:bg-white\/\[0\.03\]:hover:is(.dark *){
  background-color: rgb(255 255 255 / 0.03);
}
.dark\:hover\:text-gray-200:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-gray-300:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-red-300:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-white:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.dark\:focus\:bg-white\/\[0\.05\]:focus:is(.dark *){
  background-color: rgb(255 255 255 / 0.05);
}
.group:hover .dark\:group-hover\:text-gray-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}
.group:hover .dark\:group-hover\:text-purple-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1));
}
@media not all and (min-width: 1280px){

  .max-xl\:hidden{
    display: none;
  }
}
@media not all and (min-width: 1024px){

  .max-lg\:hidden{
    display: none;
  }
}
@media not all and (min-width: 768px){

  .max-md\:inset-x-0{
    left: 0px;
    right: 0px;
  }

  .max-md\:bottom-0{
    bottom: 0px;
  }

  .max-md\:hidden{
    display: none;
  }

  .max-md\:bg-fd-background\/80{
    background-color: hsl(var(--fd-background) / 0.8);
  }

  .max-md\:text-\[15px\]{
    font-size: 15px;
  }

  .max-md\:backdrop-blur-lg{
    --tw-backdrop-blur: blur(16px);
    -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
    backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  }

  .max-md\:data-\[open\=false\]\:invisible[data-open="false"]{
    visibility: hidden;
  }
}
@media not all and (min-width: 640px){

  .max-sm\:mt-2{
    margin-top: 0.5rem;
  }

  .max-sm\:hidden{
    display: none;
  }
}
@media (min-width: 640px){

  .sm\:hidden{
    display: none;
  }

  .sm\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:flex-row{
    flex-direction: row;
  }

  .sm\:items-center{
    align-items: center;
  }

  .sm\:justify-end{
    justify-content: flex-end;
  }
}
@media (min-width: 768px){

  .md\:sticky{
    position: sticky;
  }

  .md\:order-first{
    order: -9999;
  }

  .md\:-me-\[var\(--fd-sidebar-width\)\]{
    margin-inline-end: calc(var(--fd-sidebar-width) * -1);
  }

  .md\:ms-2{
    margin-inline-start: 0.5rem;
  }

  .md\:ms-auto{
    margin-inline-start: auto;
  }

  .md\:hidden{
    display: none;
  }

  .md\:h-\[var\(--fd-sidebar-height\)\]{
    height: var(--fd-sidebar-height);
  }

  .md\:w-\[var\(--fd-sidebar-width\)\]{
    width: var(--fd-sidebar-width);
  }

  .md\:translate-x-0{
    --tw-translate-x: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:translate-x-\[calc\(var\(--fd-sidebar-offset\)\*-1\)\]{
    --tw-translate-x: calc(var(--fd-sidebar-offset) * -1);
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:gap-1\.5{
    gap: 0.375rem;
  }

  .md\:border-e{
    border-inline-end-width: 1px;
  }

  .md\:px-2{
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .md\:py-1\.5{
    padding-top: 0.375rem;
    padding-bottom: 0.375rem;
  }

  .md\:ps-\[var\(--fd-layout-offset\)\]{
    padding-inline-start: var(--fd-layout-offset);
  }

  .md\:pt-12{
    padding-top: 3rem;
  }

  .md\:pt-4{
    padding-top: 1rem;
  }

  .md\:opacity-0{
    opacity: 0;
  }

  .md\:transition-all{
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }

  .md\:\[--fd-nav-height\:0px\]{
    --fd-nav-height: 0px;
  }

  .md\:\[--fd-sidebar-width\:268px\]{
    --fd-sidebar-width: 268px;
  }
}
@media (min-width: 1024px){

  .lg\:mt-2{
    margin-top: 0.5rem;
  }

  .lg\:hidden{
    display: none;
  }

  .lg\:h-12{
    height: 3rem;
  }

  .lg\:w-\[calc\(100\%-1rem\)\]{
    width: calc(100% - 1rem);
  }

  .lg\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:gap-1\.5{
    gap: 0.375rem;
  }

  .lg\:rounded-2xl{
    border-radius: 1rem;
  }

  .lg\:border{
    border-width: 1px;
  }

  .lg\:px-4{
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .lg\:px-8{
    padding-left: 2rem;
    padding-right: 2rem;
  }
}
@media (min-width: 1280px){

  .xl\:mx-auto{
    margin-left: auto;
    margin-right: auto;
  }

  .xl\:hidden{
    display: none;
  }

  .xl\:\[--fd-toc-width\:268px\]{
    --fd-toc-width: 268px;
  }

  .xl\:\[--fd-tocnav-height\:0px\]{
    --fd-tocnav-height: 0px;
  }
}
.rtl\:rotate-180:where([dir="rtl"], [dir="rtl"] *){
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rtl\:-scale-x-100:where([dir="rtl"], [dir="rtl"] *){
  --tw-scale-x: -1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@media (min-width: 768px){

  .rtl\:md\:translate-x-\[var\(--fd-sidebar-offset\)\]:where([dir="rtl"], [dir="rtl"] *){
    --tw-translate-x: var(--fd-sidebar-offset);
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }
}
.\[\&\:\:-webkit-scrollbar\]\:hidden::-webkit-scrollbar{
  display: none;
}
.\[\&\>figure\:only-child\]\:-m-4>figure:only-child{
  margin: -1rem;
}
.\[\&\>figure\:only-child\]\:rounded-none>figure:only-child{
  border-radius: 0px;
}
.\[\&\>figure\:only-child\]\:border-none>figure:only-child{
  border-style: none;
}
.\[\&_svg\]\:size-3\.5 svg{
  width: 0.875rem;
  height: 0.875rem;
}
.\[\&_svg\]\:size-4 svg{
  width: 1rem;
  height: 1rem;
}
.\[\&_svg\]\:size-5 svg{
  width: 1.25rem;
  height: 1.25rem;
}

