"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/utils/messageParser.ts":
/*!************************************!*\
  !*** ./src/utils/messageParser.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StreamingMessageParser: function() { return /* binding */ StreamingMessageParser; },\n/* harmony export */   debugFileCreation: function() { return /* binding */ debugFileCreation; },\n/* harmony export */   parseMessageForFiles: function() { return /* binding */ parseMessageForFiles; },\n/* harmony export */   parseMessages: function() { return /* binding */ parseMessages; },\n/* harmony export */   testAIResponse: function() { return /* binding */ testAIResponse; },\n/* harmony export */   testCompleteFlow: function() { return /* binding */ testCompleteFlow; },\n/* harmony export */   testMessageParser: function() { return /* binding */ testMessageParser; }\n/* harmony export */ });\n/* harmony import */ var _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/stores/fileStore */ \"(app-pages-browser)/./src/stores/fileStore.ts\");\n/* __next_internal_client_entry_do_not_use__ StreamingMessageParser,parseMessages,testMessageParser,testAIResponse,debugFileCreation,testCompleteFlow,parseMessageForFiles auto */ \nclass StreamingMessageParser {\n    parse(messageId, content) {\n        console.log(\"\\uD83D\\uDD0D Parsing message \".concat(messageId, \" for artifacts...\"));\n        console.log(\"\\uD83D\\uDCC4 Message content preview:\", content.substring(0, 200) + \"...\");\n        // Parse boltArtifact tags - handle both formats\n        const artifactRegex = /<boltArtifact[^>]*>([\\s\\S]*?)<\\/boltArtifact>/g;\n        let artifactMatch;\n        let foundArtifacts = 0;\n        while((artifactMatch = artifactRegex.exec(content)) !== null){\n            foundArtifacts++;\n            const fullMatch = artifactMatch[0];\n            const artifactContent = artifactMatch[1];\n            console.log(\"\\uD83D\\uDCE6 Found artifact \".concat(foundArtifacts, \":\"), fullMatch.substring(0, 100) + \"...\");\n            // Check if this is a simplified format (type=\"file\" path=\"...\" or name=\"...\")\n            const simplifiedFileMatch = fullMatch.match(/<boltArtifact[^>]*type=\"file\"[^>]*(?:path|name)=\"([^\"]+)\"[^>]*>/);\n            if (simplifiedFileMatch) {\n                const fileName = simplifiedFileMatch[1];\n                console.log(\"\\uD83D\\uDCC4 Simplified format detected for file: \".concat(fileName));\n                if (this.callbacks.onFileAction) {\n                    this.callbacks.onFileAction({\n                        type: \"file\",\n                        filePath: fileName,\n                        content: artifactContent.trim()\n                    });\n                }\n            } else {\n                // Standard format with boltAction tags\n                this.parseActions(artifactContent);\n            }\n        }\n        if (foundArtifacts === 0) {\n            console.log(\"ℹ️ No artifacts found in message \".concat(messageId));\n            console.log(\"\\uD83D\\uDD0D Checking for boltArtifact tags in content...\");\n            if (content.includes(\"<boltArtifact\")) {\n                console.log(\"⚠️ Found boltArtifact text but regex didn't match. Content:\", content);\n            } else {\n                console.log(\"❌ No boltArtifact tags found in content at all\");\n            }\n        }\n    }\n    parseActions(content) {\n        // Parse boltAction tags - handle multiple formats\n        const actionRegex = /<boltAction\\s+([^>]+)>([\\s\\S]*?)<\\/boltAction>/g;\n        let actionMatch;\n        let foundActions = 0;\n        while((actionMatch = actionRegex.exec(content)) !== null){\n            foundActions++;\n            const [, attributes, actionContent] = actionMatch;\n            // Parse attributes\n            const typeMatch = attributes.match(/type=\"([^\"]+)\"/);\n            const filePathMatch = attributes.match(/filePath=\"([^\"]+)\"/);\n            const pathMatch = attributes.match(/path=\"([^\"]+)\"/);\n            const type = typeMatch ? typeMatch[1] : \"\";\n            const filePath = filePathMatch ? filePathMatch[1] : pathMatch ? pathMatch[1] : \"\";\n            console.log(\"⚡ Found action \".concat(foundActions, \": type=\").concat(type, \", filePath=\").concat(filePath));\n            // Handle different type variations\n            if (type === \"file\" || type === \"createFile\") {\n                if (filePath && this.callbacks.onFileAction) {\n                    this.callbacks.onFileAction({\n                        type: \"file\",\n                        filePath,\n                        content: actionContent.trim()\n                    });\n                }\n            } else if (type === \"shell\") {\n                if (this.callbacks.onShellAction) {\n                    this.callbacks.onShellAction({\n                        type: \"shell\",\n                        content: actionContent.trim()\n                    });\n                }\n            } else if (type === \"start\") {\n                if (this.callbacks.onStartAction) {\n                    this.callbacks.onStartAction({\n                        type: \"start\",\n                        content: actionContent.trim()\n                    });\n                }\n            }\n        }\n        if (foundActions === 0) {\n            console.log(\"ℹ️ No actions found in artifact content\");\n        }\n    }\n    constructor(callbacks = {}){\n        this.callbacks = callbacks;\n    }\n}\n// Create a global message parser instance\nconst messageParser = new StreamingMessageParser({\n    onFileAction: async (action)=>{\n        console.log(\"\\uD83D\\uDD25 onFileAction called with:\", action);\n        const { addFile } = _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__.useFileStore.getState();\n        console.log(\"\\uD83D\\uDCC1 File store addFile function:\", typeof addFile);\n        try {\n            console.log(\"\\uD83D\\uDE80 Attempting to add file: \".concat(action.filePath));\n            await addFile(action.filePath, action.content);\n            console.log(\"✅ Created/updated file: \".concat(action.filePath));\n            console.log(\"\\uD83D\\uDCC4 Content preview: \".concat(action.content.substring(0, 100), \"...\"));\n            // Verify file was added\n            const currentFiles = _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__.useFileStore.getState().files;\n            console.log(\"\\uD83D\\uDCCA Current files after addition:\", Object.keys(currentFiles));\n            console.log(\"\\uD83D\\uDD0D File exists in store:\", action.filePath in currentFiles);\n        } catch (error) {\n            console.error(\"❌ Failed to create file \".concat(action.filePath, \":\"), error);\n        }\n    },\n    onShellAction: (action)=>{\n        console.log(\"Shell command:\", action.content);\n    // TODO: Integrate with terminal store to execute commands\n    },\n    onStartAction: (action)=>{\n        console.log(\"Start command:\", action.content);\n    // TODO: Integrate with terminal store to execute start commands\n    }\n});\nconst parseMessages = async (messages)=>{\n    console.log(\"\\uD83D\\uDE80 parseMessages called with \".concat(messages.length, \" messages\"));\n    console.log(\"\\uD83D\\uDCCA Current file store state:\", _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__.useFileStore.getState().files);\n    for (const message of messages){\n        if (message.role === \"assistant\") {\n            console.log(\"\\uD83E\\uDD16 Parsing assistant message: \".concat(message.id));\n            console.log(\"\\uD83D\\uDCCB Message object:\", message);\n            console.log(\"\\uD83D\\uDCDD Message content type:\", typeof message.content);\n            console.log(\"\\uD83D\\uDCDD Message content:\", message.content);\n            // Check if content contains boltArtifact\n            if (message.content && typeof message.content === \"string\" && message.content.includes(\"<boltArtifact\")) {\n                console.log(\"✅ Message contains boltArtifact tags, proceeding with parsing...\");\n            } else {\n                console.log(\"❌ Message does not contain boltArtifact tags\");\n            }\n            messageParser.parse(message.id, message.content);\n            // Check file store state after parsing\n            console.log(\"\\uD83D\\uDCCA File store state after parsing:\", _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__.useFileStore.getState().files);\n        }\n    }\n};\n// Test function to verify parsing works\nconst testMessageParser = ()=>{\n    console.log(\"\\uD83E\\uDDEA Testing message parser...\");\n    const testContent1 = 'Here\\'s a simple HTML file with \\'Hello World\\' using the boltArtifact format:\\n\\n<boltArtifact type=\"file\" name=\"test.html\">\\n<!DOCTYPE html>\\n<html lang=\"en\">\\n<head>\\n    <meta charset=\"UTF-8\">\\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\\n    <title>Hello World</title>\\n</head>\\n<body>\\n    <h1>Hello World</h1>\\n</body>\\n</html>\\n</boltArtifact>';\n    const testContent2 = 'Here\\'s a simple HTML file in the boltArtifact format:\\n\\n<boltArtifact>\\n<boltAction type=\"createFile\" path=\"index.html\">\\n<!DOCTYPE html>\\n<html lang=\"en\">\\n<head>\\n    <meta charset=\"UTF-8\">\\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\\n    <title>Hello World</title>\\n</head>\\n<body>\\n    <h1>Hello World</h1>\\n</body>\\n</html>\\n</boltAction>\\n</boltArtifact>';\n    console.log(\"\\uD83E\\uDDEA Testing message parser with format 1...\");\n    messageParser.parse(\"test-message-1\", testContent1);\n    console.log(\"\\uD83E\\uDDEA Testing message parser with format 2...\");\n    messageParser.parse(\"test-message-2\", testContent2);\n};\n// Test function with the exact AI response format\nconst testAIResponse = ()=>{\n    const aiResponse = '<boltArtifact><boltAction type=\"file\" filePath=\"test.html\"><!DOCTYPE html>\\n<html>\\n<head><title>Test</title></head>\\n<body><h1>Hello World</h1></body>\\n</html></boltAction></boltArtifact>';\n    console.log(\"\\uD83E\\uDDEA Testing with exact AI response format...\");\n    console.log(\"\\uD83D\\uDCC4 Test content:\", aiResponse);\n    messageParser.parse(\"ai-response-test\", aiResponse);\n};\n// Debug function to test file creation\nconst debugFileCreation = ()=>{\n    console.log(\"\\uD83D\\uDD27 Testing file creation directly...\");\n    const { addFile } = _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__.useFileStore.getState();\n    addFile(\"debug-test.html\", \"<h1>Debug Test</h1>\").then(()=>{\n        console.log(\"✅ Direct file creation successful\");\n        const files = _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__.useFileStore.getState().files;\n        console.log(\"\\uD83D\\uDCC1 Current files:\", Object.keys(files));\n        console.log(\"\\uD83D\\uDCC4 Debug file content:\", files[\"debug-test.html\"]);\n    }).catch((error)=>{\n        console.error(\"❌ Direct file creation failed:\", error);\n    });\n};\n// Function to test the complete flow\nconst testCompleteFlow = ()=>{\n    console.log(\"\\uD83E\\uDDEA Testing complete AI response flow...\");\n    // Simulate an AI response with boltArtifact\n    const mockAIResponse = {\n        id: \"test-ai-response\",\n        role: \"assistant\",\n        content: 'I\\'ll create a simple HTML file for you:\\n\\n<boltArtifact id=\"simple-html\" title=\"Simple HTML File\">\\n<boltAction type=\"file\" filePath=\"index.html\"><!DOCTYPE html>\\n<html lang=\"en\">\\n<head>\\n    <meta charset=\"UTF-8\">\\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\\n    <title>Test Page</title>\\n</head>\\n<body>\\n    <h1>Hello from AI!</h1>\\n    <p>This file was created by the AI response parser.</p>\\n</body>\\n</html></boltAction>\\n</boltArtifact>\\n\\nThe HTML file has been created successfully.'\n    };\n    console.log(\"\\uD83D\\uDCE4 Simulating AI response:\", mockAIResponse);\n    parseMessages([\n        mockAIResponse\n    ]);\n};\n// Make test functions available globally for debugging\nif (true) {\n    window.testMessageParser = testMessageParser;\n    window.testAIResponse = testAIResponse;\n    window.debugFileCreation = debugFileCreation;\n    window.testCompleteFlow = testCompleteFlow;\n}\n// Simple function to extract files from message content\nfunction parseMessageForFiles(content) {\n    const files = {};\n    // Parse boltArtifact and boltAction tags\n    const artifactRegex = /<boltArtifact[^>]*>([\\s\\S]*?)<\\/boltArtifact>/g;\n    let artifactMatch;\n    while((artifactMatch = artifactRegex.exec(content)) !== null){\n        const artifactContent = artifactMatch[1];\n        // Extract file actions\n        const fileActionRegex = /<boltAction\\s+type=\"file\"\\s+filePath=\"([^\"]+)\"\\s*>([\\s\\S]*?)<\\/boltAction>/g;\n        let fileMatch;\n        while((fileMatch = fileActionRegex.exec(artifactContent)) !== null){\n            const [, filePath, fileContent] = fileMatch;\n            files[filePath] = fileContent.trim();\n        }\n    }\n    return files;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy91dGlscy9tZXNzYWdlUGFyc2VyLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O21MQUVrRDtBQThCM0MsTUFBTUM7SUFPWEMsTUFBTUMsU0FBaUIsRUFBRUMsT0FBZSxFQUFFO1FBQ3hDQyxRQUFRQyxHQUFHLENBQUMsZ0NBQWdDLE9BQVZILFdBQVU7UUFDNUNFLFFBQVFDLEdBQUcsQ0FBRSx5Q0FBOEJGLFFBQVFHLFNBQVMsQ0FBQyxHQUFHLE9BQU87UUFFdkUsZ0RBQWdEO1FBQ2hELE1BQU1DLGdCQUFnQjtRQUN0QixJQUFJQztRQUNKLElBQUlDLGlCQUFpQjtRQUVyQixNQUFPLENBQUNELGdCQUFnQkQsY0FBY0csSUFBSSxDQUFDUCxRQUFPLE1BQU8sS0FBTTtZQUM3RE07WUFDQSxNQUFNRSxZQUFZSCxhQUFhLENBQUMsRUFBRTtZQUNsQyxNQUFNSSxrQkFBa0JKLGFBQWEsQ0FBQyxFQUFFO1lBRXhDSixRQUFRQyxHQUFHLENBQUMsK0JBQW9DLE9BQWZJLGdCQUFlLE1BQUlFLFVBQVVMLFNBQVMsQ0FBQyxHQUFHLE9BQU87WUFFbEYsOEVBQThFO1lBQzlFLE1BQU1PLHNCQUFzQkYsVUFBVUcsS0FBSyxDQUFDO1lBQzVDLElBQUlELHFCQUFxQjtnQkFDdkIsTUFBTUUsV0FBV0YsbUJBQW1CLENBQUMsRUFBRTtnQkFDdkNULFFBQVFDLEdBQUcsQ0FBQyxxREFBb0QsT0FBVFU7Z0JBRXZELElBQUksSUFBSSxDQUFDQyxTQUFTLENBQUNDLFlBQVksRUFBRTtvQkFDL0IsSUFBSSxDQUFDRCxTQUFTLENBQUNDLFlBQVksQ0FBQzt3QkFDMUJDLE1BQU07d0JBQ05DLFVBQVVKO3dCQUNWWixTQUFTUyxnQkFBZ0JRLElBQUk7b0JBQy9CO2dCQUNGO1lBQ0YsT0FBTztnQkFDTCx1Q0FBdUM7Z0JBQ3ZDLElBQUksQ0FBQ0MsWUFBWSxDQUFDVDtZQUNwQjtRQUNGO1FBRUEsSUFBSUgsbUJBQW1CLEdBQUc7WUFDeEJMLFFBQVFDLEdBQUcsQ0FBQyxvQ0FBOEMsT0FBVkg7WUFDaERFLFFBQVFDLEdBQUcsQ0FBRTtZQUNiLElBQUlGLFFBQVFtQixRQUFRLENBQUMsa0JBQWtCO2dCQUNyQ2xCLFFBQVFDLEdBQUcsQ0FBRSwrREFBOERGO1lBQzdFLE9BQU87Z0JBQ0xDLFFBQVFDLEdBQUcsQ0FBRTtZQUNmO1FBQ0Y7SUFDRjtJQUVRZ0IsYUFBYWxCLE9BQWUsRUFBRTtRQUNwQyxrREFBa0Q7UUFDbEQsTUFBTW9CLGNBQWM7UUFDcEIsSUFBSUM7UUFDSixJQUFJQyxlQUFlO1FBRW5CLE1BQU8sQ0FBQ0QsY0FBY0QsWUFBWWIsSUFBSSxDQUFDUCxRQUFPLE1BQU8sS0FBTTtZQUN6RHNCO1lBQ0EsTUFBTSxHQUFHQyxZQUFZQyxjQUFjLEdBQUdIO1lBRXRDLG1CQUFtQjtZQUNuQixNQUFNSSxZQUFZRixXQUFXWixLQUFLLENBQUM7WUFDbkMsTUFBTWUsZ0JBQWdCSCxXQUFXWixLQUFLLENBQUM7WUFDdkMsTUFBTWdCLFlBQVlKLFdBQVdaLEtBQUssQ0FBQztZQUVuQyxNQUFNSSxPQUFPVSxZQUFZQSxTQUFTLENBQUMsRUFBRSxHQUFHO1lBQ3hDLE1BQU1ULFdBQVdVLGdCQUFnQkEsYUFBYSxDQUFDLEVBQUUsR0FBSUMsWUFBWUEsU0FBUyxDQUFDLEVBQUUsR0FBRztZQUVoRjFCLFFBQVFDLEdBQUcsQ0FBQyxrQkFBd0NhLE9BQXRCTyxjQUFhLFdBQTJCTixPQUFsQkQsTUFBSyxlQUFzQixPQUFUQztZQUV0RSxtQ0FBbUM7WUFDbkMsSUFBSUQsU0FBUyxVQUFVQSxTQUFTLGNBQWM7Z0JBQzVDLElBQUlDLFlBQVksSUFBSSxDQUFDSCxTQUFTLENBQUNDLFlBQVksRUFBRTtvQkFDM0MsSUFBSSxDQUFDRCxTQUFTLENBQUNDLFlBQVksQ0FBQzt3QkFDMUJDLE1BQU07d0JBQ05DO3dCQUNBaEIsU0FBU3dCLGNBQWNQLElBQUk7b0JBQzdCO2dCQUNGO1lBQ0YsT0FBTyxJQUFJRixTQUFTLFNBQVM7Z0JBQzNCLElBQUksSUFBSSxDQUFDRixTQUFTLENBQUNlLGFBQWEsRUFBRTtvQkFDaEMsSUFBSSxDQUFDZixTQUFTLENBQUNlLGFBQWEsQ0FBQzt3QkFDM0JiLE1BQU07d0JBQ05mLFNBQVN3QixjQUFjUCxJQUFJO29CQUM3QjtnQkFDRjtZQUNGLE9BQU8sSUFBSUYsU0FBUyxTQUFTO2dCQUMzQixJQUFJLElBQUksQ0FBQ0YsU0FBUyxDQUFDZ0IsYUFBYSxFQUFFO29CQUNoQyxJQUFJLENBQUNoQixTQUFTLENBQUNnQixhQUFhLENBQUM7d0JBQzNCZCxNQUFNO3dCQUNOZixTQUFTd0IsY0FBY1AsSUFBSTtvQkFDN0I7Z0JBQ0Y7WUFDRjtRQUNGO1FBRUEsSUFBSUssaUJBQWlCLEdBQUc7WUFDdEJyQixRQUFRQyxHQUFHLENBQUU7UUFDZjtJQUNGO0lBbkdBNEIsWUFBWWpCLFlBQTZCLENBQUMsQ0FBQyxDQUFFO1FBQzNDLElBQUksQ0FBQ0EsU0FBUyxHQUFHQTtJQUNuQjtBQWtHRjtBQUVBLDBDQUEwQztBQUMxQyxNQUFNa0IsZ0JBQWdCLElBQUlsQyx1QkFBdUI7SUFDL0NpQixjQUFjLE9BQU9rQjtRQUNuQi9CLFFBQVFDLEdBQUcsQ0FBRSwwQ0FBK0I4QjtRQUM1QyxNQUFNLEVBQUVDLE9BQU8sRUFBRSxHQUFHckMsMkRBQVlBLENBQUNzQyxRQUFRO1FBQ3pDakMsUUFBUUMsR0FBRyxDQUFFLDZDQUFrQyxPQUFPK0I7UUFFdEQsSUFBSTtZQUNGaEMsUUFBUUMsR0FBRyxDQUFDLHdDQUE4QyxPQUFoQjhCLE9BQU9oQixRQUFRO1lBQ3pELE1BQU1pQixRQUFRRCxPQUFPaEIsUUFBUSxFQUFFZ0IsT0FBT2hDLE9BQU87WUFDN0NDLFFBQVFDLEdBQUcsQ0FBQywyQkFBMkMsT0FBaEI4QixPQUFPaEIsUUFBUTtZQUN0RGYsUUFBUUMsR0FBRyxDQUFDLGlDQUF3RCxPQUFqQzhCLE9BQU9oQyxPQUFPLENBQUNHLFNBQVMsQ0FBQyxHQUFHLE1BQUs7WUFFcEUsd0JBQXdCO1lBQ3hCLE1BQU1nQyxlQUFldkMsMkRBQVlBLENBQUNzQyxRQUFRLEdBQUdFLEtBQUs7WUFDbERuQyxRQUFRQyxHQUFHLENBQUUsOENBQW1DbUMsT0FBT0MsSUFBSSxDQUFDSDtZQUM1RGxDLFFBQVFDLEdBQUcsQ0FBRSxzQ0FBMkI4QixPQUFPaEIsUUFBUSxJQUFJbUI7UUFDN0QsRUFBRSxPQUFPSSxPQUFPO1lBQ2R0QyxRQUFRc0MsS0FBSyxDQUFDLDJCQUEyQyxPQUFoQlAsT0FBT2hCLFFBQVEsRUFBQyxNQUFJdUI7UUFDL0Q7SUFDRjtJQUNBWCxlQUFlLENBQUNJO1FBQ2QvQixRQUFRQyxHQUFHLENBQUMsa0JBQWtCOEIsT0FBT2hDLE9BQU87SUFDNUMsMERBQTBEO0lBQzVEO0lBQ0E2QixlQUFlLENBQUNHO1FBQ2QvQixRQUFRQyxHQUFHLENBQUMsa0JBQWtCOEIsT0FBT2hDLE9BQU87SUFDNUMsZ0VBQWdFO0lBQ2xFO0FBQ0Y7QUFFTyxNQUFNd0MsZ0JBQWdCLE9BQU9DO0lBQ2xDeEMsUUFBUUMsR0FBRyxDQUFDLDBDQUFnRCxPQUFoQnVDLFNBQVNDLE1BQU0sRUFBQztJQUM1RHpDLFFBQVFDLEdBQUcsQ0FBRSwwQ0FBK0JOLDJEQUFZQSxDQUFDc0MsUUFBUSxHQUFHRSxLQUFLO0lBRXpFLEtBQUssTUFBTU8sV0FBV0YsU0FBVTtRQUM5QixJQUFJRSxRQUFRQyxJQUFJLEtBQUssYUFBYTtZQUNoQzNDLFFBQVFDLEdBQUcsQ0FBQywyQ0FBNEMsT0FBWHlDLFFBQVFFLEVBQUU7WUFDdkQ1QyxRQUFRQyxHQUFHLENBQUUsZ0NBQXFCeUM7WUFDbEMxQyxRQUFRQyxHQUFHLENBQUUsc0NBQTJCLE9BQU95QyxRQUFRM0MsT0FBTztZQUM5REMsUUFBUUMsR0FBRyxDQUFFLGlDQUFzQnlDLFFBQVEzQyxPQUFPO1lBRWxELHlDQUF5QztZQUN6QyxJQUFJMkMsUUFBUTNDLE9BQU8sSUFBSSxPQUFPMkMsUUFBUTNDLE9BQU8sS0FBSyxZQUFZMkMsUUFBUTNDLE9BQU8sQ0FBQ21CLFFBQVEsQ0FBQyxrQkFBa0I7Z0JBQ3ZHbEIsUUFBUUMsR0FBRyxDQUFFO1lBQ2YsT0FBTztnQkFDTEQsUUFBUUMsR0FBRyxDQUFFO1lBQ2Y7WUFFQTZCLGNBQWNqQyxLQUFLLENBQUM2QyxRQUFRRSxFQUFFLEVBQUVGLFFBQVEzQyxPQUFPO1lBRS9DLHVDQUF1QztZQUN2Q0MsUUFBUUMsR0FBRyxDQUFFLGdEQUFxQ04sMkRBQVlBLENBQUNzQyxRQUFRLEdBQUdFLEtBQUs7UUFDakY7SUFDRjtBQUNGLEVBQUU7QUFFRix3Q0FBd0M7QUFDakMsTUFBTVUsb0JBQW9CO0lBQy9CN0MsUUFBUUMsR0FBRyxDQUFDO0lBRVosTUFBTTZDLGVBQWdCO0lBZ0J0QixNQUFNQyxlQUFnQjtJQWtCdEIvQyxRQUFRQyxHQUFHLENBQUM7SUFDWjZCLGNBQWNqQyxLQUFLLENBQUMsa0JBQWtCaUQ7SUFFdEM5QyxRQUFRQyxHQUFHLENBQUM7SUFDWjZCLGNBQWNqQyxLQUFLLENBQUMsa0JBQWtCa0Q7QUFDeEMsRUFBRTtBQUVGLGtEQUFrRDtBQUMzQyxNQUFNQyxpQkFBaUI7SUFDNUIsTUFBTUMsYUFBYztJQU1wQmpELFFBQVFDLEdBQUcsQ0FBQztJQUNaRCxRQUFRQyxHQUFHLENBQUMsOEJBQW9CZ0Q7SUFDaENuQixjQUFjakMsS0FBSyxDQUFDLG9CQUFvQm9EO0FBQzFDLEVBQUU7QUFFRix1Q0FBdUM7QUFDaEMsTUFBTUMsb0JBQW9CO0lBQy9CbEQsUUFBUUMsR0FBRyxDQUFDO0lBQ1osTUFBTSxFQUFFK0IsT0FBTyxFQUFFLEdBQUdyQywyREFBWUEsQ0FBQ3NDLFFBQVE7SUFFekNELFFBQVEsbUJBQW1CLHVCQUN4Qm1CLElBQUksQ0FBQztRQUNKbkQsUUFBUUMsR0FBRyxDQUFDO1FBQ1osTUFBTWtDLFFBQVF4QywyREFBWUEsQ0FBQ3NDLFFBQVEsR0FBR0UsS0FBSztRQUMzQ25DLFFBQVFDLEdBQUcsQ0FBQywrQkFBcUJtQyxPQUFPQyxJQUFJLENBQUNGO1FBQzdDbkMsUUFBUUMsR0FBRyxDQUFDLG9DQUEwQmtDLEtBQUssQ0FBQyxrQkFBa0I7SUFDaEUsR0FDQ2lCLEtBQUssQ0FBQyxDQUFDZDtRQUNOdEMsUUFBUXNDLEtBQUssQ0FBQyxrQ0FBa0NBO0lBQ2xEO0FBQ0osRUFBRTtBQUVGLHFDQUFxQztBQUM5QixNQUFNZSxtQkFBbUI7SUFDOUJyRCxRQUFRQyxHQUFHLENBQUM7SUFFWiw0Q0FBNEM7SUFDNUMsTUFBTXFELGlCQUFpQjtRQUNyQlYsSUFBSTtRQUNKRCxNQUFNO1FBQ041QyxTQUFVO0lBa0JaO0lBRUFDLFFBQVFDLEdBQUcsQ0FBQyx3Q0FBOEJxRDtJQUMxQ2YsY0FBYztRQUFDZTtLQUFlO0FBQ2hDLEVBQUU7QUFFRix1REFBdUQ7QUFDdkQsSUFBSSxJQUFrQixFQUFhO0lBQ2hDQyxPQUFlVixpQkFBaUIsR0FBR0E7SUFDbkNVLE9BQWVQLGNBQWMsR0FBR0E7SUFDaENPLE9BQWVMLGlCQUFpQixHQUFHQTtJQUNuQ0ssT0FBZUYsZ0JBQWdCLEdBQUdBO0FBQ3JDO0FBRUEsd0RBQXdEO0FBQ2pELFNBQVNHLHFCQUFxQnpELE9BQWU7SUFDbEQsTUFBTW9DLFFBQWdDLENBQUM7SUFFdkMseUNBQXlDO0lBQ3pDLE1BQU1oQyxnQkFBZ0I7SUFDdEIsSUFBSUM7SUFFSixNQUFPLENBQUNBLGdCQUFnQkQsY0FBY0csSUFBSSxDQUFDUCxRQUFPLE1BQU8sS0FBTTtRQUM3RCxNQUFNUyxrQkFBa0JKLGFBQWEsQ0FBQyxFQUFFO1FBRXhDLHVCQUF1QjtRQUN2QixNQUFNcUQsa0JBQWtCO1FBQ3hCLElBQUlDO1FBRUosTUFBTyxDQUFDQSxZQUFZRCxnQkFBZ0JuRCxJQUFJLENBQUNFLGdCQUFlLE1BQU8sS0FBTTtZQUNuRSxNQUFNLEdBQUdPLFVBQVU0QyxZQUFZLEdBQUdEO1lBQ2xDdkIsS0FBSyxDQUFDcEIsU0FBUyxHQUFHNEMsWUFBWTNDLElBQUk7UUFDcEM7SUFDRjtJQUVBLE9BQU9tQjtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy91dGlscy9tZXNzYWdlUGFyc2VyLnRzP2M2MTIiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VGaWxlU3RvcmUgfSBmcm9tICdAL3N0b3Jlcy9maWxlU3RvcmUnO1xuaW1wb3J0IHsgTWVzc2FnZSB9IGZyb20gJ2FpL3JlYWN0JztcblxuZXhwb3J0IHR5cGUgQWN0aW9uVHlwZSA9ICdmaWxlJyB8ICdzaGVsbCcgfCAnc3RhcnQnO1xuXG5leHBvcnQgaW50ZXJmYWNlIEJhc2VBY3Rpb24ge1xuICBjb250ZW50OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgRmlsZUFjdGlvbiBleHRlbmRzIEJhc2VBY3Rpb24ge1xuICB0eXBlOiAnZmlsZSc7XG4gIGZpbGVQYXRoOiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgU2hlbGxBY3Rpb24gZXh0ZW5kcyBCYXNlQWN0aW9uIHtcbiAgdHlwZTogJ3NoZWxsJztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBTdGFydEFjdGlvbiBleHRlbmRzIEJhc2VBY3Rpb24ge1xuICB0eXBlOiAnc3RhcnQnO1xufVxuXG5leHBvcnQgdHlwZSBCb2x0QWN0aW9uID0gRmlsZUFjdGlvbiB8IFNoZWxsQWN0aW9uIHwgU3RhcnRBY3Rpb247XG5cbmludGVyZmFjZSBQYXJzZXJDYWxsYmFja3Mge1xuICBvbkZpbGVBY3Rpb24/OiAoYWN0aW9uOiBGaWxlQWN0aW9uKSA9PiB2b2lkO1xuICBvblNoZWxsQWN0aW9uPzogKGFjdGlvbjogU2hlbGxBY3Rpb24pID0+IHZvaWQ7XG4gIG9uU3RhcnRBY3Rpb24/OiAoYWN0aW9uOiBTdGFydEFjdGlvbikgPT4gdm9pZDtcbn1cblxuZXhwb3J0IGNsYXNzIFN0cmVhbWluZ01lc3NhZ2VQYXJzZXIge1xuICBwcml2YXRlIGNhbGxiYWNrczogUGFyc2VyQ2FsbGJhY2tzO1xuXG4gIGNvbnN0cnVjdG9yKGNhbGxiYWNrczogUGFyc2VyQ2FsbGJhY2tzID0ge30pIHtcbiAgICB0aGlzLmNhbGxiYWNrcyA9IGNhbGxiYWNrcztcbiAgfVxuXG4gIHBhcnNlKG1lc3NhZ2VJZDogc3RyaW5nLCBjb250ZW50OiBzdHJpbmcpIHtcbiAgICBjb25zb2xlLmxvZyhg8J+UjSBQYXJzaW5nIG1lc3NhZ2UgJHttZXNzYWdlSWR9IGZvciBhcnRpZmFjdHMuLi5gKTtcbiAgICBjb25zb2xlLmxvZyhg8J+ThCBNZXNzYWdlIGNvbnRlbnQgcHJldmlldzpgLCBjb250ZW50LnN1YnN0cmluZygwLCAyMDApICsgJy4uLicpO1xuXG4gICAgLy8gUGFyc2UgYm9sdEFydGlmYWN0IHRhZ3MgLSBoYW5kbGUgYm90aCBmb3JtYXRzXG4gICAgY29uc3QgYXJ0aWZhY3RSZWdleCA9IC88Ym9sdEFydGlmYWN0W14+XSo+KFtcXHNcXFNdKj8pPFxcL2JvbHRBcnRpZmFjdD4vZztcbiAgICBsZXQgYXJ0aWZhY3RNYXRjaDtcbiAgICBsZXQgZm91bmRBcnRpZmFjdHMgPSAwO1xuXG4gICAgd2hpbGUgKChhcnRpZmFjdE1hdGNoID0gYXJ0aWZhY3RSZWdleC5leGVjKGNvbnRlbnQpKSAhPT0gbnVsbCkge1xuICAgICAgZm91bmRBcnRpZmFjdHMrKztcbiAgICAgIGNvbnN0IGZ1bGxNYXRjaCA9IGFydGlmYWN0TWF0Y2hbMF07XG4gICAgICBjb25zdCBhcnRpZmFjdENvbnRlbnQgPSBhcnRpZmFjdE1hdGNoWzFdO1xuXG4gICAgICBjb25zb2xlLmxvZyhg8J+TpiBGb3VuZCBhcnRpZmFjdCAke2ZvdW5kQXJ0aWZhY3RzfTpgLCBmdWxsTWF0Y2guc3Vic3RyaW5nKDAsIDEwMCkgKyAnLi4uJyk7XG5cbiAgICAgIC8vIENoZWNrIGlmIHRoaXMgaXMgYSBzaW1wbGlmaWVkIGZvcm1hdCAodHlwZT1cImZpbGVcIiBwYXRoPVwiLi4uXCIgb3IgbmFtZT1cIi4uLlwiKVxuICAgICAgY29uc3Qgc2ltcGxpZmllZEZpbGVNYXRjaCA9IGZ1bGxNYXRjaC5tYXRjaCgvPGJvbHRBcnRpZmFjdFtePl0qdHlwZT1cImZpbGVcIltePl0qKD86cGF0aHxuYW1lKT1cIihbXlwiXSspXCJbXj5dKj4vKTtcbiAgICAgIGlmIChzaW1wbGlmaWVkRmlsZU1hdGNoKSB7XG4gICAgICAgIGNvbnN0IGZpbGVOYW1lID0gc2ltcGxpZmllZEZpbGVNYXRjaFsxXTtcbiAgICAgICAgY29uc29sZS5sb2coYPCfk4QgU2ltcGxpZmllZCBmb3JtYXQgZGV0ZWN0ZWQgZm9yIGZpbGU6ICR7ZmlsZU5hbWV9YCk7XG5cbiAgICAgICAgaWYgKHRoaXMuY2FsbGJhY2tzLm9uRmlsZUFjdGlvbikge1xuICAgICAgICAgIHRoaXMuY2FsbGJhY2tzLm9uRmlsZUFjdGlvbih7XG4gICAgICAgICAgICB0eXBlOiAnZmlsZScsXG4gICAgICAgICAgICBmaWxlUGF0aDogZmlsZU5hbWUsXG4gICAgICAgICAgICBjb250ZW50OiBhcnRpZmFjdENvbnRlbnQudHJpbSgpLFxuICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICAvLyBTdGFuZGFyZCBmb3JtYXQgd2l0aCBib2x0QWN0aW9uIHRhZ3NcbiAgICAgICAgdGhpcy5wYXJzZUFjdGlvbnMoYXJ0aWZhY3RDb250ZW50KTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICBpZiAoZm91bmRBcnRpZmFjdHMgPT09IDApIHtcbiAgICAgIGNvbnNvbGUubG9nKGDihLnvuI8gTm8gYXJ0aWZhY3RzIGZvdW5kIGluIG1lc3NhZ2UgJHttZXNzYWdlSWR9YCk7XG4gICAgICBjb25zb2xlLmxvZyhg8J+UjSBDaGVja2luZyBmb3IgYm9sdEFydGlmYWN0IHRhZ3MgaW4gY29udGVudC4uLmApO1xuICAgICAgaWYgKGNvbnRlbnQuaW5jbHVkZXMoJzxib2x0QXJ0aWZhY3QnKSkge1xuICAgICAgICBjb25zb2xlLmxvZyhg4pqg77iPIEZvdW5kIGJvbHRBcnRpZmFjdCB0ZXh0IGJ1dCByZWdleCBkaWRuJ3QgbWF0Y2guIENvbnRlbnQ6YCwgY29udGVudCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmxvZyhg4p2MIE5vIGJvbHRBcnRpZmFjdCB0YWdzIGZvdW5kIGluIGNvbnRlbnQgYXQgYWxsYCk7XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgcHJpdmF0ZSBwYXJzZUFjdGlvbnMoY29udGVudDogc3RyaW5nKSB7XG4gICAgLy8gUGFyc2UgYm9sdEFjdGlvbiB0YWdzIC0gaGFuZGxlIG11bHRpcGxlIGZvcm1hdHNcbiAgICBjb25zdCBhY3Rpb25SZWdleCA9IC88Ym9sdEFjdGlvblxccysoW14+XSspPihbXFxzXFxTXSo/KTxcXC9ib2x0QWN0aW9uPi9nO1xuICAgIGxldCBhY3Rpb25NYXRjaDtcbiAgICBsZXQgZm91bmRBY3Rpb25zID0gMDtcblxuICAgIHdoaWxlICgoYWN0aW9uTWF0Y2ggPSBhY3Rpb25SZWdleC5leGVjKGNvbnRlbnQpKSAhPT0gbnVsbCkge1xuICAgICAgZm91bmRBY3Rpb25zKys7XG4gICAgICBjb25zdCBbLCBhdHRyaWJ1dGVzLCBhY3Rpb25Db250ZW50XSA9IGFjdGlvbk1hdGNoO1xuXG4gICAgICAvLyBQYXJzZSBhdHRyaWJ1dGVzXG4gICAgICBjb25zdCB0eXBlTWF0Y2ggPSBhdHRyaWJ1dGVzLm1hdGNoKC90eXBlPVwiKFteXCJdKylcIi8pO1xuICAgICAgY29uc3QgZmlsZVBhdGhNYXRjaCA9IGF0dHJpYnV0ZXMubWF0Y2goL2ZpbGVQYXRoPVwiKFteXCJdKylcIi8pO1xuICAgICAgY29uc3QgcGF0aE1hdGNoID0gYXR0cmlidXRlcy5tYXRjaCgvcGF0aD1cIihbXlwiXSspXCIvKTtcblxuICAgICAgY29uc3QgdHlwZSA9IHR5cGVNYXRjaCA/IHR5cGVNYXRjaFsxXSA6ICcnO1xuICAgICAgY29uc3QgZmlsZVBhdGggPSBmaWxlUGF0aE1hdGNoID8gZmlsZVBhdGhNYXRjaFsxXSA6IChwYXRoTWF0Y2ggPyBwYXRoTWF0Y2hbMV0gOiAnJyk7XG5cbiAgICAgIGNvbnNvbGUubG9nKGDimqEgRm91bmQgYWN0aW9uICR7Zm91bmRBY3Rpb25zfTogdHlwZT0ke3R5cGV9LCBmaWxlUGF0aD0ke2ZpbGVQYXRofWApO1xuXG4gICAgICAvLyBIYW5kbGUgZGlmZmVyZW50IHR5cGUgdmFyaWF0aW9uc1xuICAgICAgaWYgKHR5cGUgPT09ICdmaWxlJyB8fCB0eXBlID09PSAnY3JlYXRlRmlsZScpIHtcbiAgICAgICAgaWYgKGZpbGVQYXRoICYmIHRoaXMuY2FsbGJhY2tzLm9uRmlsZUFjdGlvbikge1xuICAgICAgICAgIHRoaXMuY2FsbGJhY2tzLm9uRmlsZUFjdGlvbih7XG4gICAgICAgICAgICB0eXBlOiAnZmlsZScsXG4gICAgICAgICAgICBmaWxlUGF0aCxcbiAgICAgICAgICAgIGNvbnRlbnQ6IGFjdGlvbkNvbnRlbnQudHJpbSgpLFxuICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICB9IGVsc2UgaWYgKHR5cGUgPT09ICdzaGVsbCcpIHtcbiAgICAgICAgaWYgKHRoaXMuY2FsbGJhY2tzLm9uU2hlbGxBY3Rpb24pIHtcbiAgICAgICAgICB0aGlzLmNhbGxiYWNrcy5vblNoZWxsQWN0aW9uKHtcbiAgICAgICAgICAgIHR5cGU6ICdzaGVsbCcsXG4gICAgICAgICAgICBjb250ZW50OiBhY3Rpb25Db250ZW50LnRyaW0oKSxcbiAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIGlmICh0eXBlID09PSAnc3RhcnQnKSB7XG4gICAgICAgIGlmICh0aGlzLmNhbGxiYWNrcy5vblN0YXJ0QWN0aW9uKSB7XG4gICAgICAgICAgdGhpcy5jYWxsYmFja3Mub25TdGFydEFjdGlvbih7XG4gICAgICAgICAgICB0eXBlOiAnc3RhcnQnLFxuICAgICAgICAgICAgY29udGVudDogYWN0aW9uQ29udGVudC50cmltKCksXG4gICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG5cbiAgICBpZiAoZm91bmRBY3Rpb25zID09PSAwKSB7XG4gICAgICBjb25zb2xlLmxvZyhg4oS577iPIE5vIGFjdGlvbnMgZm91bmQgaW4gYXJ0aWZhY3QgY29udGVudGApO1xuICAgIH1cbiAgfVxufVxuXG4vLyBDcmVhdGUgYSBnbG9iYWwgbWVzc2FnZSBwYXJzZXIgaW5zdGFuY2VcbmNvbnN0IG1lc3NhZ2VQYXJzZXIgPSBuZXcgU3RyZWFtaW5nTWVzc2FnZVBhcnNlcih7XG4gIG9uRmlsZUFjdGlvbjogYXN5bmMgKGFjdGlvbjogRmlsZUFjdGlvbikgPT4ge1xuICAgIGNvbnNvbGUubG9nKGDwn5SlIG9uRmlsZUFjdGlvbiBjYWxsZWQgd2l0aDpgLCBhY3Rpb24pO1xuICAgIGNvbnN0IHsgYWRkRmlsZSB9ID0gdXNlRmlsZVN0b3JlLmdldFN0YXRlKCk7XG4gICAgY29uc29sZS5sb2coYPCfk4EgRmlsZSBzdG9yZSBhZGRGaWxlIGZ1bmN0aW9uOmAsIHR5cGVvZiBhZGRGaWxlKTtcblxuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZyhg8J+agCBBdHRlbXB0aW5nIHRvIGFkZCBmaWxlOiAke2FjdGlvbi5maWxlUGF0aH1gKTtcbiAgICAgIGF3YWl0IGFkZEZpbGUoYWN0aW9uLmZpbGVQYXRoLCBhY3Rpb24uY29udGVudCk7XG4gICAgICBjb25zb2xlLmxvZyhg4pyFIENyZWF0ZWQvdXBkYXRlZCBmaWxlOiAke2FjdGlvbi5maWxlUGF0aH1gKTtcbiAgICAgIGNvbnNvbGUubG9nKGDwn5OEIENvbnRlbnQgcHJldmlldzogJHthY3Rpb24uY29udGVudC5zdWJzdHJpbmcoMCwgMTAwKX0uLi5gKTtcblxuICAgICAgLy8gVmVyaWZ5IGZpbGUgd2FzIGFkZGVkXG4gICAgICBjb25zdCBjdXJyZW50RmlsZXMgPSB1c2VGaWxlU3RvcmUuZ2V0U3RhdGUoKS5maWxlcztcbiAgICAgIGNvbnNvbGUubG9nKGDwn5OKIEN1cnJlbnQgZmlsZXMgYWZ0ZXIgYWRkaXRpb246YCwgT2JqZWN0LmtleXMoY3VycmVudEZpbGVzKSk7XG4gICAgICBjb25zb2xlLmxvZyhg8J+UjSBGaWxlIGV4aXN0cyBpbiBzdG9yZTpgLCBhY3Rpb24uZmlsZVBhdGggaW4gY3VycmVudEZpbGVzKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihg4p2MIEZhaWxlZCB0byBjcmVhdGUgZmlsZSAke2FjdGlvbi5maWxlUGF0aH06YCwgZXJyb3IpO1xuICAgIH1cbiAgfSxcbiAgb25TaGVsbEFjdGlvbjogKGFjdGlvbjogU2hlbGxBY3Rpb24pID0+IHtcbiAgICBjb25zb2xlLmxvZygnU2hlbGwgY29tbWFuZDonLCBhY3Rpb24uY29udGVudCk7XG4gICAgLy8gVE9ETzogSW50ZWdyYXRlIHdpdGggdGVybWluYWwgc3RvcmUgdG8gZXhlY3V0ZSBjb21tYW5kc1xuICB9LFxuICBvblN0YXJ0QWN0aW9uOiAoYWN0aW9uOiBTdGFydEFjdGlvbikgPT4ge1xuICAgIGNvbnNvbGUubG9nKCdTdGFydCBjb21tYW5kOicsIGFjdGlvbi5jb250ZW50KTtcbiAgICAvLyBUT0RPOiBJbnRlZ3JhdGUgd2l0aCB0ZXJtaW5hbCBzdG9yZSB0byBleGVjdXRlIHN0YXJ0IGNvbW1hbmRzXG4gIH0sXG59KTtcblxuZXhwb3J0IGNvbnN0IHBhcnNlTWVzc2FnZXMgPSBhc3luYyAobWVzc2FnZXM6IE1lc3NhZ2VbXSkgPT4ge1xuICBjb25zb2xlLmxvZyhg8J+agCBwYXJzZU1lc3NhZ2VzIGNhbGxlZCB3aXRoICR7bWVzc2FnZXMubGVuZ3RofSBtZXNzYWdlc2ApO1xuICBjb25zb2xlLmxvZyhg8J+TiiBDdXJyZW50IGZpbGUgc3RvcmUgc3RhdGU6YCwgdXNlRmlsZVN0b3JlLmdldFN0YXRlKCkuZmlsZXMpO1xuXG4gIGZvciAoY29uc3QgbWVzc2FnZSBvZiBtZXNzYWdlcykge1xuICAgIGlmIChtZXNzYWdlLnJvbGUgPT09ICdhc3Npc3RhbnQnKSB7XG4gICAgICBjb25zb2xlLmxvZyhg8J+kliBQYXJzaW5nIGFzc2lzdGFudCBtZXNzYWdlOiAke21lc3NhZ2UuaWR9YCk7XG4gICAgICBjb25zb2xlLmxvZyhg8J+TiyBNZXNzYWdlIG9iamVjdDpgLCBtZXNzYWdlKTtcbiAgICAgIGNvbnNvbGUubG9nKGDwn5OdIE1lc3NhZ2UgY29udGVudCB0eXBlOmAsIHR5cGVvZiBtZXNzYWdlLmNvbnRlbnQpO1xuICAgICAgY29uc29sZS5sb2coYPCfk50gTWVzc2FnZSBjb250ZW50OmAsIG1lc3NhZ2UuY29udGVudCk7XG5cbiAgICAgIC8vIENoZWNrIGlmIGNvbnRlbnQgY29udGFpbnMgYm9sdEFydGlmYWN0XG4gICAgICBpZiAobWVzc2FnZS5jb250ZW50ICYmIHR5cGVvZiBtZXNzYWdlLmNvbnRlbnQgPT09ICdzdHJpbmcnICYmIG1lc3NhZ2UuY29udGVudC5pbmNsdWRlcygnPGJvbHRBcnRpZmFjdCcpKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKGDinIUgTWVzc2FnZSBjb250YWlucyBib2x0QXJ0aWZhY3QgdGFncywgcHJvY2VlZGluZyB3aXRoIHBhcnNpbmcuLi5gKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUubG9nKGDinYwgTWVzc2FnZSBkb2VzIG5vdCBjb250YWluIGJvbHRBcnRpZmFjdCB0YWdzYCk7XG4gICAgICB9XG5cbiAgICAgIG1lc3NhZ2VQYXJzZXIucGFyc2UobWVzc2FnZS5pZCwgbWVzc2FnZS5jb250ZW50IGFzIHN0cmluZyk7XG5cbiAgICAgIC8vIENoZWNrIGZpbGUgc3RvcmUgc3RhdGUgYWZ0ZXIgcGFyc2luZ1xuICAgICAgY29uc29sZS5sb2coYPCfk4ogRmlsZSBzdG9yZSBzdGF0ZSBhZnRlciBwYXJzaW5nOmAsIHVzZUZpbGVTdG9yZS5nZXRTdGF0ZSgpLmZpbGVzKTtcbiAgICB9XG4gIH1cbn07XG5cbi8vIFRlc3QgZnVuY3Rpb24gdG8gdmVyaWZ5IHBhcnNpbmcgd29ya3NcbmV4cG9ydCBjb25zdCB0ZXN0TWVzc2FnZVBhcnNlciA9ICgpID0+IHtcbiAgY29uc29sZS5sb2coJ/Cfp6ogVGVzdGluZyBtZXNzYWdlIHBhcnNlci4uLicpO1xuXG4gIGNvbnN0IHRlc3RDb250ZW50MSA9IGBIZXJlJ3MgYSBzaW1wbGUgSFRNTCBmaWxlIHdpdGggJ0hlbGxvIFdvcmxkJyB1c2luZyB0aGUgYm9sdEFydGlmYWN0IGZvcm1hdDpcblxuPGJvbHRBcnRpZmFjdCB0eXBlPVwiZmlsZVwiIG5hbWU9XCJ0ZXN0Lmh0bWxcIj5cbjwhRE9DVFlQRSBodG1sPlxuPGh0bWwgbGFuZz1cImVuXCI+XG48aGVhZD5cbiAgICA8bWV0YSBjaGFyc2V0PVwiVVRGLThcIj5cbiAgICA8bWV0YSBuYW1lPVwidmlld3BvcnRcIiBjb250ZW50PVwid2lkdGg9ZGV2aWNlLXdpZHRoLCBpbml0aWFsLXNjYWxlPTEuMFwiPlxuICAgIDx0aXRsZT5IZWxsbyBXb3JsZDwvdGl0bGU+XG48L2hlYWQ+XG48Ym9keT5cbiAgICA8aDE+SGVsbG8gV29ybGQ8L2gxPlxuPC9ib2R5PlxuPC9odG1sPlxuPC9ib2x0QXJ0aWZhY3Q+YDtcblxuICBjb25zdCB0ZXN0Q29udGVudDIgPSBgSGVyZSdzIGEgc2ltcGxlIEhUTUwgZmlsZSBpbiB0aGUgYm9sdEFydGlmYWN0IGZvcm1hdDpcblxuPGJvbHRBcnRpZmFjdD5cbjxib2x0QWN0aW9uIHR5cGU9XCJjcmVhdGVGaWxlXCIgcGF0aD1cImluZGV4Lmh0bWxcIj5cbjwhRE9DVFlQRSBodG1sPlxuPGh0bWwgbGFuZz1cImVuXCI+XG48aGVhZD5cbiAgICA8bWV0YSBjaGFyc2V0PVwiVVRGLThcIj5cbiAgICA8bWV0YSBuYW1lPVwidmlld3BvcnRcIiBjb250ZW50PVwid2lkdGg9ZGV2aWNlLXdpZHRoLCBpbml0aWFsLXNjYWxlPTEuMFwiPlxuICAgIDx0aXRsZT5IZWxsbyBXb3JsZDwvdGl0bGU+XG48L2hlYWQ+XG48Ym9keT5cbiAgICA8aDE+SGVsbG8gV29ybGQ8L2gxPlxuPC9ib2R5PlxuPC9odG1sPlxuPC9ib2x0QWN0aW9uPlxuPC9ib2x0QXJ0aWZhY3Q+YDtcblxuICBjb25zb2xlLmxvZygn8J+nqiBUZXN0aW5nIG1lc3NhZ2UgcGFyc2VyIHdpdGggZm9ybWF0IDEuLi4nKTtcbiAgbWVzc2FnZVBhcnNlci5wYXJzZSgndGVzdC1tZXNzYWdlLTEnLCB0ZXN0Q29udGVudDEpO1xuXG4gIGNvbnNvbGUubG9nKCfwn6eqIFRlc3RpbmcgbWVzc2FnZSBwYXJzZXIgd2l0aCBmb3JtYXQgMi4uLicpO1xuICBtZXNzYWdlUGFyc2VyLnBhcnNlKCd0ZXN0LW1lc3NhZ2UtMicsIHRlc3RDb250ZW50Mik7XG59O1xuXG4vLyBUZXN0IGZ1bmN0aW9uIHdpdGggdGhlIGV4YWN0IEFJIHJlc3BvbnNlIGZvcm1hdFxuZXhwb3J0IGNvbnN0IHRlc3RBSVJlc3BvbnNlID0gKCkgPT4ge1xuICBjb25zdCBhaVJlc3BvbnNlID0gYDxib2x0QXJ0aWZhY3Q+PGJvbHRBY3Rpb24gdHlwZT1cImZpbGVcIiBmaWxlUGF0aD1cInRlc3QuaHRtbFwiPjwhRE9DVFlQRSBodG1sPlxuPGh0bWw+XG48aGVhZD48dGl0bGU+VGVzdDwvdGl0bGU+PC9oZWFkPlxuPGJvZHk+PGgxPkhlbGxvIFdvcmxkPC9oMT48L2JvZHk+XG48L2h0bWw+PC9ib2x0QWN0aW9uPjwvYm9sdEFydGlmYWN0PmA7XG5cbiAgY29uc29sZS5sb2coJ/Cfp6ogVGVzdGluZyB3aXRoIGV4YWN0IEFJIHJlc3BvbnNlIGZvcm1hdC4uLicpO1xuICBjb25zb2xlLmxvZygn8J+ThCBUZXN0IGNvbnRlbnQ6JywgYWlSZXNwb25zZSk7XG4gIG1lc3NhZ2VQYXJzZXIucGFyc2UoJ2FpLXJlc3BvbnNlLXRlc3QnLCBhaVJlc3BvbnNlKTtcbn07XG5cbi8vIERlYnVnIGZ1bmN0aW9uIHRvIHRlc3QgZmlsZSBjcmVhdGlvblxuZXhwb3J0IGNvbnN0IGRlYnVnRmlsZUNyZWF0aW9uID0gKCkgPT4ge1xuICBjb25zb2xlLmxvZygn8J+UpyBUZXN0aW5nIGZpbGUgY3JlYXRpb24gZGlyZWN0bHkuLi4nKTtcbiAgY29uc3QgeyBhZGRGaWxlIH0gPSB1c2VGaWxlU3RvcmUuZ2V0U3RhdGUoKTtcblxuICBhZGRGaWxlKCdkZWJ1Zy10ZXN0Lmh0bWwnLCAnPGgxPkRlYnVnIFRlc3Q8L2gxPicpXG4gICAgLnRoZW4oKCkgPT4ge1xuICAgICAgY29uc29sZS5sb2coJ+KchSBEaXJlY3QgZmlsZSBjcmVhdGlvbiBzdWNjZXNzZnVsJyk7XG4gICAgICBjb25zdCBmaWxlcyA9IHVzZUZpbGVTdG9yZS5nZXRTdGF0ZSgpLmZpbGVzO1xuICAgICAgY29uc29sZS5sb2coJ/Cfk4EgQ3VycmVudCBmaWxlczonLCBPYmplY3Qua2V5cyhmaWxlcykpO1xuICAgICAgY29uc29sZS5sb2coJ/Cfk4QgRGVidWcgZmlsZSBjb250ZW50OicsIGZpbGVzWydkZWJ1Zy10ZXN0Lmh0bWwnXSk7XG4gICAgfSlcbiAgICAuY2F0Y2goKGVycm9yKSA9PiB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgRGlyZWN0IGZpbGUgY3JlYXRpb24gZmFpbGVkOicsIGVycm9yKTtcbiAgICB9KTtcbn07XG5cbi8vIEZ1bmN0aW9uIHRvIHRlc3QgdGhlIGNvbXBsZXRlIGZsb3dcbmV4cG9ydCBjb25zdCB0ZXN0Q29tcGxldGVGbG93ID0gKCkgPT4ge1xuICBjb25zb2xlLmxvZygn8J+nqiBUZXN0aW5nIGNvbXBsZXRlIEFJIHJlc3BvbnNlIGZsb3cuLi4nKTtcblxuICAvLyBTaW11bGF0ZSBhbiBBSSByZXNwb25zZSB3aXRoIGJvbHRBcnRpZmFjdFxuICBjb25zdCBtb2NrQUlSZXNwb25zZSA9IHtcbiAgICBpZDogJ3Rlc3QtYWktcmVzcG9uc2UnLFxuICAgIHJvbGU6ICdhc3Npc3RhbnQnIGFzIGNvbnN0LFxuICAgIGNvbnRlbnQ6IGBJJ2xsIGNyZWF0ZSBhIHNpbXBsZSBIVE1MIGZpbGUgZm9yIHlvdTpcblxuPGJvbHRBcnRpZmFjdCBpZD1cInNpbXBsZS1odG1sXCIgdGl0bGU9XCJTaW1wbGUgSFRNTCBGaWxlXCI+XG48Ym9sdEFjdGlvbiB0eXBlPVwiZmlsZVwiIGZpbGVQYXRoPVwiaW5kZXguaHRtbFwiPjwhRE9DVFlQRSBodG1sPlxuPGh0bWwgbGFuZz1cImVuXCI+XG48aGVhZD5cbiAgICA8bWV0YSBjaGFyc2V0PVwiVVRGLThcIj5cbiAgICA8bWV0YSBuYW1lPVwidmlld3BvcnRcIiBjb250ZW50PVwid2lkdGg9ZGV2aWNlLXdpZHRoLCBpbml0aWFsLXNjYWxlPTEuMFwiPlxuICAgIDx0aXRsZT5UZXN0IFBhZ2U8L3RpdGxlPlxuPC9oZWFkPlxuPGJvZHk+XG4gICAgPGgxPkhlbGxvIGZyb20gQUkhPC9oMT5cbiAgICA8cD5UaGlzIGZpbGUgd2FzIGNyZWF0ZWQgYnkgdGhlIEFJIHJlc3BvbnNlIHBhcnNlci48L3A+XG48L2JvZHk+XG48L2h0bWw+PC9ib2x0QWN0aW9uPlxuPC9ib2x0QXJ0aWZhY3Q+XG5cblRoZSBIVE1MIGZpbGUgaGFzIGJlZW4gY3JlYXRlZCBzdWNjZXNzZnVsbHkuYFxuICB9O1xuXG4gIGNvbnNvbGUubG9nKCfwn5OkIFNpbXVsYXRpbmcgQUkgcmVzcG9uc2U6JywgbW9ja0FJUmVzcG9uc2UpO1xuICBwYXJzZU1lc3NhZ2VzKFttb2NrQUlSZXNwb25zZV0pO1xufTtcblxuLy8gTWFrZSB0ZXN0IGZ1bmN0aW9ucyBhdmFpbGFibGUgZ2xvYmFsbHkgZm9yIGRlYnVnZ2luZ1xuaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICh3aW5kb3cgYXMgYW55KS50ZXN0TWVzc2FnZVBhcnNlciA9IHRlc3RNZXNzYWdlUGFyc2VyO1xuICAod2luZG93IGFzIGFueSkudGVzdEFJUmVzcG9uc2UgPSB0ZXN0QUlSZXNwb25zZTtcbiAgKHdpbmRvdyBhcyBhbnkpLmRlYnVnRmlsZUNyZWF0aW9uID0gZGVidWdGaWxlQ3JlYXRpb247XG4gICh3aW5kb3cgYXMgYW55KS50ZXN0Q29tcGxldGVGbG93ID0gdGVzdENvbXBsZXRlRmxvdztcbn1cblxuLy8gU2ltcGxlIGZ1bmN0aW9uIHRvIGV4dHJhY3QgZmlsZXMgZnJvbSBtZXNzYWdlIGNvbnRlbnRcbmV4cG9ydCBmdW5jdGlvbiBwYXJzZU1lc3NhZ2VGb3JGaWxlcyhjb250ZW50OiBzdHJpbmcpOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+IHtcbiAgY29uc3QgZmlsZXM6IFJlY29yZDxzdHJpbmcsIHN0cmluZz4gPSB7fTtcbiAgXG4gIC8vIFBhcnNlIGJvbHRBcnRpZmFjdCBhbmQgYm9sdEFjdGlvbiB0YWdzXG4gIGNvbnN0IGFydGlmYWN0UmVnZXggPSAvPGJvbHRBcnRpZmFjdFtePl0qPihbXFxzXFxTXSo/KTxcXC9ib2x0QXJ0aWZhY3Q+L2c7XG4gIGxldCBhcnRpZmFjdE1hdGNoO1xuXG4gIHdoaWxlICgoYXJ0aWZhY3RNYXRjaCA9IGFydGlmYWN0UmVnZXguZXhlYyhjb250ZW50KSkgIT09IG51bGwpIHtcbiAgICBjb25zdCBhcnRpZmFjdENvbnRlbnQgPSBhcnRpZmFjdE1hdGNoWzFdO1xuICAgIFxuICAgIC8vIEV4dHJhY3QgZmlsZSBhY3Rpb25zXG4gICAgY29uc3QgZmlsZUFjdGlvblJlZ2V4ID0gLzxib2x0QWN0aW9uXFxzK3R5cGU9XCJmaWxlXCJcXHMrZmlsZVBhdGg9XCIoW15cIl0rKVwiXFxzKj4oW1xcc1xcU10qPyk8XFwvYm9sdEFjdGlvbj4vZztcbiAgICBsZXQgZmlsZU1hdGNoO1xuXG4gICAgd2hpbGUgKChmaWxlTWF0Y2ggPSBmaWxlQWN0aW9uUmVnZXguZXhlYyhhcnRpZmFjdENvbnRlbnQpKSAhPT0gbnVsbCkge1xuICAgICAgY29uc3QgWywgZmlsZVBhdGgsIGZpbGVDb250ZW50XSA9IGZpbGVNYXRjaDtcbiAgICAgIGZpbGVzW2ZpbGVQYXRoXSA9IGZpbGVDb250ZW50LnRyaW0oKTtcbiAgICB9XG4gIH1cblxuICByZXR1cm4gZmlsZXM7XG59XG4iXSwibmFtZXMiOlsidXNlRmlsZVN0b3JlIiwiU3RyZWFtaW5nTWVzc2FnZVBhcnNlciIsInBhcnNlIiwibWVzc2FnZUlkIiwiY29udGVudCIsImNvbnNvbGUiLCJsb2ciLCJzdWJzdHJpbmciLCJhcnRpZmFjdFJlZ2V4IiwiYXJ0aWZhY3RNYXRjaCIsImZvdW5kQXJ0aWZhY3RzIiwiZXhlYyIsImZ1bGxNYXRjaCIsImFydGlmYWN0Q29udGVudCIsInNpbXBsaWZpZWRGaWxlTWF0Y2giLCJtYXRjaCIsImZpbGVOYW1lIiwiY2FsbGJhY2tzIiwib25GaWxlQWN0aW9uIiwidHlwZSIsImZpbGVQYXRoIiwidHJpbSIsInBhcnNlQWN0aW9ucyIsImluY2x1ZGVzIiwiYWN0aW9uUmVnZXgiLCJhY3Rpb25NYXRjaCIsImZvdW5kQWN0aW9ucyIsImF0dHJpYnV0ZXMiLCJhY3Rpb25Db250ZW50IiwidHlwZU1hdGNoIiwiZmlsZVBhdGhNYXRjaCIsInBhdGhNYXRjaCIsIm9uU2hlbGxBY3Rpb24iLCJvblN0YXJ0QWN0aW9uIiwiY29uc3RydWN0b3IiLCJtZXNzYWdlUGFyc2VyIiwiYWN0aW9uIiwiYWRkRmlsZSIsImdldFN0YXRlIiwiY3VycmVudEZpbGVzIiwiZmlsZXMiLCJPYmplY3QiLCJrZXlzIiwiZXJyb3IiLCJwYXJzZU1lc3NhZ2VzIiwibWVzc2FnZXMiLCJsZW5ndGgiLCJtZXNzYWdlIiwicm9sZSIsImlkIiwidGVzdE1lc3NhZ2VQYXJzZXIiLCJ0ZXN0Q29udGVudDEiLCJ0ZXN0Q29udGVudDIiLCJ0ZXN0QUlSZXNwb25zZSIsImFpUmVzcG9uc2UiLCJkZWJ1Z0ZpbGVDcmVhdGlvbiIsInRoZW4iLCJjYXRjaCIsInRlc3RDb21wbGV0ZUZsb3ciLCJtb2NrQUlSZXNwb25zZSIsIndpbmRvdyIsInBhcnNlTWVzc2FnZUZvckZpbGVzIiwiZmlsZUFjdGlvblJlZ2V4IiwiZmlsZU1hdGNoIiwiZmlsZUNvbnRlbnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/messageParser.ts\n"));

/***/ })

});