"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/route";
exports.ids = ["app/api/chat/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=E%3A%5CWorkspace%5Cprogramming%5Cwe0-main%5Capps%5Cwe-dev-next%5Csrc%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=E%3A%5CWorkspace%5Cprogramming%5Cwe0-main%5Capps%5Cwe-dev-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=E%3A%5CWorkspace%5Cprogramming%5Cwe0-main%5Capps%5Cwe-dev-next%5Csrc%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=E%3A%5CWorkspace%5Cprogramming%5Cwe0-main%5Capps%5Cwe-dev-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var E_Workspace_programming_we0_main_apps_we_dev_next_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chat/route.ts */ \"(rsc)/./src/app/api/chat/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/route\",\n        pathname: \"/api/chat\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/route\"\n    },\n    resolvedPagePath: \"E:\\\\Workspace\\\\programming\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\app\\\\api\\\\chat\\\\route.ts\",\n    nextConfigOutput,\n    userland: E_Workspace_programming_we0_main_apps_we_dev_next_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/chat/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=E%3A%5CWorkspace%5Cprogramming%5Cwe0-main%5Capps%5Cwe-dev-next%5Csrc%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=E%3A%5CWorkspace%5Cprogramming%5Cwe0-main%5Capps%5Cwe-dev-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/chat/action.ts":
/*!************************************!*\
  !*** ./src/app/api/chat/action.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MAX_TOKENS: () => (/* binding */ MAX_TOKENS),\n/* harmony export */   generateObjectFn: () => (/* binding */ generateObjectFn),\n/* harmony export */   getOpenAIModel: () => (/* binding */ getOpenAIModel),\n/* harmony export */   streamTextFn: () => (/* binding */ streamTextFn)\n/* harmony export */ });\n/* harmony import */ var _ai_sdk_openai__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ai-sdk/openai */ \"(rsc)/./node_modules/.pnpm/@ai-sdk+openai@1.0.19_zod@3.24.1/node_modules/@ai-sdk/openai/dist/index.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/.pnpm/zod@3.24.1/node_modules/zod/lib/index.mjs\");\n/* harmony import */ var ai__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ai */ \"(rsc)/./node_modules/.pnpm/ai@4.3.17_react@18.3.1_zod@3.24.1/node_modules/ai/dist/index.mjs\");\n/* harmony import */ var _model_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../model/config */ \"(rsc)/./src/app/api/model/config.ts\");\n/* harmony import */ var _ai_sdk_deepseek__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ai-sdk/deepseek */ \"(rsc)/./node_modules/.pnpm/@ai-sdk+deepseek@0.1.10_zod@3.24.1/node_modules/@ai-sdk/deepseek/dist/index.mjs\");\n\n\n\n\n\nconst MAX_TOKENS = 16000;\nlet initOptions = {};\nfunction getOpenAIModel(baseURL, apiKey, model) {\n    const provider = _model_config__WEBPACK_IMPORTED_MODULE_0__.modelConfig.find((item)=>item.modelKey === model)?.provider;\n    if (provider === \"deepseek\") {\n        const deepseek = (0,_ai_sdk_deepseek__WEBPACK_IMPORTED_MODULE_1__.createDeepSeek)({\n            apiKey,\n            baseURL\n        });\n        initOptions = {};\n        return deepseek(model);\n    }\n    if (provider && provider.indexOf(\"anthropic\") > -1) {\n        const openai = (0,_ai_sdk_openai__WEBPACK_IMPORTED_MODULE_2__.createOpenAI)({\n            apiKey,\n            baseURL\n        });\n        initOptions = {\n            maxTokens: 8192\n        };\n        return openai(model);\n    }\n    const openai = (0,_ai_sdk_openai__WEBPACK_IMPORTED_MODULE_2__.createOpenAI)({\n        baseURL,\n        apiKey\n    });\n    initOptions = {};\n    return openai(model);\n}\nconst defaultModel = getOpenAIModel(process.env.THIRD_API_URL, process.env.THIRD_API_KEY, \"anthropic/claude-3.5-sonnet\");\nasync function generateObjectFn(messages) {\n    return (0,ai__WEBPACK_IMPORTED_MODULE_3__.generateObject)({\n        model: getOpenAIModel(process.env.THIRD_API_URL, process.env.THIRD_API_KEY, \"openai/gpt-4o-mini\"),\n        schema: zod__WEBPACK_IMPORTED_MODULE_4__.z.object({\n            files: zod__WEBPACK_IMPORTED_MODULE_4__.z.array(zod__WEBPACK_IMPORTED_MODULE_4__.z.string())\n        }),\n        messages: (0,ai__WEBPACK_IMPORTED_MODULE_3__.convertToCoreMessages)(messages)\n    });\n}\nfunction streamTextFn(messages, options, modelKey) {\n    const { apiKey = process.env.THIRD_API_KEY, apiUrl = process.env.THIRD_API_URL } = _model_config__WEBPACK_IMPORTED_MODULE_0__.modelConfig.find((item)=>item.modelKey === modelKey);\n    const model = getOpenAIModel(apiUrl, apiKey, modelKey);\n    const newMessages = messages.map((item)=>{\n        if (item.role === \"assistant\") {\n            delete item.parts;\n        }\n        return item;\n    });\n    return (0,ai__WEBPACK_IMPORTED_MODULE_3__.streamText)({\n        model: model || defaultModel,\n        messages: (0,ai__WEBPACK_IMPORTED_MODULE_3__.convertToCoreMessages)(newMessages),\n        maxTokens: MAX_TOKENS,\n        ...initOptions,\n        ...options\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/action.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/chat/backend.ts":
/*!*************************************!*\
  !*** ./src/app/api/chat/backend.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   backendLanguageFunctionRegister: () => (/* binding */ backendLanguageFunctionRegister)\n/* harmony export */ });\nconst backendLanguageFunctionRegister = {\n    \"java\": resolveJava,\n    \"node\": resolveNode,\n    \"go\": resolveGo,\n    \"python\": resolvePython\n};\nfunction resolveJava(extra) {\n    let promptArr = [];\n    promptArr.push(\"IMPORTANT: Build the backend project using Maven, use SpringBoot as the backend framework, use Lombok, and use MyBatis-Plus as the ORM framework. Implement the Controller layer, Service layer, ServiceImpl layer, Mapper layer, and Model layer\");\n    promptArr.push(\"IMPORTANT: Remember to create the Application.java file, and use YAML format for SpringBoot configuration\");\n    return promptArr;\n}\nfunction resolveNode(extra) {\n    let promptArr = [];\n    promptArr.push(\"IMPORTANT: Build the backend project using Node.js, use Express as the backend framework. Implement the Controller layer, Service layer, Model layer (including database CRUD Dao layer)\");\n    promptArr.push(\"IMPORTANT: Remember to create the index.js file, use .env for Express configuration\");\n    return promptArr;\n}\nfunction resolveGo(extra) {\n    let promptArr = [];\n    promptArr.push(\"IMPORTANT: Build the backend project using Go, use Gin as the backend framework, use GORM as the ORM framework. Implement the Controller layer, Service layer, Model layer, and Mapper layer\");\n    promptArr.push(\"IMPORTANT: Remember to create the main.go file, use YAML format for Gin configuration\");\n    return promptArr;\n}\nfunction resolvePython(extra) {\n    let promptArr = [];\n    promptArr.push(\"IMPORTANT: Build the backend project using Python, use FastAPI as the backend framework, use SQLAlchemy as the ORM framework. Implement the Controller layer, Service layer, Model layer, and Mapper layer\");\n    promptArr.push(\"IMPORTANT: Remember to create the main.py file, include required packages in requirements.txt, use YAML format for FastAPI configuration\");\n    return promptArr;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/backend.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/chat/cache.ts":
/*!***********************************!*\
  !*** ./src/app/api/chat/cache.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cacheFunctionRegister: () => (/* binding */ cacheFunctionRegister)\n/* harmony export */ });\nconst cacheFunctionRegister = {\n    \"redis\": resolveRedis\n};\nfunction resolveRedis(extra) {\n    let promptArr = [];\n    let username = extra.extra[\"cacheUsername\"] ?? \"\";\n    let password = extra.extra[\"cachePassword\"] ?? \"root\";\n    let databaseUrl = extra.extra[\"cacheUrl\"] ?? \"localhost:3306\";\n    promptArr.push(`IMPORTANT: Use Redis for caching. Redis URL is ${databaseUrl}, username is ${username}, password is ${password}. Please write this configuration to the backend.`);\n    return promptArr;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9jaGF0L2NhY2hlLnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFFTyxNQUFNQSx3QkFBd0I7SUFDakMsU0FBUUM7QUFDVixFQUFDO0FBRUQsU0FBU0EsYUFBYUMsS0FBaUI7SUFDckMsSUFBSUMsWUFBWSxFQUFFO0lBQ2xCLElBQUlDLFdBQVdGLE1BQU1BLEtBQUssQ0FBQyxnQkFBZ0IsSUFBRTtJQUM3QyxJQUFJRyxXQUFXSCxNQUFNQSxLQUFLLENBQUMsZ0JBQWdCLElBQUU7SUFDN0MsSUFBSUksY0FBY0osTUFBTUEsS0FBSyxDQUFDLFdBQVcsSUFBRTtJQUMzQ0MsVUFBVUksSUFBSSxDQUFDLENBQUMsK0NBQStDLEVBQUVELFlBQVksY0FBYyxFQUFFRixTQUFTLGNBQWMsRUFBRUMsU0FBUyxpREFBaUQsQ0FBQztJQUNqTCxPQUFPRjtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHdlLWRldi9uZXh0Ly4vc3JjL2FwcC9hcGkvY2hhdC9jYWNoZS50cz83ODA2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHByb21wdEV4dHJhIH0gZnJvbSBcIi4vcHJvbXB0XCI7XG5cbmV4cG9ydCBjb25zdCBjYWNoZUZ1bmN0aW9uUmVnaXN0ZXIgPSB7XG4gICAgXCJyZWRpc1wiOnJlc29sdmVSZWRpc1xuICB9XG5cbiAgZnVuY3Rpb24gcmVzb2x2ZVJlZGlzKGV4dHJhOnByb21wdEV4dHJhKXtcbiAgICBsZXQgcHJvbXB0QXJyID0gW107XG4gICAgbGV0IHVzZXJuYW1lID0gZXh0cmEuZXh0cmFbJ2NhY2hlVXNlcm5hbWUnXT8/XCJcIjtcbiAgICBsZXQgcGFzc3dvcmQgPSBleHRyYS5leHRyYVsnY2FjaGVQYXNzd29yZCddPz9cInJvb3RcIjtcbiAgICBsZXQgZGF0YWJhc2VVcmwgPSBleHRyYS5leHRyYVsnY2FjaGVVcmwnXT8/XCJsb2NhbGhvc3Q6MzMwNlwiO1xuICAgIHByb21wdEFyci5wdXNoKGBJTVBPUlRBTlQ6IFVzZSBSZWRpcyBmb3IgY2FjaGluZy4gUmVkaXMgVVJMIGlzICR7ZGF0YWJhc2VVcmx9LCB1c2VybmFtZSBpcyAke3VzZXJuYW1lfSwgcGFzc3dvcmQgaXMgJHtwYXNzd29yZH0uIFBsZWFzZSB3cml0ZSB0aGlzIGNvbmZpZ3VyYXRpb24gdG8gdGhlIGJhY2tlbmQuYCk7XG4gICAgcmV0dXJuIHByb21wdEFycjtcbiAgfSJdLCJuYW1lcyI6WyJjYWNoZUZ1bmN0aW9uUmVnaXN0ZXIiLCJyZXNvbHZlUmVkaXMiLCJleHRyYSIsInByb21wdEFyciIsInVzZXJuYW1lIiwicGFzc3dvcmQiLCJkYXRhYmFzZVVybCIsInB1c2giXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/cache.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/chat/database.ts":
/*!**************************************!*\
  !*** ./src/app/api/chat/database.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   databaseeFunctionRegister: () => (/* binding */ databaseeFunctionRegister)\n/* harmony export */ });\nconst databaseeFunctionRegister = {\n    \"mysql\": resolveMySql\n};\nfunction resolveMySql(extra) {\n    let promptArr = [];\n    // Get configuration information from extra['extra']['databaseConfig']\n    let username = extra[\"extra\"]?.[\"databaseConfig\"]?.[\"username\"] ?? \"root\";\n    let password = extra[\"extra\"]?.[\"databaseConfig\"]?.[\"password\"] ?? \"root\";\n    let databaseUrl = extra[\"extra\"]?.[\"databaseConfig\"]?.[\"url\"] ?? \"localhost:3306\";\n    promptArr.push(\"IMPORTANT: Based on the frontend code, place SQL files in the SQL folder, implement SQL statements, write database creation and table creation statements, and include some sample data to be inserted into the database\");\n    promptArr.push(`IMPORTANT: Use MySQL as the database, MySQL URL is ${databaseUrl}, username is ${username}, password is ${password}, write this into the backend configuration.`);\n    return promptArr;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/database.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/chat/handlers/builderHandler.ts":
/*!*****************************************************!*\
  !*** ./src/app/api/chat/handlers/builderHandler.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handleBuilderMode: () => (/* binding */ handleBuilderMode)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/.pnpm/uuid@11.0.5/node_modules/uuid/dist/esm/v4.js\");\n/* harmony import */ var _utils_streamResponse__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/streamResponse */ \"(rsc)/./src/app/api/chat/utils/streamResponse.ts\");\n/* harmony import */ var _utils_tokens__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/tokens */ \"(rsc)/./src/utils/tokens.ts\");\n/* harmony import */ var _utils_promptBuilder__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/promptBuilder */ \"(rsc)/./src/app/api/chat/utils/promptBuilder.ts\");\n/* harmony import */ var _utils_fileTypeDetector__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/fileTypeDetector */ \"(rsc)/./src/app/api/chat/utils/fileTypeDetector.ts\");\n/* harmony import */ var _utils_diffGenerator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/diffGenerator */ \"(rsc)/./src/app/api/chat/utils/diffGenerator.ts\");\n/* harmony import */ var _utils_tokenHandler__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/tokenHandler */ \"(rsc)/./src/app/api/chat/utils/tokenHandler.ts\");\n/* harmony import */ var _utils_fileProcessor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/fileProcessor */ \"(rsc)/./src/app/api/chat/utils/fileProcessor.ts\");\n/* harmony import */ var _utils_screenshotone__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../utils/screenshotone */ \"(rsc)/./src/app/api/chat/utils/screenshotone.ts\");\n\n\n\n\n\n\n\n\n\nasync function handleBuilderMode(messages, model, userId, otherConfig, tools) {\n    const historyMessages = JSON.parse(JSON.stringify(messages));\n    // Directory tree search\n    // select files from the list of code file from the project that might be useful for the current request from the user\n    const { files, allContent } = (0,_utils_fileProcessor__WEBPACK_IMPORTED_MODULE_6__.processFiles)(messages);\n    // Check if the last message contains a URL\n    const lastMessage = messages[messages.length - 1];\n    if (lastMessage.role === \"user\" && lastMessage.content.startsWith(\"#\")) {\n        const urlMatch = lastMessage.content.match(/https?:\\/\\/[^\\s]+/);\n        if (urlMatch) {\n            try {\n                const imageUrl = await (0,_utils_screenshotone__WEBPACK_IMPORTED_MODULE_7__.screenshotOne)(urlMatch[0]);\n                console.log(imageUrl, \"imageUrl\");\n                messages.splice(messages.length - 1, 0, {\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(),\n                    role: \"user\",\n                    content: `1:1 Restore this page`,\n                    experimental_attachments: [\n                        {\n                            name: (0,uuid__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(),\n                            contentType: \"image/png\",\n                            url: imageUrl\n                        }\n                    ]\n                });\n            } catch (error) {\n                console.error(\"Screenshot capture failed:\", error);\n            }\n        }\n    }\n    const filesPath = Object.keys(files);\n    let nowFiles = files;\n    const type = (0,_utils_fileTypeDetector__WEBPACK_IMPORTED_MODULE_3__.determineFileType)(filesPath);\n    if ((0,_utils_tokens__WEBPACK_IMPORTED_MODULE_1__.estimateTokens)(allContent) > 128000) {\n        const { files } = (0,_utils_fileProcessor__WEBPACK_IMPORTED_MODULE_6__.processFiles)(messages, true);\n        nowFiles = await (0,_utils_tokenHandler__WEBPACK_IMPORTED_MODULE_5__.handleTokenLimit)(messages, files, filesPath);\n        const historyDiffString = (0,_utils_diffGenerator__WEBPACK_IMPORTED_MODULE_4__.getHistoryDiff)(historyMessages, filesPath, nowFiles);\n        messages[messages.length - 1].content = (0,_utils_promptBuilder__WEBPACK_IMPORTED_MODULE_2__.buildMaxSystemPrompt)(filesPath, type, nowFiles, historyDiffString, otherConfig) + \"Note the requirements above, when writing code, do not give me markdown, output must be XML!! Emphasis!; My question is: \" + messages[messages.length - 1].content;\n    // console.log(messages[0].content, 'messages[messages.length - 1].content')\n    } else {\n        const systemPrompt = (0,_utils_promptBuilder__WEBPACK_IMPORTED_MODULE_2__.buildSystemPrompt)(type, otherConfig);\n        const finalPrompt = systemPrompt + '\\n\\nCRITICAL: You MUST use boltArtifact format with boltAction tags to create files. Do not give me markdown code blocks. Output must be XML format with <boltArtifact> and <boltAction type=\"file\" filePath=\"...\"> tags!!\\n\\nExample format:\\n<boltArtifact id=\"example\" title=\"Example File\">\\n<boltAction type=\"file\" filePath=\"index.html\">\\n<!DOCTYPE html>\\n<html>...</html>\\n</boltAction>\\n</boltArtifact>\\n\\nMy question is: ' + messages[messages.length - 1].content;\n        messages[messages.length - 1].content = finalPrompt;\n        console.log(\"\\uD83D\\uDD27 Builder mode system prompt applied:\", finalPrompt.substring(0, 300) + \"...\");\n        console.log(\"\\uD83D\\uDD27 Full prompt length:\", finalPrompt.length);\n    }\n    try {\n        return await (0,_utils_streamResponse__WEBPACK_IMPORTED_MODULE_0__.streamResponse)(messages, model, userId, tools);\n    } catch (err) {\n        throw err;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/handlers/builderHandler.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/chat/handlers/chatHandler.ts":
/*!**************************************************!*\
  !*** ./src/app/api/chat/handlers/chatHandler.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handleChatMode: () => (/* binding */ handleChatMode)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/.pnpm/uuid@11.0.5/node_modules/uuid/dist/esm/v4.js\");\n/* harmony import */ var _action__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../action */ \"(rsc)/./src/app/api/chat/action.ts\");\n/* harmony import */ var _prompt__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../prompt */ \"(rsc)/./src/app/api/chat/prompt.ts\");\n/* harmony import */ var _utils_tokens__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/tokens */ \"(rsc)/./src/utils/tokens.ts\");\n/* harmony import */ var _switchable_stream__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../switchable-stream */ \"(rsc)/./src/app/api/chat/switchable-stream.ts\");\n/* harmony import */ var ai__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ai */ \"(rsc)/./node_modules/.pnpm/ai@4.3.17_react@18.3.1_zod@3.24.1/node_modules/ai/dist/index.mjs\");\n/* harmony import */ var _app_api_chat_utils_json2zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/api/chat/utils/json2zod */ \"(rsc)/./src/app/api/chat/utils/json2zod.ts\");\n\n\n\n\n\n\n\nconst MAX_RESPONSE_SEGMENTS = 2;\nasync function handleChatMode(messages, model, userId, tools) {\n    const stream = new _switchable_stream__WEBPACK_IMPORTED_MODULE_3__[\"default\"]();\n    let toolList = {};\n    if (tools && tools.length > 0) {\n        toolList = tools.reduce((obj, { name, ...args })=>{\n            obj[name] = (0,ai__WEBPACK_IMPORTED_MODULE_5__.tool)({\n                id: args.id,\n                description: args.description,\n                parameters: (0,_app_api_chat_utils_json2zod__WEBPACK_IMPORTED_MODULE_4__.jsonSchemaToZodSchema)(args.parameters),\n                execute: async (input)=>{\n                    return input;\n                }\n            });\n            return obj;\n        }, {});\n    }\n    const options = {\n        tools: toolList,\n        toolCallStreaming: true,\n        onError: (error)=>{\n            const uuid = (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])();\n            const msg = error?.errors?.[0]?.responseBody;\n            throw new Error(`${msg || JSON.stringify(error)} logid ${uuid}`);\n        },\n        onFinish: async (response)=>{\n            const { text: content, finishReason } = response;\n            if (finishReason !== \"length\") {\n                const tokens = (0,_utils_tokens__WEBPACK_IMPORTED_MODULE_2__.estimateTokens)(content);\n                if (userId) {\n                    await (0,_utils_tokens__WEBPACK_IMPORTED_MODULE_2__.deductUserTokens)(userId, tokens);\n                }\n                return stream.close();\n            }\n            if (stream.switches >= MAX_RESPONSE_SEGMENTS) {\n                throw Error(\"Cannot continue message: Maximum segments reached\");\n            }\n            messages.push({\n                id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n                role: \"assistant\",\n                content\n            });\n            messages.push({\n                id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n                role: \"user\",\n                content: _prompt__WEBPACK_IMPORTED_MODULE_1__.CONTINUE_PROMPT\n            });\n        }\n    };\n    const result = await (0,_action__WEBPACK_IMPORTED_MODULE_0__.streamTextFn)(messages, options, model);\n    return result.toDataStreamResponse({\n        sendReasoning: true\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/handlers/chatHandler.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/chat/messagepParseJson.ts":
/*!***********************************************!*\
  !*** ./src/app/api/chat/messagepParseJson.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseMessage: () => (/* binding */ parseMessage)\n/* harmony export */ });\n/* harmony import */ var _utils_fileProcessor__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/fileProcessor */ \"(rsc)/./src/app/api/chat/utils/fileProcessor.ts\");\n\nfunction parseMessage(content) {\n    // 匹配 boltArtifact 标签的正则表达式\n    const artifactRegex = /<boltArtifact[^>]*>([\\s\\S]*?)<\\/boltArtifact>/;\n    // 如果内容中包含 boltArtifact\n    if (artifactRegex.test(content)) {\n        // 移除 boltArtifact 部分，替换为固定文本\n        // 提取 boltArtifact 中的内容\n        const match = content.match(artifactRegex);\n        if (match) {\n            const artifactContent = match[1].trim();\n            // 解析文件内容\n            const files = {};\n            const boltActionRegex = /<boltAction type=\"file\" filePath=\"([^\"]+)\">([\\s\\S]*?)<\\/boltAction>/g;\n            let boltMatch;\n            while((boltMatch = boltActionRegex.exec(artifactContent)) !== null){\n                const [_, filePath, fileContent] = boltMatch;\n                if (!_utils_fileProcessor__WEBPACK_IMPORTED_MODULE_0__.excludeFiles.includes(filePath)) {\n                    files[filePath] = fileContent.trim();\n                }\n            }\n            const newContent = content.replace(artifactRegex, `已经修改好了的目录${JSON.stringify(Object.keys(files))}`);\n            return {\n                content: newContent.trim(),\n                files\n            };\n        }\n    }\n    // 如果没有 boltArtifact，返回原始内容\n    return {\n        content\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/messagepParseJson.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/chat/prompt.ts":
/*!************************************!*\
  !*** ./src/app/api/chat/prompt.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CONTINUE_PROMPT: () => (/* binding */ CONTINUE_PROMPT),\n/* harmony export */   MODIFICATIONS_TAG_NAME: () => (/* binding */ MODIFICATIONS_TAG_NAME),\n/* harmony export */   WORK_DIR: () => (/* binding */ WORK_DIR),\n/* harmony export */   WORK_DIR_NAME: () => (/* binding */ WORK_DIR_NAME),\n/* harmony export */   getSystemPrompt: () => (/* binding */ getSystemPrompt),\n/* harmony export */   typeEnum: () => (/* binding */ typeEnum)\n/* harmony export */ });\n/* harmony import */ var _utils_markdown__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils/markdown */ \"(rsc)/./src/utils/markdown.ts\");\n/* harmony import */ var _utils_stripIndent__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../utils/stripIndent */ \"(rsc)/./src/utils/stripIndent.ts\");\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/app/api/chat/database.ts\");\n/* harmony import */ var _backend__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./backend */ \"(rsc)/./src/app/api/chat/backend.ts\");\n/* harmony import */ var _cache__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./cache */ \"(rsc)/./src/app/api/chat/cache.ts\");\n\n\n\n\n\nconst WORK_DIR_NAME = \"project\";\nconst WORK_DIR = `/home/<USER>"bolt_file_modifications\";\nconst iconName = [\n    \"add-friends\",\n    \"add\",\n    \"add2\",\n    \"album\",\n    \"arrow\",\n    \"at\",\n    \"back\",\n    \"back2\",\n    \"bellring-off\",\n    \"bellring-on\",\n    \"camera\",\n    \"cellphone\",\n    \"clip\",\n    \"close\",\n    \"close2\",\n    \"comment\",\n    \"contacts\",\n    \"copy\",\n    \"delete-on\",\n    \"delete\",\n    \"discover\",\n    \"display\",\n    \"done\",\n    \"done2\",\n    \"download\",\n    \"email\",\n    \"error\",\n    \"eyes-off\",\n    \"eyes-on\",\n    \"folder\",\n    \"group-detail\",\n    \"help\",\n    \"home\",\n    \"imac\",\n    \"info\",\n    \"keyboard\",\n    \"like\",\n    \"link\",\n    \"location\",\n    \"lock\",\n    \"max-window\",\n    \"me\",\n    \"mike\",\n    \"mike2\",\n    \"mobile-contacts\",\n    \"more\",\n    \"more2\",\n    \"mosaic\",\n    \"music-off\",\n    \"music\",\n    \"note\",\n    \"pad\",\n    \"pause\",\n    \"pencil\",\n    \"photo-wall\",\n    \"play\",\n    \"play2\",\n    \"previous\",\n    \"previous2\",\n    \"qr-code\",\n    \"refresh\",\n    \"report-problem\",\n    \"search\",\n    \"sending\",\n    \"setting\",\n    \"share\",\n    \"shop\",\n    \"star\",\n    \"sticker\",\n    \"tag\",\n    \"text\",\n    \"time\",\n    \"transfer-text\",\n    \"transfer2\",\n    \"translate\",\n    \"tv\",\n    \"video-call\",\n    \"voice\",\n    \"volume-down\",\n    \"volume-off\",\n    \"volume-up\"\n];\nvar typeEnum;\n(function(typeEnum) {\n    typeEnum[\"Other\"] = \"other\";\n})(typeEnum || (typeEnum = {}));\nconst getExtraPrompt = (type, startNum = 15, extra = void 0)=>{\n    const promptArr = [];\n    promptArr.push(`IMPORTANT: All code must be complete code, do not generate code snippets, and do not use Markdown`);\n    if (type === \"other\") {\n        promptArr.push(`IMPORTANT: If you are a react project, you must use import React from 'react' to introduce react\n`);\n    }\n    if (extra) {\n        const ret = resolveExtra(extra);\n        promptArr.unshift(...ret);\n    }\n    let prompt = \"\";\n    for(let index = 0; index < promptArr.length; index++){\n        prompt += `${index + startNum}. ${promptArr[index]}\\n`;\n    }\n    console.log(prompt, \"prompt\");\n    return prompt;\n};\nfunction resolveExtra(extra) {\n    const promptArr = [];\n    if (extra.isBackEnd) {\n        promptArr.push(\"IMPORTANT: You must generate backend code, do not only generate frontend code\");\n        promptArr.push(\"IMPORTANT: Backend must handle CORS for all domains\");\n        let language = (extra.backendLanguage || \"java\").toLocaleLowerCase();\n        if (language == \"\") {\n            language = \"java\";\n        }\n        const backPromptArr = _backend__WEBPACK_IMPORTED_MODULE_3__.backendLanguageFunctionRegister[language](extra) //Strategy pattern backend execution\n        ;\n        promptArr.push(...backPromptArr);\n        if (extra.extra[\"isOpenDataBase\"] ?? false) {\n            let database = (extra.extra[\"database\"] ?? \"mysql\").toLocaleLowerCase();\n            if (database == \"\") {\n                database = \"mysql\";\n            }\n            const databasePromptArr = _database__WEBPACK_IMPORTED_MODULE_2__.databaseeFunctionRegister[database](extra) //Strategy pattern database execution\n            ;\n            promptArr.push(...databasePromptArr);\n        } else {\n            promptArr.push(\"IMPORTANT: Backend does not need database, use Map for storage\");\n        }\n        if (extra.extra[\"isOpenCache\"] ?? false) {\n            let cache = extra.extra[\"cache\"] ?? \"redis\";\n            if (cache == \"\") {\n                cache = \"redis\";\n            }\n            const cachePromptArr = _cache__WEBPACK_IMPORTED_MODULE_4__.cacheFunctionRegister[cache](extra) //Strategy pattern cache execution\n            ;\n            promptArr.push(...cachePromptArr);\n        }\n        promptArr.push(`IMPORTANT: Write the defined interfaces into a json file named api.json, json (URL with complete ip+port) format as {\"id\":\"root\",\"name\":\"APICollection\",\"type\":\"folder\",\"children\":[{\"id\":\"folder-1\",\"type\":\"folder\",\"name\":\"\"//folder name,\"children\":[{\"id\":\"1\",\"type\":\"api\",\"name\":\"\",\"method\":\"\",//GET\"url\":\"\",\"headers\":[{\"key\":\"\",\"value\":\"\"}],\"query\":[{\"key\":\"\",\"value\":\"\"}],\"cookies\":[{\"key\":\"\",\"value\":\"\"}]},{\"id\":\"2\",\"type\":\"api\",\"name\":\"\",//API name\"method\":\"\",//POSTorPUTorDELETE\"url\":\"\",\"headers\":[{\"key\":\"\",\"value\":\"\"}],\"query\":[{\"key\":\"\",\"value\":\"\"}],\"cookies\":[{\"key\":\"\",\"value\":\"\"}],\"bodyType\":\"\",//jsonorformDataorurlencodedorrawornoneorbinary\"body\":{\"none\":\"\",\"formData\":[],\"urlencoded\":[],\"raw\":\"\",\"json\":{},\"binary\":null}}]}]}`);\n        promptArr.push(\"IMPORTANT: Use localhost for backend address, do not use remote ip addresses, especially not database ones, connect frontend to backend, abstract frontend-backend interface connections into an api.js, and separate frontend and backend files, put frontend files under src, package.json in current directory, backend files in backend directory.\");\n    }\n    return promptArr;\n}\nconst getSystemPrompt = (type, otherConfig)=>`\nYou are We0 AI, an expert AI assistant and exceptional senior software developer with vast knowledge across multiple programming languages, frameworks, and best practices.\nWhen modifying the code, the output must be in the following format! ! ! ! emphasize! ! ! ! ! ! ! ! ! ! ! !\n<system_constraints>\n  You are operating in an environment called WebContainer, an in-browser Node.js runtime that emulates a Linux system to some degree. However, it runs in the browser and doesn't run a full-fledged Linux system and doesn't rely on a cloud VM to execute code. All code is executed in the browser. It does come with a shell that emulates zsh. The container cannot run native binaries since those cannot be executed in the browser. That means it can only execute code that is native to a browser including JS, WebAssembly, etc.\n\n  The shell comes with \\`python\\` and \\`python3\\` binaries, but they are LIMITED TO THE PYTHON STANDARD LIBRARY ONLY This means:\n\n    - There is NO \\`pip\\` support! If you attempt to use \\`pip\\`, you should explicitly state that it's not available.\n    - CRITICAL: Third-party libraries cannot be installed or imported.\n    - Even some standard library modules that require additional system dependencies (like \\`curses\\`) are not available.\n    - Only modules from the core Python standard library can be used.\n\n  Additionally, there is no \\`g++\\` or any C/C++ compiler available. WebContainer CANNOT run native binaries or compile C/C++ code!\n\n  Keep these limitations in mind when suggesting Python or C++ solutions and explicitly mention these constraints if relevant to the task at hand.\n\n  WebContainer has the ability to run a web server but requires to use an npm package (e.g., Vite, servor, serve, http-server) or use the Node.js APIs to implement a web server.\n\n  IMPORTANT: Prefer using Vite instead of implementing a custom web server.\n\n  IMPORTANT: Git is NOT available.\n\n  IMPORTANT: Prefer writing Node.js scripts instead of shell scripts. The environment doesn't fully support shell scripts, so use Node.js for scripting tasks whenever possible!\n\n  IMPORTANT: When choosing databases or npm packages, prefer options that don't rely on native binaries. For databases, prefer libsql, sqlite, or other solutions that don't involve native code. WebContainer CANNOT execute arbitrary native binaries.\n\n  Available shell commands:\n    File Operations:\n      - cat: Display file contents\n      - cp: Copy files/directories\n      - ls: List directory contents\n      - mkdir: Create directory\n      - mv: Move/rename files\n      - rm: Remove files\n      - rmdir: Remove empty directories\n      - touch: Create empty file/update timestamp\n\n    System Information:\n      - hostname: Show system name\n      - ps: Display running processes\n      - pwd: Print working directory\n      - uptime: Show system uptime\n      - env: Environment variables\n\n    Development Tools:\n      - node: Execute Node.js code\n      - python3: Run Python scripts\n      - code: VSCode operations\n      - jq: Process JSON\n\n    Other Utilities:\n      - curl, head, sort, tail, clear, which, export, chmod, scho, hostname, kill, ln, xxd, alias, false,  getconf, true, loadenv, wasm, xdg-open, command, exit, source\n</system_constraints>\n\n<code_formatting_info>\n  Use 2 spaces for code indentation\n</code_formatting_info>\n\n<message_formatting_info>\n  You can make the output pretty by using only the following available HTML elements: ${_utils_markdown__WEBPACK_IMPORTED_MODULE_0__.allowedHTMLElements.map((tagName)=>`<${tagName}>`).join(\", \")}\n</message_formatting_info>\n\n<diff_spec>\n  For user-made file modifications, a \\`<${MODIFICATIONS_TAG_NAME}>\\` section will appear at the start of the user message. It will contain either \\`<diff>\\` or \\`<file>\\` elements for each modified file:\n\n    - \\`<diff path=\"/some/file/path.ext\">\\`: Contains GNU unified diff format changes\n    - \\`<file path=\"/some/file/path.ext\">\\`: Contains the full new content of the file\n\n  The system chooses \\`<file>\\` if the diff exceeds the new content size, otherwise \\`<diff>\\`.\n\n  GNU unified diff format structure:\n\n    - For diffs the header with original and modified file names is omitted!\n    - Changed sections start with @@ -X,Y +A,B @@ where:\n      - X: Original file starting line\n      - Y: Original file line count\n      - A: Modified file starting line\n      - B: Modified file line count\n    - (-) lines: Removed from original\n    - (+) lines: Added in modified version\n    - Unmarked lines: Unchanged context\n\n  Example:\n\n  <${MODIFICATIONS_TAG_NAME}>\n    <diff path=\"/home/<USER>/src/main.js\">\n      @@ -2,7 +2,10 @@\n        return a + b;\n      }\n\n      -console.log('Hello, World!');\n      +console.log('Hello, Bolt!');\n      +\n      function greet() {\n      -  return 'Greetings!';\n      +  return 'Greetings!!';\n      }\n      +\n      +console.log('The End');\n    </diff>\n    <file path=\"/home/<USER>/package.json\">\n      // full file content here\n    </file>\n  </${MODIFICATIONS_TAG_NAME}>\n</diff_spec>\n\n<chain_of_thought_instructions>\n  Before providing a solution, BRIEFLY outline your implementation steps. This helps ensure systematic thinking and clear communication. Your planning should:\n  - List concrete steps you'll take\n  - Identify key components needed\n  - Note potential challenges\n  - Be concise (2-4 lines maximum)\n\n  Example responses:\n\n  User: \"Create a todo list app with local storage\"\n  Assistant: \"Sure. I'll start by:\n  1. Set up Vite + React\n  2. Create TodoList and TodoItem components\n  3. Implement localStorage for persistence\n  4. Add CRUD operations\n\n  Let's start now.\n\n  [Rest of response...]\"\n\n  User: \"Help debug why my API calls aren't working\"\n  Assistant: \"Great. My first steps will be:\n  1. Check network requests\n  2. Verify API endpoint format\n  3. Examine error handling\n\n  [Rest of response...]\"\n\n</chain_of_thought_instructions>\n\n<artifact_info>\n  Bolt creates a SINGLE, comprehensive artifact for each project. The artifact contains all necessary steps and components, including:\n\n  - Shell commands to run including dependencies to install using a package manager (NPM)\n  - Files to create and their contents\n  - Folders to create if necessary\n\n  <artifact_instructions>\n    1. CRITICAL: Think HOLISTICALLY and COMPREHENSIVELY BEFORE creating an artifact. This means:\n\n      - Consider ALL relevant files in the project\n      - Review ALL previous file changes and user modifications (as shown in diffs, see diff_spec)\n      - Analyze the entire project context and dependencies\n      - Anticipate potential impacts on other parts of the system\n\n      This holistic approach is ABSOLUTELY ESSENTIAL for creating coherent and effective solutions.\n\n    2. IMPORTANT: When receiving file modifications, ALWAYS use the latest file modifications and make any edits to the latest content of a file. This ensures that all changes are applied to the most up-to-date version of the file.\n\n    3. The current working directory is \\`${WORK_DIR}\\`.\n\n    4. Wrap the content in opening and closing \\`<boltArtifact>\\` tags. These tags contain more specific \\`<boltAction>\\` elements.\n\n    5. Add a title for the artifact to the \\`title\\` attribute of the opening \\`<boltArtifact>\\`.\n\n    6. Add a unique identifier to the \\`id\\` attribute of the of the opening \\`<boltArtifact>\\`. For updates, reuse the prior identifier. The identifier should be descriptive and relevant to the content, using kebab-case (e.g., \"example-code-snippet\"). This identifier will be used consistently throughout the artifact's lifecycle, even when updating or iterating on the artifact.\n\n    7. Use \\`<boltAction>\\` tags to define specific actions to perform.\n\n    8. For each \\`<boltAction>\\`, add a type to the \\`type\\` attribute of the opening \\`<boltAction>\\` tag to specify the type of the action. Assign one of the following values to the \\`type\\` attribute:\n\n      - shell: For running shell commands.\n\n        - When Using \\`npx\\`, ALWAYS provide the \\`--yes\\` flag.\n        - When running multiple shell commands, use \\`&&\\` to run them sequentially.\n        - ULTRA IMPORTANT: Do NOT re-run a dev command with shell action use dev action to run dev commands\n\n      - file: For writing new files or updating existing files. For each file add a \\`filePath\\` attribute to the opening \\`<boltAction>\\` tag to specify the file path. The content of the file artifact is the file contents. All file paths MUST BE relative to the current working directory.\n\n      - start: For starting development server.\n        - Use to start application if not already started or NEW dependencies added\n        - Only use this action when you need to run a dev server  or start the application\n        - ULTRA IMORTANT: do NOT re-run a dev server if files updated, existing dev server can autometically detect changes and executes the file changes\n\n\n    9. The order of the actions is VERY IMPORTANT. For example, if you decide to run a file it's important that the file exists in the first place and you need to create it before running a shell command that would execute the file.\n\n    10. ALWAYS install necessary dependencies FIRST before generating any other artifact. If that requires a \\`package.json\\` then you should create that first!\n\n      IMPORTANT: Add all required dependencies to the \\`package.json\\` already and try to avoid \\`npm i <pkg>\\` if possible!\n\n    11. CRITICAL: Always provide the FULL, updated content of the artifact. This means:\n\n      - Include ALL code, even if parts are unchanged\n      - NEVER use placeholders like \"// rest of the code remains the same...\" or \"<- leave original code here ->\"\n      - ALWAYS show the complete, up-to-date file contents when updating files\n      - Avoid any form of truncation or summarization\n\n    12. When running a dev server NEVER say something like \"You can now view X by opening the provided local server URL in your browser. The preview will be opened automatically or by the user manually!\n\n    13. If a dev server has already been started, do not re-run the dev command when new dependencies are installed or files were updated. Assume that installing new dependencies will be executed in a different process and changes will be picked up by the dev server.\n\n    14. IMPORTANT: Use coding best practices and split functionality into smaller modules instead of putting everything in a single gigantic file. Files should be as small as possible, and functionality should be extracted into separate modules when possible.\n      - Ensure code is clean, readable, and maintainable.\n      - Adhere to proper naming conventions and consistent formatting.\n      - Split functionality into smaller, reusable modules instead of placing everything in a single large file.\n      - Keep files as small as possible by extracting related functionalities into separate modules.\n      - Use imports to connect these modules together effectively.\n\n    15. IMPORTANT: 当要使用npm install 或者npm run dev的时候，这个命令需要放在生成代码的最后\n    ${getExtraPrompt(type, 15, otherConfig)}\n    </artifact_instructions>\n</artifact_info>\n\nNEVER use the word \"artifact\". For example:\n  - DO NOT SAY: \"This artifact sets up a simple Snake game using HTML, CSS, and JavaScript.\"\n  - INSTEAD SAY: \"We set up a simple Snake game using HTML, CSS, and JavaScript.\"\n\nIMPORTANT: Use valid markdown only for all your responses and DO NOT use HTML tags except for artifacts!\n\nULTRA IMPORTANT: Do NOT be verbose and DO NOT explain anything unless the user is asking for more information. That is VERY important.\n\nULTRA IMPORTANT: Think first and reply with the artifact that contains all necessary steps to set up the project, files, shell commands to run. It is SUPER IMPORTANT to respond with this first.\nIMPORTANT: 一定要严格按照下面约束的格式生成\nIMPORTANT: 强调：你必须每次都要按照下面格式输出<boltArtifact></boltArtifact> 例如这样的格式\nHere are some examples of correct usage of artifacts:\n\n<examples>\n  <example>\n    <user_query>Can you help me create a JavaScript function to calculate the factorial of a number?</user_query>\n\n    <assistant_response>\n      Certainly, I can help you create a JavaScript function to calculate the factorial of a number.\n\n      <boltArtifact id=\"factorial-function\" title=\"JavaScript Factorial Function\">\n        <boltAction type=\"file\" filePath=\"index.js\">\n          function factorial(n) {\n           ...\n          }\n\n          ...\n        </boltAction>\n        <boltAction type=\"file\" filePath=\"index.wxml\">\n           <view>\n             // ...\n           </view>\n          ...\n        </boltAction>\n        <boltAction type=\"file\" filePath=\"index.css\">\n          ...\n        </boltAction>\n\n      </boltArtifact>\n    </assistant_response>\n  </example>\n\n  <example>\n    <user_query>Build a snake game</user_query>\n\n    <assistant_response>\n      Certainly! I'd be happy to help you build a snake game using JavaScript and HTML5 Canvas. This will be a basic implementation that you can later expand upon. Let's create the game step by step.\n\n      <boltArtifact id=\"snake-game\" title=\"Snake Game in HTML and JavaScript\">\n        <boltAction type=\"file\" filePath=\"package.json\">\n          {\n            \"name\": \"snake\",\n            \"scripts\": {\n              \"dev\": \"vite\"\n            }\n            ...\n          }\n        </boltAction>\n        <boltAction type=\"file\" filePath=\"index.html\">\n          ...\n        </boltAction>\n        <boltAction type=\"shell\">\n          npm install --save-dev vite\n        </boltAction>\n        <boltAction type=\"start\">\n          npm run dev\n        </boltAction>\n      </boltArtifact>\n\n      Now you can play the Snake game by opening the provided local server URL in your browser. Use the arrow keys to control the snake. Eat the red food to grow and increase your score. The game ends if you hit the wall or your own tail.\n    </assistant_response>\n  </example>\n\n  <example>\n    <user_query>Make a bouncing ball with real gravity using React</user_query>\n\n    <assistant_response>\n      Certainly! I'll create a bouncing ball with real gravity using React. We'll use the react-spring library for physics-based animations.\n\n      <boltArtifact id=\"bouncing-ball-react\" title=\"Bouncing Ball with Gravity in React\">\n        <boltAction type=\"file\" filePath=\"package.json\">\n          {\n            \"name\": \"bouncing-ball\",\n            \"private\": true,\n            \"version\": \"0.0.0\",\n            \"type\": \"module\",\n            \"scripts\": {\n              \"dev\": \"vite\",\n              \"build\": \"vite build\",\n              \"preview\": \"vite preview\"\n            },\n            \"dependencies\": {\n              \"react\": \"^18.2.0\",\n              \"react-dom\": \"^18.2.0\",\n              \"react-spring\": \"^9.7.1\"\n            },\n            \"devDependencies\": {\n              \"@types/react\": \"^18.0.28\",\n              \"@types/react-dom\": \"^18.0.11\",\n              \"@vitejs/plugin-react\": \"^3.1.0\",\n              \"vite\": \"^4.2.0\"\n            }\n          }\n        </boltAction>\n\n        <boltAction type=\"file\" filePath=\"index.html\">\n          ...\n        </boltAction>\n\n        <boltAction type=\"file\" filePath=\"src/main.jsx\">\n          ...\n        </boltAction>\n\n        <boltAction type=\"file\" filePath=\"src/index.css\">\n          ...\n        </boltAction>\n\n        <boltAction type=\"file\" filePath=\"src/App.jsx\">\n          ...\n        </boltAction>\n\n        <boltAction type=\"start\">\n          npm run dev\n        </boltAction>\n      </boltArtifact>\n\n      You can now view the bouncing ball animation in the preview. The ball will start falling from the top of the screen and bounce realistically when it hits the bottom.\n    </assistant_response>\n  </example>\n</examples>\n\n`;\nconst CONTINUE_PROMPT = (0,_utils_stripIndent__WEBPACK_IMPORTED_MODULE_1__.stripIndents)`\n  Continue your prior response. IMPORTANT: Immediately begin from where you left off without any interruptions.\n  Do not repeat any content, including artifact and action tags.\n`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9jaGF0L3Byb21wdC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUF1RDtBQUNHO0FBQ0g7QUFDSztBQUNaO0FBQ3pDLE1BQU1LLGdCQUFnQixVQUFVO0FBQ2hDLE1BQU1DLFdBQVcsQ0FBQyxNQUFNLEVBQUVELGNBQWMsQ0FBQyxDQUFDO0FBQzFDLE1BQU1FLHlCQUF5QiwwQkFBMEI7QUFFaEUsTUFBTUMsV0FBVztJQUNmO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtDQUNEOztVQUVXQzs7R0FBQUEsYUFBQUE7QUFzQlosTUFBTUMsaUJBQWlCLENBQ3JCQyxNQUNBQyxXQUFtQixFQUFFLEVBQ3JCQyxRQUFxQixLQUFLLENBQUM7SUFFM0IsTUFBTUMsWUFBWSxFQUFFO0lBQ3BCQSxVQUFVQyxJQUFJLENBQ1osQ0FBQyxpR0FBaUcsQ0FBQztJQUdyRyxJQUFJSixrQkFBeUI7UUFDM0JHLFVBQVVDLElBQUksQ0FBQyxDQUFDO0FBQ3BCLENBQUM7SUFDQztJQUVBLElBQUlGLE9BQU87UUFDVCxNQUFNRyxNQUFNQyxhQUFhSjtRQUN6QkMsVUFBVUksT0FBTyxJQUFJRjtJQUN2QjtJQUVBLElBQUlHLFNBQVM7SUFDYixJQUFJLElBQUlDLFFBQVEsR0FBR0EsUUFBTU4sVUFBVU8sTUFBTSxFQUFDRCxRQUFRO1FBQ2hERCxVQUFRLENBQUMsRUFBRUMsUUFBTVIsU0FBUyxFQUFFLEVBQUVFLFNBQVMsQ0FBQ00sTUFBTSxDQUFDLEVBQUUsQ0FBQztJQUNwRDtJQUNBRSxRQUFRQyxHQUFHLENBQUNKLFFBQU87SUFDbkIsT0FBT0E7QUFDVDtBQUVBLFNBQVNGLGFBQWFKLEtBQWtCO0lBQ3RDLE1BQU1DLFlBQVksRUFBRTtJQUNwQixJQUFHRCxNQUFNVyxTQUFTLEVBQUM7UUFDakJWLFVBQVVDLElBQUksQ0FBQztRQUNmRCxVQUFVQyxJQUFJLENBQUM7UUFDYixJQUFJVSxXQUFXLENBQUNaLE1BQU1hLGVBQWUsSUFBSSxNQUFLLEVBQUdDLGlCQUFpQjtRQUNsRSxJQUFHRixZQUFZLElBQUc7WUFDaEJBLFdBQVc7UUFDYjtRQUNBLE1BQU1HLGdCQUFpQnpCLHFFQUErQixDQUFDc0IsU0FBUyxDQUFDWixPQUFPLG9DQUFvQzs7UUFDNUdDLFVBQVVDLElBQUksSUFBSWE7UUFFbEIsSUFBR2YsTUFBTUEsS0FBSyxDQUFDLGlCQUFpQixJQUFFLE9BQU07WUFDcEMsSUFBSWdCLFdBQVcsQ0FBQ2hCLE1BQU1BLEtBQUssQ0FBQyxXQUFXLElBQUUsT0FBTSxFQUFHYyxpQkFBaUI7WUFDbkUsSUFBR0UsWUFBWSxJQUFHO2dCQUNoQkEsV0FBVztZQUNiO1lBQ0EsTUFBTUMsb0JBQW9CNUIsZ0VBQXlCLENBQUMyQixTQUFTLENBQUNoQixPQUFPLHFDQUFxQzs7WUFDMUdDLFVBQVVDLElBQUksSUFBSWU7UUFDdEIsT0FBSztZQUNIaEIsVUFBVUMsSUFBSSxDQUFDO1FBQ2pCO1FBQ0EsSUFBR0YsTUFBTUEsS0FBSyxDQUFDLGNBQWMsSUFBRSxPQUFNO1lBQ25DLElBQUlrQixRQUFRbEIsTUFBTUEsS0FBSyxDQUFDLFFBQVEsSUFBRTtZQUNsQyxJQUFHa0IsU0FBUyxJQUFHO2dCQUNiQSxRQUFRO1lBQ1Y7WUFDQSxNQUFNQyxpQkFBaUI1Qix5REFBcUIsQ0FBQzJCLE1BQU0sQ0FBQ2xCLE9BQU8sa0NBQWtDOztZQUM3RkMsVUFBVUMsSUFBSSxJQUFJaUI7UUFDdEI7UUFFRWxCLFVBQVVDLElBQUksQ0FBQyxDQUFDLGt1QkFBa3VCLENBQUM7UUFDbnZCRCxVQUFVQyxJQUFJLENBQUM7SUFFbkI7SUFDQSxPQUFPRDtBQUNUO0FBRU8sTUFBTW1CLGtCQUFrQixDQUFDdEIsTUFBZXVCLGNBQTRCLENBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzRkE0RFUsRUFBRWxDLGdFQUFtQkEsQ0FDdEdtQyxHQUFHLENBQUMsQ0FBQ0MsVUFBWSxDQUFDLENBQUMsRUFBRUEsUUFBUSxDQUFDLENBQUMsRUFDL0JDLElBQUksQ0FBQyxNQUFNOzs7O3lDQUl5QixFQUFFOUIsdUJBQXVCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7R0FxQi9ELEVBQUVBLHVCQUF1Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQW1CeEIsRUFBRUEsdUJBQXVCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQW9EYSxFQUFFRCxTQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFtRGpELEVBQUVJLGVBQWVDLE1BQUssSUFBR3VCLGFBQWE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQXVJMUMsQ0FBQyxDQUFDO0FBRUssTUFBTUksa0JBQWtCckMsZ0VBQVksQ0FBQzs7O0FBRzVDLENBQUMsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL0B3ZS1kZXYvbmV4dC8uL3NyYy9hcHAvYXBpL2NoYXQvcHJvbXB0LnRzPzU4ZDUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgYWxsb3dlZEhUTUxFbGVtZW50cyB9IGZyb20gXCJAL3V0aWxzL21hcmtkb3duXCI7XG5pbXBvcnQgeyBzdHJpcEluZGVudHMgfSBmcm9tIFwiLi4vLi4vLi4vdXRpbHMvc3RyaXBJbmRlbnRcIjtcbmltcG9ydCB7IGRhdGFiYXNlZUZ1bmN0aW9uUmVnaXN0ZXIgfSBmcm9tIFwiLi9kYXRhYmFzZVwiO1xuaW1wb3J0IHsgYmFja2VuZExhbmd1YWdlRnVuY3Rpb25SZWdpc3RlciB9IGZyb20gXCIuL2JhY2tlbmRcIjtcbmltcG9ydCB7IGNhY2hlRnVuY3Rpb25SZWdpc3RlciB9IGZyb20gXCIuL2NhY2hlXCI7XG5leHBvcnQgY29uc3QgV09SS19ESVJfTkFNRSA9IFwicHJvamVjdFwiO1xuZXhwb3J0IGNvbnN0IFdPUktfRElSID0gYC9ob21lLyR7V09SS19ESVJfTkFNRX1gO1xuZXhwb3J0IGNvbnN0IE1PRElGSUNBVElPTlNfVEFHX05BTUUgPSBcImJvbHRfZmlsZV9tb2RpZmljYXRpb25zXCI7XG5cbmNvbnN0IGljb25OYW1lID0gW1xuICBcImFkZC1mcmllbmRzXCIsXG4gIFwiYWRkXCIsXG4gIFwiYWRkMlwiLFxuICBcImFsYnVtXCIsXG4gIFwiYXJyb3dcIixcbiAgXCJhdFwiLFxuICBcImJhY2tcIixcbiAgXCJiYWNrMlwiLFxuICBcImJlbGxyaW5nLW9mZlwiLFxuICBcImJlbGxyaW5nLW9uXCIsXG4gIFwiY2FtZXJhXCIsXG4gIFwiY2VsbHBob25lXCIsXG4gIFwiY2xpcFwiLFxuICBcImNsb3NlXCIsXG4gIFwiY2xvc2UyXCIsXG4gIFwiY29tbWVudFwiLFxuICBcImNvbnRhY3RzXCIsXG4gIFwiY29weVwiLFxuICBcImRlbGV0ZS1vblwiLFxuICBcImRlbGV0ZVwiLFxuICBcImRpc2NvdmVyXCIsXG4gIFwiZGlzcGxheVwiLFxuICBcImRvbmVcIixcbiAgXCJkb25lMlwiLFxuICBcImRvd25sb2FkXCIsXG4gIFwiZW1haWxcIixcbiAgXCJlcnJvclwiLFxuICBcImV5ZXMtb2ZmXCIsXG4gIFwiZXllcy1vblwiLFxuICBcImZvbGRlclwiLFxuICBcImdyb3VwLWRldGFpbFwiLFxuICBcImhlbHBcIixcbiAgXCJob21lXCIsXG4gIFwiaW1hY1wiLFxuICBcImluZm9cIixcbiAgXCJrZXlib2FyZFwiLFxuICBcImxpa2VcIixcbiAgXCJsaW5rXCIsXG4gIFwibG9jYXRpb25cIixcbiAgXCJsb2NrXCIsXG4gIFwibWF4LXdpbmRvd1wiLFxuICBcIm1lXCIsXG4gIFwibWlrZVwiLFxuICBcIm1pa2UyXCIsXG4gIFwibW9iaWxlLWNvbnRhY3RzXCIsXG4gIFwibW9yZVwiLFxuICBcIm1vcmUyXCIsXG4gIFwibW9zYWljXCIsXG4gIFwibXVzaWMtb2ZmXCIsXG4gIFwibXVzaWNcIixcbiAgXCJub3RlXCIsXG4gIFwicGFkXCIsXG4gIFwicGF1c2VcIixcbiAgXCJwZW5jaWxcIixcbiAgXCJwaG90by13YWxsXCIsXG4gIFwicGxheVwiLFxuICBcInBsYXkyXCIsXG4gIFwicHJldmlvdXNcIixcbiAgXCJwcmV2aW91czJcIixcbiAgXCJxci1jb2RlXCIsXG4gIFwicmVmcmVzaFwiLFxuICBcInJlcG9ydC1wcm9ibGVtXCIsXG4gIFwic2VhcmNoXCIsXG4gIFwic2VuZGluZ1wiLFxuICBcInNldHRpbmdcIixcbiAgXCJzaGFyZVwiLFxuICBcInNob3BcIixcbiAgXCJzdGFyXCIsXG4gIFwic3RpY2tlclwiLFxuICBcInRhZ1wiLFxuICBcInRleHRcIixcbiAgXCJ0aW1lXCIsXG4gIFwidHJhbnNmZXItdGV4dFwiLFxuICBcInRyYW5zZmVyMlwiLFxuICBcInRyYW5zbGF0ZVwiLFxuICBcInR2XCIsXG4gIFwidmlkZW8tY2FsbFwiLFxuICBcInZvaWNlXCIsXG4gIFwidm9sdW1lLWRvd25cIixcbiAgXCJ2b2x1bWUtb2ZmXCIsXG4gIFwidm9sdW1lLXVwXCIsXG5dO1xuXG5leHBvcnQgZW51bSB0eXBlRW51bSB7XG4gIE90aGVyID0gXCJvdGhlclwiLFxufVxuZXhwb3J0IGludGVyZmFjZSBwcm9tcHRFeHRyYSB7XG4gIGlzQmFja0VuZDogYm9vbGVhbjtcbiAgYmFja2VuZExhbmd1YWdlOiBzdHJpbmc7XG4gIGV4dHJhOiBvYmplY3Q7XG59XG5leHBvcnQgaW50ZXJmYWNlIFBhcmFtZXRlcnNTY2hlbWEge1xuICB0eXBlOiBzdHJpbmdcbiAgdGl0bGU/OiBzdHJpbmdcbiAgZGVzY3JpcHRpb24/OiBzdHJpbmcsXG4gIHJlcXVpcmVkPzogc3RyaW5nW11cbiAgcHJvcGVydGllczogUmVjb3JkPHN0cmluZywgb2JqZWN0PlxufVxuXG5leHBvcnQgaW50ZXJmYWNlIFRvb2xJbmZvIHtcbiAgaWQ6IGAke3N0cmluZ30uJHtzdHJpbmd9YCxcbiAgbmFtZTogc3RyaW5nLFxuICBkZXNjcmlwdGlvbj86IHN0cmluZyxcbiAgcGFyYW1ldGVyczogUGFyYW1ldGVyc1NjaGVtYVxufVxuY29uc3QgZ2V0RXh0cmFQcm9tcHQgPSAoXG4gIHR5cGU6IHR5cGVFbnVtLFxuICBzdGFydE51bTogbnVtYmVyID0gMTUsXG4gIGV4dHJhOiBwcm9tcHRFeHRyYSA9IHZvaWQgMFxuKSA9PiB7XG4gIGNvbnN0IHByb21wdEFyciA9IFtdO1xuICBwcm9tcHRBcnIucHVzaChcbiAgICBgSU1QT1JUQU5UOiBBbGwgY29kZSBtdXN0IGJlIGNvbXBsZXRlIGNvZGUsIGRvIG5vdCBnZW5lcmF0ZSBjb2RlIHNuaXBwZXRzLCBhbmQgZG8gbm90IHVzZSBNYXJrZG93bmBcbiAgKTtcblxuICBpZiAodHlwZSA9PT0gdHlwZUVudW0uT3RoZXIpIHtcbiAgICBwcm9tcHRBcnIucHVzaChgSU1QT1JUQU5UOiBJZiB5b3UgYXJlIGEgcmVhY3QgcHJvamVjdCwgeW91IG11c3QgdXNlIGltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCcgdG8gaW50cm9kdWNlIHJlYWN0XG5gKTtcbiAgfVxuXG4gIGlmIChleHRyYSkge1xuICAgIGNvbnN0IHJldCA9IHJlc29sdmVFeHRyYShleHRyYSk7XG4gICAgcHJvbXB0QXJyLnVuc2hpZnQoLi4ucmV0KTtcbiAgfVxuICBcbiAgbGV0IHByb21wdCA9ICcnO1xuICBmb3IobGV0IGluZGV4ID0gMDsgaW5kZXg8cHJvbXB0QXJyLmxlbmd0aDtpbmRleCsrKXtcbiAgICBwcm9tcHQrPWAke2luZGV4K3N0YXJ0TnVtfS4gJHtwcm9tcHRBcnJbaW5kZXhdfVxcbmBcbiAgfVxuICBjb25zb2xlLmxvZyhwcm9tcHQsJ3Byb21wdCcpXG4gIHJldHVybiBwcm9tcHQ7XG59O1xuXG5mdW5jdGlvbiByZXNvbHZlRXh0cmEoZXh0cmE6IHByb21wdEV4dHJhKSB7XG4gIGNvbnN0IHByb21wdEFyciA9IFtdO1xuICBpZihleHRyYS5pc0JhY2tFbmQpe1xuICAgIHByb21wdEFyci5wdXNoKFwiSU1QT1JUQU5UOiBZb3UgbXVzdCBnZW5lcmF0ZSBiYWNrZW5kIGNvZGUsIGRvIG5vdCBvbmx5IGdlbmVyYXRlIGZyb250ZW5kIGNvZGVcIilcbiAgICBwcm9tcHRBcnIucHVzaChcIklNUE9SVEFOVDogQmFja2VuZCBtdXN0IGhhbmRsZSBDT1JTIGZvciBhbGwgZG9tYWluc1wiKVxuICAgICAgbGV0IGxhbmd1YWdlID0gKGV4dHJhLmJhY2tlbmRMYW5ndWFnZSB8fCAnamF2YScpLnRvTG9jYWxlTG93ZXJDYXNlKCk7XG4gICAgICBpZihsYW5ndWFnZSA9PSAnJyl7XG4gICAgICAgIGxhbmd1YWdlID0gJ2phdmEnO1xuICAgICAgfVxuICAgICAgY29uc3QgYmFja1Byb21wdEFyciAgPSBiYWNrZW5kTGFuZ3VhZ2VGdW5jdGlvblJlZ2lzdGVyW2xhbmd1YWdlXShleHRyYSkgLy9TdHJhdGVneSBwYXR0ZXJuIGJhY2tlbmQgZXhlY3V0aW9uXG4gICAgICBwcm9tcHRBcnIucHVzaCguLi5iYWNrUHJvbXB0QXJyKTtcblxuICAgICAgaWYoZXh0cmEuZXh0cmFbJ2lzT3BlbkRhdGFCYXNlJ10/P2ZhbHNlKXtcbiAgICAgICAgICBsZXQgZGF0YWJhc2UgPSAoZXh0cmEuZXh0cmFbJ2RhdGFiYXNlJ10/P1wibXlzcWxcIikudG9Mb2NhbGVMb3dlckNhc2UoKTtcbiAgICAgICAgICBpZihkYXRhYmFzZSA9PSAnJyl7XG4gICAgICAgICAgICBkYXRhYmFzZSA9ICdteXNxbCc7XG4gICAgICAgICAgfVxuICAgICAgICAgIGNvbnN0IGRhdGFiYXNlUHJvbXB0QXJyID0gZGF0YWJhc2VlRnVuY3Rpb25SZWdpc3RlcltkYXRhYmFzZV0oZXh0cmEpIC8vU3RyYXRlZ3kgcGF0dGVybiBkYXRhYmFzZSBleGVjdXRpb25cbiAgICAgICAgICBwcm9tcHRBcnIucHVzaCguLi5kYXRhYmFzZVByb21wdEFycik7XG4gICAgICB9ZWxzZXtcbiAgICAgICAgcHJvbXB0QXJyLnB1c2goXCJJTVBPUlRBTlQ6IEJhY2tlbmQgZG9lcyBub3QgbmVlZCBkYXRhYmFzZSwgdXNlIE1hcCBmb3Igc3RvcmFnZVwiKVxuICAgICAgfVxuICAgICAgaWYoZXh0cmEuZXh0cmFbJ2lzT3BlbkNhY2hlJ10/P2ZhbHNlKXtcbiAgICAgICAgbGV0IGNhY2hlID0gZXh0cmEuZXh0cmFbJ2NhY2hlJ10/P1wicmVkaXNcIlxuICAgICAgICBpZihjYWNoZSA9PSAnJyl7XG4gICAgICAgICAgY2FjaGUgPSAncmVkaXMnO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGNhY2hlUHJvbXB0QXJyID0gY2FjaGVGdW5jdGlvblJlZ2lzdGVyW2NhY2hlXShleHRyYSkgLy9TdHJhdGVneSBwYXR0ZXJuIGNhY2hlIGV4ZWN1dGlvblxuICAgICAgICBwcm9tcHRBcnIucHVzaCguLi5jYWNoZVByb21wdEFycik7XG4gICAgfVxuXG4gICAgICBwcm9tcHRBcnIucHVzaChgSU1QT1JUQU5UOiBXcml0ZSB0aGUgZGVmaW5lZCBpbnRlcmZhY2VzIGludG8gYSBqc29uIGZpbGUgbmFtZWQgYXBpLmpzb24sIGpzb24gKFVSTCB3aXRoIGNvbXBsZXRlIGlwK3BvcnQpIGZvcm1hdCBhcyB7XCJpZFwiOlwicm9vdFwiLFwibmFtZVwiOlwiQVBJQ29sbGVjdGlvblwiLFwidHlwZVwiOlwiZm9sZGVyXCIsXCJjaGlsZHJlblwiOlt7XCJpZFwiOlwiZm9sZGVyLTFcIixcInR5cGVcIjpcImZvbGRlclwiLFwibmFtZVwiOlwiXCIvL2ZvbGRlciBuYW1lLFwiY2hpbGRyZW5cIjpbe1wiaWRcIjpcIjFcIixcInR5cGVcIjpcImFwaVwiLFwibmFtZVwiOlwiXCIsXCJtZXRob2RcIjpcIlwiLC8vR0VUXCJ1cmxcIjpcIlwiLFwiaGVhZGVyc1wiOlt7XCJrZXlcIjpcIlwiLFwidmFsdWVcIjpcIlwifV0sXCJxdWVyeVwiOlt7XCJrZXlcIjpcIlwiLFwidmFsdWVcIjpcIlwifV0sXCJjb29raWVzXCI6W3tcImtleVwiOlwiXCIsXCJ2YWx1ZVwiOlwiXCJ9XX0se1wiaWRcIjpcIjJcIixcInR5cGVcIjpcImFwaVwiLFwibmFtZVwiOlwiXCIsLy9BUEkgbmFtZVwibWV0aG9kXCI6XCJcIiwvL1BPU1RvclBVVG9yREVMRVRFXCJ1cmxcIjpcIlwiLFwiaGVhZGVyc1wiOlt7XCJrZXlcIjpcIlwiLFwidmFsdWVcIjpcIlwifV0sXCJxdWVyeVwiOlt7XCJrZXlcIjpcIlwiLFwidmFsdWVcIjpcIlwifV0sXCJjb29raWVzXCI6W3tcImtleVwiOlwiXCIsXCJ2YWx1ZVwiOlwiXCJ9XSxcImJvZHlUeXBlXCI6XCJcIiwvL2pzb25vcmZvcm1EYXRhb3J1cmxlbmNvZGVkb3JyYXdvcm5vbmVvcmJpbmFyeVwiYm9keVwiOntcIm5vbmVcIjpcIlwiLFwiZm9ybURhdGFcIjpbXSxcInVybGVuY29kZWRcIjpbXSxcInJhd1wiOlwiXCIsXCJqc29uXCI6e30sXCJiaW5hcnlcIjpudWxsfX1dfV19YClcbiAgICAgIHByb21wdEFyci5wdXNoKFwiSU1QT1JUQU5UOiBVc2UgbG9jYWxob3N0IGZvciBiYWNrZW5kIGFkZHJlc3MsIGRvIG5vdCB1c2UgcmVtb3RlIGlwIGFkZHJlc3NlcywgZXNwZWNpYWxseSBub3QgZGF0YWJhc2Ugb25lcywgY29ubmVjdCBmcm9udGVuZCB0byBiYWNrZW5kLCBhYnN0cmFjdCBmcm9udGVuZC1iYWNrZW5kIGludGVyZmFjZSBjb25uZWN0aW9ucyBpbnRvIGFuIGFwaS5qcywgYW5kIHNlcGFyYXRlIGZyb250ZW5kIGFuZCBiYWNrZW5kIGZpbGVzLCBwdXQgZnJvbnRlbmQgZmlsZXMgdW5kZXIgc3JjLCBwYWNrYWdlLmpzb24gaW4gY3VycmVudCBkaXJlY3RvcnksIGJhY2tlbmQgZmlsZXMgaW4gYmFja2VuZCBkaXJlY3RvcnkuXCIpXG5cbiAgfVxuICByZXR1cm4gcHJvbXB0QXJyO1xufVxuXG5leHBvcnQgY29uc3QgZ2V0U3lzdGVtUHJvbXB0ID0gKHR5cGU6IHR5cGVFbnVtLG90aGVyQ29uZmlnOnByb21wdEV4dHJhKSA9PiBgXG5Zb3UgYXJlIFdlMCBBSSwgYW4gZXhwZXJ0IEFJIGFzc2lzdGFudCBhbmQgZXhjZXB0aW9uYWwgc2VuaW9yIHNvZnR3YXJlIGRldmVsb3BlciB3aXRoIHZhc3Qga25vd2xlZGdlIGFjcm9zcyBtdWx0aXBsZSBwcm9ncmFtbWluZyBsYW5ndWFnZXMsIGZyYW1ld29ya3MsIGFuZCBiZXN0IHByYWN0aWNlcy5cbldoZW4gbW9kaWZ5aW5nIHRoZSBjb2RlLCB0aGUgb3V0cHV0IG11c3QgYmUgaW4gdGhlIGZvbGxvd2luZyBmb3JtYXQhICEgISAhIGVtcGhhc2l6ZSEgISAhICEgISAhICEgISAhICEgISAhXG48c3lzdGVtX2NvbnN0cmFpbnRzPlxuICBZb3UgYXJlIG9wZXJhdGluZyBpbiBhbiBlbnZpcm9ubWVudCBjYWxsZWQgV2ViQ29udGFpbmVyLCBhbiBpbi1icm93c2VyIE5vZGUuanMgcnVudGltZSB0aGF0IGVtdWxhdGVzIGEgTGludXggc3lzdGVtIHRvIHNvbWUgZGVncmVlLiBIb3dldmVyLCBpdCBydW5zIGluIHRoZSBicm93c2VyIGFuZCBkb2Vzbid0IHJ1biBhIGZ1bGwtZmxlZGdlZCBMaW51eCBzeXN0ZW0gYW5kIGRvZXNuJ3QgcmVseSBvbiBhIGNsb3VkIFZNIHRvIGV4ZWN1dGUgY29kZS4gQWxsIGNvZGUgaXMgZXhlY3V0ZWQgaW4gdGhlIGJyb3dzZXIuIEl0IGRvZXMgY29tZSB3aXRoIGEgc2hlbGwgdGhhdCBlbXVsYXRlcyB6c2guIFRoZSBjb250YWluZXIgY2Fubm90IHJ1biBuYXRpdmUgYmluYXJpZXMgc2luY2UgdGhvc2UgY2Fubm90IGJlIGV4ZWN1dGVkIGluIHRoZSBicm93c2VyLiBUaGF0IG1lYW5zIGl0IGNhbiBvbmx5IGV4ZWN1dGUgY29kZSB0aGF0IGlzIG5hdGl2ZSB0byBhIGJyb3dzZXIgaW5jbHVkaW5nIEpTLCBXZWJBc3NlbWJseSwgZXRjLlxuXG4gIFRoZSBzaGVsbCBjb21lcyB3aXRoIFxcYHB5dGhvblxcYCBhbmQgXFxgcHl0aG9uM1xcYCBiaW5hcmllcywgYnV0IHRoZXkgYXJlIExJTUlURUQgVE8gVEhFIFBZVEhPTiBTVEFOREFSRCBMSUJSQVJZIE9OTFkgVGhpcyBtZWFuczpcblxuICAgIC0gVGhlcmUgaXMgTk8gXFxgcGlwXFxgIHN1cHBvcnQhIElmIHlvdSBhdHRlbXB0IHRvIHVzZSBcXGBwaXBcXGAsIHlvdSBzaG91bGQgZXhwbGljaXRseSBzdGF0ZSB0aGF0IGl0J3Mgbm90IGF2YWlsYWJsZS5cbiAgICAtIENSSVRJQ0FMOiBUaGlyZC1wYXJ0eSBsaWJyYXJpZXMgY2Fubm90IGJlIGluc3RhbGxlZCBvciBpbXBvcnRlZC5cbiAgICAtIEV2ZW4gc29tZSBzdGFuZGFyZCBsaWJyYXJ5IG1vZHVsZXMgdGhhdCByZXF1aXJlIGFkZGl0aW9uYWwgc3lzdGVtIGRlcGVuZGVuY2llcyAobGlrZSBcXGBjdXJzZXNcXGApIGFyZSBub3QgYXZhaWxhYmxlLlxuICAgIC0gT25seSBtb2R1bGVzIGZyb20gdGhlIGNvcmUgUHl0aG9uIHN0YW5kYXJkIGxpYnJhcnkgY2FuIGJlIHVzZWQuXG5cbiAgQWRkaXRpb25hbGx5LCB0aGVyZSBpcyBubyBcXGBnKytcXGAgb3IgYW55IEMvQysrIGNvbXBpbGVyIGF2YWlsYWJsZS4gV2ViQ29udGFpbmVyIENBTk5PVCBydW4gbmF0aXZlIGJpbmFyaWVzIG9yIGNvbXBpbGUgQy9DKysgY29kZSFcblxuICBLZWVwIHRoZXNlIGxpbWl0YXRpb25zIGluIG1pbmQgd2hlbiBzdWdnZXN0aW5nIFB5dGhvbiBvciBDKysgc29sdXRpb25zIGFuZCBleHBsaWNpdGx5IG1lbnRpb24gdGhlc2UgY29uc3RyYWludHMgaWYgcmVsZXZhbnQgdG8gdGhlIHRhc2sgYXQgaGFuZC5cblxuICBXZWJDb250YWluZXIgaGFzIHRoZSBhYmlsaXR5IHRvIHJ1biBhIHdlYiBzZXJ2ZXIgYnV0IHJlcXVpcmVzIHRvIHVzZSBhbiBucG0gcGFja2FnZSAoZS5nLiwgVml0ZSwgc2Vydm9yLCBzZXJ2ZSwgaHR0cC1zZXJ2ZXIpIG9yIHVzZSB0aGUgTm9kZS5qcyBBUElzIHRvIGltcGxlbWVudCBhIHdlYiBzZXJ2ZXIuXG5cbiAgSU1QT1JUQU5UOiBQcmVmZXIgdXNpbmcgVml0ZSBpbnN0ZWFkIG9mIGltcGxlbWVudGluZyBhIGN1c3RvbSB3ZWIgc2VydmVyLlxuXG4gIElNUE9SVEFOVDogR2l0IGlzIE5PVCBhdmFpbGFibGUuXG5cbiAgSU1QT1JUQU5UOiBQcmVmZXIgd3JpdGluZyBOb2RlLmpzIHNjcmlwdHMgaW5zdGVhZCBvZiBzaGVsbCBzY3JpcHRzLiBUaGUgZW52aXJvbm1lbnQgZG9lc24ndCBmdWxseSBzdXBwb3J0IHNoZWxsIHNjcmlwdHMsIHNvIHVzZSBOb2RlLmpzIGZvciBzY3JpcHRpbmcgdGFza3Mgd2hlbmV2ZXIgcG9zc2libGUhXG5cbiAgSU1QT1JUQU5UOiBXaGVuIGNob29zaW5nIGRhdGFiYXNlcyBvciBucG0gcGFja2FnZXMsIHByZWZlciBvcHRpb25zIHRoYXQgZG9uJ3QgcmVseSBvbiBuYXRpdmUgYmluYXJpZXMuIEZvciBkYXRhYmFzZXMsIHByZWZlciBsaWJzcWwsIHNxbGl0ZSwgb3Igb3RoZXIgc29sdXRpb25zIHRoYXQgZG9uJ3QgaW52b2x2ZSBuYXRpdmUgY29kZS4gV2ViQ29udGFpbmVyIENBTk5PVCBleGVjdXRlIGFyYml0cmFyeSBuYXRpdmUgYmluYXJpZXMuXG5cbiAgQXZhaWxhYmxlIHNoZWxsIGNvbW1hbmRzOlxuICAgIEZpbGUgT3BlcmF0aW9uczpcbiAgICAgIC0gY2F0OiBEaXNwbGF5IGZpbGUgY29udGVudHNcbiAgICAgIC0gY3A6IENvcHkgZmlsZXMvZGlyZWN0b3JpZXNcbiAgICAgIC0gbHM6IExpc3QgZGlyZWN0b3J5IGNvbnRlbnRzXG4gICAgICAtIG1rZGlyOiBDcmVhdGUgZGlyZWN0b3J5XG4gICAgICAtIG12OiBNb3ZlL3JlbmFtZSBmaWxlc1xuICAgICAgLSBybTogUmVtb3ZlIGZpbGVzXG4gICAgICAtIHJtZGlyOiBSZW1vdmUgZW1wdHkgZGlyZWN0b3JpZXNcbiAgICAgIC0gdG91Y2g6IENyZWF0ZSBlbXB0eSBmaWxlL3VwZGF0ZSB0aW1lc3RhbXBcblxuICAgIFN5c3RlbSBJbmZvcm1hdGlvbjpcbiAgICAgIC0gaG9zdG5hbWU6IFNob3cgc3lzdGVtIG5hbWVcbiAgICAgIC0gcHM6IERpc3BsYXkgcnVubmluZyBwcm9jZXNzZXNcbiAgICAgIC0gcHdkOiBQcmludCB3b3JraW5nIGRpcmVjdG9yeVxuICAgICAgLSB1cHRpbWU6IFNob3cgc3lzdGVtIHVwdGltZVxuICAgICAgLSBlbnY6IEVudmlyb25tZW50IHZhcmlhYmxlc1xuXG4gICAgRGV2ZWxvcG1lbnQgVG9vbHM6XG4gICAgICAtIG5vZGU6IEV4ZWN1dGUgTm9kZS5qcyBjb2RlXG4gICAgICAtIHB5dGhvbjM6IFJ1biBQeXRob24gc2NyaXB0c1xuICAgICAgLSBjb2RlOiBWU0NvZGUgb3BlcmF0aW9uc1xuICAgICAgLSBqcTogUHJvY2VzcyBKU09OXG5cbiAgICBPdGhlciBVdGlsaXRpZXM6XG4gICAgICAtIGN1cmwsIGhlYWQsIHNvcnQsIHRhaWwsIGNsZWFyLCB3aGljaCwgZXhwb3J0LCBjaG1vZCwgc2NobywgaG9zdG5hbWUsIGtpbGwsIGxuLCB4eGQsIGFsaWFzLCBmYWxzZSwgIGdldGNvbmYsIHRydWUsIGxvYWRlbnYsIHdhc20sIHhkZy1vcGVuLCBjb21tYW5kLCBleGl0LCBzb3VyY2Vcbjwvc3lzdGVtX2NvbnN0cmFpbnRzPlxuXG48Y29kZV9mb3JtYXR0aW5nX2luZm8+XG4gIFVzZSAyIHNwYWNlcyBmb3IgY29kZSBpbmRlbnRhdGlvblxuPC9jb2RlX2Zvcm1hdHRpbmdfaW5mbz5cblxuPG1lc3NhZ2VfZm9ybWF0dGluZ19pbmZvPlxuICBZb3UgY2FuIG1ha2UgdGhlIG91dHB1dCBwcmV0dHkgYnkgdXNpbmcgb25seSB0aGUgZm9sbG93aW5nIGF2YWlsYWJsZSBIVE1MIGVsZW1lbnRzOiAke2FsbG93ZWRIVE1MRWxlbWVudHNcbiAgICAubWFwKCh0YWdOYW1lKSA9PiBgPCR7dGFnTmFtZX0+YClcbiAgICAuam9pbihcIiwgXCIpfVxuPC9tZXNzYWdlX2Zvcm1hdHRpbmdfaW5mbz5cblxuPGRpZmZfc3BlYz5cbiAgRm9yIHVzZXItbWFkZSBmaWxlIG1vZGlmaWNhdGlvbnMsIGEgXFxgPCR7TU9ESUZJQ0FUSU9OU19UQUdfTkFNRX0+XFxgIHNlY3Rpb24gd2lsbCBhcHBlYXIgYXQgdGhlIHN0YXJ0IG9mIHRoZSB1c2VyIG1lc3NhZ2UuIEl0IHdpbGwgY29udGFpbiBlaXRoZXIgXFxgPGRpZmY+XFxgIG9yIFxcYDxmaWxlPlxcYCBlbGVtZW50cyBmb3IgZWFjaCBtb2RpZmllZCBmaWxlOlxuXG4gICAgLSBcXGA8ZGlmZiBwYXRoPVwiL3NvbWUvZmlsZS9wYXRoLmV4dFwiPlxcYDogQ29udGFpbnMgR05VIHVuaWZpZWQgZGlmZiBmb3JtYXQgY2hhbmdlc1xuICAgIC0gXFxgPGZpbGUgcGF0aD1cIi9zb21lL2ZpbGUvcGF0aC5leHRcIj5cXGA6IENvbnRhaW5zIHRoZSBmdWxsIG5ldyBjb250ZW50IG9mIHRoZSBmaWxlXG5cbiAgVGhlIHN5c3RlbSBjaG9vc2VzIFxcYDxmaWxlPlxcYCBpZiB0aGUgZGlmZiBleGNlZWRzIHRoZSBuZXcgY29udGVudCBzaXplLCBvdGhlcndpc2UgXFxgPGRpZmY+XFxgLlxuXG4gIEdOVSB1bmlmaWVkIGRpZmYgZm9ybWF0IHN0cnVjdHVyZTpcblxuICAgIC0gRm9yIGRpZmZzIHRoZSBoZWFkZXIgd2l0aCBvcmlnaW5hbCBhbmQgbW9kaWZpZWQgZmlsZSBuYW1lcyBpcyBvbWl0dGVkIVxuICAgIC0gQ2hhbmdlZCBzZWN0aW9ucyBzdGFydCB3aXRoIEBAIC1YLFkgK0EsQiBAQCB3aGVyZTpcbiAgICAgIC0gWDogT3JpZ2luYWwgZmlsZSBzdGFydGluZyBsaW5lXG4gICAgICAtIFk6IE9yaWdpbmFsIGZpbGUgbGluZSBjb3VudFxuICAgICAgLSBBOiBNb2RpZmllZCBmaWxlIHN0YXJ0aW5nIGxpbmVcbiAgICAgIC0gQjogTW9kaWZpZWQgZmlsZSBsaW5lIGNvdW50XG4gICAgLSAoLSkgbGluZXM6IFJlbW92ZWQgZnJvbSBvcmlnaW5hbFxuICAgIC0gKCspIGxpbmVzOiBBZGRlZCBpbiBtb2RpZmllZCB2ZXJzaW9uXG4gICAgLSBVbm1hcmtlZCBsaW5lczogVW5jaGFuZ2VkIGNvbnRleHRcblxuICBFeGFtcGxlOlxuXG4gIDwke01PRElGSUNBVElPTlNfVEFHX05BTUV9PlxuICAgIDxkaWZmIHBhdGg9XCIvaG9tZS9wcm9qZWN0L3NyYy9tYWluLmpzXCI+XG4gICAgICBAQCAtMiw3ICsyLDEwIEBAXG4gICAgICAgIHJldHVybiBhICsgYjtcbiAgICAgIH1cblxuICAgICAgLWNvbnNvbGUubG9nKCdIZWxsbywgV29ybGQhJyk7XG4gICAgICArY29uc29sZS5sb2coJ0hlbGxvLCBCb2x0IScpO1xuICAgICAgK1xuICAgICAgZnVuY3Rpb24gZ3JlZXQoKSB7XG4gICAgICAtICByZXR1cm4gJ0dyZWV0aW5ncyEnO1xuICAgICAgKyAgcmV0dXJuICdHcmVldGluZ3MhISc7XG4gICAgICB9XG4gICAgICArXG4gICAgICArY29uc29sZS5sb2coJ1RoZSBFbmQnKTtcbiAgICA8L2RpZmY+XG4gICAgPGZpbGUgcGF0aD1cIi9ob21lL3Byb2plY3QvcGFja2FnZS5qc29uXCI+XG4gICAgICAvLyBmdWxsIGZpbGUgY29udGVudCBoZXJlXG4gICAgPC9maWxlPlxuICA8LyR7TU9ESUZJQ0FUSU9OU19UQUdfTkFNRX0+XG48L2RpZmZfc3BlYz5cblxuPGNoYWluX29mX3Rob3VnaHRfaW5zdHJ1Y3Rpb25zPlxuICBCZWZvcmUgcHJvdmlkaW5nIGEgc29sdXRpb24sIEJSSUVGTFkgb3V0bGluZSB5b3VyIGltcGxlbWVudGF0aW9uIHN0ZXBzLiBUaGlzIGhlbHBzIGVuc3VyZSBzeXN0ZW1hdGljIHRoaW5raW5nIGFuZCBjbGVhciBjb21tdW5pY2F0aW9uLiBZb3VyIHBsYW5uaW5nIHNob3VsZDpcbiAgLSBMaXN0IGNvbmNyZXRlIHN0ZXBzIHlvdSdsbCB0YWtlXG4gIC0gSWRlbnRpZnkga2V5IGNvbXBvbmVudHMgbmVlZGVkXG4gIC0gTm90ZSBwb3RlbnRpYWwgY2hhbGxlbmdlc1xuICAtIEJlIGNvbmNpc2UgKDItNCBsaW5lcyBtYXhpbXVtKVxuXG4gIEV4YW1wbGUgcmVzcG9uc2VzOlxuXG4gIFVzZXI6IFwiQ3JlYXRlIGEgdG9kbyBsaXN0IGFwcCB3aXRoIGxvY2FsIHN0b3JhZ2VcIlxuICBBc3Npc3RhbnQ6IFwiU3VyZS4gSSdsbCBzdGFydCBieTpcbiAgMS4gU2V0IHVwIFZpdGUgKyBSZWFjdFxuICAyLiBDcmVhdGUgVG9kb0xpc3QgYW5kIFRvZG9JdGVtIGNvbXBvbmVudHNcbiAgMy4gSW1wbGVtZW50IGxvY2FsU3RvcmFnZSBmb3IgcGVyc2lzdGVuY2VcbiAgNC4gQWRkIENSVUQgb3BlcmF0aW9uc1xuXG4gIExldCdzIHN0YXJ0IG5vdy5cblxuICBbUmVzdCBvZiByZXNwb25zZS4uLl1cIlxuXG4gIFVzZXI6IFwiSGVscCBkZWJ1ZyB3aHkgbXkgQVBJIGNhbGxzIGFyZW4ndCB3b3JraW5nXCJcbiAgQXNzaXN0YW50OiBcIkdyZWF0LiBNeSBmaXJzdCBzdGVwcyB3aWxsIGJlOlxuICAxLiBDaGVjayBuZXR3b3JrIHJlcXVlc3RzXG4gIDIuIFZlcmlmeSBBUEkgZW5kcG9pbnQgZm9ybWF0XG4gIDMuIEV4YW1pbmUgZXJyb3IgaGFuZGxpbmdcblxuICBbUmVzdCBvZiByZXNwb25zZS4uLl1cIlxuXG48L2NoYWluX29mX3Rob3VnaHRfaW5zdHJ1Y3Rpb25zPlxuXG48YXJ0aWZhY3RfaW5mbz5cbiAgQm9sdCBjcmVhdGVzIGEgU0lOR0xFLCBjb21wcmVoZW5zaXZlIGFydGlmYWN0IGZvciBlYWNoIHByb2plY3QuIFRoZSBhcnRpZmFjdCBjb250YWlucyBhbGwgbmVjZXNzYXJ5IHN0ZXBzIGFuZCBjb21wb25lbnRzLCBpbmNsdWRpbmc6XG5cbiAgLSBTaGVsbCBjb21tYW5kcyB0byBydW4gaW5jbHVkaW5nIGRlcGVuZGVuY2llcyB0byBpbnN0YWxsIHVzaW5nIGEgcGFja2FnZSBtYW5hZ2VyIChOUE0pXG4gIC0gRmlsZXMgdG8gY3JlYXRlIGFuZCB0aGVpciBjb250ZW50c1xuICAtIEZvbGRlcnMgdG8gY3JlYXRlIGlmIG5lY2Vzc2FyeVxuXG4gIDxhcnRpZmFjdF9pbnN0cnVjdGlvbnM+XG4gICAgMS4gQ1JJVElDQUw6IFRoaW5rIEhPTElTVElDQUxMWSBhbmQgQ09NUFJFSEVOU0lWRUxZIEJFRk9SRSBjcmVhdGluZyBhbiBhcnRpZmFjdC4gVGhpcyBtZWFuczpcblxuICAgICAgLSBDb25zaWRlciBBTEwgcmVsZXZhbnQgZmlsZXMgaW4gdGhlIHByb2plY3RcbiAgICAgIC0gUmV2aWV3IEFMTCBwcmV2aW91cyBmaWxlIGNoYW5nZXMgYW5kIHVzZXIgbW9kaWZpY2F0aW9ucyAoYXMgc2hvd24gaW4gZGlmZnMsIHNlZSBkaWZmX3NwZWMpXG4gICAgICAtIEFuYWx5emUgdGhlIGVudGlyZSBwcm9qZWN0IGNvbnRleHQgYW5kIGRlcGVuZGVuY2llc1xuICAgICAgLSBBbnRpY2lwYXRlIHBvdGVudGlhbCBpbXBhY3RzIG9uIG90aGVyIHBhcnRzIG9mIHRoZSBzeXN0ZW1cblxuICAgICAgVGhpcyBob2xpc3RpYyBhcHByb2FjaCBpcyBBQlNPTFVURUxZIEVTU0VOVElBTCBmb3IgY3JlYXRpbmcgY29oZXJlbnQgYW5kIGVmZmVjdGl2ZSBzb2x1dGlvbnMuXG5cbiAgICAyLiBJTVBPUlRBTlQ6IFdoZW4gcmVjZWl2aW5nIGZpbGUgbW9kaWZpY2F0aW9ucywgQUxXQVlTIHVzZSB0aGUgbGF0ZXN0IGZpbGUgbW9kaWZpY2F0aW9ucyBhbmQgbWFrZSBhbnkgZWRpdHMgdG8gdGhlIGxhdGVzdCBjb250ZW50IG9mIGEgZmlsZS4gVGhpcyBlbnN1cmVzIHRoYXQgYWxsIGNoYW5nZXMgYXJlIGFwcGxpZWQgdG8gdGhlIG1vc3QgdXAtdG8tZGF0ZSB2ZXJzaW9uIG9mIHRoZSBmaWxlLlxuXG4gICAgMy4gVGhlIGN1cnJlbnQgd29ya2luZyBkaXJlY3RvcnkgaXMgXFxgJHtXT1JLX0RJUn1cXGAuXG5cbiAgICA0LiBXcmFwIHRoZSBjb250ZW50IGluIG9wZW5pbmcgYW5kIGNsb3NpbmcgXFxgPGJvbHRBcnRpZmFjdD5cXGAgdGFncy4gVGhlc2UgdGFncyBjb250YWluIG1vcmUgc3BlY2lmaWMgXFxgPGJvbHRBY3Rpb24+XFxgIGVsZW1lbnRzLlxuXG4gICAgNS4gQWRkIGEgdGl0bGUgZm9yIHRoZSBhcnRpZmFjdCB0byB0aGUgXFxgdGl0bGVcXGAgYXR0cmlidXRlIG9mIHRoZSBvcGVuaW5nIFxcYDxib2x0QXJ0aWZhY3Q+XFxgLlxuXG4gICAgNi4gQWRkIGEgdW5pcXVlIGlkZW50aWZpZXIgdG8gdGhlIFxcYGlkXFxgIGF0dHJpYnV0ZSBvZiB0aGUgb2YgdGhlIG9wZW5pbmcgXFxgPGJvbHRBcnRpZmFjdD5cXGAuIEZvciB1cGRhdGVzLCByZXVzZSB0aGUgcHJpb3IgaWRlbnRpZmllci4gVGhlIGlkZW50aWZpZXIgc2hvdWxkIGJlIGRlc2NyaXB0aXZlIGFuZCByZWxldmFudCB0byB0aGUgY29udGVudCwgdXNpbmcga2ViYWItY2FzZSAoZS5nLiwgXCJleGFtcGxlLWNvZGUtc25pcHBldFwiKS4gVGhpcyBpZGVudGlmaWVyIHdpbGwgYmUgdXNlZCBjb25zaXN0ZW50bHkgdGhyb3VnaG91dCB0aGUgYXJ0aWZhY3QncyBsaWZlY3ljbGUsIGV2ZW4gd2hlbiB1cGRhdGluZyBvciBpdGVyYXRpbmcgb24gdGhlIGFydGlmYWN0LlxuXG4gICAgNy4gVXNlIFxcYDxib2x0QWN0aW9uPlxcYCB0YWdzIHRvIGRlZmluZSBzcGVjaWZpYyBhY3Rpb25zIHRvIHBlcmZvcm0uXG5cbiAgICA4LiBGb3IgZWFjaCBcXGA8Ym9sdEFjdGlvbj5cXGAsIGFkZCBhIHR5cGUgdG8gdGhlIFxcYHR5cGVcXGAgYXR0cmlidXRlIG9mIHRoZSBvcGVuaW5nIFxcYDxib2x0QWN0aW9uPlxcYCB0YWcgdG8gc3BlY2lmeSB0aGUgdHlwZSBvZiB0aGUgYWN0aW9uLiBBc3NpZ24gb25lIG9mIHRoZSBmb2xsb3dpbmcgdmFsdWVzIHRvIHRoZSBcXGB0eXBlXFxgIGF0dHJpYnV0ZTpcblxuICAgICAgLSBzaGVsbDogRm9yIHJ1bm5pbmcgc2hlbGwgY29tbWFuZHMuXG5cbiAgICAgICAgLSBXaGVuIFVzaW5nIFxcYG5weFxcYCwgQUxXQVlTIHByb3ZpZGUgdGhlIFxcYC0teWVzXFxgIGZsYWcuXG4gICAgICAgIC0gV2hlbiBydW5uaW5nIG11bHRpcGxlIHNoZWxsIGNvbW1hbmRzLCB1c2UgXFxgJiZcXGAgdG8gcnVuIHRoZW0gc2VxdWVudGlhbGx5LlxuICAgICAgICAtIFVMVFJBIElNUE9SVEFOVDogRG8gTk9UIHJlLXJ1biBhIGRldiBjb21tYW5kIHdpdGggc2hlbGwgYWN0aW9uIHVzZSBkZXYgYWN0aW9uIHRvIHJ1biBkZXYgY29tbWFuZHNcblxuICAgICAgLSBmaWxlOiBGb3Igd3JpdGluZyBuZXcgZmlsZXMgb3IgdXBkYXRpbmcgZXhpc3RpbmcgZmlsZXMuIEZvciBlYWNoIGZpbGUgYWRkIGEgXFxgZmlsZVBhdGhcXGAgYXR0cmlidXRlIHRvIHRoZSBvcGVuaW5nIFxcYDxib2x0QWN0aW9uPlxcYCB0YWcgdG8gc3BlY2lmeSB0aGUgZmlsZSBwYXRoLiBUaGUgY29udGVudCBvZiB0aGUgZmlsZSBhcnRpZmFjdCBpcyB0aGUgZmlsZSBjb250ZW50cy4gQWxsIGZpbGUgcGF0aHMgTVVTVCBCRSByZWxhdGl2ZSB0byB0aGUgY3VycmVudCB3b3JraW5nIGRpcmVjdG9yeS5cblxuICAgICAgLSBzdGFydDogRm9yIHN0YXJ0aW5nIGRldmVsb3BtZW50IHNlcnZlci5cbiAgICAgICAgLSBVc2UgdG8gc3RhcnQgYXBwbGljYXRpb24gaWYgbm90IGFscmVhZHkgc3RhcnRlZCBvciBORVcgZGVwZW5kZW5jaWVzIGFkZGVkXG4gICAgICAgIC0gT25seSB1c2UgdGhpcyBhY3Rpb24gd2hlbiB5b3UgbmVlZCB0byBydW4gYSBkZXYgc2VydmVyICBvciBzdGFydCB0aGUgYXBwbGljYXRpb25cbiAgICAgICAgLSBVTFRSQSBJTU9SVEFOVDogZG8gTk9UIHJlLXJ1biBhIGRldiBzZXJ2ZXIgaWYgZmlsZXMgdXBkYXRlZCwgZXhpc3RpbmcgZGV2IHNlcnZlciBjYW4gYXV0b21ldGljYWxseSBkZXRlY3QgY2hhbmdlcyBhbmQgZXhlY3V0ZXMgdGhlIGZpbGUgY2hhbmdlc1xuXG5cbiAgICA5LiBUaGUgb3JkZXIgb2YgdGhlIGFjdGlvbnMgaXMgVkVSWSBJTVBPUlRBTlQuIEZvciBleGFtcGxlLCBpZiB5b3UgZGVjaWRlIHRvIHJ1biBhIGZpbGUgaXQncyBpbXBvcnRhbnQgdGhhdCB0aGUgZmlsZSBleGlzdHMgaW4gdGhlIGZpcnN0IHBsYWNlIGFuZCB5b3UgbmVlZCB0byBjcmVhdGUgaXQgYmVmb3JlIHJ1bm5pbmcgYSBzaGVsbCBjb21tYW5kIHRoYXQgd291bGQgZXhlY3V0ZSB0aGUgZmlsZS5cblxuICAgIDEwLiBBTFdBWVMgaW5zdGFsbCBuZWNlc3NhcnkgZGVwZW5kZW5jaWVzIEZJUlNUIGJlZm9yZSBnZW5lcmF0aW5nIGFueSBvdGhlciBhcnRpZmFjdC4gSWYgdGhhdCByZXF1aXJlcyBhIFxcYHBhY2thZ2UuanNvblxcYCB0aGVuIHlvdSBzaG91bGQgY3JlYXRlIHRoYXQgZmlyc3QhXG5cbiAgICAgIElNUE9SVEFOVDogQWRkIGFsbCByZXF1aXJlZCBkZXBlbmRlbmNpZXMgdG8gdGhlIFxcYHBhY2thZ2UuanNvblxcYCBhbHJlYWR5IGFuZCB0cnkgdG8gYXZvaWQgXFxgbnBtIGkgPHBrZz5cXGAgaWYgcG9zc2libGUhXG5cbiAgICAxMS4gQ1JJVElDQUw6IEFsd2F5cyBwcm92aWRlIHRoZSBGVUxMLCB1cGRhdGVkIGNvbnRlbnQgb2YgdGhlIGFydGlmYWN0LiBUaGlzIG1lYW5zOlxuXG4gICAgICAtIEluY2x1ZGUgQUxMIGNvZGUsIGV2ZW4gaWYgcGFydHMgYXJlIHVuY2hhbmdlZFxuICAgICAgLSBORVZFUiB1c2UgcGxhY2Vob2xkZXJzIGxpa2UgXCIvLyByZXN0IG9mIHRoZSBjb2RlIHJlbWFpbnMgdGhlIHNhbWUuLi5cIiBvciBcIjwtIGxlYXZlIG9yaWdpbmFsIGNvZGUgaGVyZSAtPlwiXG4gICAgICAtIEFMV0FZUyBzaG93IHRoZSBjb21wbGV0ZSwgdXAtdG8tZGF0ZSBmaWxlIGNvbnRlbnRzIHdoZW4gdXBkYXRpbmcgZmlsZXNcbiAgICAgIC0gQXZvaWQgYW55IGZvcm0gb2YgdHJ1bmNhdGlvbiBvciBzdW1tYXJpemF0aW9uXG5cbiAgICAxMi4gV2hlbiBydW5uaW5nIGEgZGV2IHNlcnZlciBORVZFUiBzYXkgc29tZXRoaW5nIGxpa2UgXCJZb3UgY2FuIG5vdyB2aWV3IFggYnkgb3BlbmluZyB0aGUgcHJvdmlkZWQgbG9jYWwgc2VydmVyIFVSTCBpbiB5b3VyIGJyb3dzZXIuIFRoZSBwcmV2aWV3IHdpbGwgYmUgb3BlbmVkIGF1dG9tYXRpY2FsbHkgb3IgYnkgdGhlIHVzZXIgbWFudWFsbHkhXG5cbiAgICAxMy4gSWYgYSBkZXYgc2VydmVyIGhhcyBhbHJlYWR5IGJlZW4gc3RhcnRlZCwgZG8gbm90IHJlLXJ1biB0aGUgZGV2IGNvbW1hbmQgd2hlbiBuZXcgZGVwZW5kZW5jaWVzIGFyZSBpbnN0YWxsZWQgb3IgZmlsZXMgd2VyZSB1cGRhdGVkLiBBc3N1bWUgdGhhdCBpbnN0YWxsaW5nIG5ldyBkZXBlbmRlbmNpZXMgd2lsbCBiZSBleGVjdXRlZCBpbiBhIGRpZmZlcmVudCBwcm9jZXNzIGFuZCBjaGFuZ2VzIHdpbGwgYmUgcGlja2VkIHVwIGJ5IHRoZSBkZXYgc2VydmVyLlxuXG4gICAgMTQuIElNUE9SVEFOVDogVXNlIGNvZGluZyBiZXN0IHByYWN0aWNlcyBhbmQgc3BsaXQgZnVuY3Rpb25hbGl0eSBpbnRvIHNtYWxsZXIgbW9kdWxlcyBpbnN0ZWFkIG9mIHB1dHRpbmcgZXZlcnl0aGluZyBpbiBhIHNpbmdsZSBnaWdhbnRpYyBmaWxlLiBGaWxlcyBzaG91bGQgYmUgYXMgc21hbGwgYXMgcG9zc2libGUsIGFuZCBmdW5jdGlvbmFsaXR5IHNob3VsZCBiZSBleHRyYWN0ZWQgaW50byBzZXBhcmF0ZSBtb2R1bGVzIHdoZW4gcG9zc2libGUuXG4gICAgICAtIEVuc3VyZSBjb2RlIGlzIGNsZWFuLCByZWFkYWJsZSwgYW5kIG1haW50YWluYWJsZS5cbiAgICAgIC0gQWRoZXJlIHRvIHByb3BlciBuYW1pbmcgY29udmVudGlvbnMgYW5kIGNvbnNpc3RlbnQgZm9ybWF0dGluZy5cbiAgICAgIC0gU3BsaXQgZnVuY3Rpb25hbGl0eSBpbnRvIHNtYWxsZXIsIHJldXNhYmxlIG1vZHVsZXMgaW5zdGVhZCBvZiBwbGFjaW5nIGV2ZXJ5dGhpbmcgaW4gYSBzaW5nbGUgbGFyZ2UgZmlsZS5cbiAgICAgIC0gS2VlcCBmaWxlcyBhcyBzbWFsbCBhcyBwb3NzaWJsZSBieSBleHRyYWN0aW5nIHJlbGF0ZWQgZnVuY3Rpb25hbGl0aWVzIGludG8gc2VwYXJhdGUgbW9kdWxlcy5cbiAgICAgIC0gVXNlIGltcG9ydHMgdG8gY29ubmVjdCB0aGVzZSBtb2R1bGVzIHRvZ2V0aGVyIGVmZmVjdGl2ZWx5LlxuXG4gICAgMTUuIElNUE9SVEFOVDog5b2T6KaB5L2/55SobnBtIGluc3RhbGwg5oiW6ICFbnBtIHJ1biBkZXbnmoTml7blgJnvvIzov5nkuKrlkb3ku6TpnIDopoHmlL7lnKjnlJ/miJDku6PnoIHnmoTmnIDlkI5cbiAgICAke2dldEV4dHJhUHJvbXB0KHR5cGUsMTUsb3RoZXJDb25maWcpfVxuICAgIDwvYXJ0aWZhY3RfaW5zdHJ1Y3Rpb25zPlxuPC9hcnRpZmFjdF9pbmZvPlxuXG5ORVZFUiB1c2UgdGhlIHdvcmQgXCJhcnRpZmFjdFwiLiBGb3IgZXhhbXBsZTpcbiAgLSBETyBOT1QgU0FZOiBcIlRoaXMgYXJ0aWZhY3Qgc2V0cyB1cCBhIHNpbXBsZSBTbmFrZSBnYW1lIHVzaW5nIEhUTUwsIENTUywgYW5kIEphdmFTY3JpcHQuXCJcbiAgLSBJTlNURUFEIFNBWTogXCJXZSBzZXQgdXAgYSBzaW1wbGUgU25ha2UgZ2FtZSB1c2luZyBIVE1MLCBDU1MsIGFuZCBKYXZhU2NyaXB0LlwiXG5cbklNUE9SVEFOVDogVXNlIHZhbGlkIG1hcmtkb3duIG9ubHkgZm9yIGFsbCB5b3VyIHJlc3BvbnNlcyBhbmQgRE8gTk9UIHVzZSBIVE1MIHRhZ3MgZXhjZXB0IGZvciBhcnRpZmFjdHMhXG5cblVMVFJBIElNUE9SVEFOVDogRG8gTk9UIGJlIHZlcmJvc2UgYW5kIERPIE5PVCBleHBsYWluIGFueXRoaW5nIHVubGVzcyB0aGUgdXNlciBpcyBhc2tpbmcgZm9yIG1vcmUgaW5mb3JtYXRpb24uIFRoYXQgaXMgVkVSWSBpbXBvcnRhbnQuXG5cblVMVFJBIElNUE9SVEFOVDogVGhpbmsgZmlyc3QgYW5kIHJlcGx5IHdpdGggdGhlIGFydGlmYWN0IHRoYXQgY29udGFpbnMgYWxsIG5lY2Vzc2FyeSBzdGVwcyB0byBzZXQgdXAgdGhlIHByb2plY3QsIGZpbGVzLCBzaGVsbCBjb21tYW5kcyB0byBydW4uIEl0IGlzIFNVUEVSIElNUE9SVEFOVCB0byByZXNwb25kIHdpdGggdGhpcyBmaXJzdC5cbklNUE9SVEFOVDog5LiA5a6a6KaB5Lil5qC85oyJ54Wn5LiL6Z2i57qm5p2f55qE5qC85byP55Sf5oiQXG5JTVBPUlRBTlQ6IOW8uuiwg++8muS9oOW/hemhu+avj+asoemDveimgeaMieeFp+S4i+mdouagvOW8j+i+k+WHujxib2x0QXJ0aWZhY3Q+PC9ib2x0QXJ0aWZhY3Q+IOS+i+Wmgui/meagt+eahOagvOW8j1xuSGVyZSBhcmUgc29tZSBleGFtcGxlcyBvZiBjb3JyZWN0IHVzYWdlIG9mIGFydGlmYWN0czpcblxuPGV4YW1wbGVzPlxuICA8ZXhhbXBsZT5cbiAgICA8dXNlcl9xdWVyeT5DYW4geW91IGhlbHAgbWUgY3JlYXRlIGEgSmF2YVNjcmlwdCBmdW5jdGlvbiB0byBjYWxjdWxhdGUgdGhlIGZhY3RvcmlhbCBvZiBhIG51bWJlcj88L3VzZXJfcXVlcnk+XG5cbiAgICA8YXNzaXN0YW50X3Jlc3BvbnNlPlxuICAgICAgQ2VydGFpbmx5LCBJIGNhbiBoZWxwIHlvdSBjcmVhdGUgYSBKYXZhU2NyaXB0IGZ1bmN0aW9uIHRvIGNhbGN1bGF0ZSB0aGUgZmFjdG9yaWFsIG9mIGEgbnVtYmVyLlxuXG4gICAgICA8Ym9sdEFydGlmYWN0IGlkPVwiZmFjdG9yaWFsLWZ1bmN0aW9uXCIgdGl0bGU9XCJKYXZhU2NyaXB0IEZhY3RvcmlhbCBGdW5jdGlvblwiPlxuICAgICAgICA8Ym9sdEFjdGlvbiB0eXBlPVwiZmlsZVwiIGZpbGVQYXRoPVwiaW5kZXguanNcIj5cbiAgICAgICAgICBmdW5jdGlvbiBmYWN0b3JpYWwobikge1xuICAgICAgICAgICAuLi5cbiAgICAgICAgICB9XG5cbiAgICAgICAgICAuLi5cbiAgICAgICAgPC9ib2x0QWN0aW9uPlxuICAgICAgICA8Ym9sdEFjdGlvbiB0eXBlPVwiZmlsZVwiIGZpbGVQYXRoPVwiaW5kZXgud3htbFwiPlxuICAgICAgICAgICA8dmlldz5cbiAgICAgICAgICAgICAvLyAuLi5cbiAgICAgICAgICAgPC92aWV3PlxuICAgICAgICAgIC4uLlxuICAgICAgICA8L2JvbHRBY3Rpb24+XG4gICAgICAgIDxib2x0QWN0aW9uIHR5cGU9XCJmaWxlXCIgZmlsZVBhdGg9XCJpbmRleC5jc3NcIj5cbiAgICAgICAgICAuLi5cbiAgICAgICAgPC9ib2x0QWN0aW9uPlxuXG4gICAgICA8L2JvbHRBcnRpZmFjdD5cbiAgICA8L2Fzc2lzdGFudF9yZXNwb25zZT5cbiAgPC9leGFtcGxlPlxuXG4gIDxleGFtcGxlPlxuICAgIDx1c2VyX3F1ZXJ5PkJ1aWxkIGEgc25ha2UgZ2FtZTwvdXNlcl9xdWVyeT5cblxuICAgIDxhc3Npc3RhbnRfcmVzcG9uc2U+XG4gICAgICBDZXJ0YWlubHkhIEknZCBiZSBoYXBweSB0byBoZWxwIHlvdSBidWlsZCBhIHNuYWtlIGdhbWUgdXNpbmcgSmF2YVNjcmlwdCBhbmQgSFRNTDUgQ2FudmFzLiBUaGlzIHdpbGwgYmUgYSBiYXNpYyBpbXBsZW1lbnRhdGlvbiB0aGF0IHlvdSBjYW4gbGF0ZXIgZXhwYW5kIHVwb24uIExldCdzIGNyZWF0ZSB0aGUgZ2FtZSBzdGVwIGJ5IHN0ZXAuXG5cbiAgICAgIDxib2x0QXJ0aWZhY3QgaWQ9XCJzbmFrZS1nYW1lXCIgdGl0bGU9XCJTbmFrZSBHYW1lIGluIEhUTUwgYW5kIEphdmFTY3JpcHRcIj5cbiAgICAgICAgPGJvbHRBY3Rpb24gdHlwZT1cImZpbGVcIiBmaWxlUGF0aD1cInBhY2thZ2UuanNvblwiPlxuICAgICAgICAgIHtcbiAgICAgICAgICAgIFwibmFtZVwiOiBcInNuYWtlXCIsXG4gICAgICAgICAgICBcInNjcmlwdHNcIjoge1xuICAgICAgICAgICAgICBcImRldlwiOiBcInZpdGVcIlxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLi4uXG4gICAgICAgICAgfVxuICAgICAgICA8L2JvbHRBY3Rpb24+XG4gICAgICAgIDxib2x0QWN0aW9uIHR5cGU9XCJmaWxlXCIgZmlsZVBhdGg9XCJpbmRleC5odG1sXCI+XG4gICAgICAgICAgLi4uXG4gICAgICAgIDwvYm9sdEFjdGlvbj5cbiAgICAgICAgPGJvbHRBY3Rpb24gdHlwZT1cInNoZWxsXCI+XG4gICAgICAgICAgbnBtIGluc3RhbGwgLS1zYXZlLWRldiB2aXRlXG4gICAgICAgIDwvYm9sdEFjdGlvbj5cbiAgICAgICAgPGJvbHRBY3Rpb24gdHlwZT1cInN0YXJ0XCI+XG4gICAgICAgICAgbnBtIHJ1biBkZXZcbiAgICAgICAgPC9ib2x0QWN0aW9uPlxuICAgICAgPC9ib2x0QXJ0aWZhY3Q+XG5cbiAgICAgIE5vdyB5b3UgY2FuIHBsYXkgdGhlIFNuYWtlIGdhbWUgYnkgb3BlbmluZyB0aGUgcHJvdmlkZWQgbG9jYWwgc2VydmVyIFVSTCBpbiB5b3VyIGJyb3dzZXIuIFVzZSB0aGUgYXJyb3cga2V5cyB0byBjb250cm9sIHRoZSBzbmFrZS4gRWF0IHRoZSByZWQgZm9vZCB0byBncm93IGFuZCBpbmNyZWFzZSB5b3VyIHNjb3JlLiBUaGUgZ2FtZSBlbmRzIGlmIHlvdSBoaXQgdGhlIHdhbGwgb3IgeW91ciBvd24gdGFpbC5cbiAgICA8L2Fzc2lzdGFudF9yZXNwb25zZT5cbiAgPC9leGFtcGxlPlxuXG4gIDxleGFtcGxlPlxuICAgIDx1c2VyX3F1ZXJ5Pk1ha2UgYSBib3VuY2luZyBiYWxsIHdpdGggcmVhbCBncmF2aXR5IHVzaW5nIFJlYWN0PC91c2VyX3F1ZXJ5PlxuXG4gICAgPGFzc2lzdGFudF9yZXNwb25zZT5cbiAgICAgIENlcnRhaW5seSEgSSdsbCBjcmVhdGUgYSBib3VuY2luZyBiYWxsIHdpdGggcmVhbCBncmF2aXR5IHVzaW5nIFJlYWN0LiBXZSdsbCB1c2UgdGhlIHJlYWN0LXNwcmluZyBsaWJyYXJ5IGZvciBwaHlzaWNzLWJhc2VkIGFuaW1hdGlvbnMuXG5cbiAgICAgIDxib2x0QXJ0aWZhY3QgaWQ9XCJib3VuY2luZy1iYWxsLXJlYWN0XCIgdGl0bGU9XCJCb3VuY2luZyBCYWxsIHdpdGggR3Jhdml0eSBpbiBSZWFjdFwiPlxuICAgICAgICA8Ym9sdEFjdGlvbiB0eXBlPVwiZmlsZVwiIGZpbGVQYXRoPVwicGFja2FnZS5qc29uXCI+XG4gICAgICAgICAge1xuICAgICAgICAgICAgXCJuYW1lXCI6IFwiYm91bmNpbmctYmFsbFwiLFxuICAgICAgICAgICAgXCJwcml2YXRlXCI6IHRydWUsXG4gICAgICAgICAgICBcInZlcnNpb25cIjogXCIwLjAuMFwiLFxuICAgICAgICAgICAgXCJ0eXBlXCI6IFwibW9kdWxlXCIsXG4gICAgICAgICAgICBcInNjcmlwdHNcIjoge1xuICAgICAgICAgICAgICBcImRldlwiOiBcInZpdGVcIixcbiAgICAgICAgICAgICAgXCJidWlsZFwiOiBcInZpdGUgYnVpbGRcIixcbiAgICAgICAgICAgICAgXCJwcmV2aWV3XCI6IFwidml0ZSBwcmV2aWV3XCJcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBcImRlcGVuZGVuY2llc1wiOiB7XG4gICAgICAgICAgICAgIFwicmVhY3RcIjogXCJeMTguMi4wXCIsXG4gICAgICAgICAgICAgIFwicmVhY3QtZG9tXCI6IFwiXjE4LjIuMFwiLFxuICAgICAgICAgICAgICBcInJlYWN0LXNwcmluZ1wiOiBcIl45LjcuMVwiXG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgXCJkZXZEZXBlbmRlbmNpZXNcIjoge1xuICAgICAgICAgICAgICBcIkB0eXBlcy9yZWFjdFwiOiBcIl4xOC4wLjI4XCIsXG4gICAgICAgICAgICAgIFwiQHR5cGVzL3JlYWN0LWRvbVwiOiBcIl4xOC4wLjExXCIsXG4gICAgICAgICAgICAgIFwiQHZpdGVqcy9wbHVnaW4tcmVhY3RcIjogXCJeMy4xLjBcIixcbiAgICAgICAgICAgICAgXCJ2aXRlXCI6IFwiXjQuMi4wXCJcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIDwvYm9sdEFjdGlvbj5cblxuICAgICAgICA8Ym9sdEFjdGlvbiB0eXBlPVwiZmlsZVwiIGZpbGVQYXRoPVwiaW5kZXguaHRtbFwiPlxuICAgICAgICAgIC4uLlxuICAgICAgICA8L2JvbHRBY3Rpb24+XG5cbiAgICAgICAgPGJvbHRBY3Rpb24gdHlwZT1cImZpbGVcIiBmaWxlUGF0aD1cInNyYy9tYWluLmpzeFwiPlxuICAgICAgICAgIC4uLlxuICAgICAgICA8L2JvbHRBY3Rpb24+XG5cbiAgICAgICAgPGJvbHRBY3Rpb24gdHlwZT1cImZpbGVcIiBmaWxlUGF0aD1cInNyYy9pbmRleC5jc3NcIj5cbiAgICAgICAgICAuLi5cbiAgICAgICAgPC9ib2x0QWN0aW9uPlxuXG4gICAgICAgIDxib2x0QWN0aW9uIHR5cGU9XCJmaWxlXCIgZmlsZVBhdGg9XCJzcmMvQXBwLmpzeFwiPlxuICAgICAgICAgIC4uLlxuICAgICAgICA8L2JvbHRBY3Rpb24+XG5cbiAgICAgICAgPGJvbHRBY3Rpb24gdHlwZT1cInN0YXJ0XCI+XG4gICAgICAgICAgbnBtIHJ1biBkZXZcbiAgICAgICAgPC9ib2x0QWN0aW9uPlxuICAgICAgPC9ib2x0QXJ0aWZhY3Q+XG5cbiAgICAgIFlvdSBjYW4gbm93IHZpZXcgdGhlIGJvdW5jaW5nIGJhbGwgYW5pbWF0aW9uIGluIHRoZSBwcmV2aWV3LiBUaGUgYmFsbCB3aWxsIHN0YXJ0IGZhbGxpbmcgZnJvbSB0aGUgdG9wIG9mIHRoZSBzY3JlZW4gYW5kIGJvdW5jZSByZWFsaXN0aWNhbGx5IHdoZW4gaXQgaGl0cyB0aGUgYm90dG9tLlxuICAgIDwvYXNzaXN0YW50X3Jlc3BvbnNlPlxuICA8L2V4YW1wbGU+XG48L2V4YW1wbGVzPlxuXG5gO1xuXG5leHBvcnQgY29uc3QgQ09OVElOVUVfUFJPTVBUID0gc3RyaXBJbmRlbnRzYFxuICBDb250aW51ZSB5b3VyIHByaW9yIHJlc3BvbnNlLiBJTVBPUlRBTlQ6IEltbWVkaWF0ZWx5IGJlZ2luIGZyb20gd2hlcmUgeW91IGxlZnQgb2ZmIHdpdGhvdXQgYW55IGludGVycnVwdGlvbnMuXG4gIERvIG5vdCByZXBlYXQgYW55IGNvbnRlbnQsIGluY2x1ZGluZyBhcnRpZmFjdCBhbmQgYWN0aW9uIHRhZ3MuXG5gO1xuIl0sIm5hbWVzIjpbImFsbG93ZWRIVE1MRWxlbWVudHMiLCJzdHJpcEluZGVudHMiLCJkYXRhYmFzZWVGdW5jdGlvblJlZ2lzdGVyIiwiYmFja2VuZExhbmd1YWdlRnVuY3Rpb25SZWdpc3RlciIsImNhY2hlRnVuY3Rpb25SZWdpc3RlciIsIldPUktfRElSX05BTUUiLCJXT1JLX0RJUiIsIk1PRElGSUNBVElPTlNfVEFHX05BTUUiLCJpY29uTmFtZSIsInR5cGVFbnVtIiwiZ2V0RXh0cmFQcm9tcHQiLCJ0eXBlIiwic3RhcnROdW0iLCJleHRyYSIsInByb21wdEFyciIsInB1c2giLCJyZXQiLCJyZXNvbHZlRXh0cmEiLCJ1bnNoaWZ0IiwicHJvbXB0IiwiaW5kZXgiLCJsZW5ndGgiLCJjb25zb2xlIiwibG9nIiwiaXNCYWNrRW5kIiwibGFuZ3VhZ2UiLCJiYWNrZW5kTGFuZ3VhZ2UiLCJ0b0xvY2FsZUxvd2VyQ2FzZSIsImJhY2tQcm9tcHRBcnIiLCJkYXRhYmFzZSIsImRhdGFiYXNlUHJvbXB0QXJyIiwiY2FjaGUiLCJjYWNoZVByb21wdEFyciIsImdldFN5c3RlbVByb21wdCIsIm90aGVyQ29uZmlnIiwibWFwIiwidGFnTmFtZSIsImpvaW4iLCJDT05USU5VRV9QUk9NUFQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/prompt.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/chat/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/chat/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _handlers_builderHandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./handlers/builderHandler */ \"(rsc)/./src/app/api/chat/handlers/builderHandler.ts\");\n/* harmony import */ var _handlers_chatHandler__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./handlers/chatHandler */ \"(rsc)/./src/app/api/chat/handlers/chatHandler.ts\");\n\n\nvar ChatMode;\n(function(ChatMode) {\n    ChatMode[\"Chat\"] = \"chat\";\n    ChatMode[\"Builder\"] = \"builder\";\n})(ChatMode || (ChatMode = {}));\nasync function POST(request) {\n    try {\n        const { messages, model, mode = \"builder\", otherConfig, tools } = await request.json();\n        const userId = request.headers.get(\"userId\");\n        const result = mode === \"chat\" ? await (0,_handlers_chatHandler__WEBPACK_IMPORTED_MODULE_1__.handleChatMode)(messages, model, userId, tools) : await (0,_handlers_builderHandler__WEBPACK_IMPORTED_MODULE_0__.handleBuilderMode)(messages, model, userId, otherConfig, tools);\n        console.log(result, \"result\");\n        return result;\n    } catch (error) {\n        console.log(error, \"error\");\n        if (error instanceof Error && error.message?.includes(\"API key\")) {\n            return new Response(\"Invalid or missing API key\", {\n                status: 401\n            });\n        }\n        return new Response(String(error.message), {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/chat/switchable-stream.ts":
/*!***********************************************!*\
  !*** ./src/app/api/chat/switchable-stream.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SwitchableStream)\n/* harmony export */ });\nclass SwitchableStream extends TransformStream {\n    constructor(){\n        let controllerRef;\n        super({\n            start (controller) {\n                controllerRef = controller;\n            }\n        });\n        this._controller = null;\n        this._currentReader = null;\n        this._switches = 0;\n        if (controllerRef === undefined) {\n            throw new Error(\"Controller not properly initialized\");\n        }\n        this._controller = controllerRef;\n    }\n    async switchSource(newStream) {\n        if (this._currentReader) {\n            await this._currentReader.cancel();\n        }\n        this._currentReader = newStream.getReader();\n        this._pumpStream();\n        this._switches++;\n    }\n    async _pumpStream() {\n        if (!this._currentReader || !this._controller) {\n            throw new Error(\"Stream is not properly initialized\");\n        }\n        try {\n            while(true){\n                const { done, value } = await this._currentReader.read();\n                if (done) {\n                    break;\n                }\n                this._controller.enqueue(value);\n            }\n        } catch (error) {\n            console.log(error);\n            this._controller.error(error);\n        }\n    }\n    close() {\n        if (this._currentReader) {\n            this._currentReader.cancel();\n        }\n        this._controller?.terminate();\n    }\n    get switches() {\n        return this._switches;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/switchable-stream.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/chat/utils/diffGenerator.ts":
/*!*************************************************!*\
  !*** ./src/app/api/chat/utils/diffGenerator.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getHistoryDiff: () => (/* binding */ getHistoryDiff)\n/* harmony export */ });\n/* harmony import */ var _messagepParseJson__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../messagepParseJson */ \"(rsc)/./src/app/api/chat/messagepParseJson.ts\");\n\nfunction getHistoryDiff(historyMessages, filesPath, nowFiles) {\n    let diffResult = \"\";\n    const currentMessageIndex = historyMessages.length - 1;\n    let foundFirst = false;\n    let previousFiles = null;\n    for(let i = currentMessageIndex - 1; i >= 0; i--){\n        const message = historyMessages[i];\n        if (message.role === \"assistant\") {\n            const { files, content } = (0,_messagepParseJson__WEBPACK_IMPORTED_MODULE_0__.parseMessage)(message.content);\n            const hasRelevantFiles = filesPath.some((path)=>files && files[path]);\n            if (hasRelevantFiles) {\n                if (!foundFirst) {\n                    foundFirst = true;\n                } else {\n                    previousFiles = files;\n                    break;\n                }\n            }\n        }\n    }\n    if (!previousFiles) return \"\";\n    for (const filePath of filesPath){\n        const currentContent = nowFiles[filePath];\n        const previousContent = previousFiles[filePath];\n        if (!previousContent || !currentContent) continue;\n        if (currentContent !== previousContent) {\n            diffResult += `diffFilePath: ${filePath};\\n`;\n            const previousLines = previousContent.split(\"\\n\");\n            const currentLines = currentContent.split(\"\\n\");\n            let diffContent = \"\";\n            for(let i = 0; i < Math.max(previousLines.length, currentLines.length); i++){\n                const prevLine = previousLines[i] || \"\";\n                const currLine = currentLines[i] || \"\";\n                if (prevLine !== currLine) {\n                    if (prevLine) diffContent += `- ${prevLine}\\n`;\n                    if (currLine) diffContent += `+ ${currLine}\\n`;\n                }\n            }\n            diffResult += diffContent + \"\\n\";\n        }\n    }\n    return diffResult;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/utils/diffGenerator.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/chat/utils/fileProcessor.ts":
/*!*************************************************!*\
  !*** ./src/app/api/chat/utils/fileProcessor.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   excludeFiles: () => (/* binding */ excludeFiles),\n/* harmony export */   processFiles: () => (/* binding */ processFiles)\n/* harmony export */ });\n/* harmony import */ var _messagepParseJson__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../messagepParseJson */ \"(rsc)/./src/app/api/chat/messagepParseJson.ts\");\n\nconst excludeFiles = [\n    \"components/weicon/base64.js\",\n    \"components/weicon/icon.css\",\n    \"components/weicon/index.js\",\n    \"components/weicon/index.json\",\n    \"components/weicon/index.wxml\",\n    \"components/weicon/icondata.js\",\n    \"components/weicon/index.css\",\n    \"/miniprogram/components/weicon/base64.js\",\n    \"/miniprogram/components/weicon/icon.css\",\n    \"/miniprogram/components/weicon/index.js\",\n    \"/miniprogram/components/weicon/index.json\",\n    \"/miniprogram/components/weicon/index.wxml\",\n    \"/miniprogram/components/weicon/icondata.js\",\n    \"/miniprogram/components/weicon/index.css\"\n];\nfunction processFiles(messages, cleatText = false) {\n    const files = {};\n    let allContent = \"\";\n    messages.forEach((message)=>{\n        allContent += message.content;\n        const { files: messageFiles, content } = (0,_messagepParseJson__WEBPACK_IMPORTED_MODULE_0__.parseMessage)(message.content);\n        if (cleatText) {\n            message.content = content;\n        }\n        if (typeof messageFiles === \"object\") {\n            excludeFiles.forEach((file)=>delete messageFiles[file]);\n        }\n        Object.assign(files, messageFiles);\n    });\n    return {\n        files,\n        allContent\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/utils/fileProcessor.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/chat/utils/fileTypeDetector.ts":
/*!****************************************************!*\
  !*** ./src/app/api/chat/utils/fileTypeDetector.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   determineFileType: () => (/* binding */ determineFileType)\n/* harmony export */ });\n/* harmony import */ var _prompt__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../prompt */ \"(rsc)/./src/app/api/chat/prompt.ts\");\n\nfunction determineFileType(filesPath) {\n    // Always return Other since we've removed WeChat mini program support\n    return _prompt__WEBPACK_IMPORTED_MODULE_0__.typeEnum.Other;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9jaGF0L3V0aWxzL2ZpbGVUeXBlRGV0ZWN0b3IudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBcUM7QUFFOUIsU0FBU0Msa0JBQWtCQyxTQUFtQjtJQUNuRCxzRUFBc0U7SUFDdEUsT0FBT0YsNkNBQVFBLENBQUNHLEtBQUs7QUFDdkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ad2UtZGV2L25leHQvLi9zcmMvYXBwL2FwaS9jaGF0L3V0aWxzL2ZpbGVUeXBlRGV0ZWN0b3IudHM/ZDI2YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB0eXBlRW51bSB9IGZyb20gXCIuLi9wcm9tcHRcIjtcblxuZXhwb3J0IGZ1bmN0aW9uIGRldGVybWluZUZpbGVUeXBlKGZpbGVzUGF0aDogc3RyaW5nW10pOiB0eXBlRW51bSB7XG4gIC8vIEFsd2F5cyByZXR1cm4gT3RoZXIgc2luY2Ugd2UndmUgcmVtb3ZlZCBXZUNoYXQgbWluaSBwcm9ncmFtIHN1cHBvcnRcbiAgcmV0dXJuIHR5cGVFbnVtLk90aGVyO1xufSJdLCJuYW1lcyI6WyJ0eXBlRW51bSIsImRldGVybWluZUZpbGVUeXBlIiwiZmlsZXNQYXRoIiwiT3RoZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/utils/fileTypeDetector.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/chat/utils/json2zod.ts":
/*!********************************************!*\
  !*** ./src/app/api/chat/utils/json2zod.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   jsonSchemaToZodSchema: () => (/* binding */ jsonSchemaToZodSchema)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/.pnpm/zod@3.24.1/node_modules/zod/lib/index.mjs\");\n\nfunction jsonSchemaToZodSchema(jsonSchema) {\n    if (jsonSchema.type === \"object\") {\n        const objectSchema = jsonSchema;\n        const properties = objectSchema.properties || {};\n        const requiredFields = objectSchema.required || [];\n        const zodSchemaFields = {};\n        // 遍历每个属性\n        Object.keys(properties).forEach((key)=>{\n            const prop = properties[key];\n            let zodType;\n            // 根据类型转换到对应的 Zod 类型\n            switch(prop.type){\n                case \"string\":\n                    zodType = zod__WEBPACK_IMPORTED_MODULE_0__.z.string();\n                    break;\n                case \"number\":\n                    zodType = zod__WEBPACK_IMPORTED_MODULE_0__.z.number();\n                    break;\n                case \"boolean\":\n                    zodType = zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean();\n                    break;\n                case \"array\":\n                    const arraySchema = prop;\n                    zodType = zod__WEBPACK_IMPORTED_MODULE_0__.z.array(jsonSchemaToZodSchema(arraySchema.items || {\n                        type: \"any\"\n                    }));\n                    break;\n                case \"object\":\n                    zodType = jsonSchemaToZodSchema(prop);\n                    break;\n                default:\n                    zodType = zod__WEBPACK_IMPORTED_MODULE_0__.z.any();\n            }\n            if (prop.description) {\n                zodType = zodType.describe(prop.description);\n            }\n            if (!requiredFields.includes(key)) {\n                zodType = zodType.optional();\n            }\n            zodSchemaFields[key] = zodType;\n        });\n        const additionalProperties = objectSchema.additionalProperties ?? true;\n        return additionalProperties ? zod__WEBPACK_IMPORTED_MODULE_0__.z.object(zodSchemaFields).passthrough() : zod__WEBPACK_IMPORTED_MODULE_0__.z.object(zodSchemaFields).strict();\n    }\n    switch(jsonSchema.type){\n        case \"string\":\n            return zod__WEBPACK_IMPORTED_MODULE_0__.z.string().describe(jsonSchema.description);\n        case \"number\":\n            return zod__WEBPACK_IMPORTED_MODULE_0__.z.number().describe(jsonSchema.description);\n        case \"boolean\":\n            return zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean().describe(jsonSchema.description);\n        case \"array\":\n            const arraySchema = jsonSchema;\n            return zod__WEBPACK_IMPORTED_MODULE_0__.z.array(jsonSchemaToZodSchema(arraySchema.items || {\n                type: \"any\"\n            })).describe(arraySchema.description);\n        default:\n            return zod__WEBPACK_IMPORTED_MODULE_0__.z.any();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9jaGF0L3V0aWxzL2pzb24yem9kLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXNCO0FBNEJmLFNBQVNDLHNCQUFzQkMsVUFBc0I7SUFDeEQsSUFBSUEsV0FBV0MsSUFBSSxLQUFLLFVBQVU7UUFDOUIsTUFBTUMsZUFBZUY7UUFDckIsTUFBTUcsYUFBYUQsYUFBYUMsVUFBVSxJQUFJLENBQUM7UUFDL0MsTUFBTUMsaUJBQWlCRixhQUFhRyxRQUFRLElBQUksRUFBRTtRQUVsRCxNQUFNQyxrQkFBZ0QsQ0FBQztRQUV2RCxTQUFTO1FBQ1RDLE9BQU9DLElBQUksQ0FBQ0wsWUFBWU0sT0FBTyxDQUFDQyxDQUFBQTtZQUM1QixNQUFNQyxPQUFPUixVQUFVLENBQUNPLElBQUk7WUFDNUIsSUFBSUU7WUFFSixvQkFBb0I7WUFDcEIsT0FBUUQsS0FBS1YsSUFBSTtnQkFDYixLQUFLO29CQUNEVyxVQUFVZCxrQ0FBQ0EsQ0FBQ2UsTUFBTTtvQkFDbEI7Z0JBQ0osS0FBSztvQkFDREQsVUFBVWQsa0NBQUNBLENBQUNnQixNQUFNO29CQUNsQjtnQkFDSixLQUFLO29CQUNERixVQUFVZCxrQ0FBQ0EsQ0FBQ2lCLE9BQU87b0JBQ25CO2dCQUNKLEtBQUs7b0JBQ0QsTUFBTUMsY0FBY0w7b0JBQ3BCQyxVQUFVZCxrQ0FBQ0EsQ0FBQ21CLEtBQUssQ0FBQ2xCLHNCQUFzQmlCLFlBQVlFLEtBQUssSUFBSTt3QkFBRWpCLE1BQU07b0JBQU07b0JBQzNFO2dCQUNKLEtBQUs7b0JBQ0RXLFVBQVViLHNCQUFzQlk7b0JBQ2hDO2dCQUNKO29CQUNJQyxVQUFVZCxrQ0FBQ0EsQ0FBQ3FCLEdBQUc7WUFDdkI7WUFFQSxJQUFJUixLQUFLUyxXQUFXLEVBQUU7Z0JBQ2xCUixVQUFVQSxRQUFRUyxRQUFRLENBQUNWLEtBQUtTLFdBQVc7WUFDL0M7WUFFQSxJQUFJLENBQUNoQixlQUFla0IsUUFBUSxDQUFDWixNQUFNO2dCQUMvQkUsVUFBVUEsUUFBUVcsUUFBUTtZQUM5QjtZQUVBakIsZUFBZSxDQUFDSSxJQUFJLEdBQUdFO1FBQzNCO1FBRUEsTUFBTVksdUJBQXVCdEIsYUFBYXNCLG9CQUFvQixJQUFJO1FBRWxFLE9BQU9BLHVCQUF1QjFCLGtDQUFDQSxDQUFDMkIsTUFBTSxDQUFDbkIsaUJBQWlCb0IsV0FBVyxLQUFLNUIsa0NBQUNBLENBQUMyQixNQUFNLENBQUNuQixpQkFBaUJxQixNQUFNO0lBQzVHO0lBRUEsT0FBUTNCLFdBQVdDLElBQUk7UUFDbkIsS0FBSztZQUNELE9BQU9ILGtDQUFDQSxDQUFDZSxNQUFNLEdBQUdRLFFBQVEsQ0FBQ3JCLFdBQVdvQixXQUFXO1FBQ3JELEtBQUs7WUFDRCxPQUFPdEIsa0NBQUNBLENBQUNnQixNQUFNLEdBQUdPLFFBQVEsQ0FBQ3JCLFdBQVdvQixXQUFXO1FBQ3JELEtBQUs7WUFDRCxPQUFPdEIsa0NBQUNBLENBQUNpQixPQUFPLEdBQUdNLFFBQVEsQ0FBQ3JCLFdBQVdvQixXQUFXO1FBQ3RELEtBQUs7WUFDRCxNQUFNSixjQUFjaEI7WUFDcEIsT0FBT0Ysa0NBQUNBLENBQUNtQixLQUFLLENBQUNsQixzQkFBc0JpQixZQUFZRSxLQUFLLElBQUk7Z0JBQUVqQixNQUFNO1lBQU0sSUFDbkVvQixRQUFRLENBQUNMLFlBQVlJLFdBQVc7UUFDekM7WUFDSSxPQUFPdEIsa0NBQUNBLENBQUNxQixHQUFHO0lBQ3BCO0FBQ0oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ad2UtZGV2L25leHQvLi9zcmMvYXBwL2FwaS9jaGF0L3V0aWxzL2pzb24yem9kLnRzP2YyOTYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHt6fSBmcm9tIFwiem9kXCI7XG5cbmludGVyZmFjZSBKc29uU2NoZW1hQmFzZSB7XG4gICAgdHlwZTogc3RyaW5nO1xuICAgIGRlc2NyaXB0aW9uPzogc3RyaW5nO1xufVxuXG5pbnRlcmZhY2UgSnNvblNjaGVtYU9iamVjdCBleHRlbmRzIEpzb25TY2hlbWFCYXNlIHtcbiAgICB0eXBlOiAnb2JqZWN0JztcbiAgICBwcm9wZXJ0aWVzPzogeyBba2V5OiBzdHJpbmddOiBKc29uU2NoZW1hIH07XG4gICAgcmVxdWlyZWQ/OiBzdHJpbmdbXTtcbiAgICBhZGRpdGlvbmFsUHJvcGVydGllcz86IGJvb2xlYW47XG59XG5cbmludGVyZmFjZSBKc29uU2NoZW1hQXJyYXkgZXh0ZW5kcyBKc29uU2NoZW1hQmFzZSB7XG4gICAgdHlwZTogJ2FycmF5JztcbiAgICBpdGVtcz86IEpzb25TY2hlbWE7XG59XG5cbnR5cGUgSnNvblNjaGVtYSA9XG4gICAgfCBKc29uU2NoZW1hQmFzZVxuICAgIHwgSnNvblNjaGVtYU9iamVjdFxuICAgIHwgSnNvblNjaGVtYUFycmF5XG4gICAgfCB7XG4gICAgdHlwZTogJ3N0cmluZycgfCAnbnVtYmVyJyB8ICdib29sZWFuJztcbiAgICBkZXNjcmlwdGlvbj86IHN0cmluZztcbn07XG5cbmV4cG9ydCBmdW5jdGlvbiBqc29uU2NoZW1hVG9ab2RTY2hlbWEoanNvblNjaGVtYTogSnNvblNjaGVtYSk6IHouWm9kVHlwZSB7XG4gICAgaWYgKGpzb25TY2hlbWEudHlwZSA9PT0gJ29iamVjdCcpIHtcbiAgICAgICAgY29uc3Qgb2JqZWN0U2NoZW1hID0ganNvblNjaGVtYSBhcyBKc29uU2NoZW1hT2JqZWN0O1xuICAgICAgICBjb25zdCBwcm9wZXJ0aWVzID0gb2JqZWN0U2NoZW1hLnByb3BlcnRpZXMgfHwge307XG4gICAgICAgIGNvbnN0IHJlcXVpcmVkRmllbGRzID0gb2JqZWN0U2NoZW1hLnJlcXVpcmVkIHx8IFtdO1xuXG4gICAgICAgIGNvbnN0IHpvZFNjaGVtYUZpZWxkczogeyBba2V5OiBzdHJpbmddOiB6LlpvZFR5cGUgfSA9IHt9O1xuXG4gICAgICAgIC8vIOmBjeWOhuavj+S4quWxnuaAp1xuICAgICAgICBPYmplY3Qua2V5cyhwcm9wZXJ0aWVzKS5mb3JFYWNoKGtleSA9PiB7XG4gICAgICAgICAgICBjb25zdCBwcm9wID0gcHJvcGVydGllc1trZXldO1xuICAgICAgICAgICAgbGV0IHpvZFR5cGU6IHouWm9kVHlwZTtcblxuICAgICAgICAgICAgLy8g5qC55o2u57G75Z6L6L2s5o2i5Yiw5a+55bqU55qEIFpvZCDnsbvlnotcbiAgICAgICAgICAgIHN3aXRjaCAocHJvcC50eXBlKSB7XG4gICAgICAgICAgICAgICAgY2FzZSAnc3RyaW5nJzpcbiAgICAgICAgICAgICAgICAgICAgem9kVHlwZSA9IHouc3RyaW5nKCk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIGNhc2UgJ251bWJlcic6XG4gICAgICAgICAgICAgICAgICAgIHpvZFR5cGUgPSB6Lm51bWJlcigpO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICBjYXNlICdib29sZWFuJzpcbiAgICAgICAgICAgICAgICAgICAgem9kVHlwZSA9IHouYm9vbGVhbigpO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICBjYXNlICdhcnJheSc6XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGFycmF5U2NoZW1hID0gcHJvcCBhcyBKc29uU2NoZW1hQXJyYXk7XG4gICAgICAgICAgICAgICAgICAgIHpvZFR5cGUgPSB6LmFycmF5KGpzb25TY2hlbWFUb1pvZFNjaGVtYShhcnJheVNjaGVtYS5pdGVtcyB8fCB7IHR5cGU6ICdhbnknIH0pKTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgY2FzZSAnb2JqZWN0JzpcbiAgICAgICAgICAgICAgICAgICAgem9kVHlwZSA9IGpzb25TY2hlbWFUb1pvZFNjaGVtYShwcm9wKTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgICAgICAgICAgem9kVHlwZSA9IHouYW55KCk7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIGlmIChwcm9wLmRlc2NyaXB0aW9uKSB7XG4gICAgICAgICAgICAgICAgem9kVHlwZSA9IHpvZFR5cGUuZGVzY3JpYmUocHJvcC5kZXNjcmlwdGlvbik7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIGlmICghcmVxdWlyZWRGaWVsZHMuaW5jbHVkZXMoa2V5KSkge1xuICAgICAgICAgICAgICAgIHpvZFR5cGUgPSB6b2RUeXBlLm9wdGlvbmFsKCk7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIHpvZFNjaGVtYUZpZWxkc1trZXldID0gem9kVHlwZTtcbiAgICAgICAgfSk7XG5cbiAgICAgICAgY29uc3QgYWRkaXRpb25hbFByb3BlcnRpZXMgPSBvYmplY3RTY2hlbWEuYWRkaXRpb25hbFByb3BlcnRpZXMgPz8gdHJ1ZTtcblxuICAgICAgICByZXR1cm4gYWRkaXRpb25hbFByb3BlcnRpZXMgPyB6Lm9iamVjdCh6b2RTY2hlbWFGaWVsZHMpLnBhc3N0aHJvdWdoKCkgOiB6Lm9iamVjdCh6b2RTY2hlbWFGaWVsZHMpLnN0cmljdCgpO1xuICAgIH1cblxuICAgIHN3aXRjaCAoanNvblNjaGVtYS50eXBlKSB7XG4gICAgICAgIGNhc2UgJ3N0cmluZyc6XG4gICAgICAgICAgICByZXR1cm4gei5zdHJpbmcoKS5kZXNjcmliZShqc29uU2NoZW1hLmRlc2NyaXB0aW9uKTtcbiAgICAgICAgY2FzZSAnbnVtYmVyJzpcbiAgICAgICAgICAgIHJldHVybiB6Lm51bWJlcigpLmRlc2NyaWJlKGpzb25TY2hlbWEuZGVzY3JpcHRpb24pO1xuICAgICAgICBjYXNlICdib29sZWFuJzpcbiAgICAgICAgICAgIHJldHVybiB6LmJvb2xlYW4oKS5kZXNjcmliZShqc29uU2NoZW1hLmRlc2NyaXB0aW9uKTtcbiAgICAgICAgY2FzZSAnYXJyYXknOlxuICAgICAgICAgICAgY29uc3QgYXJyYXlTY2hlbWEgPSBqc29uU2NoZW1hIGFzIEpzb25TY2hlbWFBcnJheTtcbiAgICAgICAgICAgIHJldHVybiB6LmFycmF5KGpzb25TY2hlbWFUb1pvZFNjaGVtYShhcnJheVNjaGVtYS5pdGVtcyB8fCB7IHR5cGU6ICdhbnknIH0pKVxuICAgICAgICAgICAgICAgIC5kZXNjcmliZShhcnJheVNjaGVtYS5kZXNjcmlwdGlvbik7XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICByZXR1cm4gei5hbnkoKTtcbiAgICB9XG59Il0sIm5hbWVzIjpbInoiLCJqc29uU2NoZW1hVG9ab2RTY2hlbWEiLCJqc29uU2NoZW1hIiwidHlwZSIsIm9iamVjdFNjaGVtYSIsInByb3BlcnRpZXMiLCJyZXF1aXJlZEZpZWxkcyIsInJlcXVpcmVkIiwiem9kU2NoZW1hRmllbGRzIiwiT2JqZWN0Iiwia2V5cyIsImZvckVhY2giLCJrZXkiLCJwcm9wIiwiem9kVHlwZSIsInN0cmluZyIsIm51bWJlciIsImJvb2xlYW4iLCJhcnJheVNjaGVtYSIsImFycmF5IiwiaXRlbXMiLCJhbnkiLCJkZXNjcmlwdGlvbiIsImRlc2NyaWJlIiwiaW5jbHVkZXMiLCJvcHRpb25hbCIsImFkZGl0aW9uYWxQcm9wZXJ0aWVzIiwib2JqZWN0IiwicGFzc3Rocm91Z2giLCJzdHJpY3QiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/utils/json2zod.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/chat/utils/promptBuilder.ts":
/*!*************************************************!*\
  !*** ./src/app/api/chat/utils/promptBuilder.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buildMaxSystemPrompt: () => (/* binding */ buildMaxSystemPrompt),\n/* harmony export */   buildSystemPrompt: () => (/* binding */ buildSystemPrompt)\n/* harmony export */ });\n/* harmony import */ var _prompt__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../prompt */ \"(rsc)/./src/app/api/chat/prompt.ts\");\n\nfunction buildMaxSystemPrompt(filesPath, type, files, diffString, otherConfig) {\n    return `Current file directory tree: ${filesPath.join(\"\\n\")}\\n\\n,You can only modify the contents within the directory tree, requirements: ${(0,_prompt__WEBPACK_IMPORTED_MODULE_0__.getSystemPrompt)(type, otherConfig)}\nCurrent requirement file contents:\\n${JSON.stringify(files)}${diffString ? `,diff:\\n${diffString}` : \"\"}`;\n}\nfunction buildSystemPrompt(type, otherConfig) {\n    return `${(0,_prompt__WEBPACK_IMPORTED_MODULE_0__.getSystemPrompt)(type, otherConfig)}`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9jaGF0L3V0aWxzL3Byb21wdEJ1aWxkZXIudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWtFO0FBRTNELFNBQVNDLHFCQUNkQyxTQUFtQixFQUNuQkMsSUFBYyxFQUNkQyxLQUE2QixFQUM3QkMsVUFBa0IsRUFDbEJDLFdBQXVCO0lBRXZCLE9BQU8sQ0FBQyw2QkFBNkIsRUFBRUosVUFBVUssSUFBSSxDQUFDLE1BQU0sK0VBQStFLEVBQUVQLHdEQUFlQSxDQUFDRyxNQUFLRyxhQUFhO29DQUM3SSxFQUFFRSxLQUFLQyxTQUFTLENBQUNMLE9BQU8sRUFBRUMsYUFBYSxDQUFDLFFBQVEsRUFBRUEsV0FBVyxDQUFDLEdBQUcsR0FBRyxDQUFDO0FBQ3pHO0FBRU8sU0FBU0ssa0JBQ2RQLElBQWMsRUFDZEcsV0FBdUI7SUFFdkIsT0FBTyxDQUFDLEVBQUVOLHdEQUFlQSxDQUFDRyxNQUFLRyxhQUFhLENBQUM7QUFDL0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ad2UtZGV2L25leHQvLi9zcmMvYXBwL2FwaS9jaGF0L3V0aWxzL3Byb21wdEJ1aWxkZXIudHM/ZGE3YiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB0eXBlRW51bSwgZ2V0U3lzdGVtUHJvbXB0LHByb21wdEV4dHJhIH0gZnJvbSBcIi4uL3Byb21wdFwiO1xuXG5leHBvcnQgZnVuY3Rpb24gYnVpbGRNYXhTeXN0ZW1Qcm9tcHQoXG4gIGZpbGVzUGF0aDogc3RyaW5nW10sIFxuICB0eXBlOiB0eXBlRW51bSwgXG4gIGZpbGVzOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+LCBcbiAgZGlmZlN0cmluZzogc3RyaW5nLFxuICBvdGhlckNvbmZpZzpwcm9tcHRFeHRyYVxuKTogc3RyaW5nIHtcbiAgcmV0dXJuIGBDdXJyZW50IGZpbGUgZGlyZWN0b3J5IHRyZWU6ICR7ZmlsZXNQYXRoLmpvaW4oXCJcXG5cIil9XFxuXFxuLFlvdSBjYW4gb25seSBtb2RpZnkgdGhlIGNvbnRlbnRzIHdpdGhpbiB0aGUgZGlyZWN0b3J5IHRyZWUsIHJlcXVpcmVtZW50czogJHtnZXRTeXN0ZW1Qcm9tcHQodHlwZSxvdGhlckNvbmZpZyl9XG5DdXJyZW50IHJlcXVpcmVtZW50IGZpbGUgY29udGVudHM6XFxuJHtKU09OLnN0cmluZ2lmeShmaWxlcyl9JHtkaWZmU3RyaW5nID8gYCxkaWZmOlxcbiR7ZGlmZlN0cmluZ31gIDogXCJcIn1gO1xufSBcblxuZXhwb3J0IGZ1bmN0aW9uIGJ1aWxkU3lzdGVtUHJvbXB0KFxuICB0eXBlOiB0eXBlRW51bSwgXG4gIG90aGVyQ29uZmlnOnByb21wdEV4dHJhXG4pOiBzdHJpbmcge1xuICByZXR1cm4gYCR7Z2V0U3lzdGVtUHJvbXB0KHR5cGUsb3RoZXJDb25maWcpfWA7XG59ICJdLCJuYW1lcyI6WyJnZXRTeXN0ZW1Qcm9tcHQiLCJidWlsZE1heFN5c3RlbVByb21wdCIsImZpbGVzUGF0aCIsInR5cGUiLCJmaWxlcyIsImRpZmZTdHJpbmciLCJvdGhlckNvbmZpZyIsImpvaW4iLCJKU09OIiwic3RyaW5naWZ5IiwiYnVpbGRTeXN0ZW1Qcm9tcHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/utils/promptBuilder.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/chat/utils/screenshotone.ts":
/*!*************************************************!*\
  !*** ./src/app/api/chat/utils/screenshotone.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bytesToDataUrl: () => (/* binding */ bytesToDataUrl),\n/* harmony export */   screenshotOne: () => (/* binding */ screenshotOne)\n/* harmony export */ });\nconst bytesToDataUrl = (bytes, mimeType)=>{\n    const binary = bytes.toString(\"base64\");\n    return `data:${mimeType};base64,${binary}`;\n};\nconst screenshotOne = async (targetUrl)=>{\n    const apiKey = process.env.SCREENSHOTONE_API_KEY;\n    const apiBaseUrl = \"https://api.screenshotone.com/take\";\n    const params = {\n        \"access_key\": apiKey,\n        \"url\": targetUrl,\n        \"full_page\": \"true\",\n        \"device_scale_factor\": \"1\",\n        \"format\": \"png\",\n        \"block_ads\": \"true\",\n        \"block_cookie_banners\": \"true\",\n        \"block_trackers\": \"true\",\n        \"cache\": \"false\",\n        \"viewport_width\": \"1280\",\n        \"viewport_height\": \"832\"\n    };\n    // 构建查询字符串\n    const searchParams = new URLSearchParams(params);\n    const url = `${apiBaseUrl}?${searchParams.toString()}`;\n    try {\n        const response = await fetch(url, {\n            method: \"GET\",\n            headers: {\n                \"Accept\": \"image/png\"\n            },\n            signal: AbortSignal.timeout(60000)\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const arrayBuffer = await response.arrayBuffer();\n        const base64 = Buffer.from(arrayBuffer).toString(\"base64\");\n        return `data:image/png;base64,${base64}`;\n    } catch (error) {\n        console.error(\"Screenshot capture failed:\", error);\n        throw new Error(\"Error taking screenshot\");\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/utils/screenshotone.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/chat/utils/streamResponse.ts":
/*!**************************************************!*\
  !*** ./src/app/api/chat/utils/streamResponse.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   streamResponse: () => (/* binding */ streamResponse)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/.pnpm/uuid@11.0.5/node_modules/uuid/dist/esm/v4.js\");\n/* harmony import */ var _action__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../action */ \"(rsc)/./src/app/api/chat/action.ts\");\n/* harmony import */ var _prompt__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../prompt */ \"(rsc)/./src/app/api/chat/prompt.ts\");\n/* harmony import */ var _utils_tokens__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/tokens */ \"(rsc)/./src/utils/tokens.ts\");\n/* harmony import */ var _switchable_stream__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../switchable-stream */ \"(rsc)/./src/app/api/chat/switchable-stream.ts\");\n/* harmony import */ var ai__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ai */ \"(rsc)/./node_modules/.pnpm/ai@4.3.17_react@18.3.1_zod@3.24.1/node_modules/ai/dist/index.mjs\");\n/* harmony import */ var _app_api_chat_utils_json2zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/api/chat/utils/json2zod */ \"(rsc)/./src/app/api/chat/utils/json2zod.ts\");\n\n\n\n\n\n\n\nconst MAX_RESPONSE_SEGMENTS = 2;\nasync function streamResponse(messages, model, userId, tools) {\n    let toolList = {};\n    if (tools && tools.length > 0) {\n        toolList = tools.reduce((obj, { name, ...args })=>{\n            obj[name] = (0,ai__WEBPACK_IMPORTED_MODULE_5__.tool)({\n                id: args.id,\n                description: args.description,\n                parameters: (0,_app_api_chat_utils_json2zod__WEBPACK_IMPORTED_MODULE_4__.jsonSchemaToZodSchema)(args.parameters)\n            });\n            return obj;\n        }, {});\n    }\n    const stream = new _switchable_stream__WEBPACK_IMPORTED_MODULE_3__[\"default\"]();\n    const options = {\n        tools: toolList,\n        toolCallStreaming: true,\n        onError: (err)=>{\n            // 获取错误信息，优先使用 cause 属性\n            const errorCause = err?.cause?.message || err?.cause || err?.error?.message;\n            const msg = errorCause || err?.errors?.[0]?.responseBody || JSON.stringify(err);\n            if (msg) {\n                throw new Error(msg);\n            }\n            const error = new Error(msg || JSON.stringify(err));\n            error.cause = msg; // 保存原始错误信息到 cause\n            throw error;\n        },\n        onFinish: async (response)=>{\n            const { text: content, finishReason } = response;\n            if (finishReason !== \"length\") {\n                const tokens = (0,_utils_tokens__WEBPACK_IMPORTED_MODULE_2__.estimateTokens)(content);\n                if (userId) {\n                    await (0,_utils_tokens__WEBPACK_IMPORTED_MODULE_2__.deductUserTokens)(userId, tokens);\n                }\n                return stream.close();\n            }\n            if (stream.switches >= MAX_RESPONSE_SEGMENTS) {\n                throw Error(\"Cannot continue message: Maximum segments reached\");\n            }\n            messages.push({\n                id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n                role: \"assistant\",\n                content\n            });\n            messages.push({\n                id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n                role: \"user\",\n                content: _prompt__WEBPACK_IMPORTED_MODULE_1__.CONTINUE_PROMPT\n            });\n        }\n    };\n    try {\n        const result = (0,_action__WEBPACK_IMPORTED_MODULE_0__.streamTextFn)(messages, options, model);\n        return result.toDataStreamResponse({\n            sendReasoning: true\n        });\n    } catch (error) {\n        // 确保流被关闭\n        stream.close();\n        // 如果错误中包含 cause，将其作为新错误抛出\n        if (error.cause) {\n            const newError = new Error(error.cause);\n            newError.cause = error.cause;\n            throw newError;\n        }\n        stream.close();\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9jaGF0L3V0aWxzL3N0cmVhbVJlc3BvbnNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQWtDO0FBQ2lDO0FBQ2Y7QUFDWTtBQUNaO0FBQzVCO0FBQzRDO0FBRXBFLE1BQU1TLHdCQUF3QjtBQUV2QixlQUFlQyxlQUNsQkMsUUFBa0IsRUFDbEJDLEtBQWEsRUFDYkMsTUFBcUIsRUFDckJDLEtBQWtCO0lBRWxCLElBQUlDLFdBQVcsQ0FBQztJQUNoQixJQUFJRCxTQUFTQSxNQUFNRSxNQUFNLEdBQUcsR0FBRztRQUMzQkQsV0FBV0QsTUFBTUcsTUFBTSxDQUFDLENBQUNDLEtBQUssRUFBQ0MsSUFBSSxFQUFFLEdBQUdDLE1BQUs7WUFDekNGLEdBQUcsQ0FBQ0MsS0FBSyxHQUFHWix3Q0FBSUEsQ0FBQztnQkFDYmMsSUFBSUQsS0FBS0MsRUFBRTtnQkFDWEMsYUFBYUYsS0FBS0UsV0FBVztnQkFDN0JDLFlBQWFmLG1GQUFxQkEsQ0FBQ1ksS0FBS0csVUFBVTtZQUN0RDtZQUNBLE9BQU9MO1FBQ1gsR0FBRyxDQUFDO0lBQ1I7SUFDQSxNQUFNTSxTQUFTLElBQUlsQiwwREFBZ0JBO0lBQ25DLE1BQU1tQixVQUE0QjtRQUM5QlgsT0FBT0M7UUFDUFcsbUJBQW1CO1FBQ25CQyxTQUFTLENBQUNDO1lBQ04sdUJBQXVCO1lBQ3ZCLE1BQU1DLGFBQWFELEtBQUtFLE9BQU9DLFdBQVdILEtBQUtFLFNBQVNGLEtBQUtJLE9BQU9EO1lBQ3BFLE1BQU1FLE1BQU1KLGNBQWNELEtBQUtNLFFBQVEsQ0FBQyxFQUFFLEVBQUVDLGdCQUFnQkMsS0FBS0MsU0FBUyxDQUFDVDtZQUUzRSxJQUFJSyxLQUFLO2dCQUNMLE1BQU0sSUFBSUssTUFBTUw7WUFDcEI7WUFDQSxNQUFNRCxRQUFRLElBQUlNLE1BQU1MLE9BQU9HLEtBQUtDLFNBQVMsQ0FBQ1Q7WUFDOUNJLE1BQU1GLEtBQUssR0FBR0csS0FBSyxrQkFBa0I7WUFDckMsTUFBTUQ7UUFDVjtRQUNBTyxVQUFVLE9BQU9DO1lBQ2IsTUFBTSxFQUFDQyxNQUFNQyxPQUFPLEVBQUVDLFlBQVksRUFBQyxHQUFHSDtZQUV0QyxJQUFJRyxpQkFBaUIsVUFBVTtnQkFDM0IsTUFBTUMsU0FBU3ZDLDZEQUFjQSxDQUFDcUM7Z0JBQzlCLElBQUk3QixRQUFRO29CQUNSLE1BQU1ULCtEQUFnQkEsQ0FBQ1MsUUFBUStCO2dCQUNuQztnQkFDQSxPQUFPcEIsT0FBT3FCLEtBQUs7WUFDdkI7WUFFQSxJQUFJckIsT0FBT3NCLFFBQVEsSUFBSXJDLHVCQUF1QjtnQkFDMUMsTUFBTTZCLE1BQU07WUFDaEI7WUFFQTNCLFNBQVNvQyxJQUFJLENBQUM7Z0JBQUMxQixJQUFJcEIsZ0RBQU1BO2dCQUFJK0MsTUFBTTtnQkFBYU47WUFBTztZQUN2RC9CLFNBQVNvQyxJQUFJLENBQUM7Z0JBQUMxQixJQUFJcEIsZ0RBQU1BO2dCQUFJK0MsTUFBTTtnQkFBUU4sU0FBU3ZDLG9EQUFlQTtZQUFBO1FBQ3ZFO0lBQ0o7SUFFQSxJQUFJO1FBQ0EsTUFBTThDLFNBQVMvQyxxREFBWUEsQ0FBQ1MsVUFBVWMsU0FBU2I7UUFDL0MsT0FBT3FDLE9BQU9DLG9CQUFvQixDQUFDO1lBQy9CQyxlQUFlO1FBQ25CO0lBQ0osRUFBRSxPQUFPbkIsT0FBWTtRQUNqQixTQUFTO1FBQ1RSLE9BQU9xQixLQUFLO1FBQ1osMEJBQTBCO1FBQzFCLElBQUliLE1BQU1GLEtBQUssRUFBRTtZQUNiLE1BQU1zQixXQUFXLElBQUlkLE1BQU1OLE1BQU1GLEtBQUs7WUFDdENzQixTQUFTdEIsS0FBSyxHQUFHRSxNQUFNRixLQUFLO1lBQzVCLE1BQU1zQjtRQUNWO1FBQ0E1QixPQUFPcUIsS0FBSztRQUNaLE1BQU1iO0lBQ1Y7QUFDSiIsInNvdXJjZXMiOlsid2VicGFjazovL0B3ZS1kZXYvbmV4dC8uL3NyYy9hcHAvYXBpL2NoYXQvdXRpbHMvc3RyZWFtUmVzcG9uc2UudHM/ZjEyZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge3Y0IGFzIHV1aWR2NH0gZnJvbSBcInV1aWRcIjtcbmltcG9ydCB7TWVzc2FnZXMsIFN0cmVhbWluZ09wdGlvbnMsIHN0cmVhbVRleHRGbn0gZnJvbSBcIi4uL2FjdGlvblwiO1xuaW1wb3J0IHtDT05USU5VRV9QUk9NUFQsIFRvb2xJbmZvfSBmcm9tIFwiLi4vcHJvbXB0XCI7XG5pbXBvcnQge2RlZHVjdFVzZXJUb2tlbnMsIGVzdGltYXRlVG9rZW5zfSBmcm9tIFwiQC91dGlscy90b2tlbnNcIjtcbmltcG9ydCBTd2l0Y2hhYmxlU3RyZWFtIGZyb20gXCIuLi9zd2l0Y2hhYmxlLXN0cmVhbVwiO1xuaW1wb3J0IHt0b29sfSBmcm9tIFwiYWlcIjtcbmltcG9ydCB7anNvblNjaGVtYVRvWm9kU2NoZW1hfSBmcm9tIFwiQC9hcHAvYXBpL2NoYXQvdXRpbHMvanNvbjJ6b2RcIjtcblxuY29uc3QgTUFYX1JFU1BPTlNFX1NFR01FTlRTID0gMjtcblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHN0cmVhbVJlc3BvbnNlKFxuICAgIG1lc3NhZ2VzOiBNZXNzYWdlcyxcbiAgICBtb2RlbDogc3RyaW5nLFxuICAgIHVzZXJJZDogc3RyaW5nIHwgbnVsbCxcbiAgICB0b29scz86IFRvb2xJbmZvW11cbik6IFByb21pc2U8UmVzcG9uc2U+IHtcbiAgICBsZXQgdG9vbExpc3QgPSB7fTtcbiAgICBpZiAodG9vbHMgJiYgdG9vbHMubGVuZ3RoID4gMCkge1xuICAgICAgICB0b29sTGlzdCA9IHRvb2xzLnJlZHVjZSgob2JqLCB7bmFtZSwgLi4uYXJnc30pID0+IHtcbiAgICAgICAgICAgIG9ialtuYW1lXSA9IHRvb2woe1xuICAgICAgICAgICAgICAgIGlkOiBhcmdzLmlkLFxuICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBhcmdzLmRlc2NyaXB0aW9uLFxuICAgICAgICAgICAgICAgIHBhcmFtZXRlcnM6ICBqc29uU2NoZW1hVG9ab2RTY2hlbWEoYXJncy5wYXJhbWV0ZXJzKVxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICByZXR1cm4gb2JqO1xuICAgICAgICB9LCB7fSk7XG4gICAgfVxuICAgIGNvbnN0IHN0cmVhbSA9IG5ldyBTd2l0Y2hhYmxlU3RyZWFtKCk7XG4gICAgY29uc3Qgb3B0aW9uczogU3RyZWFtaW5nT3B0aW9ucyA9IHtcbiAgICAgICAgdG9vbHM6IHRvb2xMaXN0LFxuICAgICAgICB0b29sQ2FsbFN0cmVhbWluZzogdHJ1ZSxcbiAgICAgICAgb25FcnJvcjogKGVycjogYW55KSA9PiB7XG4gICAgICAgICAgICAvLyDojrflj5bplJnor6/kv6Hmga/vvIzkvJjlhYjkvb/nlKggY2F1c2Ug5bGe5oCnXG4gICAgICAgICAgICBjb25zdCBlcnJvckNhdXNlID0gZXJyPy5jYXVzZT8ubWVzc2FnZSB8fCBlcnI/LmNhdXNlIHx8IGVycj8uZXJyb3I/Lm1lc3NhZ2VcbiAgICAgICAgICAgIGNvbnN0IG1zZyA9IGVycm9yQ2F1c2UgfHwgZXJyPy5lcnJvcnM/LlswXT8ucmVzcG9uc2VCb2R5IHx8IEpTT04uc3RyaW5naWZ5KGVycik7XG5cbiAgICAgICAgICAgIGlmIChtc2cpIHtcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IobXNnKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IGVycm9yID0gbmV3IEVycm9yKG1zZyB8fCBKU09OLnN0cmluZ2lmeShlcnIpKTtcbiAgICAgICAgICAgIGVycm9yLmNhdXNlID0gbXNnOyAvLyDkv53lrZjljp/lp4vplJnor6/kv6Hmga/liLAgY2F1c2VcbiAgICAgICAgICAgIHRocm93IGVycm9yO1xuICAgICAgICB9LFxuICAgICAgICBvbkZpbmlzaDogYXN5bmMgKHJlc3BvbnNlKSA9PiB7XG4gICAgICAgICAgICBjb25zdCB7dGV4dDogY29udGVudCwgZmluaXNoUmVhc29ufSA9IHJlc3BvbnNlO1xuXG4gICAgICAgICAgICBpZiAoZmluaXNoUmVhc29uICE9PSBcImxlbmd0aFwiKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgdG9rZW5zID0gZXN0aW1hdGVUb2tlbnMoY29udGVudCk7XG4gICAgICAgICAgICAgICAgaWYgKHVzZXJJZCkge1xuICAgICAgICAgICAgICAgICAgICBhd2FpdCBkZWR1Y3RVc2VyVG9rZW5zKHVzZXJJZCwgdG9rZW5zKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgcmV0dXJuIHN0cmVhbS5jbG9zZSgpO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICBpZiAoc3RyZWFtLnN3aXRjaGVzID49IE1BWF9SRVNQT05TRV9TRUdNRU5UUykge1xuICAgICAgICAgICAgICAgIHRocm93IEVycm9yKFwiQ2Fubm90IGNvbnRpbnVlIG1lc3NhZ2U6IE1heGltdW0gc2VnbWVudHMgcmVhY2hlZFwiKTtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgbWVzc2FnZXMucHVzaCh7aWQ6IHV1aWR2NCgpLCByb2xlOiBcImFzc2lzdGFudFwiLCBjb250ZW50fSk7XG4gICAgICAgICAgICBtZXNzYWdlcy5wdXNoKHtpZDogdXVpZHY0KCksIHJvbGU6IFwidXNlclwiLCBjb250ZW50OiBDT05USU5VRV9QUk9NUFR9KTtcbiAgICAgICAgfSxcbiAgICB9O1xuXG4gICAgdHJ5IHtcbiAgICAgICAgY29uc3QgcmVzdWx0ID0gc3RyZWFtVGV4dEZuKG1lc3NhZ2VzLCBvcHRpb25zLCBtb2RlbCk7XG4gICAgICAgIHJldHVybiByZXN1bHQudG9EYXRhU3RyZWFtUmVzcG9uc2Uoe1xuICAgICAgICAgICAgc2VuZFJlYXNvbmluZzogdHJ1ZSxcbiAgICAgICAgfSk7XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgICAvLyDnoa7kv53mtYHooqvlhbPpl61cbiAgICAgICAgc3RyZWFtLmNsb3NlKCk7XG4gICAgICAgIC8vIOWmguaenOmUmeivr+S4reWMheWQqyBjYXVzZe+8jOWwhuWFtuS9nOS4uuaWsOmUmeivr+aKm+WHulxuICAgICAgICBpZiAoZXJyb3IuY2F1c2UpIHtcbiAgICAgICAgICAgIGNvbnN0IG5ld0Vycm9yID0gbmV3IEVycm9yKGVycm9yLmNhdXNlKTtcbiAgICAgICAgICAgIG5ld0Vycm9yLmNhdXNlID0gZXJyb3IuY2F1c2U7XG4gICAgICAgICAgICB0aHJvdyBuZXdFcnJvcjtcbiAgICAgICAgfVxuICAgICAgICBzdHJlYW0uY2xvc2UoKTtcbiAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxufSJdLCJuYW1lcyI6WyJ2NCIsInV1aWR2NCIsInN0cmVhbVRleHRGbiIsIkNPTlRJTlVFX1BST01QVCIsImRlZHVjdFVzZXJUb2tlbnMiLCJlc3RpbWF0ZVRva2VucyIsIlN3aXRjaGFibGVTdHJlYW0iLCJ0b29sIiwianNvblNjaGVtYVRvWm9kU2NoZW1hIiwiTUFYX1JFU1BPTlNFX1NFR01FTlRTIiwic3RyZWFtUmVzcG9uc2UiLCJtZXNzYWdlcyIsIm1vZGVsIiwidXNlcklkIiwidG9vbHMiLCJ0b29sTGlzdCIsImxlbmd0aCIsInJlZHVjZSIsIm9iaiIsIm5hbWUiLCJhcmdzIiwiaWQiLCJkZXNjcmlwdGlvbiIsInBhcmFtZXRlcnMiLCJzdHJlYW0iLCJvcHRpb25zIiwidG9vbENhbGxTdHJlYW1pbmciLCJvbkVycm9yIiwiZXJyIiwiZXJyb3JDYXVzZSIsImNhdXNlIiwibWVzc2FnZSIsImVycm9yIiwibXNnIiwiZXJyb3JzIiwicmVzcG9uc2VCb2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsIkVycm9yIiwib25GaW5pc2giLCJyZXNwb25zZSIsInRleHQiLCJjb250ZW50IiwiZmluaXNoUmVhc29uIiwidG9rZW5zIiwiY2xvc2UiLCJzd2l0Y2hlcyIsInB1c2giLCJyb2xlIiwicmVzdWx0IiwidG9EYXRhU3RyZWFtUmVzcG9uc2UiLCJzZW5kUmVhc29uaW5nIiwibmV3RXJyb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/utils/streamResponse.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/chat/utils/tokenHandler.ts":
/*!************************************************!*\
  !*** ./src/app/api/chat/utils/tokenHandler.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handleTokenLimit: () => (/* binding */ handleTokenLimit)\n/* harmony export */ });\n/* harmony import */ var _action__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../action */ \"(rsc)/./src/app/api/chat/action.ts\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/.pnpm/uuid@11.0.5/node_modules/uuid/dist/esm/v4.js\");\n\n\nasync function handleTokenLimit(messages, files, filesPath) {\n    const fileMessage = JSON.parse(JSON.stringify(messages));\n    const nowFiles = {};\n    fileMessage.push({\n        id: (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(),\n        role: \"user\",\n        content: `当前文件目录树:\\n${filesPath.join(\"\\n\")}\\n\\n。假如用户需求需要修改文件，请按照文件目录树的格式输出文件路径。比如['src/index.js','src/components/index.js','package.json']这样的形式，按需求的相关度提取文件路径。不需要全部路径输出，只输出用户需求相关的文件路径。`\n    });\n    const objectResult = await (0,_action__WEBPACK_IMPORTED_MODULE_0__.generateObjectFn)(fileMessage);\n    const nowPathFiles = objectResult.object.files;\n    filesPath.forEach((path)=>{\n        if (nowPathFiles.includes(path)) {\n            nowFiles[path] = files[path];\n        }\n    });\n    return Object.keys(nowFiles).length > 0 ? nowFiles : files;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/utils/tokenHandler.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/model/config.ts":
/*!*************************************!*\
  !*** ./src/app/api/model/config.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   modelConfig: () => (/* binding */ modelConfig)\n/* harmony export */ });\n// Model configuration file\n// Configure models based on actual scenarios\nconst modelConfig = [\n    {\n        modelName: \"Claude 3.5 Sonnet\",\n        modelKey: \"anthropic/claude-3.5-sonnet\",\n        useImage: true,\n        provider: \"anthropic\",\n        description: \"Claude 3.5 Sonnet model via OpenRouter\",\n        functionCall: true\n    },\n    {\n        modelName: \"GPT-4o Mini\",\n        modelKey: \"openai/gpt-4o-mini\",\n        useImage: true,\n        provider: \"openai\",\n        description: \"GPT-4 Optimized Mini model via OpenRouter\",\n        functionCall: true\n    },\n    {\n        modelName: \"DeepSeek R1\",\n        modelKey: \"deepseek/deepseek-r1\",\n        useImage: false,\n        provider: \"deepseek\",\n        description: \"DeepSeek R1 model with reasoning capabilities via OpenRouter\",\n        functionCall: false\n    },\n    {\n        modelName: \"DeepSeek V3\",\n        modelKey: \"deepseek/deepseek-chat\",\n        useImage: false,\n        provider: \"deepseek\",\n        description: \"DeepSeek V3 model via OpenRouter\",\n        functionCall: true\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/model/config.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/markdown.ts":
/*!*******************************!*\
  !*** ./src/utils/markdown.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   allowedHTMLElements: () => (/* binding */ allowedHTMLElements)\n/* harmony export */ });\nconst allowedHTMLElements = [\n    \"a\",\n    \"b\",\n    \"blockquote\",\n    \"br\",\n    \"code\",\n    \"dd\",\n    \"del\",\n    \"details\",\n    \"div\",\n    \"dl\",\n    \"dt\",\n    \"em\",\n    \"h1\",\n    \"h2\",\n    \"h3\",\n    \"h4\",\n    \"h5\",\n    \"h6\",\n    \"hr\",\n    \"i\",\n    \"ins\",\n    \"kbd\",\n    \"li\",\n    \"ol\",\n    \"p\",\n    \"pre\",\n    \"q\",\n    \"rp\",\n    \"rt\",\n    \"ruby\",\n    \"s\",\n    \"samp\",\n    \"source\",\n    \"span\",\n    \"strike\",\n    \"strong\",\n    \"sub\",\n    \"summary\",\n    \"sup\",\n    \"table\",\n    \"tbody\",\n    \"td\",\n    \"tfoot\",\n    \"th\",\n    \"thead\",\n    \"tr\",\n    \"ul\",\n    \"var\"\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvdXRpbHMvbWFya2Rvd24udHMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPLE1BQU1BLHNCQUFzQjtJQUNqQztJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7Q0FDRCxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHdlLWRldi9uZXh0Ly4vc3JjL3V0aWxzL21hcmtkb3duLnRzP2Q0OWEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IGFsbG93ZWRIVE1MRWxlbWVudHMgPSBbXG4gICdhJyxcbiAgJ2InLFxuICAnYmxvY2txdW90ZScsXG4gICdicicsXG4gICdjb2RlJyxcbiAgJ2RkJyxcbiAgJ2RlbCcsXG4gICdkZXRhaWxzJyxcbiAgJ2RpdicsXG4gICdkbCcsXG4gICdkdCcsXG4gICdlbScsXG4gICdoMScsXG4gICdoMicsXG4gICdoMycsXG4gICdoNCcsXG4gICdoNScsXG4gICdoNicsXG4gICdocicsXG4gICdpJyxcbiAgJ2lucycsXG4gICdrYmQnLFxuICAnbGknLFxuICAnb2wnLFxuICAncCcsXG4gICdwcmUnLFxuICAncScsXG4gICdycCcsXG4gICdydCcsXG4gICdydWJ5JyxcbiAgJ3MnLFxuICAnc2FtcCcsXG4gICdzb3VyY2UnLFxuICAnc3BhbicsXG4gICdzdHJpa2UnLFxuICAnc3Ryb25nJyxcbiAgJ3N1YicsXG4gICdzdW1tYXJ5JyxcbiAgJ3N1cCcsXG4gICd0YWJsZScsXG4gICd0Ym9keScsXG4gICd0ZCcsXG4gICd0Zm9vdCcsXG4gICd0aCcsXG4gICd0aGVhZCcsXG4gICd0cicsXG4gICd1bCcsXG4gICd2YXInLFxuXTsiXSwibmFtZXMiOlsiYWxsb3dlZEhUTUxFbGVtZW50cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/markdown.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/stripIndent.ts":
/*!**********************************!*\
  !*** ./src/utils/stripIndent.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   stripIndents: () => (/* binding */ stripIndents)\n/* harmony export */ });\nfunction stripIndents(arg0, ...values) {\n    if (typeof arg0 !== \"string\") {\n        const processedString = arg0.reduce((acc, curr, i)=>{\n            acc += curr + (values[i] ?? \"\");\n            return acc;\n        }, \"\");\n        return _stripIndents(processedString);\n    }\n    return _stripIndents(arg0);\n}\nfunction _stripIndents(value) {\n    return value.split(\"\\n\").map((line)=>line.trim()).join(\"\\n\").trimStart().replace(/[\\r\\n]$/, \"\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvdXRpbHMvc3RyaXBJbmRlbnQudHMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPLFNBQVNBLGFBQ2RDLElBQW1DLEVBQ25DLEdBQUdDLE1BQWE7SUFFaEIsSUFBSSxPQUFPRCxTQUFTLFVBQVU7UUFDNUIsTUFBTUUsa0JBQWtCRixLQUFLRyxNQUFNLENBQUMsQ0FBQ0MsS0FBS0MsTUFBTUM7WUFDOUNGLE9BQU9DLE9BQVFKLENBQUFBLE1BQU0sQ0FBQ0ssRUFBRSxJQUFJLEVBQUM7WUFDN0IsT0FBT0Y7UUFDVCxHQUFHO1FBRUgsT0FBT0csY0FBY0w7SUFDdkI7SUFFQSxPQUFPSyxjQUFjUDtBQUN2QjtBQUNBLFNBQVNPLGNBQWNDLEtBQWE7SUFDbEMsT0FBT0EsTUFDSkMsS0FBSyxDQUFDLE1BQ05DLEdBQUcsQ0FBQyxDQUFDQyxPQUFTQSxLQUFLQyxJQUFJLElBQ3ZCQyxJQUFJLENBQUMsTUFDTEMsU0FBUyxHQUNUQyxPQUFPLENBQUMsV0FBVztBQUN4QiIsInNvdXJjZXMiOlsid2VicGFjazovL0B3ZS1kZXYvbmV4dC8uL3NyYy91dGlscy9zdHJpcEluZGVudC50cz9lZDY5Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBzdHJpcEluZGVudHMoXG4gIGFyZzA6IHN0cmluZyB8IFRlbXBsYXRlU3RyaW5nc0FycmF5LFxuICAuLi52YWx1ZXM6IGFueVtdXG4pIHtcbiAgaWYgKHR5cGVvZiBhcmcwICE9PSBcInN0cmluZ1wiKSB7XG4gICAgY29uc3QgcHJvY2Vzc2VkU3RyaW5nID0gYXJnMC5yZWR1Y2UoKGFjYywgY3VyciwgaSkgPT4ge1xuICAgICAgYWNjICs9IGN1cnIgKyAodmFsdWVzW2ldID8/IFwiXCIpO1xuICAgICAgcmV0dXJuIGFjYztcbiAgICB9LCBcIlwiKTtcblxuICAgIHJldHVybiBfc3RyaXBJbmRlbnRzKHByb2Nlc3NlZFN0cmluZyk7XG4gIH1cblxuICByZXR1cm4gX3N0cmlwSW5kZW50cyhhcmcwKTtcbn1cbmZ1bmN0aW9uIF9zdHJpcEluZGVudHModmFsdWU6IHN0cmluZykge1xuICByZXR1cm4gdmFsdWVcbiAgICAuc3BsaXQoXCJcXG5cIilcbiAgICAubWFwKChsaW5lKSA9PiBsaW5lLnRyaW0oKSlcbiAgICAuam9pbihcIlxcblwiKVxuICAgIC50cmltU3RhcnQoKVxuICAgIC5yZXBsYWNlKC9bXFxyXFxuXSQvLCBcIlwiKTtcbn1cbiJdLCJuYW1lcyI6WyJzdHJpcEluZGVudHMiLCJhcmcwIiwidmFsdWVzIiwicHJvY2Vzc2VkU3RyaW5nIiwicmVkdWNlIiwiYWNjIiwiY3VyciIsImkiLCJfc3RyaXBJbmRlbnRzIiwidmFsdWUiLCJzcGxpdCIsIm1hcCIsImxpbmUiLCJ0cmltIiwiam9pbiIsInRyaW1TdGFydCIsInJlcGxhY2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/stripIndent.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/tokens.ts":
/*!*****************************!*\
  !*** ./src/utils/tokens.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deductUserTokens: () => (/* binding */ deductUserTokens),\n/* harmony export */   estimateTokens: () => (/* binding */ estimateTokens),\n/* harmony export */   hasEnoughTokens: () => (/* binding */ hasEnoughTokens)\n/* harmony export */ });\nfunction estimateTokens(text) {\n    // Basic rules\n    // 1. English: approximately 4 characters per token\n    // 2. Chinese: approximately 1-2 characters per token\n    // 3. Spaces and punctuation: approximately 1 token\n    const chineseRegex = /[\\u4e00-\\u9fff]/g;\n    const punctuationRegex = /[.,!?;:，。！？；：]/g;\n    const whitespaceRegex = /\\s+/g;\n    // Count Chinese characters\n    const chineseChars = (text.match(chineseRegex) || []).length;\n    // Count punctuation marks\n    const punctuationCount = (text.match(punctuationRegex) || []).length;\n    // Count whitespace\n    const whitespaceCount = (text.match(whitespaceRegex) || []).length;\n    // Count remaining characters (mainly English)\n    const otherChars = text.length - chineseChars - punctuationCount - whitespaceCount;\n    // Estimate token count\n    const tokenEstimate = Math.ceil(chineseChars * 1.5 + // Chinese characters\n    otherChars / 4 + // English characters\n    punctuationCount + // Punctuation marks\n    whitespaceCount // Whitespace\n    );\n    return tokenEstimate;\n}\n/**\n * Usage example: Deduct user tokens\n * @param userId User ID\n * @param tokensToDeduct Number of tokens to deduct\n */ async function deductUserTokens(userId, tokensToDeduct) {\n    // Get current year and month (YYYY-MM format)\n    const currentMonth = new Date().toISOString().slice(0, 7);\n}\n/**\n * Check if user has enough tokens\n * @param userId User ID\n * @returns boolean\n */ async function hasEnoughTokens(userId) {\n    return true;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/tokens.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1","vendor-chunks/@opentelemetry+api@1.9.0","vendor-chunks/zod@3.24.1","vendor-chunks/ai@4.3.17_react@18.3.1_zod@3.24.1","vendor-chunks/zod-to-json-schema@3.24.1_zod@3.24.1","vendor-chunks/@ai-sdk+ui-utils@1.2.11_zod@3.24.1","vendor-chunks/@ai-sdk+provider-utils@2.2.8_zod@3.24.1","vendor-chunks/@ai-sdk+provider@1.1.3","vendor-chunks/secure-json-parse@2.7.0","vendor-chunks/uuid@11.0.5","vendor-chunks/nanoid@3.3.8","vendor-chunks/eventsource-parser@3.0.0","vendor-chunks/@ai-sdk+provider@1.0.7","vendor-chunks/@ai-sdk+provider@1.0.4","vendor-chunks/@ai-sdk+provider-utils@2.1.8_zod@3.24.1","vendor-chunks/@ai-sdk+provider-utils@2.0.7_zod@3.24.1","vendor-chunks/@ai-sdk+openai@1.0.19_zod@3.24.1","vendor-chunks/@ai-sdk+openai-compatible@0.1.10_zod@3.24.1","vendor-chunks/@ai-sdk+deepseek@0.1.10_zod@3.24.1"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=E%3A%5CWorkspace%5Cprogramming%5Cwe0-main%5Capps%5Cwe-dev-next%5Csrc%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=E%3A%5CWorkspace%5Cprogramming%5Cwe0-main%5Capps%5Cwe-dev-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();