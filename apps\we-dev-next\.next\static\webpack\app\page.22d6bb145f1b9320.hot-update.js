"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/utils/messageParserNew.ts":
/*!***************************************!*\
  !*** ./src/utils/messageParserNew.ts ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StreamingMessageParser: function() { return /* binding */ StreamingMessageParser; },\n/* harmony export */   debugFileCreation: function() { return /* binding */ debugFileCreation; },\n/* harmony export */   parseMessages: function() { return /* binding */ parseMessages; },\n/* harmony export */   testWorkingFormat: function() { return /* binding */ testWorkingFormat; }\n/* harmony export */ });\n/* harmony import */ var _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/stores/fileStore */ \"(app-pages-browser)/./src/stores/fileStore.ts\");\n/* __next_internal_client_entry_do_not_use__ StreamingMessageParser,parseMessages,testWorkingFormat,debugFileCreation auto */ \nclass StreamingMessageParser {\n    parse(messageId, input) {\n        console.log(\"\\uD83D\\uDD0D Parsing message \".concat(messageId, \" with content length: \").concat(input.length));\n        console.log(\"\\uD83D\\uDCC4 Content preview:\", input.substring(0, 200) + \"...\");\n        let state = this.messages.get(messageId);\n        if (!state) {\n            state = {\n                position: 0,\n                insideAction: false,\n                insideArtifact: false,\n                currentAction: {\n                    content: \"\"\n                },\n                actionId: 0,\n                hasInstallExecuted: false\n            };\n            this.messages.set(messageId, state);\n        }\n        let output = \"\";\n        const regex = {\n            artifactOpen: /<boltArtifact[^>]*>/g,\n            artifactClose: /<\\/boltArtifact>/g,\n            actionOpen: /<boltAction[^>]*>/g,\n            actionClose: /<\\/boltAction>/g\n        };\n        const allActionData = {};\n        while(state.position < input.length){\n            if (state.insideArtifact) {\n                if (state.insideAction) {\n                    // 查找动作结束标签\n                    regex.actionClose.lastIndex = state.position;\n                    const actionCloseMatch = regex.actionClose.exec(input);\n                    if (actionCloseMatch) {\n                        const content = input.slice(state.position, actionCloseMatch.index);\n                        // 处理 file 和 shell 类型的 action\n                        if (\"type\" in state.currentAction) {\n                            const actionData = {\n                                artifactId: state.currentArtifact.id,\n                                messageId,\n                                actionId: String(state.actionId - 1),\n                                action: {\n                                    ...state.currentAction,\n                                    content\n                                }\n                            };\n                            console.log(\"\\uD83D\\uDCE6 Found complete action:\", actionData);\n                            // 根据 action 类型调用不同的回调\n                            if (state.currentAction.type === \"file\") {\n                                var // Call onActionStream for file creation\n                                _this_options_callbacks_onActionStream, _this_options_callbacks;\n                                allActionData[state.currentAction.filePath] = actionData;\n                                (_this_options_callbacks = this.options.callbacks) === null || _this_options_callbacks === void 0 ? void 0 : (_this_options_callbacks_onActionStream = _this_options_callbacks.onActionStream) === null || _this_options_callbacks_onActionStream === void 0 ? void 0 : _this_options_callbacks_onActionStream.call(_this_options_callbacks, actionData);\n                            } else if (state.currentAction.type === \"shell\" || state.currentAction.type === \"start\") {\n                                var // shell 类型只在关闭时处理\n                                _this_options_callbacks_onActionClose, _this_options_callbacks1;\n                                (_this_options_callbacks1 = this.options.callbacks) === null || _this_options_callbacks1 === void 0 ? void 0 : (_this_options_callbacks_onActionClose = _this_options_callbacks1.onActionClose) === null || _this_options_callbacks_onActionClose === void 0 ? void 0 : _this_options_callbacks_onActionClose.call(_this_options_callbacks1, actionData);\n                            }\n                        }\n                        state.position = actionCloseMatch.index + actionCloseMatch[0].length;\n                        state.insideAction = false;\n                    } else {\n                        // 只对 file 类型进行流式处理\n                        const remainingContent = input.slice(state.position);\n                        if (\"type\" in state.currentAction && state.currentAction.type === \"file\" && !allActionData[state.currentAction.filePath]) {\n                            var // Call onActionStream for streaming file content\n                            _this_options_callbacks_onActionStream1, _this_options_callbacks2;\n                            allActionData[state.currentAction.filePath] = {\n                                artifactId: state.currentArtifact.id,\n                                messageId,\n                                actionId: String(state.actionId - 1),\n                                action: {\n                                    ...state.currentAction,\n                                    content: remainingContent,\n                                    filePath: state.currentAction.filePath\n                                }\n                            };\n                            console.log(\"\\uD83D\\uDCE6 Found streaming action:\", allActionData[state.currentAction.filePath]);\n                            (_this_options_callbacks2 = this.options.callbacks) === null || _this_options_callbacks2 === void 0 ? void 0 : (_this_options_callbacks_onActionStream1 = _this_options_callbacks2.onActionStream) === null || _this_options_callbacks_onActionStream1 === void 0 ? void 0 : _this_options_callbacks_onActionStream1.call(_this_options_callbacks2, allActionData[state.currentAction.filePath]);\n                        }\n                        break;\n                    }\n                } else {\n                    // 查找下一个动作开始标签或者 artifact 结束标签\n                    const nextActionMatch = regex.actionOpen.exec(input.slice(state.position));\n                    const artifactCloseMatch = regex.artifactClose.exec(input.slice(state.position));\n                    if (nextActionMatch && (!artifactCloseMatch || nextActionMatch.index < artifactCloseMatch.index)) {\n                        var _this_options_callbacks_onActionOpen, _this_options_callbacks3;\n                        const actionTag = nextActionMatch[0];\n                        state.currentAction = this.parseActionTag(actionTag);\n                        state.insideAction = true;\n                        state.position += nextActionMatch.index + nextActionMatch[0].length;\n                        console.log(\"\\uD83D\\uDE80 Found action tag:\", actionTag, state.currentAction);\n                        (_this_options_callbacks3 = this.options.callbacks) === null || _this_options_callbacks3 === void 0 ? void 0 : (_this_options_callbacks_onActionOpen = _this_options_callbacks3.onActionOpen) === null || _this_options_callbacks_onActionOpen === void 0 ? void 0 : _this_options_callbacks_onActionOpen.call(_this_options_callbacks3, {\n                            artifactId: state.currentArtifact.id,\n                            messageId,\n                            actionId: String(state.actionId++),\n                            action: state.currentAction\n                        });\n                    } else if (artifactCloseMatch) {\n                        var _this_options_callbacks_onArtifactClose, _this_options_callbacks4;\n                        state.position += artifactCloseMatch.index + artifactCloseMatch[0].length;\n                        state.insideArtifact = false;\n                        (_this_options_callbacks4 = this.options.callbacks) === null || _this_options_callbacks4 === void 0 ? void 0 : (_this_options_callbacks_onArtifactClose = _this_options_callbacks4.onArtifactClose) === null || _this_options_callbacks_onArtifactClose === void 0 ? void 0 : _this_options_callbacks_onArtifactClose.call(_this_options_callbacks4, {\n                            messageId,\n                            ...state.currentArtifact\n                        });\n                    } else {\n                        break;\n                    }\n                }\n            } else {\n                // 查找 artifact 开始标签\n                const artifactMatch = regex.artifactOpen.exec(input.slice(state.position));\n                if (artifactMatch) {\n                    output += input.slice(state.position, state.position + artifactMatch.index);\n                    const artifactTag = artifactMatch[0];\n                    const artifactTitle = this.extractAttribute(artifactTag, \"title\");\n                    const artifactId = this.extractAttribute(artifactTag, \"id\");\n                    state.currentArtifact = {\n                        id: artifactId || \"default\",\n                        title: artifactTitle || \"Untitled\"\n                    };\n                    console.log(\"\\uD83C\\uDFAF Found artifact:\", state.currentArtifact);\n                    state.insideArtifact = true;\n                    state.position += artifactMatch.index + artifactMatch[0].length;\n                } else {\n                    output += input.slice(state.position);\n                    break;\n                }\n            }\n        }\n        return output;\n    }\n    parseActionTag(tag) {\n        const typeMatch = tag.match(/type=\"([^\"]+)\"/);\n        const filePathMatch = tag.match(/filePath=\"([^\"]+)\"/);\n        return {\n            type: typeMatch === null || typeMatch === void 0 ? void 0 : typeMatch[1],\n            filePath: filePathMatch === null || filePathMatch === void 0 ? void 0 : filePathMatch[1],\n            content: \"\"\n        };\n    }\n    extractAttribute(tag, attribute) {\n        const match = tag.match(new RegExp(\"\".concat(attribute, '=\"([^\"]+)\"')));\n        return match ? match[1] : null;\n    }\n    constructor(options = {}){\n        this.options = options;\n        this.messages = new Map();\n        this.isUseStartCommand = false;\n    }\n}\n// Create file with content function (equivalent to we-dev-client's createFileWithContent)\nconst createFileWithContent = async (filePath, content, syncFileClose)=>{\n    const { addFile } = _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__.useFileStore.getState();\n    console.log(\"\\uD83D\\uDCC1 Creating file: \".concat(filePath));\n    console.log(\"\\uD83D\\uDCC4 Content preview: \".concat(content.substring(0, 100), \"...\"));\n    await addFile(filePath, content, syncFileClose);\n    console.log(\"✅ File created successfully: \".concat(filePath));\n    return filePath;\n};\n// Create a global message parser instance with the working callback\nconst messageParser = new StreamingMessageParser({\n    callbacks: {\n        onActionStream: async (data)=>{\n            console.log(\"\\uD83D\\uDD25 onActionStream called with:\", data);\n            const action = data.action;\n            if (action.type === \"file\" && action.filePath && action.content) {\n                await createFileWithContent(action.filePath, action.content, true);\n                console.log(\"✅ File created via onActionStream: \".concat(action.filePath));\n            }\n        }\n    }\n});\nconst parseMessages = async (messages)=>{\n    console.log(\"\\uD83D\\uDE80 parseMessages called with \".concat(messages.length, \" messages\"));\n    for (const message of messages){\n        if (message.role === \"assistant\") {\n            console.log(\"\\uD83E\\uDD16 Parsing assistant message: \".concat(message.id));\n            console.log(\"\\uD83D\\uDCDD Message content:\", message.content);\n            messageParser.parse(message.id, message.content);\n        }\n    }\n};\n// Test functions\nconst testWorkingFormat = ()=>{\n    const testContent = 'I\\'ll create a simple HTML file for you:\\n\\n<boltArtifact id=\"simple-html\" title=\"Simple HTML File\">\\n<boltAction type=\"file\" filePath=\"index.html\"><!DOCTYPE html>\\n<html lang=\"en\">\\n<head>\\n    <meta charset=\"UTF-8\">\\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\\n    <title>Test Page</title>\\n</head>\\n<body>\\n    <h1>Hello from AI!</h1>\\n    <p>This file was created by the AI response parser.</p>\\n</body>\\n</html></boltAction>\\n</boltArtifact>\\n\\nThe HTML file has been created successfully.';\n    console.log(\"\\uD83E\\uDDEA Testing working format...\");\n    messageParser.parse(\"test-working\", testContent);\n};\nconst debugFileCreation = ()=>{\n    console.log(\"\\uD83D\\uDD27 Testing file creation directly...\");\n    const { addFile } = _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__.useFileStore.getState();\n    addFile(\"debug-test.html\", \"<h1>Debug Test</h1>\").then(()=>{\n        console.log(\"✅ Direct file creation successful\");\n        const files = _stores_fileStore__WEBPACK_IMPORTED_MODULE_0__.useFileStore.getState().files;\n        console.log(\"\\uD83D\\uDCC1 Current files:\", Object.keys(files));\n    }).catch((error)=>{\n        console.error(\"❌ Direct file creation failed:\", error);\n    });\n};\n// Make test functions available globally for debugging\nif (true) {\n    window.testWorkingFormat = testWorkingFormat;\n    window.debugFileCreation = debugFileCreation;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/messageParserNew.ts\n"));

/***/ })

});